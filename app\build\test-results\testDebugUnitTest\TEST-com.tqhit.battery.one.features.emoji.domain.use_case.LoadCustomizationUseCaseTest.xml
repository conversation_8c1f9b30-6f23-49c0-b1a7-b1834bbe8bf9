<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest" tests="19" skipped="0" failures="19" errors="0" timestamp="2025-06-20T09:39:52" hostname="DESKTOP-KBSUI08" time="0.048">
  <properties/>
  <testcase name="getCurrentUserCustomization should return current user customization" classname="com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest" time="0.044">
    <failure message="io.mockk.MockKException: no answer found for CustomizationRepository(#200).getUserCustomizationFlow() among the configured answers: ()" type="io.mockk.MockKException">io.mockk.MockKException: no answer found for CustomizationRepository(#200).getUserCustomizationFlow() among the configured answers: ()
	at app//io.mockk.impl.stub.MockKStub.defaultAnswer(MockKStub.kt:91)
	at app//io.mockk.impl.stub.MockKStub.answer(MockKStub.kt:42)
	at app//io.mockk.impl.recording.states.AnsweringState.call(AnsweringState.kt:16)
	at app//io.mockk.impl.recording.CommonCallRecorder.call(CommonCallRecorder.kt:53)
	at app//io.mockk.impl.stub.MockKStub.handleInvocation(MockKStub.kt:269)
	at app//io.mockk.impl.instantiation.JvmMockFactoryHelper$mockHandler$1.invocation(JvmMockFactoryHelper.kt:24)
	at app//io.mockk.proxy.jvm.advice.Interceptor.call(Interceptor.kt:21)
	at app//io.mockk.proxy.jvm.advice.BaseAdvice.handle(BaseAdvice.kt:42)
	at app//io.mockk.proxy.jvm.advice.jvm.JvmMockKProxyInterceptor.interceptNoSuper(JvmMockKProxyInterceptor.java:45)
	at app//com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository$Subclass7.getUserCustomizationFlow(Unknown Source)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase.&lt;init&gt;(LoadCustomizationUseCase.kt:47)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest.setUp(LoadCustomizationUseCaseTest.kt:72)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at app//org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at app//org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at app//org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at app//org.junit.internal.runners.statements.RunBefores.invokeMethod(RunBefores.java:33)
	at app//org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:24)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at app//org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at app//org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at app//org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at app//org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at app//org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at app//org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.runTestClass(JUnitTestClassExecutor.java:112)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:58)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:40)
	at org.gradle.api.internal.tasks.testing.junit.AbstractJUnitTestClassProcessor.processTestClass(AbstractJUnitTestClassProcessor.java:54)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.processTestClass(SuiteTestClassProcessor.java:53)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.processTestClass(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$2.run(TestWorker.java:183)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
</failure>
  </testcase>
  <testcase name="getCurrentSelectedStyle should return selected style when available" classname="com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest" time="0.0">
    <failure message="io.mockk.MockKException: no answer found for CustomizationRepository(#202).getUserCustomizationFlow() among the configured answers: ()" type="io.mockk.MockKException">io.mockk.MockKException: no answer found for CustomizationRepository(#202).getUserCustomizationFlow() among the configured answers: ()
	at app//io.mockk.impl.stub.MockKStub.defaultAnswer(MockKStub.kt:91)
	at app//io.mockk.impl.stub.MockKStub.answer(MockKStub.kt:42)
	at app//io.mockk.impl.recording.states.AnsweringState.call(AnsweringState.kt:16)
	at app//io.mockk.impl.recording.CommonCallRecorder.call(CommonCallRecorder.kt:53)
	at app//io.mockk.impl.stub.MockKStub.handleInvocation(MockKStub.kt:269)
	at app//io.mockk.impl.instantiation.JvmMockFactoryHelper$mockHandler$1.invocation(JvmMockFactoryHelper.kt:24)
	at app//io.mockk.proxy.jvm.advice.Interceptor.call(Interceptor.kt:21)
	at app//io.mockk.proxy.jvm.advice.BaseAdvice.handle(BaseAdvice.kt:42)
	at app//io.mockk.proxy.jvm.advice.jvm.JvmMockKProxyInterceptor.interceptNoSuper(JvmMockKProxyInterceptor.java:45)
	at app//com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository$Subclass7.getUserCustomizationFlow(Unknown Source)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase.&lt;init&gt;(LoadCustomizationUseCase.kt:47)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest.setUp(LoadCustomizationUseCaseTest.kt:72)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at app//org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at app//org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at app//org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at app//org.junit.internal.runners.statements.RunBefores.invokeMethod(RunBefores.java:33)
	at app//org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:24)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at app//org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at app//org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at app//org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at app//org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at app//org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at app//org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.runTestClass(JUnitTestClassExecutor.java:112)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:58)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:40)
	at org.gradle.api.internal.tasks.testing.junit.AbstractJUnitTestClassProcessor.processTestClass(AbstractJUnitTestClassProcessor.java:54)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.processTestClass(SuiteTestClassProcessor.java:53)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.processTestClass(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$2.run(TestWorker.java:183)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
</failure>
  </testcase>
  <testcase name="isFeatureReady should return false when permissions missing" classname="com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest" time="0.0">
    <failure message="io.mockk.MockKException: no answer found for CustomizationRepository(#204).getUserCustomizationFlow() among the configured answers: ()" type="io.mockk.MockKException">io.mockk.MockKException: no answer found for CustomizationRepository(#204).getUserCustomizationFlow() among the configured answers: ()
	at app//io.mockk.impl.stub.MockKStub.defaultAnswer(MockKStub.kt:91)
	at app//io.mockk.impl.stub.MockKStub.answer(MockKStub.kt:42)
	at app//io.mockk.impl.recording.states.AnsweringState.call(AnsweringState.kt:16)
	at app//io.mockk.impl.recording.CommonCallRecorder.call(CommonCallRecorder.kt:53)
	at app//io.mockk.impl.stub.MockKStub.handleInvocation(MockKStub.kt:269)
	at app//io.mockk.impl.instantiation.JvmMockFactoryHelper$mockHandler$1.invocation(JvmMockFactoryHelper.kt:24)
	at app//io.mockk.proxy.jvm.advice.Interceptor.call(Interceptor.kt:21)
	at app//io.mockk.proxy.jvm.advice.BaseAdvice.handle(BaseAdvice.kt:42)
	at app//io.mockk.proxy.jvm.advice.jvm.JvmMockKProxyInterceptor.interceptNoSuper(JvmMockKProxyInterceptor.java:45)
	at app//com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository$Subclass7.getUserCustomizationFlow(Unknown Source)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase.&lt;init&gt;(LoadCustomizationUseCase.kt:47)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest.setUp(LoadCustomizationUseCaseTest.kt:72)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at app//org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at app//org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at app//org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at app//org.junit.internal.runners.statements.RunBefores.invokeMethod(RunBefores.java:33)
	at app//org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:24)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at app//org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at app//org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at app//org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at app//org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at app//org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at app//org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.runTestClass(JUnitTestClassExecutor.java:112)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:58)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:40)
	at org.gradle.api.internal.tasks.testing.junit.AbstractJUnitTestClassProcessor.processTestClass(AbstractJUnitTestClassProcessor.java:54)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.processTestClass(SuiteTestClassProcessor.java:53)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.processTestClass(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$2.run(TestWorker.java:183)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
</failure>
  </testcase>
  <testcase name="getFeatureStatus should return correct status" classname="com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest" time="0.0">
    <failure message="io.mockk.MockKException: no answer found for CustomizationRepository(#206).getUserCustomizationFlow() among the configured answers: ()" type="io.mockk.MockKException">io.mockk.MockKException: no answer found for CustomizationRepository(#206).getUserCustomizationFlow() among the configured answers: ()
	at app//io.mockk.impl.stub.MockKStub.defaultAnswer(MockKStub.kt:91)
	at app//io.mockk.impl.stub.MockKStub.answer(MockKStub.kt:42)
	at app//io.mockk.impl.recording.states.AnsweringState.call(AnsweringState.kt:16)
	at app//io.mockk.impl.recording.CommonCallRecorder.call(CommonCallRecorder.kt:53)
	at app//io.mockk.impl.stub.MockKStub.handleInvocation(MockKStub.kt:269)
	at app//io.mockk.impl.instantiation.JvmMockFactoryHelper$mockHandler$1.invocation(JvmMockFactoryHelper.kt:24)
	at app//io.mockk.proxy.jvm.advice.Interceptor.call(Interceptor.kt:21)
	at app//io.mockk.proxy.jvm.advice.BaseAdvice.handle(BaseAdvice.kt:42)
	at app//io.mockk.proxy.jvm.advice.jvm.JvmMockKProxyInterceptor.interceptNoSuper(JvmMockKProxyInterceptor.java:45)
	at app//com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository$Subclass7.getUserCustomizationFlow(Unknown Source)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase.&lt;init&gt;(LoadCustomizationUseCase.kt:47)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest.setUp(LoadCustomizationUseCaseTest.kt:72)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at app//org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at app//org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at app//org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at app//org.junit.internal.runners.statements.RunBefores.invokeMethod(RunBefores.java:33)
	at app//org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:24)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at app//org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at app//org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at app//org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at app//org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at app//org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at app//org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.runTestClass(JUnitTestClassExecutor.java:112)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:58)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:40)
	at org.gradle.api.internal.tasks.testing.junit.AbstractJUnitTestClassProcessor.processTestClass(AbstractJUnitTestClassProcessor.java:54)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.processTestClass(SuiteTestClassProcessor.java:53)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.processTestClass(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$2.run(TestWorker.java:183)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
</failure>
  </testcase>
  <testcase name="userCustomizationFlow should handle missing selected style" classname="com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest" time="0.0">
    <failure message="io.mockk.MockKException: no answer found for CustomizationRepository(#208).getUserCustomizationFlow() among the configured answers: ()" type="io.mockk.MockKException">io.mockk.MockKException: no answer found for CustomizationRepository(#208).getUserCustomizationFlow() among the configured answers: ()
	at app//io.mockk.impl.stub.MockKStub.defaultAnswer(MockKStub.kt:91)
	at app//io.mockk.impl.stub.MockKStub.answer(MockKStub.kt:42)
	at app//io.mockk.impl.recording.states.AnsweringState.call(AnsweringState.kt:16)
	at app//io.mockk.impl.recording.CommonCallRecorder.call(CommonCallRecorder.kt:53)
	at app//io.mockk.impl.stub.MockKStub.handleInvocation(MockKStub.kt:269)
	at app//io.mockk.impl.instantiation.JvmMockFactoryHelper$mockHandler$1.invocation(JvmMockFactoryHelper.kt:24)
	at app//io.mockk.proxy.jvm.advice.Interceptor.call(Interceptor.kt:21)
	at app//io.mockk.proxy.jvm.advice.BaseAdvice.handle(BaseAdvice.kt:42)
	at app//io.mockk.proxy.jvm.advice.jvm.JvmMockKProxyInterceptor.interceptNoSuper(JvmMockKProxyInterceptor.java:45)
	at app//com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository$Subclass7.getUserCustomizationFlow(Unknown Source)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase.&lt;init&gt;(LoadCustomizationUseCase.kt:47)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest.setUp(LoadCustomizationUseCaseTest.kt:72)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at app//org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at app//org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at app//org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at app//org.junit.internal.runners.statements.RunBefores.invokeMethod(RunBefores.java:33)
	at app//org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:24)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at app//org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at app//org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at app//org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at app//org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at app//org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at app//org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.runTestClass(JUnitTestClassExecutor.java:112)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:58)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:40)
	at org.gradle.api.internal.tasks.testing.junit.AbstractJUnitTestClassProcessor.processTestClass(AbstractJUnitTestClassProcessor.java:54)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.processTestClass(SuiteTestClassProcessor.java:53)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.processTestClass(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$2.run(TestWorker.java:183)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
</failure>
  </testcase>
  <testcase name="isFeatureReadyFlow should emit correct readiness state" classname="com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest" time="0.0">
    <failure message="io.mockk.MockKException: no answer found for CustomizationRepository(#210).getUserCustomizationFlow() among the configured answers: ()" type="io.mockk.MockKException">io.mockk.MockKException: no answer found for CustomizationRepository(#210).getUserCustomizationFlow() among the configured answers: ()
	at app//io.mockk.impl.stub.MockKStub.defaultAnswer(MockKStub.kt:91)
	at app//io.mockk.impl.stub.MockKStub.answer(MockKStub.kt:42)
	at app//io.mockk.impl.recording.states.AnsweringState.call(AnsweringState.kt:16)
	at app//io.mockk.impl.recording.CommonCallRecorder.call(CommonCallRecorder.kt:53)
	at app//io.mockk.impl.stub.MockKStub.handleInvocation(MockKStub.kt:269)
	at app//io.mockk.impl.instantiation.JvmMockFactoryHelper$mockHandler$1.invocation(JvmMockFactoryHelper.kt:24)
	at app//io.mockk.proxy.jvm.advice.Interceptor.call(Interceptor.kt:21)
	at app//io.mockk.proxy.jvm.advice.BaseAdvice.handle(BaseAdvice.kt:42)
	at app//io.mockk.proxy.jvm.advice.jvm.JvmMockKProxyInterceptor.interceptNoSuper(JvmMockKProxyInterceptor.java:45)
	at app//com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository$Subclass7.getUserCustomizationFlow(Unknown Source)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase.&lt;init&gt;(LoadCustomizationUseCase.kt:47)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest.setUp(LoadCustomizationUseCaseTest.kt:72)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at app//org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at app//org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at app//org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at app//org.junit.internal.runners.statements.RunBefores.invokeMethod(RunBefores.java:33)
	at app//org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:24)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at app//org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at app//org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at app//org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at app//org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at app//org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at app//org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.runTestClass(JUnitTestClassExecutor.java:112)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:58)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:40)
	at org.gradle.api.internal.tasks.testing.junit.AbstractJUnitTestClassProcessor.processTestClass(AbstractJUnitTestClassProcessor.java:54)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.processTestClass(SuiteTestClassProcessor.java:53)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.processTestClass(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$2.run(TestWorker.java:183)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
</failure>
  </testcase>
  <testcase name="getCurrentEnrichedUserCustomization should return enriched data" classname="com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest" time="0.0">
    <failure message="io.mockk.MockKException: no answer found for CustomizationRepository(#212).getUserCustomizationFlow() among the configured answers: ()" type="io.mockk.MockKException">io.mockk.MockKException: no answer found for CustomizationRepository(#212).getUserCustomizationFlow() among the configured answers: ()
	at app//io.mockk.impl.stub.MockKStub.defaultAnswer(MockKStub.kt:91)
	at app//io.mockk.impl.stub.MockKStub.answer(MockKStub.kt:42)
	at app//io.mockk.impl.recording.states.AnsweringState.call(AnsweringState.kt:16)
	at app//io.mockk.impl.recording.CommonCallRecorder.call(CommonCallRecorder.kt:53)
	at app//io.mockk.impl.stub.MockKStub.handleInvocation(MockKStub.kt:269)
	at app//io.mockk.impl.instantiation.JvmMockFactoryHelper$mockHandler$1.invocation(JvmMockFactoryHelper.kt:24)
	at app//io.mockk.proxy.jvm.advice.Interceptor.call(Interceptor.kt:21)
	at app//io.mockk.proxy.jvm.advice.BaseAdvice.handle(BaseAdvice.kt:42)
	at app//io.mockk.proxy.jvm.advice.jvm.JvmMockKProxyInterceptor.interceptNoSuper(JvmMockKProxyInterceptor.java:45)
	at app//com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository$Subclass7.getUserCustomizationFlow(Unknown Source)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase.&lt;init&gt;(LoadCustomizationUseCase.kt:47)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest.setUp(LoadCustomizationUseCaseTest.kt:72)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at app//org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at app//org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at app//org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at app//org.junit.internal.runners.statements.RunBefores.invokeMethod(RunBefores.java:33)
	at app//org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:24)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at app//org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at app//org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at app//org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at app//org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at app//org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at app//org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.runTestClass(JUnitTestClassExecutor.java:112)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:58)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:40)
	at org.gradle.api.internal.tasks.testing.junit.AbstractJUnitTestClassProcessor.processTestClass(AbstractJUnitTestClassProcessor.java:54)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.processTestClass(SuiteTestClassProcessor.java:53)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.processTestClass(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$2.run(TestWorker.java:183)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
</failure>
  </testcase>
  <testcase name="getCurrentSelectedStyle should return null when no style selected" classname="com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest" time="0.0">
    <failure message="io.mockk.MockKException: no answer found for CustomizationRepository(#214).getUserCustomizationFlow() among the configured answers: ()" type="io.mockk.MockKException">io.mockk.MockKException: no answer found for CustomizationRepository(#214).getUserCustomizationFlow() among the configured answers: ()
	at app//io.mockk.impl.stub.MockKStub.defaultAnswer(MockKStub.kt:91)
	at app//io.mockk.impl.stub.MockKStub.answer(MockKStub.kt:42)
	at app//io.mockk.impl.recording.states.AnsweringState.call(AnsweringState.kt:16)
	at app//io.mockk.impl.recording.CommonCallRecorder.call(CommonCallRecorder.kt:53)
	at app//io.mockk.impl.stub.MockKStub.handleInvocation(MockKStub.kt:269)
	at app//io.mockk.impl.instantiation.JvmMockFactoryHelper$mockHandler$1.invocation(JvmMockFactoryHelper.kt:24)
	at app//io.mockk.proxy.jvm.advice.Interceptor.call(Interceptor.kt:21)
	at app//io.mockk.proxy.jvm.advice.BaseAdvice.handle(BaseAdvice.kt:42)
	at app//io.mockk.proxy.jvm.advice.jvm.JvmMockKProxyInterceptor.interceptNoSuper(JvmMockKProxyInterceptor.java:45)
	at app//com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository$Subclass7.getUserCustomizationFlow(Unknown Source)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase.&lt;init&gt;(LoadCustomizationUseCase.kt:47)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest.setUp(LoadCustomizationUseCaseTest.kt:72)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at app//org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at app//org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at app//org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at app//org.junit.internal.runners.statements.RunBefores.invokeMethod(RunBefores.java:33)
	at app//org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:24)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at app//org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at app//org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at app//org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at app//org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at app//org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at app//org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.runTestClass(JUnitTestClassExecutor.java:112)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:58)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:40)
	at org.gradle.api.internal.tasks.testing.junit.AbstractJUnitTestClassProcessor.processTestClass(AbstractJUnitTestClassProcessor.java:54)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.processTestClass(SuiteTestClassProcessor.java:53)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.processTestClass(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$2.run(TestWorker.java:183)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
</failure>
  </testcase>
  <testcase name="isFeatureReady should return true when all conditions met" classname="com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest" time="0.001">
    <failure message="io.mockk.MockKException: no answer found for CustomizationRepository(#216).getUserCustomizationFlow() among the configured answers: ()" type="io.mockk.MockKException">io.mockk.MockKException: no answer found for CustomizationRepository(#216).getUserCustomizationFlow() among the configured answers: ()
	at app//io.mockk.impl.stub.MockKStub.defaultAnswer(MockKStub.kt:91)
	at app//io.mockk.impl.stub.MockKStub.answer(MockKStub.kt:42)
	at app//io.mockk.impl.recording.states.AnsweringState.call(AnsweringState.kt:16)
	at app//io.mockk.impl.recording.CommonCallRecorder.call(CommonCallRecorder.kt:53)
	at app//io.mockk.impl.stub.MockKStub.handleInvocation(MockKStub.kt:269)
	at app//io.mockk.impl.instantiation.JvmMockFactoryHelper$mockHandler$1.invocation(JvmMockFactoryHelper.kt:24)
	at app//io.mockk.proxy.jvm.advice.Interceptor.call(Interceptor.kt:21)
	at app//io.mockk.proxy.jvm.advice.BaseAdvice.handle(BaseAdvice.kt:42)
	at app//io.mockk.proxy.jvm.advice.jvm.JvmMockKProxyInterceptor.interceptNoSuper(JvmMockKProxyInterceptor.java:45)
	at app//com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository$Subclass7.getUserCustomizationFlow(Unknown Source)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase.&lt;init&gt;(LoadCustomizationUseCase.kt:47)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest.setUp(LoadCustomizationUseCaseTest.kt:72)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at app//org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at app//org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at app//org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at app//org.junit.internal.runners.statements.RunBefores.invokeMethod(RunBefores.java:33)
	at app//org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:24)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at app//org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at app//org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at app//org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at app//org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at app//org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at app//org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.runTestClass(JUnitTestClassExecutor.java:112)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:58)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:40)
	at org.gradle.api.internal.tasks.testing.junit.AbstractJUnitTestClassProcessor.processTestClass(AbstractJUnitTestClassProcessor.java:54)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.processTestClass(SuiteTestClassProcessor.java:53)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.processTestClass(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$2.run(TestWorker.java:183)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
</failure>
  </testcase>
  <testcase name="userCustomizationFlow should handle empty style list" classname="com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest" time="0.0">
    <failure message="io.mockk.MockKException: no answer found for CustomizationRepository(#218).getUserCustomizationFlow() among the configured answers: ()" type="io.mockk.MockKException">io.mockk.MockKException: no answer found for CustomizationRepository(#218).getUserCustomizationFlow() among the configured answers: ()
	at app//io.mockk.impl.stub.MockKStub.defaultAnswer(MockKStub.kt:91)
	at app//io.mockk.impl.stub.MockKStub.answer(MockKStub.kt:42)
	at app//io.mockk.impl.recording.states.AnsweringState.call(AnsweringState.kt:16)
	at app//io.mockk.impl.recording.CommonCallRecorder.call(CommonCallRecorder.kt:53)
	at app//io.mockk.impl.stub.MockKStub.handleInvocation(MockKStub.kt:269)
	at app//io.mockk.impl.instantiation.JvmMockFactoryHelper$mockHandler$1.invocation(JvmMockFactoryHelper.kt:24)
	at app//io.mockk.proxy.jvm.advice.Interceptor.call(Interceptor.kt:21)
	at app//io.mockk.proxy.jvm.advice.BaseAdvice.handle(BaseAdvice.kt:42)
	at app//io.mockk.proxy.jvm.advice.jvm.JvmMockKProxyInterceptor.interceptNoSuper(JvmMockKProxyInterceptor.java:45)
	at app//com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository$Subclass7.getUserCustomizationFlow(Unknown Source)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase.&lt;init&gt;(LoadCustomizationUseCase.kt:47)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest.setUp(LoadCustomizationUseCaseTest.kt:72)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at app//org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at app//org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at app//org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at app//org.junit.internal.runners.statements.RunBefores.invokeMethod(RunBefores.java:33)
	at app//org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:24)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at app//org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at app//org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at app//org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at app//org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at app//org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at app//org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.runTestClass(JUnitTestClassExecutor.java:112)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:58)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:40)
	at org.gradle.api.internal.tasks.testing.junit.AbstractJUnitTestClassProcessor.processTestClass(AbstractJUnitTestClassProcessor.java:54)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.processTestClass(SuiteTestClassProcessor.java:53)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.processTestClass(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$2.run(TestWorker.java:183)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
</failure>
  </testcase>
  <testcase name="userCustomizationFlow should emit enriched customization data" classname="com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest" time="0.0">
    <failure message="io.mockk.MockKException: no answer found for CustomizationRepository(#220).getUserCustomizationFlow() among the configured answers: ()" type="io.mockk.MockKException">io.mockk.MockKException: no answer found for CustomizationRepository(#220).getUserCustomizationFlow() among the configured answers: ()
	at app//io.mockk.impl.stub.MockKStub.defaultAnswer(MockKStub.kt:91)
	at app//io.mockk.impl.stub.MockKStub.answer(MockKStub.kt:42)
	at app//io.mockk.impl.recording.states.AnsweringState.call(AnsweringState.kt:16)
	at app//io.mockk.impl.recording.CommonCallRecorder.call(CommonCallRecorder.kt:53)
	at app//io.mockk.impl.stub.MockKStub.handleInvocation(MockKStub.kt:269)
	at app//io.mockk.impl.instantiation.JvmMockFactoryHelper$mockHandler$1.invocation(JvmMockFactoryHelper.kt:24)
	at app//io.mockk.proxy.jvm.advice.Interceptor.call(Interceptor.kt:21)
	at app//io.mockk.proxy.jvm.advice.BaseAdvice.handle(BaseAdvice.kt:42)
	at app//io.mockk.proxy.jvm.advice.jvm.JvmMockKProxyInterceptor.interceptNoSuper(JvmMockKProxyInterceptor.java:45)
	at app//com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository$Subclass7.getUserCustomizationFlow(Unknown Source)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase.&lt;init&gt;(LoadCustomizationUseCase.kt:47)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest.setUp(LoadCustomizationUseCaseTest.kt:72)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at app//org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at app//org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at app//org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at app//org.junit.internal.runners.statements.RunBefores.invokeMethod(RunBefores.java:33)
	at app//org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:24)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at app//org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at app//org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at app//org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at app//org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at app//org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at app//org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.runTestClass(JUnitTestClassExecutor.java:112)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:58)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:40)
	at org.gradle.api.internal.tasks.testing.junit.AbstractJUnitTestClassProcessor.processTestClass(AbstractJUnitTestClassProcessor.java:54)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.processTestClass(SuiteTestClassProcessor.java:53)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.processTestClass(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$2.run(TestWorker.java:183)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
</failure>
  </testcase>
  <testcase name="validateCurrentConfiguration should return issues for invalid configuration" classname="com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest" time="0.0">
    <failure message="io.mockk.MockKException: no answer found for CustomizationRepository(#222).getUserCustomizationFlow() among the configured answers: ()" type="io.mockk.MockKException">io.mockk.MockKException: no answer found for CustomizationRepository(#222).getUserCustomizationFlow() among the configured answers: ()
	at app//io.mockk.impl.stub.MockKStub.defaultAnswer(MockKStub.kt:91)
	at app//io.mockk.impl.stub.MockKStub.answer(MockKStub.kt:42)
	at app//io.mockk.impl.recording.states.AnsweringState.call(AnsweringState.kt:16)
	at app//io.mockk.impl.recording.CommonCallRecorder.call(CommonCallRecorder.kt:53)
	at app//io.mockk.impl.stub.MockKStub.handleInvocation(MockKStub.kt:269)
	at app//io.mockk.impl.instantiation.JvmMockFactoryHelper$mockHandler$1.invocation(JvmMockFactoryHelper.kt:24)
	at app//io.mockk.proxy.jvm.advice.Interceptor.call(Interceptor.kt:21)
	at app//io.mockk.proxy.jvm.advice.BaseAdvice.handle(BaseAdvice.kt:42)
	at app//io.mockk.proxy.jvm.advice.jvm.JvmMockKProxyInterceptor.interceptNoSuper(JvmMockKProxyInterceptor.java:45)
	at app//com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository$Subclass7.getUserCustomizationFlow(Unknown Source)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase.&lt;init&gt;(LoadCustomizationUseCase.kt:47)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest.setUp(LoadCustomizationUseCaseTest.kt:72)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at app//org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at app//org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at app//org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at app//org.junit.internal.runners.statements.RunBefores.invokeMethod(RunBefores.java:33)
	at app//org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:24)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at app//org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at app//org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at app//org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at app//org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at app//org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at app//org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.runTestClass(JUnitTestClassExecutor.java:112)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:58)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:40)
	at org.gradle.api.internal.tasks.testing.junit.AbstractJUnitTestClassProcessor.processTestClass(AbstractJUnitTestClassProcessor.java:54)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.processTestClass(SuiteTestClassProcessor.java:53)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.processTestClass(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$2.run(TestWorker.java:183)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
</failure>
  </testcase>
  <testcase name="getFeatureStatus should return permissions required when missing permissions" classname="com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest" time="0.001">
    <failure message="io.mockk.MockKException: no answer found for CustomizationRepository(#224).getUserCustomizationFlow() among the configured answers: ()" type="io.mockk.MockKException">io.mockk.MockKException: no answer found for CustomizationRepository(#224).getUserCustomizationFlow() among the configured answers: ()
	at app//io.mockk.impl.stub.MockKStub.defaultAnswer(MockKStub.kt:91)
	at app//io.mockk.impl.stub.MockKStub.answer(MockKStub.kt:42)
	at app//io.mockk.impl.recording.states.AnsweringState.call(AnsweringState.kt:16)
	at app//io.mockk.impl.recording.CommonCallRecorder.call(CommonCallRecorder.kt:53)
	at app//io.mockk.impl.stub.MockKStub.handleInvocation(MockKStub.kt:269)
	at app//io.mockk.impl.instantiation.JvmMockFactoryHelper$mockHandler$1.invocation(JvmMockFactoryHelper.kt:24)
	at app//io.mockk.proxy.jvm.advice.Interceptor.call(Interceptor.kt:21)
	at app//io.mockk.proxy.jvm.advice.BaseAdvice.handle(BaseAdvice.kt:42)
	at app//io.mockk.proxy.jvm.advice.jvm.JvmMockKProxyInterceptor.interceptNoSuper(JvmMockKProxyInterceptor.java:45)
	at app//com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository$Subclass7.getUserCustomizationFlow(Unknown Source)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase.&lt;init&gt;(LoadCustomizationUseCase.kt:47)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest.setUp(LoadCustomizationUseCaseTest.kt:72)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at app//org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at app//org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at app//org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at app//org.junit.internal.runners.statements.RunBefores.invokeMethod(RunBefores.java:33)
	at app//org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:24)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at app//org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at app//org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at app//org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at app//org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at app//org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at app//org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.runTestClass(JUnitTestClassExecutor.java:112)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:58)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:40)
	at org.gradle.api.internal.tasks.testing.junit.AbstractJUnitTestClassProcessor.processTestClass(AbstractJUnitTestClassProcessor.java:54)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.processTestClass(SuiteTestClassProcessor.java:53)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.processTestClass(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$2.run(TestWorker.java:183)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
</failure>
  </testcase>
  <testcase name="featureStatusFlow should emit correct status changes" classname="com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest" time="0.0">
    <failure message="io.mockk.MockKException: no answer found for CustomizationRepository(#226).getUserCustomizationFlow() among the configured answers: ()" type="io.mockk.MockKException">io.mockk.MockKException: no answer found for CustomizationRepository(#226).getUserCustomizationFlow() among the configured answers: ()
	at app//io.mockk.impl.stub.MockKStub.defaultAnswer(MockKStub.kt:91)
	at app//io.mockk.impl.stub.MockKStub.answer(MockKStub.kt:42)
	at app//io.mockk.impl.recording.states.AnsweringState.call(AnsweringState.kt:16)
	at app//io.mockk.impl.recording.CommonCallRecorder.call(CommonCallRecorder.kt:53)
	at app//io.mockk.impl.stub.MockKStub.handleInvocation(MockKStub.kt:269)
	at app//io.mockk.impl.instantiation.JvmMockFactoryHelper$mockHandler$1.invocation(JvmMockFactoryHelper.kt:24)
	at app//io.mockk.proxy.jvm.advice.Interceptor.call(Interceptor.kt:21)
	at app//io.mockk.proxy.jvm.advice.BaseAdvice.handle(BaseAdvice.kt:42)
	at app//io.mockk.proxy.jvm.advice.jvm.JvmMockKProxyInterceptor.interceptNoSuper(JvmMockKProxyInterceptor.java:45)
	at app//com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository$Subclass7.getUserCustomizationFlow(Unknown Source)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase.&lt;init&gt;(LoadCustomizationUseCase.kt:47)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest.setUp(LoadCustomizationUseCaseTest.kt:72)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at app//org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at app//org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at app//org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at app//org.junit.internal.runners.statements.RunBefores.invokeMethod(RunBefores.java:33)
	at app//org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:24)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at app//org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at app//org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at app//org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at app//org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at app//org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at app//org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.runTestClass(JUnitTestClassExecutor.java:112)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:58)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:40)
	at org.gradle.api.internal.tasks.testing.junit.AbstractJUnitTestClassProcessor.processTestClass(AbstractJUnitTestClassProcessor.java:54)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.processTestClass(SuiteTestClassProcessor.java:53)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.processTestClass(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$2.run(TestWorker.java:183)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
</failure>
  </testcase>
  <testcase name="getCurrentCustomizationConfig should return default on error" classname="com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest" time="0.0">
    <failure message="io.mockk.MockKException: no answer found for CustomizationRepository(#228).getUserCustomizationFlow() among the configured answers: ()" type="io.mockk.MockKException">io.mockk.MockKException: no answer found for CustomizationRepository(#228).getUserCustomizationFlow() among the configured answers: ()
	at app//io.mockk.impl.stub.MockKStub.defaultAnswer(MockKStub.kt:91)
	at app//io.mockk.impl.stub.MockKStub.answer(MockKStub.kt:42)
	at app//io.mockk.impl.recording.states.AnsweringState.call(AnsweringState.kt:16)
	at app//io.mockk.impl.recording.CommonCallRecorder.call(CommonCallRecorder.kt:53)
	at app//io.mockk.impl.stub.MockKStub.handleInvocation(MockKStub.kt:269)
	at app//io.mockk.impl.instantiation.JvmMockFactoryHelper$mockHandler$1.invocation(JvmMockFactoryHelper.kt:24)
	at app//io.mockk.proxy.jvm.advice.Interceptor.call(Interceptor.kt:21)
	at app//io.mockk.proxy.jvm.advice.BaseAdvice.handle(BaseAdvice.kt:42)
	at app//io.mockk.proxy.jvm.advice.jvm.JvmMockKProxyInterceptor.interceptNoSuper(JvmMockKProxyInterceptor.java:45)
	at app//com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository$Subclass7.getUserCustomizationFlow(Unknown Source)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase.&lt;init&gt;(LoadCustomizationUseCase.kt:47)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest.setUp(LoadCustomizationUseCaseTest.kt:72)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at app//org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at app//org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at app//org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at app//org.junit.internal.runners.statements.RunBefores.invokeMethod(RunBefores.java:33)
	at app//org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:24)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at app//org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at app//org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at app//org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at app//org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at app//org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at app//org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.runTestClass(JUnitTestClassExecutor.java:112)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:58)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:40)
	at org.gradle.api.internal.tasks.testing.junit.AbstractJUnitTestClassProcessor.processTestClass(AbstractJUnitTestClassProcessor.java:54)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.processTestClass(SuiteTestClassProcessor.java:53)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.processTestClass(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$2.run(TestWorker.java:183)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
</failure>
  </testcase>
  <testcase name="getCurrentCustomizationConfig should return current configuration" classname="com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest" time="0.0">
    <failure message="io.mockk.MockKException: no answer found for CustomizationRepository(#230).getUserCustomizationFlow() among the configured answers: ()" type="io.mockk.MockKException">io.mockk.MockKException: no answer found for CustomizationRepository(#230).getUserCustomizationFlow() among the configured answers: ()
	at app//io.mockk.impl.stub.MockKStub.defaultAnswer(MockKStub.kt:91)
	at app//io.mockk.impl.stub.MockKStub.answer(MockKStub.kt:42)
	at app//io.mockk.impl.recording.states.AnsweringState.call(AnsweringState.kt:16)
	at app//io.mockk.impl.recording.CommonCallRecorder.call(CommonCallRecorder.kt:53)
	at app//io.mockk.impl.stub.MockKStub.handleInvocation(MockKStub.kt:269)
	at app//io.mockk.impl.instantiation.JvmMockFactoryHelper$mockHandler$1.invocation(JvmMockFactoryHelper.kt:24)
	at app//io.mockk.proxy.jvm.advice.Interceptor.call(Interceptor.kt:21)
	at app//io.mockk.proxy.jvm.advice.BaseAdvice.handle(BaseAdvice.kt:42)
	at app//io.mockk.proxy.jvm.advice.jvm.JvmMockKProxyInterceptor.interceptNoSuper(JvmMockKProxyInterceptor.java:45)
	at app//com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository$Subclass7.getUserCustomizationFlow(Unknown Source)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase.&lt;init&gt;(LoadCustomizationUseCase.kt:47)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest.setUp(LoadCustomizationUseCaseTest.kt:72)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at app//org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at app//org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at app//org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at app//org.junit.internal.runners.statements.RunBefores.invokeMethod(RunBefores.java:33)
	at app//org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:24)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at app//org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at app//org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at app//org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at app//org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at app//org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at app//org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.runTestClass(JUnitTestClassExecutor.java:112)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:58)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:40)
	at org.gradle.api.internal.tasks.testing.junit.AbstractJUnitTestClassProcessor.processTestClass(AbstractJUnitTestClassProcessor.java:54)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.processTestClass(SuiteTestClassProcessor.java:53)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.processTestClass(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$2.run(TestWorker.java:183)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
</failure>
  </testcase>
  <testcase name="getCurrentSelectedStyle should return null when style not found" classname="com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest" time="0.001">
    <failure message="io.mockk.MockKException: no answer found for CustomizationRepository(#232).getUserCustomizationFlow() among the configured answers: ()" type="io.mockk.MockKException">io.mockk.MockKException: no answer found for CustomizationRepository(#232).getUserCustomizationFlow() among the configured answers: ()
	at app//io.mockk.impl.stub.MockKStub.defaultAnswer(MockKStub.kt:91)
	at app//io.mockk.impl.stub.MockKStub.answer(MockKStub.kt:42)
	at app//io.mockk.impl.recording.states.AnsweringState.call(AnsweringState.kt:16)
	at app//io.mockk.impl.recording.CommonCallRecorder.call(CommonCallRecorder.kt:53)
	at app//io.mockk.impl.stub.MockKStub.handleInvocation(MockKStub.kt:269)
	at app//io.mockk.impl.instantiation.JvmMockFactoryHelper$mockHandler$1.invocation(JvmMockFactoryHelper.kt:24)
	at app//io.mockk.proxy.jvm.advice.Interceptor.call(Interceptor.kt:21)
	at app//io.mockk.proxy.jvm.advice.BaseAdvice.handle(BaseAdvice.kt:42)
	at app//io.mockk.proxy.jvm.advice.jvm.JvmMockKProxyInterceptor.interceptNoSuper(JvmMockKProxyInterceptor.java:45)
	at app//com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository$Subclass7.getUserCustomizationFlow(Unknown Source)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase.&lt;init&gt;(LoadCustomizationUseCase.kt:47)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest.setUp(LoadCustomizationUseCaseTest.kt:72)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at app//org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at app//org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at app//org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at app//org.junit.internal.runners.statements.RunBefores.invokeMethod(RunBefores.java:33)
	at app//org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:24)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at app//org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at app//org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at app//org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at app//org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at app//org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at app//org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.runTestClass(JUnitTestClassExecutor.java:112)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:58)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:40)
	at org.gradle.api.internal.tasks.testing.junit.AbstractJUnitTestClassProcessor.processTestClass(AbstractJUnitTestClassProcessor.java:54)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.processTestClass(SuiteTestClassProcessor.java:53)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.processTestClass(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$2.run(TestWorker.java:183)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
</failure>
  </testcase>
  <testcase name="validateCurrentConfiguration should handle validation errors" classname="com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest" time="0.0">
    <failure message="io.mockk.MockKException: no answer found for CustomizationRepository(#234).getUserCustomizationFlow() among the configured answers: ()" type="io.mockk.MockKException">io.mockk.MockKException: no answer found for CustomizationRepository(#234).getUserCustomizationFlow() among the configured answers: ()
	at app//io.mockk.impl.stub.MockKStub.defaultAnswer(MockKStub.kt:91)
	at app//io.mockk.impl.stub.MockKStub.answer(MockKStub.kt:42)
	at app//io.mockk.impl.recording.states.AnsweringState.call(AnsweringState.kt:16)
	at app//io.mockk.impl.recording.CommonCallRecorder.call(CommonCallRecorder.kt:53)
	at app//io.mockk.impl.stub.MockKStub.handleInvocation(MockKStub.kt:269)
	at app//io.mockk.impl.instantiation.JvmMockFactoryHelper$mockHandler$1.invocation(JvmMockFactoryHelper.kt:24)
	at app//io.mockk.proxy.jvm.advice.Interceptor.call(Interceptor.kt:21)
	at app//io.mockk.proxy.jvm.advice.BaseAdvice.handle(BaseAdvice.kt:42)
	at app//io.mockk.proxy.jvm.advice.jvm.JvmMockKProxyInterceptor.interceptNoSuper(JvmMockKProxyInterceptor.java:45)
	at app//com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository$Subclass7.getUserCustomizationFlow(Unknown Source)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase.&lt;init&gt;(LoadCustomizationUseCase.kt:47)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest.setUp(LoadCustomizationUseCaseTest.kt:72)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at app//org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at app//org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at app//org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at app//org.junit.internal.runners.statements.RunBefores.invokeMethod(RunBefores.java:33)
	at app//org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:24)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at app//org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at app//org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at app//org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at app//org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at app//org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at app//org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.runTestClass(JUnitTestClassExecutor.java:112)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:58)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:40)
	at org.gradle.api.internal.tasks.testing.junit.AbstractJUnitTestClassProcessor.processTestClass(AbstractJUnitTestClassProcessor.java:54)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.processTestClass(SuiteTestClassProcessor.java:53)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.processTestClass(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$2.run(TestWorker.java:183)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
</failure>
  </testcase>
  <testcase name="validateCurrentConfiguration should return empty list for valid configuration" classname="com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest" time="0.0">
    <failure message="io.mockk.MockKException: no answer found for CustomizationRepository(#236).getUserCustomizationFlow() among the configured answers: ()" type="io.mockk.MockKException">io.mockk.MockKException: no answer found for CustomizationRepository(#236).getUserCustomizationFlow() among the configured answers: ()
	at app//io.mockk.impl.stub.MockKStub.defaultAnswer(MockKStub.kt:91)
	at app//io.mockk.impl.stub.MockKStub.answer(MockKStub.kt:42)
	at app//io.mockk.impl.recording.states.AnsweringState.call(AnsweringState.kt:16)
	at app//io.mockk.impl.recording.CommonCallRecorder.call(CommonCallRecorder.kt:53)
	at app//io.mockk.impl.stub.MockKStub.handleInvocation(MockKStub.kt:269)
	at app//io.mockk.impl.instantiation.JvmMockFactoryHelper$mockHandler$1.invocation(JvmMockFactoryHelper.kt:24)
	at app//io.mockk.proxy.jvm.advice.Interceptor.call(Interceptor.kt:21)
	at app//io.mockk.proxy.jvm.advice.BaseAdvice.handle(BaseAdvice.kt:42)
	at app//io.mockk.proxy.jvm.advice.jvm.JvmMockKProxyInterceptor.interceptNoSuper(JvmMockKProxyInterceptor.java:45)
	at app//com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository$Subclass7.getUserCustomizationFlow(Unknown Source)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase.&lt;init&gt;(LoadCustomizationUseCase.kt:47)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest.setUp(LoadCustomizationUseCaseTest.kt:72)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at app//org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at app//org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at app//org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at app//org.junit.internal.runners.statements.RunBefores.invokeMethod(RunBefores.java:33)
	at app//org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:24)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at app//org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at app//org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at app//org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at app//org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at app//org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at app//org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.runTestClass(JUnitTestClassExecutor.java:112)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:58)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:40)
	at org.gradle.api.internal.tasks.testing.junit.AbstractJUnitTestClassProcessor.processTestClass(AbstractJUnitTestClassProcessor.java:54)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.processTestClass(SuiteTestClassProcessor.java:53)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.processTestClass(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$2.run(TestWorker.java:183)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
</failure>
  </testcase>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
