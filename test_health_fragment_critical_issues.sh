#!/bin/bash

# Health Fragment Critical Issues Test Script
# Tests the two remaining critical issues:
# 1. Cumulative Sessions Counter Still Stuck at 10
# 2. Temperature Chart Y-Axis Labels Still Incorrect

set -e

APP_PACKAGE="com.fc.p.tj.charginganimation.batterycharging.chargeeffect"
LOGCAT_TAGS="HealthFragment:D HealthRepository:D ChargingSessionManager:D SESSION_RESET:D TEMP_Y_AXIS:D COMPREHENSIVE_TEST:D"

# Use emulator for testing
DEVICE_ID="emulator-5554"
ADB_CMD="adb -s $DEVICE_ID"

echo "=========================================="
echo "Health Fragment Critical Issues Test"
echo "=========================================="

# Function to log with timestamp
log_with_timestamp() {
    echo "[$(date '+%H:%M:%S')] $1"
}

# Function to wait for user input
wait_for_user() {
    echo ""
    read -p "Press Enter to continue..."
    echo ""
}

# Function to check if device/emulator is connected
check_device() {
    if ! $ADB_CMD devices | grep -q "$DEVICE_ID.*device$"; then
        echo "ERROR: Target emulator $DEVICE_ID not connected or not authorized"
        echo "Available devices:"
        adb devices
        echo "Please start the emulator and enable USB debugging"
        exit 1
    fi
    log_with_timestamp "Target emulator $DEVICE_ID connected and ready"
}

# Function to clear app data and restart
reset_app() {
    log_with_timestamp "Clearing app data and restarting..."
    $ADB_CMD shell am force-stop $APP_PACKAGE
    $ADB_CMD shell pm clear $APP_PACKAGE
    sleep 2
    $ADB_CMD shell am start -n "$APP_PACKAGE/com.tqhit.battery.one.activity.splash.SplashActivity"
    sleep 5
    log_with_timestamp "App restarted with clean data"
}

# Function to start filtered logcat monitoring
start_logcat_monitoring() {
    log_with_timestamp "Starting filtered logcat monitoring for critical issues..."
    $ADB_CMD logcat -c  # Clear existing logs
    $ADB_CMD logcat $LOGCAT_TAGS > critical_issues_logs.txt &
    LOGCAT_PID=$!
    log_with_timestamp "Logcat monitoring started (PID: $LOGCAT_PID)"
    sleep 2
}

# Function to stop logcat monitoring
stop_logcat_monitoring() {
    if [ ! -z "$LOGCAT_PID" ]; then
        kill $LOGCAT_PID 2>/dev/null || true
        log_with_timestamp "Logcat monitoring stopped"
    fi
}

# Function to navigate to health fragment
navigate_to_health() {
    log_with_timestamp "Navigating to Health fragment..."
    # Tap on health/stats tab (rightmost tab in bottom navigation)
    $ADB_CMD shell input tap 540 2100  # Adjust coordinates for emulator
    sleep 3
    log_with_timestamp "Health fragment should now be visible"
}

# Function to trigger session clearing test via ADB
test_session_clearing() {
    log_with_timestamp "Testing session clearing functionality..."
    
    echo "This test will:"
    echo "1. Check current session count (should be 10 if stuck)"
    echo "2. Trigger session clearing via ADB"
    echo "3. Verify session count becomes 0 and stays 0"
    echo "4. Ensure no automatic sample generation occurs"
    
    wait_for_user
    
    # Use ADB to trigger the comprehensive test method
    log_with_timestamp "Triggering comprehensive Health fragment test via ADB..."
    
    # Method 1: Try to trigger via broadcast (if implemented)
    adb shell am broadcast -a "com.tqhit.battery.one.TEST_HEALTH_FRAGMENT" 2>/dev/null || true
    
    # Method 2: Try to trigger via activity intent
    $ADB_CMD shell am start -n "$APP_PACKAGE/com.tqhit.battery.one.activity.splash.SplashActivity" \
        --es "test_action" "comprehensive_health_test" 2>/dev/null || true
    
    # Method 3: Manual instruction for user
    echo ""
    echo "MANUAL TEST REQUIRED:"
    echo "Since we cannot directly call the test method via ADB, please:"
    echo "1. Add a button or gesture in the Health fragment to call runComprehensiveHealthFragmentTest()"
    echo "2. Or use Android Studio debugger to call the method manually"
    echo "3. Or implement a debug menu accessible via long press"
    echo ""
    echo "The test method runComprehensiveHealthFragmentTest() will:"
    echo "- Clear all cached session data"
    echo "- Test temperature Y-axis labeling"
    echo "- Force chart updates with real data"
    echo ""
    
    wait_for_user
    
    log_with_timestamp "Waiting for test execution and monitoring logs..."
    sleep 10
}

# Function to test temperature Y-axis labels
test_temperature_labels() {
    log_with_timestamp "Testing temperature Y-axis labels..."
    
    echo "This test will:"
    echo "1. Check if temperature Y-axis shows proper temperature values"
    echo "2. Verify labels are not showing '0' values"
    echo "3. Test with different temperature ranges"
    
    wait_for_user
    
    # Simulate temperature changes to generate data
    log_with_timestamp "Simulating temperature changes..."
    
    # Note: Emulator temperature simulation is limited
    # We'll rely on the manual test method instead
    
    echo ""
    echo "TEMPERATURE TEST INSTRUCTIONS:"
    echo "1. Look at the temperature chart Y-axis labels (t1Temp through t9Temp)"
    echo "2. They should show temperature values like '25', '30', '35', etc."
    echo "3. They should NOT show '0' or empty values"
    echo "4. The range should be based on actual temperature data"
    echo ""
    
    wait_for_user
    
    log_with_timestamp "Temperature label test monitoring..."
    sleep 5
}

# Function to analyze logs for specific issues
analyze_critical_issues() {
    log_with_timestamp "Analyzing logs for critical issues..."
    
    echo ""
    echo "=== SESSION CLEARING ANALYSIS ==="
    echo "Looking for session clearing evidence:"
    grep -i "session.*clear\|session_reset\|session.*before\|session.*after" critical_issues_logs.txt | tail -10
    
    echo ""
    echo "=== SAMPLE GENERATION PREVENTION ==="
    echo "Checking if sample generation was prevented:"
    grep -i "sample.*generation\|sessions.*cleared.*testing\|skip.*sample" critical_issues_logs.txt | tail -5
    
    echo ""
    echo "=== TEMPERATURE Y-AXIS ANALYSIS ==="
    echo "Looking for temperature Y-axis updates:"
    grep -i "temp_y_axis\|temperature.*label\|updated.*label" critical_issues_logs.txt | tail -10
    
    echo ""
    echo "=== COMPREHENSIVE TEST RESULTS ==="
    echo "Checking comprehensive test execution:"
    grep -i "comprehensive_test" critical_issues_logs.txt | tail -10
    
    echo ""
    echo "=== ERROR ANALYSIS ==="
    echo "Looking for any errors:"
    grep -i "error\|exception\|failed" critical_issues_logs.txt | tail -5
}

# Function to verify fixes
verify_fixes() {
    log_with_timestamp "Verifying fixes..."
    
    echo ""
    echo "VERIFICATION CHECKLIST:"
    echo ""
    echo "1. SESSION COUNTER FIX:"
    echo "   [ ] Session count is NOT stuck at exactly 10"
    echo "   [ ] After clearing, session count becomes 0"
    echo "   [ ] No automatic sample generation after clearing"
    echo "   [ ] Logs show 'SESSION_RESET: Sample generation disabled'"
    echo ""
    echo "2. TEMPERATURE Y-AXIS FIX:"
    echo "   [ ] Temperature labels show actual temperature values"
    echo "   [ ] Labels are NOT showing '0' or empty values"
    echo "   [ ] Logs show 'TEMP_Y_AXIS: Temperature Y-axis labels updated successfully'"
    echo "   [ ] UI shows proper temperature range (e.g., 25°C to 35°C)"
    echo ""
    
    wait_for_user
}

# Main test execution
main() {
    log_with_timestamp "Starting Health Fragment critical issues test"
    
    # Pre-test setup
    check_device
    
    echo ""
    echo "This test focuses on the two remaining critical issues:"
    echo "1. Cumulative Sessions Counter Still Stuck at 10"
    echo "2. Temperature Chart Y-Axis Labels Still Incorrect"
    echo ""
    echo "The test will:"
    echo "1. Reset the app to clean state"
    echo "2. Navigate to Health fragment"
    echo "3. Test session clearing functionality"
    echo "4. Test temperature Y-axis labeling"
    echo "5. Analyze logs for evidence of fixes"
    echo ""
    
    wait_for_user
    
    # Start monitoring
    start_logcat_monitoring
    
    # Reset app to clean state
    reset_app
    
    # Navigate to health fragment
    navigate_to_health
    
    # Test session clearing
    test_session_clearing
    
    # Test temperature labels
    test_temperature_labels
    
    # Wait for data processing
    log_with_timestamp "Waiting for final data processing..."
    sleep 15
    
    # Stop monitoring and analyze
    stop_logcat_monitoring
    analyze_critical_issues
    
    # Verify fixes
    verify_fixes
    
    echo ""
    echo "=========================================="
    echo "CRITICAL ISSUES TEST COMPLETED"
    echo "=========================================="
    echo ""
    echo "Log file: critical_issues_logs.txt"
    echo ""
    echo "Next steps:"
    echo "1. Review the verification checklist above"
    echo "2. Check the log analysis for evidence of fixes"
    echo "3. Manually verify UI changes in the Health fragment"
    echo "4. If issues persist, check the detailed logs for error messages"
    echo ""
}

# Cleanup function
cleanup() {
    stop_logcat_monitoring
}

# Set up cleanup on script exit
trap cleanup EXIT

# Run main test
main
