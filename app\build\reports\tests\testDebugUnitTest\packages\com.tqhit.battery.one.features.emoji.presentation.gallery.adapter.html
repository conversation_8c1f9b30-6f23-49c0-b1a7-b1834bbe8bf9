<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - Package com.tqhit.battery.one.features.emoji.presentation.gallery.adapter</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>Package com.tqhit.battery.one.features.emoji.presentation.gallery.adapter</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; com.tqhit.battery.one.features.emoji.presentation.gallery.adapter</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">15</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">15</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.348s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox failures" id="successRate">
<div class="percent">0%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div id="tabs">
<ul class="tabLinks">
<li>
<a href="#tab0">Failed tests</a>
</li>
<li>
<a href="#tab1">Classes</a>
</li>
</ul>
<div id="tab0" class="tab">
<h2>Failed tests</h2>
<ul class="linkList">
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html">BatteryStyleAdapterTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html#action button is hidden for free styles">action button is hidden for free styles</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html">BatteryStyleAdapterTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html#action button is visible for premium styles">action button is visible for premium styles</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html">BatteryStyleAdapterTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html#adapter creates correct number of view holders">adapter creates correct number of view holders</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html">BatteryStyleAdapterTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html#adapter handles empty list correctly">adapter handles empty list correctly</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html">BatteryStyleAdapterTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html#adapter handles list updates correctly">adapter handles list updates correctly</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html">BatteryStyleAdapterTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html#adapter returns correct item at position">adapter returns correct item at position</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html">BatteryStyleAdapterTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html#category text includes premium status">category text includes premium status</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html">BatteryStyleAdapterTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html#popular badge is hidden for non-popular styles">popular badge is hidden for non-popular styles</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html">BatteryStyleAdapterTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html#popular badge is visible for popular styles">popular badge is visible for popular styles</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html">BatteryStyleAdapterTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html#premium badge is hidden for free styles">premium badge is hidden for free styles</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html">BatteryStyleAdapterTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html#premium badge is visible for premium styles">premium badge is visible for premium styles</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html">BatteryStyleAdapterTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html#premium unlock click triggers callback">premium unlock click triggers callback</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html">BatteryStyleAdapterTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html#style click triggers callback">style click triggers callback</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html">BatteryStyleAdapterTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html#style long click triggers callback">style long click triggers callback</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html">BatteryStyleAdapterTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html#view holder binds style data correctly">view holder binds style data correctly</a>
</li>
</ul>
</div>
<div id="tab1" class="tab">
<h2>Classes</h2>
<table>
<thead>
<tr>
<th>Class</th>
<th>Tests</th>
<th>Failures</th>
<th>Ignored</th>
<th>Duration</th>
<th>Success rate</th>
</tr>
</thead>
<tr>
<td class="failures">
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html">BatteryStyleAdapterTest</a>
</td>
<td>15</td>
<td>15</td>
<td>0</td>
<td>0.348s</td>
<td class="failures">0%</td>
</tr>
</table>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="http://www.gradle.org">Gradle 8.11.1</a> at Jun 20, 2025, 4:39:53 PM</p>
</div>
</div>
</body>
</html>
