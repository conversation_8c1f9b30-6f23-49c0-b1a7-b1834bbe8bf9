<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android" android:exitFadeDuration="250">
    <item android:state_pressed="true">
        <layer-list>
            <item>
                <shape android:tint="?attr/colorr">
                    <size
                        android:height="10dp"
                        android:width="10dp"/>
                    <corners
                        android:topLeftRadius="10dp"
                        android:topRightRadius="10dp"
                        android:bottomLeftRadius="4dp"
                        android:bottomRightRadius="4dp"/>
                </shape>
            </item>
            <item
                android:left="2dp"
                android:top="2dp"
                android:right="2dp"
                android:bottom="2dp">
                <shape android:tint="?attr/colorr">
                    <size
                        android:height="10dp"
                        android:width="10dp"/>
                    <corners
                        android:topLeftRadius="8dp"
                        android:topRightRadius="8dp"
                        android:bottomLeftRadius="2dp"
                        android:bottomRightRadius="2dp"/>
                </shape>
            </item>
        </layer-list>
    </item>
    <item>
        <layer-list>
            <item>
                <shape android:tint="?attr/color_poz">
                    <size
                        android:height="10dp"
                        android:width="10dp"/>
                    <corners
                        android:topLeftRadius="10dp"
                        android:topRightRadius="10dp"
                        android:bottomLeftRadius="4dp"
                        android:bottomRightRadius="4dp"/>
                </shape>
            </item>
            <item
                android:left="2dp"
                android:top="2dp"
                android:right="2dp"
                android:bottom="2dp">
                <shape android:tint="?attr/grey">
                    <size
                        android:height="10dp"
                        android:width="10dp"/>
                    <corners
                        android:topLeftRadius="8dp"
                        android:topRightRadius="8dp"
                        android:bottomLeftRadius="2dp"
                        android:bottomRightRadius="2dp"/>
                </shape>
            </item>
        </layer-list>
    </item>
</selector>
