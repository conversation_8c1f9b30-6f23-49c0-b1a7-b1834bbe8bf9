package com.tqhit.battery.one.dialog.capacity

import android.content.Context
import android.graphics.Color
import android.view.ViewGroup
import android.view.WindowManager
import androidx.core.graphics.drawable.toDrawable
import com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog
import com.tqhit.battery.one.databinding.DialogChangeCapacityBinding
import com.tqhit.battery.one.viewmodel.battery.BatteryViewModel
import dagger.hilt.android.qualifiers.ActivityContext

class ChangeCapacityDialog(
    @ActivityContext private val context: Context,
    private val batteryViewModel: BatteryViewModel
) : AdLibBaseDialog<DialogChangeCapacityBinding>(context) {
    override val binding by lazy { DialogChangeCapacityBinding.inflate(layoutInflater) }

    override fun initWindow() {
        window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        window?.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
        val layoutParams = WindowManager.LayoutParams()
        layoutParams.copyFrom(window?.attributes)
        window?.attributes = layoutParams
    }

    override fun setupData() {
        super.setupData()

        val currentCapacity = batteryViewModel.getBatteryCapacity()
        binding.textInputEdit.hint = currentCapacity.toString()
    }

    override fun setupListener() {
        super.setupListener()

        binding.confirmChangeCapacity.setOnClickListener {
            val newCapacity = binding.textInputEdit.text.toString().toIntOrNull()
            if (newCapacity != null && newCapacity > 0) {
                batteryViewModel.setBatteryCapacity(newCapacity)
                dismiss()
            }
        }

        binding.cancelChangeCapacity.setOnClickListener { dismiss() }

        binding.exitChangeCapacity.setOnClickListener { dismiss() }
    }
}