<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - Class com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>Class com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; 
<a href="../packages/com.tqhit.battery.one.features.emoji.domain.model.html">com.tqhit.battery.one.features.emoji.domain.model</a> &gt; BatteryStyleCategoryTest</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">20</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.001s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div id="tabs">
<ul class="tabLinks">
<li>
<a href="#tab0">Tests</a>
</li>
</ul>
<div id="tab0" class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Duration</th>
<th>Result</th>
</tr>
</thead>
<tr>
<td class="success">test CHARACTER category properties</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">test HEART category properties</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">test HOT category properties</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">test all categories have required properties</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">test all categories have unique sort orders</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">test findByDisplayName with case insensitive match</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">test findByDisplayName with exact match</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">test findByDisplayName with non-matching name returns null</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">test fromString with blank string returns default</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">test fromString with display name fallback</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">test fromString with empty string returns default</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">test fromString with invalid value returns default</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">test fromString with lowercase enum name</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">test fromString with null returns default</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">test fromString with valid enum name</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">test getAllSorted returns categories in sort order</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">test getDefault returns CHARACTER</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">test getDisplayText returns formatted text</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">test getMainFilterCategories returns expected categories</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">test isFeatured returns true only for HOT category</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
</table>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="http://www.gradle.org">Gradle 8.11.1</a> at Jun 20, 2025, 4:39:53 PM</p>
</div>
</div>
</body>
</html>
