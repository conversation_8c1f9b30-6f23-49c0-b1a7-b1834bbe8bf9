{"logs": [{"outputFile": "com.tqhit.battery.one.app-mergeDebugResources-99:/values-pl/values-pl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\179e6486bd57a16ea175623aa423e7ed\\transformed\\jetified-material3-release\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,287,409,524,624,723,839,977,1099,1241,1325,1424,1516,1612,1729,1853,1957,2097,2233,2377,2538,2670,2791,2916,3037,3130,3230,3350,3474,3573,3677,3783,3924,4071,4182,4281,4355,4450,4546,4650,4737,4824,4936,5016,5103,5198,5303,5394,5503,5591,5697,5798,5908,6026,6106,6209", "endColumns": "114,116,121,114,99,98,115,137,121,141,83,98,91,95,116,123,103,139,135,143,160,131,120,124,120,92,99,119,123,98,103,105,140,146,110,98,73,94,95,103,86,86,111,79,86,94,104,90,108,87,105,100,109,117,79,102,96", "endOffsets": "165,282,404,519,619,718,834,972,1094,1236,1320,1419,1511,1607,1724,1848,1952,2092,2228,2372,2533,2665,2786,2911,3032,3125,3225,3345,3469,3568,3672,3778,3919,4066,4177,4276,4350,4445,4541,4645,4732,4819,4931,5011,5098,5193,5298,5389,5498,5586,5692,5793,5903,6021,6101,6204,6301"}, "to": {"startLines": "312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "28599,28714,28831,28953,29068,29168,29267,29383,29521,29643,29785,29869,29968,30060,30156,30273,30397,30501,30641,30777,30921,31082,31214,31335,31460,31581,31674,31774,31894,32018,32117,32221,32327,32468,32615,32726,32825,32899,32994,33090,33194,33281,33368,33480,33560,33647,33742,33847,33938,34047,34135,34241,34342,34452,34570,34650,34753", "endColumns": "114,116,121,114,99,98,115,137,121,141,83,98,91,95,116,123,103,139,135,143,160,131,120,124,120,92,99,119,123,98,103,105,140,146,110,98,73,94,95,103,86,86,111,79,86,94,104,90,108,87,105,100,109,117,79,102,96", "endOffsets": "28709,28826,28948,29063,29163,29262,29378,29516,29638,29780,29864,29963,30055,30151,30268,30392,30496,30636,30772,30916,31077,31209,31330,31455,31576,31669,31769,31889,32013,32112,32216,32322,32463,32610,32721,32820,32894,32989,33085,33189,33276,33363,33475,33555,33642,33737,33842,33933,34042,34130,34236,34337,34447,34565,34645,34748,34845"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5b80dcbf636dc26335bd1b8e4f16f918\\transformed\\material-1.12.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,386,461,536,615,719,814,899,1016,1098,1163,1227,1308,1372,1433,1544,1608,1676,1730,1799,1861,1915,2026,2087,2149,2203,2275,2404,2493,2572,2667,2752,2834,2983,3065,3148,3285,3372,3449,3503,3554,3620,3691,3767,3838,3921,3998,4076,4154,4230,4338,4428,4501,4596,4693,4765,4839,4939,4991,5076,5142,5230,5320,5382,5446,5509,5580,5687,5799,5898,6005,6063,6118,6194,6278,6355", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "endColumns": "12,74,74,78,103,94,84,116,81,64,63,80,63,60,110,63,67,53,68,61,53,110,60,61,53,71,128,88,78,94,84,81,148,81,82,136,86,76,53,50,65,70,75,70,82,76,77,77,75,107,89,72,94,96,71,73,99,51,84,65,87,89,61,63,62,70,106,111,98,106,57,54,75,83,76,77", "endOffsets": "381,456,531,610,714,809,894,1011,1093,1158,1222,1303,1367,1428,1539,1603,1671,1725,1794,1856,1910,2021,2082,2144,2198,2270,2399,2488,2567,2662,2747,2829,2978,3060,3143,3280,3367,3444,3498,3549,3615,3686,3762,3833,3916,3993,4071,4149,4225,4333,4423,4496,4591,4688,4760,4834,4934,4986,5071,5137,5225,5315,5377,5441,5504,5575,5682,5794,5893,6000,6058,6113,6189,6273,6350,6428"}, "to": {"startLines": "23,112,113,114,115,116,132,133,147,215,217,269,293,301,373,374,375,376,377,378,379,380,381,382,383,384,385,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,469,503,504,515", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1039,9293,9368,9443,9522,9626,10991,11076,12189,20897,21052,24998,26912,27470,35009,35120,35184,35252,35306,35375,35437,35491,35602,35663,35725,35779,35851,36111,36200,36279,36374,36459,36541,36690,36772,36855,36992,37079,37156,37210,37261,37327,37398,37474,37545,37628,37705,37783,37861,37937,38045,38135,38208,38303,38400,38472,38546,38646,38698,38783,38849,38937,39027,39089,39153,39216,39287,39394,39506,39605,39712,39770,43094,45463,45547,46461", "endLines": "28,112,113,114,115,116,132,133,147,215,217,269,293,301,373,374,375,376,377,378,379,380,381,382,383,384,385,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,469,503,504,515", "endColumns": "12,74,74,78,103,94,84,116,81,64,63,80,63,60,110,63,67,53,68,61,53,110,60,61,53,71,128,88,78,94,84,81,148,81,82,136,86,76,53,50,65,70,75,70,82,76,77,77,75,107,89,72,94,96,71,73,99,51,84,65,87,89,61,63,62,70,106,111,98,106,57,54,75,83,76,77", "endOffsets": "1320,9363,9438,9517,9621,9716,11071,11188,12266,20957,21111,25074,26971,27526,35115,35179,35247,35301,35370,35432,35486,35597,35658,35720,35774,35846,35975,36195,36274,36369,36454,36536,36685,36767,36850,36987,37074,37151,37205,37256,37322,37393,37469,37540,37623,37700,37778,37856,37932,38040,38130,38203,38298,38395,38467,38541,38641,38693,38778,38844,38932,39022,39084,39148,39211,39282,39389,39501,39600,39707,39765,39820,43165,45542,45619,46534"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b572512e02266e069f95737c22215ab9\\transformed\\jetified-media3-exoplayer-1.6.1\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,189,251,320,398,468,561,652", "endColumns": "72,60,61,68,77,69,92,90,64", "endOffsets": "123,184,246,315,393,463,556,647,712"}, "to": {"startLines": "242,243,244,245,246,247,248,249,250", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "23155,23228,23289,23351,23420,23498,23568,23661,23752", "endColumns": "72,60,61,68,77,69,92,90,64", "endOffsets": "23223,23284,23346,23415,23493,23563,23656,23747,23812"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d56ddc8f70c1b6c4f2dfff25a6818549\\transformed\\jetified-play-services-base-18.5.0\\res\\values-pl\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,457,575,681,828,949,1056,1151,1318,1423,1594,1718,1873,2030,2095,2157", "endColumns": "99,163,117,105,146,120,106,94,166,104,170,123,154,156,64,61,79", "endOffsets": "292,456,574,680,827,948,1055,1150,1317,1422,1593,1717,1872,2029,2094,2156,2236"}, "to": {"startLines": "150,151,152,153,154,155,156,157,159,160,161,162,163,164,165,166,167", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12451,12555,12723,12845,12955,13106,13231,13342,13581,13752,13861,14036,14164,14323,14484,14553,14619", "endColumns": "103,167,121,109,150,124,110,98,170,108,174,127,158,160,68,65,83", "endOffsets": "12550,12718,12840,12950,13101,13226,13337,13436,13747,13856,14031,14159,14318,14479,14548,14614,14698"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5c5c72c6ff4a7863322da50648a25e99\\transformed\\browser-1.8.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,254,369", "endColumns": "99,98,114,103", "endOffsets": "150,249,364,468"}, "to": {"startLines": "172,270,271,272", "startColumns": "4,4,4,4", "startOffsets": "14979,25079,25178,25293", "endColumns": "99,98,114,103", "endOffsets": "15074,25173,25288,25392"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\237e0b5db534c615c4317f1b214e3e7f\\transformed\\jetified-play-services-ads-24.2.0\\res\\values-pl\\values.xml", "from": {"startLines": "4,5,6,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,241,290,584,647,778,892,1016,1066,1117,1223,1321,1361,1443,1481,1515,1573,1657,1700", "endColumns": "41,48,59,62,130,113,123,49,50,105,97,39,81,37,33,57,83,42,55", "endOffsets": "240,289,349,646,777,891,1015,1065,1116,1222,1320,1360,1442,1480,1514,1572,1656,1699,1755"}, "to": {"startLines": "434,435,436,454,455,456,457,458,459,460,461,493,494,495,496,497,498,499,550", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "39883,39929,39982,41464,41531,41666,41784,41912,41966,42021,42131,44841,44885,44971,45013,45051,45113,45201,49296", "endColumns": "45,52,63,66,134,117,127,53,54,109,101,43,85,41,37,61,87,46,59", "endOffsets": "39924,39977,40041,41526,41661,41779,41907,41961,42016,42126,42228,44880,44966,45008,45046,45108,45196,45243,49351"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\42f95d9fa807b14415e836fc15872a54\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-pl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "135", "endOffsets": "330"}, "to": {"startLines": "158", "startColumns": "4", "startOffsets": "13441", "endColumns": "139", "endOffsets": "13576"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\237635df39b25799c092d66a208ce67d\\transformed\\jetified-foundation-release\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,87", "endOffsets": "138,226"}, "to": {"startLines": "538,539", "startColumns": "4,4", "startOffsets": "48531,48619", "endColumns": "87,87", "endOffsets": "48614,48702"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f908cdc45776521b403beeef1508641c\\transformed\\core-1.16.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,451,565,670,792", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "147,249,347,446,560,665,787,888"}, "to": {"startLines": "122,123,124,125,126,127,128,525", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "10062,10159,10261,10359,10458,10572,10677,47456", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "10154,10256,10354,10453,10567,10672,10794,47552"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5444be4bc77930bd89cfbb9f2224d8e4\\transformed\\navigation-ui-2.8.9\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,163", "endColumns": "107,116", "endOffsets": "158,275"}, "to": {"startLines": "437,438", "startColumns": "4,4", "startOffsets": "40046,40154", "endColumns": "107,116", "endOffsets": "40149,40266"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8bd7feaae90e869538df51f29dd16595\\transformed\\jetified-media3-ui-1.6.1\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,622,944,1022,1100,1183,1272,1361,1444,1511,1605,1699,1768,1834,1899,1971,2098,2221,2344,2420,2501,2574,2657,2754,2851,2919,2983,3036,3094,3142,3203,3276,3342,3406,3483,3550,3608,3675,3727,3794,3885,3976,4031", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63", "endColumns": "17,12,12,77,77,82,88,88,82,66,93,93,68,65,64,71,126,122,122,75,80,72,82,96,96,67,63,52,57,47,60,72,65,63,76,66,57,66,51,66,90,90,54,66", "endOffsets": "282,617,939,1017,1095,1178,1267,1356,1439,1506,1600,1694,1763,1829,1894,1966,2093,2216,2339,2415,2496,2569,2652,2749,2846,2914,2978,3031,3089,3137,3198,3271,3337,3401,3478,3545,3603,3670,3722,3789,3880,3971,4026,4093"}, "to": {"startLines": "2,11,17,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,382,717,21116,21194,21272,21355,21444,21533,21616,21683,21777,21871,21940,22006,22071,22143,22270,22393,22516,22592,22673,22746,22829,22926,23023,23091,23817,23870,23928,23976,24037,24110,24176,24240,24317,24384,24442,24509,24561,24628,24719,24810,24865", "endLines": "10,16,22,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267", "endColumns": "17,12,12,77,77,82,88,88,82,66,93,93,68,65,64,71,126,122,122,75,80,72,82,96,96,67,63,52,57,47,60,72,65,63,76,66,57,66,51,66,90,90,54,66", "endOffsets": "377,712,1034,21189,21267,21350,21439,21528,21611,21678,21772,21866,21935,22001,22066,22138,22265,22388,22511,22587,22668,22741,22824,22921,23018,23086,23150,23865,23923,23971,24032,24105,24171,24235,24312,24379,24437,24504,24556,24623,24714,24805,24860,24927"}}, {"source": "D:\\Duc\\AndroidStudioProjects\\TJ_BatteryOne\\app\\src\\main\\res\\values-pl\\strings.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,237,18,19,20,21,22,23,24,25,26,27,28,29,264,30,31,32,33,34,35,36,240,250,253,255,37,2,263,243,261,38,39,40,41,42,43,44,45,115,116,114,113,117,46,47,48,49,50,51,52,53,54,55,56,238,57,58,242,59,60,61,62,63,64,65,66,67,68,248,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,119,120,258,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,259,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,241,168,169,170,171,172,173,174,175,176,177,178,246,245,244,252,256,179,180,181,182,183,184,254,185,186,187,239,188,260,189,190,191,192,193,194,195,196,247,197,198,257,199,200,201,202,203,251,204,205,206,207,208,209,210,211,212,213,214,215,216,262,217,218,219,220,221,222,223,224,225,226,249,227,228,229,230,231,232,233,234,235,236", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "125,196,249,301,352,404,458,505,553,610,675,727,778,831,881,21486,933,1012,1069,1119,1167,1220,1300,1370,1450,1533,1600,1664,23963,1720,1759,1923,1983,2075,2118,2180,21688,22498,22921,23050,2266,57,23914,21888,23787,2309,2358,2397,2483,2543,2620,3031,3451,12144,12208,11500,11418,12273,3494,3567,3649,3852,4166,4243,4304,4346,4407,4484,4543,21576,4612,4686,21815,4729,4808,4850,4930,5094,5169,5264,5349,5421,5508,22375,5574,5630,5686,5750,5807,5854,5927,6030,6082,6433,6489,6548,7144,7763,7833,7884,8028,8092,8366,8418,8885,8951,9088,9251,9411,9478,9526,9574,9655,9755,9847,9904,10045,10142,10203,10272,10342,10624,10876,11055,11207,11295,12360,12429,23591,12484,12524,12615,12682,12740,12966,13015,13093,13160,13218,13350,13409,13540,13754,13794,13853,13891,13929,13965,14001,14069,14114,14167,14217,14283,23650,14352,14420,14477,14521,14562,14637,15056,15118,15205,15369,15442,15518,15555,15590,15626,15664,15718,15781,15851,15910,15991,16138,21736,16174,16238,16294,16385,16454,16529,16634,16751,16825,16864,16940,22190,22016,21932,22819,23367,17221,17261,17311,17362,17426,17521,22974,17570,17652,17703,21643,17742,23719,17858,17952,18048,18099,18172,18366,18442,18495,22297,18554,18610,23411,18671,18755,18855,18943,18992,22750,19062,19135,19178,19235,19284,19403,19783,19858,19905,19965,20041,20098,20151,23855,20192,20258,20467,20559,20625,20698,20753,20827,20901,20935,22442,20992,21056,21102,21139,21194,21248,21308,21368,21405,21441", "endColumns": "69,51,50,49,50,52,45,46,55,63,50,49,51,48,50,88,77,55,48,46,51,78,68,78,81,65,62,54,65,37,162,58,90,41,60,84,46,250,51,315,41,66,47,42,66,47,37,84,58,75,409,418,41,62,63,642,80,83,71,80,201,312,75,59,40,59,75,57,67,65,72,41,71,77,40,78,162,73,93,83,70,85,64,65,54,54,62,55,45,71,101,50,349,54,57,594,617,68,49,142,62,272,50,465,64,135,161,158,65,46,46,79,98,90,55,139,95,59,67,68,280,250,177,150,86,76,67,53,57,38,89,65,56,224,47,76,65,56,130,57,129,212,38,57,36,36,34,34,66,43,51,48,64,67,67,66,55,42,39,73,417,60,85,162,71,74,35,33,34,36,52,61,68,57,79,145,34,77,62,54,89,67,73,103,115,72,37,74,279,105,172,82,100,42,38,48,49,62,93,47,74,80,49,37,43,114,66,92,94,49,71,192,74,51,57,76,54,59,178,82,98,86,47,68,67,71,41,55,47,117,378,73,45,58,74,55,51,39,57,64,207,90,64,71,53,72,72,32,55,54,62,44,35,53,52,58,58,35,34,43", "endOffsets": "190,243,295,346,398,452,499,547,604,669,721,772,825,875,927,21570,1006,1063,1113,1161,1214,1294,1364,1444,1527,1594,1658,1714,24024,1753,1917,1977,2069,2112,2174,2260,21730,22744,22968,23361,2303,119,23957,21926,23849,2352,2391,2477,2537,2614,3025,3445,3488,12202,12267,12138,11494,12352,3561,3643,3846,4160,4237,4298,4340,4401,4478,4537,4606,21637,4680,4723,21882,4802,4844,4924,5088,5163,5258,5343,5415,5502,5568,22436,5624,5680,5744,5801,5848,5921,6024,6076,6427,6483,6542,7138,7757,7827,7878,8022,8086,8360,8412,8879,8945,9082,9245,9405,9472,9520,9568,9649,9749,9841,9898,10039,10136,10197,10266,10336,10618,10870,11049,11201,11289,11367,12423,12478,23644,12518,12609,12676,12734,12960,13009,13087,13154,13212,13344,13403,13534,13748,13788,13847,13885,13923,13959,13995,14063,14108,14161,14211,14277,14346,23713,14414,14471,14515,14556,14631,15050,15112,15199,15363,15436,15512,15549,15584,15620,15658,15712,15775,15845,15904,15985,16132,16168,21809,16232,16288,16379,16448,16523,16628,16745,16819,16858,16934,17215,22291,22184,22010,22915,23405,17255,17305,17356,17420,17515,17564,23044,17646,17697,17736,21682,17852,23781,17946,18042,18093,18166,18360,18436,18489,18548,22369,18604,18665,23585,18749,18849,18937,18986,19056,22813,19129,19172,19229,19278,19397,19777,19852,19899,19959,20035,20092,20145,20186,23908,20252,20461,20553,20619,20692,20747,20821,20895,20929,20986,22492,21050,21096,21133,21188,21242,21302,21362,21399,21435,21480"}, "to": {"startLines": "29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,47,48,51,52,63,64,66,67,68,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,117,118,119,120,121,129,130,131,134,135,136,137,138,139,140,141,142,143,144,145,146,168,169,170,171,173,174,175,176,177,178,179,180,181,182,183,184,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,210,211,212,213,214,216,268,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,294,295,297,299,300,302,303,304,305,306,307,308,309,310,311,369,370,371,372,386,387,433,440,441,442,443,444,446,447,448,449,450,451,452,453,462,463,464,465,466,467,468,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,487,488,489,490,491,492,500,501,505,507,508,509,510,511,512,513,514,516,517,518,519,520,521,522,526,527,528,531,533,534,535,536,537,540,541,542,543,544,545,546,547,548,549,551,552,553,554,555,556,557,558", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1325,1395,1447,1498,1548,1599,1652,1698,1745,1801,1865,1916,1966,2018,2067,2118,2424,2502,2784,2833,3819,3871,4050,4119,4198,4554,4620,4683,4738,4804,4842,5005,5064,5155,5197,5258,5343,5390,5641,5693,6009,6051,6118,6166,6209,6276,6324,6362,6447,6506,6582,6992,7411,7453,7516,7580,8223,8304,8388,8460,8541,8743,9056,9132,9192,9233,9721,9797,9855,9923,9989,10799,10841,10913,11193,11234,11313,11476,11550,11644,11728,11799,11885,11950,12016,12071,12126,14703,14759,14805,14877,15079,15130,15480,15535,15593,16188,16806,16875,16925,17068,17131,17404,17669,18135,18200,18336,18498,18657,18723,18770,18817,18897,18996,19087,19143,19283,19379,19439,19507,19576,19857,20108,20286,20437,20601,20678,20746,20800,20858,20962,24932,25397,25454,25679,25727,25804,25870,25927,26058,26116,26246,26459,26498,26556,26593,26630,26665,26700,26767,26811,26863,26976,27041,27186,27347,27414,27531,27574,27614,27688,28106,28167,28253,28416,28488,28563,34850,34884,34919,34956,35980,36042,39825,40361,40441,40587,40622,40700,40846,40901,40991,41059,41133,41237,41353,41426,42233,42308,42588,42694,42867,42950,43051,43170,43209,43258,43308,43371,43465,43513,43588,43669,43719,43757,43801,43916,43983,44076,44341,44391,44463,44656,44731,44783,45248,45325,45624,45756,45935,46018,46117,46204,46252,46321,46389,46539,46581,46637,46685,46803,47182,47256,47557,47616,47691,47897,48069,48109,48167,48232,48440,48707,48772,48844,48898,48971,49044,49077,49133,49188,49251,49356,49392,49446,49499,49558,49617,49653,49688", "endColumns": "69,51,50,49,50,52,45,46,55,63,50,49,51,48,50,88,77,55,48,46,51,78,68,78,81,65,62,54,65,37,162,58,90,41,60,84,46,250,51,315,41,66,47,42,66,47,37,84,58,75,409,418,41,62,63,642,80,83,71,80,201,312,75,59,40,59,75,57,67,65,72,41,71,77,40,78,162,73,93,83,70,85,64,65,54,54,62,55,45,71,101,50,349,54,57,594,617,68,49,142,62,272,50,465,64,135,161,158,65,46,46,79,98,90,55,139,95,59,67,68,280,250,177,150,86,76,67,53,57,38,89,65,56,224,47,76,65,56,130,57,129,212,38,57,36,36,34,34,66,43,51,48,64,67,67,66,55,42,39,73,417,60,85,162,71,74,35,33,34,36,52,61,68,57,79,145,34,77,62,54,89,67,73,103,115,72,37,74,279,105,172,82,100,42,38,48,49,62,93,47,74,80,49,37,43,114,66,92,94,49,71,192,74,51,57,76,54,59,178,82,98,86,47,68,67,71,41,55,47,117,378,73,45,58,74,55,51,39,57,64,207,90,64,71,53,72,72,32,55,54,62,44,35,53,52,58,58,35,34,43", "endOffsets": "1390,1442,1493,1543,1594,1647,1693,1740,1796,1860,1911,1961,2013,2062,2113,2202,2497,2553,2828,2875,3866,3945,4114,4193,4275,4615,4678,4733,4799,4837,5000,5059,5150,5192,5253,5338,5385,5636,5688,6004,6046,6113,6161,6204,6271,6319,6357,6442,6501,6577,6987,7406,7448,7511,7575,8218,8299,8383,8455,8536,8738,9051,9127,9187,9228,9288,9792,9850,9918,9984,10057,10836,10908,10986,11229,11308,11471,11545,11639,11723,11794,11880,11945,12011,12066,12121,12184,14754,14800,14872,14974,15125,15475,15530,15588,16183,16801,16870,16920,17063,17126,17399,17450,18130,18195,18331,18493,18652,18718,18765,18812,18892,18991,19082,19138,19278,19374,19434,19502,19571,19852,20103,20281,20432,20519,20673,20741,20795,20853,20892,21047,24993,25449,25674,25722,25799,25865,25922,26053,26111,26241,26454,26493,26551,26588,26625,26660,26695,26762,26806,26858,26907,27036,27104,27249,27409,27465,27569,27609,27683,28101,28162,28248,28411,28483,28558,28594,34879,34914,34951,35004,36037,36106,39878,40436,40582,40617,40695,40758,40896,40986,41054,41128,41232,41348,41421,41459,42303,42583,42689,42862,42945,43046,43089,43204,43253,43303,43366,43460,43508,43583,43664,43714,43752,43796,43911,43978,44071,44166,44386,44458,44651,44726,44778,44836,45320,45375,45679,45930,46013,46112,46199,46247,46316,46384,46456,46576,46632,46680,46798,47177,47251,47297,47611,47686,47742,47944,48104,48162,48227,48435,48526,48767,48839,48893,48966,49039,49072,49128,49183,49246,49291,49387,49441,49494,49553,49612,49648,49683,49727"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9e0a763189144907fb0197c2b097244b\\transformed\\jetified-ui-release\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,285,394,499,576,653,746,836,919,1002,1089,1237,1315,1391,1473,1541", "endColumns": "94,84,108,104,76,76,92,89,82,82,86,71,77,75,81,67,119", "endOffsets": "195,280,389,494,571,648,741,831,914,997,1084,1156,1310,1386,1468,1536,1656"}, "to": {"startLines": "148,149,185,186,209,296,298,439,445,485,486,506,523,524,529,530,532", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12271,12366,17455,17564,20524,27109,27254,40271,40763,44171,44254,45684,47302,47380,47747,47829,47949", "endColumns": "94,84,108,104,76,76,92,89,82,82,86,71,77,75,81,67,119", "endOffsets": "12361,12446,17559,17664,20596,27181,27342,40356,40841,44249,44336,45751,47375,47451,47824,47892,48064"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\93d3043f0a8b9466a00a736e170a6ddc\\transformed\\appcompat-1.7.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,6,7,10,11,12,13,14,15,16,17,18,19,22,26,27,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,516,623,897,988,1081,1176,1270,1371,1464,1559,1654,1745,2027,2447,2558,2817", "endColumns": "114,101,106,118,90,92,94,93,100,92,94,94,90,90,99,110,162,82", "endOffsets": "215,317,618,737,983,1076,1171,1265,1366,1459,1554,1649,1740,1831,2122,2553,2716,2895"}, "to": {"startLines": "45,46,49,50,53,54,55,56,57,58,59,60,61,62,65,69,70,502", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2207,2322,2558,2665,2880,2971,3064,3159,3253,3354,3447,3542,3637,3728,3950,4280,4391,45380", "endColumns": "114,101,106,118,90,92,94,93,100,92,94,94,90,90,99,110,162,82", "endOffsets": "2317,2419,2660,2779,2966,3059,3154,3248,3349,3442,3537,3632,3723,3814,4045,4386,4549,45458"}}]}]}