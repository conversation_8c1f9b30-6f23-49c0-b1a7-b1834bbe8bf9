<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_slide_layout_1" modulePackage="com.tqhit.battery.one" filePath="app\src\main\res\layout\item_slide_layout_1.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.RelativeLayout"><Targets><Target tag="layout/item_slide_layout_1_0" view="RelativeLayout"><Expressions/><location startLine="1" startOffset="0" endLine="290" endOffset="16"/></Target><Target id="@+id/logo" view="LinearLayout"><Expressions/><location startLine="10" startOffset="8" endLine="17" endOffset="49"/></Target><Target id="@+id/textView2" view="TextView"><Expressions/><location startLine="23" startOffset="12" endLine="30" endOffset="48"/></Target><Target id="@+id/textView" view="TextView"><Expressions/><location startLine="31" startOffset="12" endLine="39" endOffset="45"/></Target><Target id="@+id/textView14" view="TextView"><Expressions/><location startLine="42" startOffset="4" endLine="51" endOffset="45"/></Target><Target id="@+id/lenght_layout" view="LinearLayout"><Expressions/><location startLine="52" startOffset="4" endLine="244" endOffset="18"/></Target><Target id="@+id/main" view="LinearLayout"><Expressions/><location startLine="59" startOffset="8" endLine="187" endOffset="22"/></Target><Target id="@+id/textView4" view="TextView"><Expressions/><location startLine="65" startOffset="12" endLine="78" endOffset="48"/></Target><Target id="@+id/text5" view="LinearLayout"><Expressions/><location startLine="79" startOffset="12" endLine="100" endOffset="26"/></Target><Target id="@+id/reset_session_charge_layout" view="RelativeLayout"><Expressions/><location startLine="220" startOffset="12" endLine="242" endOffset="28"/></Target><Target id="@+id/text_view_reset_charge" view="TextView"><Expressions/><location startLine="228" startOffset="16" endLine="241" endOffset="52"/></Target><Target id="@+id/button" view="RelativeLayout"><Expressions/><location startLine="245" startOffset="4" endLine="283" endOffset="20"/></Target><Target id="@+id/next_page" view="Button"><Expressions/><location startLine="266" startOffset="8" endLine="274" endOffset="62"/></Target><Target id="@+id/display" view="LinearLayout"><Expressions/><location startLine="284" startOffset="4" endLine="289" endOffset="45"/></Target></Targets></Layout>