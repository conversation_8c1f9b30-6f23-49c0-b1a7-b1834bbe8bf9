package com.tqhit.battery.one.features.stats.apppower.presentation

import android.content.Context
import com.tqhit.battery.one.features.stats.apppower.permission.UsageStatsPermissionManager
import com.tqhit.battery.one.features.stats.apppower.repository.AppUsageStatsRepository
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Factory for creating AppPowerConsumptionDialog instances with proper dependency injection
 */
@Singleton
class AppPowerConsumptionDialogFactory @Inject constructor(
    private val appUsageStatsRepository: AppUsageStatsRepository,
    private val permissionManager: UsageStatsPermissionManager
) {
    
    /**
     * Creates an AppPowerConsumptionDialog with the provided parameters
     */
    fun create(
        context: Context,
        sessionStartTime: Long,
        sessionEndTime: Long,
        batteryCapacityMah: Int,
        screenOnTimeMillis: Long,
        screenOffTimeMillis: Long
    ): AppPowerConsumptionDialog {
        return AppPowerConsumptionDialog(
            context = context,
            sessionStartTime = sessionStartTime,
            sessionEndTime = sessionEndTime,
            batteryCapacityMah = batteryCapacityMah,
            screenOnTimeMillis = screenOnTimeMillis,
            screenOffTimeMillis = screenOffTimeMillis,
            appUsageStatsRepository = appUsageStatsRepository,
            permissionManager = permissionManager
        )
    }
}
