package com.tqhit.battery.one.viewmodel.animation

import android.content.Context
import androidx.lifecycle.ViewModel
import com.tqhit.battery.one.repository.AnimationRepository
import com.tqhit.battery.one.repository.AnimationRepository.AnimationApplyEntry
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import jakarta.inject.Inject

@HiltViewModel
class AnimationViewModel @Inject constructor(
    @ApplicationContext private val context: Context,
    private val repository: AnimationRepository
) : ViewModel() {
    fun getApplyList() : List<AnimationApplyEntry> {
        return repository.getApplyList()
    }

    fun setApplyList(list: List<AnimationApplyEntry>) {
        repository.setApplyList(list)
    }

    fun getEntry(mediaOriginal: String): AnimationApplyEntry? {
        return repository.getEntry(mediaOriginal)
    }

    fun isApplied(mediaOriginal: String): Boolean {
        return repository.isApplied(mediaOriginal)
    }

    fun isExpired(mediaOriginal: String): Boolean {
        return repository.isExpired(mediaOriginal)
    }

    fun applyAnimation(mediaOriginal: String) {
        repository.applyAnimation(mediaOriginal)
    }

    fun clearAnimation(mediaOriginal: String) {
        repository.clearAnimation(mediaOriginal)
    }

    fun getTimeRemaining(mediaOriginal: String): Long {
        return repository.getTimeRemaining(mediaOriginal)
    }

    fun getTrialEndTime(): Long {
        return repository.getTrialEndTime()
    }

    fun setTrialEndTime(mediaOriginal: String) {
        repository.setTrialEndTime(mediaOriginal)
    }

    fun getApplied(): String {
        return repository.getApplied()
    }

    fun setApplied(mediaOriginal: String) {
        repository.setApplied(mediaOriginal)
    }
}