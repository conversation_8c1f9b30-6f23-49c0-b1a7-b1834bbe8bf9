package com.tqhit.battery.one.features.emoji.data.repository;

import com.google.gson.Gson;
import com.tqhit.battery.one.features.emoji.data.datastore.CustomizationDataStore;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class CustomizationRepositoryImpl_Factory implements Factory<CustomizationRepositoryImpl> {
  private final Provider<CustomizationDataStore> dataStoreProvider;

  private final Provider<Gson> gsonProvider;

  public CustomizationRepositoryImpl_Factory(Provider<CustomizationDataStore> dataStoreProvider,
      Provider<Gson> gsonProvider) {
    this.dataStoreProvider = dataStoreProvider;
    this.gsonProvider = gsonProvider;
  }

  @Override
  public CustomizationRepositoryImpl get() {
    return newInstance(dataStoreProvider.get(), gsonProvider.get());
  }

  public static CustomizationRepositoryImpl_Factory create(
      Provider<CustomizationDataStore> dataStoreProvider, Provider<Gson> gsonProvider) {
    return new CustomizationRepositoryImpl_Factory(dataStoreProvider, gsonProvider);
  }

  public static CustomizationRepositoryImpl newInstance(CustomizationDataStore dataStore,
      Gson gson) {
    return new CustomizationRepositoryImpl(dataStore, gson);
  }
}
