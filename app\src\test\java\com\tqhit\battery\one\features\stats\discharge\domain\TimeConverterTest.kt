package com.tqhit.battery.one.features.stats.discharge.domain

import org.junit.Assert.*
import org.junit.Before
import org.junit.Test

/**
 * Unit tests for TimeConverter
 */
class TimeConverterTest {

    private lateinit var timeConverter: TimeConverter

    @Before
    fun setUp() {
        timeConverter = TimeConverter()
    }

    @Test
    fun `hoursToMillis converts correctly`() {
        // Given
        val hours = 2.5

        // When
        val result = timeConverter.hoursToMillis(hours)

        // Then
        val expectedMillis = (2.5 * 3600.0 * 1000.0).toLong()
        assertEquals(expectedMillis, result)
    }

    @Test
    fun `millisToHours converts correctly`() {
        // Given
        val millis = 7200000L // 2 hours in milliseconds

        // When
        val result = timeConverter.millisToHours(millis)

        // Then
        assertEquals(2.0, result, 0.01)
    }

    @Test
    fun `formatMillisToMinutes formats correctly`() {
        // Given
        val millis = 300000L // 5 minutes

        // When
        val result = timeConverter.formatMillisToMinutes(millis)

        // Then
        assertEquals("5m", result)
    }

    @Test
    fun `formatMillisToHoursMinutes formats hours and minutes correctly`() {
        // Given
        val millis = 5400000L // 1 hour 30 minutes

        // When
        val result = timeConverter.formatMillisToHoursMinutes(millis)

        // Then
        assertEquals("1h 30m", result)
    }

    @Test
    fun `formatMillisToHoursMinutes formats minutes only when less than hour`() {
        // Given
        val millis = 2700000L // 45 minutes

        // When
        val result = timeConverter.formatMillisToHoursMinutes(millis)

        // Then
        assertEquals("45m", result)
    }

    @Test
    fun `formatMillisToHoursMinutes returns 0m for zero or negative input`() {
        // Given
        val millis = 0L

        // When
        val result = timeConverter.formatMillisToHoursMinutes(millis)

        // Then
        assertEquals("0m", result)
    }

    @Test
    fun `formatMillisToHoursMinutesSeconds formats full time correctly`() {
        // Given
        val millis = 3665000L // 1 hour 1 minute 5 seconds

        // When
        val result = timeConverter.formatMillisToHoursMinutesSeconds(millis)

        // Then
        assertEquals("1h 1m 5s", result)
    }

    @Test
    fun `formatMillisToHoursMinutesSeconds formats minutes and seconds only`() {
        // Given
        val millis = 125000L // 2 minutes 5 seconds

        // When
        val result = timeConverter.formatMillisToHoursMinutesSeconds(millis)

        // Then
        assertEquals("2m 5s", result)
    }

    @Test
    fun `formatMillisToHoursMinutesSeconds formats seconds only`() {
        // Given
        val millis = 30000L // 30 seconds

        // When
        val result = timeConverter.formatMillisToHoursMinutesSeconds(millis)

        // Then
        assertEquals("30s", result)
    }

    @Test
    fun `formatMillisToHoursMinutesSeconds returns 0s for zero or negative input`() {
        // Given
        val millis = -1000L

        // When
        val result = timeConverter.formatMillisToHoursMinutesSeconds(millis)

        // Then
        assertEquals("0s", result)
    }
}
