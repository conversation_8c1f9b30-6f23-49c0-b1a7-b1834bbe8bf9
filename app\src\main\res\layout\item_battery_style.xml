<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/styleCard"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    android:clickable="true"
    android:focusable="true"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp"
    app:rippleColor="?attr/colorPrimary"
    app:strokeColor="?attr/colorOutline"
    app:strokeWidth="1dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Preview Section -->
        <FrameLayout
            android:id="@+id/previewContainer"
            android:layout_width="match_parent"
            android:layout_height="120dp"
            android:layout_marginBottom="12dp"
            android:background="@drawable/preview_background"
            android:gravity="center">

            <!-- Battery Image -->
            <ImageView
                android:id="@+id/batteryImageView"
                android:layout_width="60dp"
                android:layout_height="60dp"
                android:layout_gravity="center"
                android:contentDescription="@string/battery_image"
                android:scaleType="centerInside"
                tools:src="@drawable/ic_battery_placeholder" />

            <!-- Emoji Image -->
            <ImageView
                android:id="@+id/emojiImageView"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_gravity="center"
                android:layout_marginStart="8dp"
                android:layout_marginTop="-8dp"
                android:contentDescription="@string/emoji_image"
                android:scaleType="centerInside"
                tools:src="@drawable/ic_emoji_placeholder" />

            <!-- Battery Percentage Preview -->
            <TextView
                android:id="@+id/percentagePreview"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="40dp"
                android:text="85%"
                android:textAppearance="?attr/textAppearanceBodySmall"
                android:textColor="?attr/colorOnSurface"
                android:textStyle="bold" />

            <!-- Premium Badge -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/premiumBadge"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="top|end"
                android:layout_margin="8dp"
                android:visibility="gone"
                app:cardBackgroundColor="?attr/colorPrimary"
                app:cardCornerRadius="12dp"
                app:cardElevation="0dp"
                tools:visibility="visible">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingHorizontal="8dp"
                    android:paddingVertical="4dp">

                    <ImageView
                        android:layout_width="12dp"
                        android:layout_height="12dp"
                        android:src="@drawable/ic_diamond"
                        app:tint="?attr/colorOnPrimary" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="4dp"
                        android:text="@string/premium"
                        android:textAppearance="?attr/textAppearanceLabelSmall"
                        android:textColor="?attr/colorOnPrimary"
                        android:textStyle="bold" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Popular Badge -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/popularBadge"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="top|start"
                android:layout_margin="8dp"
                android:visibility="gone"
                app:cardBackgroundColor="?attr/colorSecondary"
                app:cardCornerRadius="12dp"
                app:cardElevation="0dp"
                tools:visibility="visible">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingHorizontal="8dp"
                    android:paddingVertical="4dp">

                    <ImageView
                        android:layout_width="12dp"
                        android:layout_height="12dp"
                        android:src="@drawable/ic_fire"
                        app:tint="?attr/colorOnSecondary" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="4dp"
                        android:text="@string/hot"
                        android:textAppearance="?attr/textAppearanceLabelSmall"
                        android:textColor="?attr/colorOnSecondary"
                        android:textStyle="bold" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Loading Indicator for Images -->
            <com.google.android.material.progressindicator.CircularProgressIndicator
                android:id="@+id/imageLoadingIndicator"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_gravity="center"
                android:indeterminate="true"
                android:visibility="gone" />

            <!-- Error Indicator for Failed Images -->
            <ImageView
                android:id="@+id/imageErrorIndicator"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_gravity="center"
                android:src="@drawable/ic_image_error"
                android:visibility="gone"
                app:tint="?attr/colorError"
                tools:visibility="visible" />

        </FrameLayout>

        <!-- Style Info -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Style Name -->
            <TextView
                android:id="@+id/styleNameText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="1"
                android:textAppearance="?attr/textAppearanceBodyLarge"
                android:textColor="?attr/colorOnSurface"
                android:textStyle="bold"
                tools:text="Heart Battery" />

            <!-- Category -->
            <TextView
                android:id="@+id/categoryText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:ellipsize="end"
                android:maxLines="1"
                android:textAppearance="?attr/textAppearanceBodySmall"
                android:textColor="?attr/colorOnSurfaceVariant"
                tools:text="Heart • Free" />

        </LinearLayout>

        <!-- Action Button (for premium styles) -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/actionButton"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="@string/customize"
            android:visibility="gone"
            tools:visibility="visible" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
