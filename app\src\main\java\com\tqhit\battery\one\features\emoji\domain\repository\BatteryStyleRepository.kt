package com.tqhit.battery.one.features.emoji.domain.repository

import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory
import kotlinx.coroutines.flow.Flow

/**
 * Interface for the BatteryStyleRepository.
 * Provides reactive streams for battery styles and manages data fetching from remote config and local fallback.
 * 
 * This repository follows the established stats module architecture pattern:
 * - Uses Flow for reactive data streams
 * - Integrates with Firebase Remote Config for dynamic content
 * - Provides local JSON fallback for offline functionality
 * - Supports caching for performance optimization
 */
interface BatteryStyleRepository {
    
    /**
     * Flow that emits the complete list of available battery styles.
     * This flow will emit whenever the styles are updated from remote config or local fallback.
     */
    val batteryStylesFlow: Flow<List<BatteryStyle>>
    
    /**
     * Flow that emits the loading state of the repository.
     * True when fetching data from remote config, false when complete or using cached data.
     */
    val isLoadingFlow: Flow<Boolean>
    
    /**
     * Gets all available battery styles.
     * This method will first try to fetch from Firebase Remote Config,
     * then fall back to local JSON if remote fetch fails.
     * 
     * @param forceRefresh If true, bypasses cache and fetches fresh data
     * @return List of available battery styles
     */
    suspend fun getAllStyles(forceRefresh: Boolean = false): List<BatteryStyle>
    
    /**
     * Gets battery styles filtered by category.
     * 
     * @param category The category to filter by
     * @param forceRefresh If true, bypasses cache and fetches fresh data
     * @return List of battery styles in the specified category
     */
    suspend fun getStylesByCategory(
        category: BatteryStyleCategory,
        forceRefresh: Boolean = false
    ): List<BatteryStyle>
    
    /**
     * Gets popular/trending battery styles (marked as isPopular = true).
     * 
     * @param forceRefresh If true, bypasses cache and fetches fresh data
     * @return List of popular battery styles
     */
    suspend fun getPopularStyles(forceRefresh: Boolean = false): List<BatteryStyle>
    
    /**
     * Gets premium battery styles (marked as isPremium = true).
     * 
     * @param forceRefresh If true, bypasses cache and fetches fresh data
     * @return List of premium battery styles
     */
    suspend fun getPremiumStyles(forceRefresh: Boolean = false): List<BatteryStyle>
    
    /**
     * Gets free battery styles (marked as isPremium = false).
     * 
     * @param forceRefresh If true, bypasses cache and fetches fresh data
     * @return List of free battery styles
     */
    suspend fun getFreeStyles(forceRefresh: Boolean = false): List<BatteryStyle>
    
    /**
     * Searches battery styles by name or category.
     * 
     * @param query Search query (case-insensitive)
     * @param forceRefresh If true, bypasses cache and fetches fresh data
     * @return List of battery styles matching the search query
     */
    suspend fun searchStyles(
        query: String,
        forceRefresh: Boolean = false
    ): List<BatteryStyle>
    
    /**
     * Gets a specific battery style by its ID.
     * 
     * @param styleId The unique identifier of the style
     * @return The battery style if found, null otherwise
     */
    suspend fun getStyleById(styleId: String): BatteryStyle?
    
    /**
     * Refreshes the battery styles from Firebase Remote Config.
     * Falls back to local JSON if remote fetch fails.
     * This method updates the batteryStylesFlow with fresh data.
     * 
     * @return True if refresh was successful, false if fallback was used
     */
    suspend fun refreshStyles(): Boolean
    
    /**
     * Gets the current battery styles synchronously from cache.
     * Returns empty list if no styles are cached yet.
     * 
     * @return Current list of cached battery styles
     */
    fun getCurrentStyles(): List<BatteryStyle>
    
    /**
     * Checks if the repository has cached data available.
     * 
     * @return True if cached data is available, false otherwise
     */
    fun hasCachedData(): Boolean
    
    /**
     * Gets the timestamp of the last successful data fetch.
     * 
     * @return Timestamp in epoch milliseconds, or 0 if never fetched
     */
    suspend fun getLastFetchTimestamp(): Long
    
    /**
     * Clears all cached battery styles and forces a fresh fetch on next access.
     */
    suspend fun clearCache()
}
