package com.tqhit.battery.one.features.emoji.data.repository

import com.google.gson.Gson
import com.tqhit.battery.one.features.emoji.data.datastore.CustomizationDataStore
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfig
import com.tqhit.battery.one.features.emoji.domain.model.CustomizationConfig
import com.tqhit.battery.one.features.emoji.domain.model.UserCustomization
import com.tqhit.battery.one.features.emoji.domain.model.UserPreferences
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.runTest
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test

/**
 * Unit tests for CustomizationRepositoryImpl.
 * 
 * Tests the repository implementation including DataStore integration,
 * data validation, error handling, and export/import functionality.
 */
class CustomizationRepositoryImplTest {
    
    private lateinit var repository: CustomizationRepositoryImpl
    private lateinit var mockDataStore: CustomizationDataStore
    private lateinit var gson: Gson
    
    // Test data
    private val testStyleConfig = BatteryStyleConfig(
        showEmoji = true,
        showPercentage = true,
        percentageFontSizeDp = 14,
        emojiSizeScale = 1.0f,
        percentageColor = 0xFFFFFFFF.toInt()
    )
    
    private val testCustomizationConfig = CustomizationConfig(
        selectedStyleId = "test_style_1",
        selectedBatteryImageUrl = "https://example.com/battery.png",
        selectedEmojiImageUrl = "https://example.com/emoji.png",
        styleConfig = testStyleConfig,
        isFeatureEnabled = false
    )
    
    private val testUserCustomization = UserCustomization(
        customizationConfig = testCustomizationConfig,
        hasAccessibilityPermission = true,
        hasOverlayPermission = true,
        isAccessibilityServiceEnabled = true
    )
    
    private val testUserPreferences = UserPreferences(
        showOnboardingTips = true,
        enableHapticFeedback = true,
        autoSaveChanges = true,
        showPreviewInGallery = true
    )
    
    @Before
    fun setUp() {
        mockDataStore = mockk()
        gson = Gson()
        
        repository = CustomizationRepositoryImpl(
            dataStore = mockDataStore,
            gson = gson
        )
    }
    
    @Test
    fun `userCustomizationFlow should expose DataStore flow`() = runTest {
        // Given
        every { mockDataStore.userCustomizationFlow } returns flowOf(testUserCustomization)
        
        // When
        val result = repository.userCustomizationFlow.first()
        
        // Then
        assertEquals("Should expose DataStore flow", testUserCustomization, result)
    }
    
    @Test
    fun `customizationConfigFlow should expose DataStore flow`() = runTest {
        // Given
        every { mockDataStore.customizationConfigFlow } returns flowOf(testCustomizationConfig)
        
        // When
        val result = repository.customizationConfigFlow.first()
        
        // Then
        assertEquals("Should expose DataStore flow", testCustomizationConfig, result)
    }
    
    @Test
    fun `userPreferencesFlow should expose DataStore flow`() = runTest {
        // Given
        every { mockDataStore.userPreferencesFlow } returns flowOf(testUserPreferences)
        
        // When
        val result = repository.userPreferencesFlow.first()
        
        // Then
        assertEquals("Should expose DataStore flow", testUserPreferences, result)
    }
    
    @Test
    fun `getCurrentCustomizationConfig should return current config`() = runTest {
        // Given
        every { mockDataStore.customizationConfigFlow } returns flowOf(testCustomizationConfig)
        
        // When
        val result = repository.getCurrentCustomizationConfig()
        
        // Then
        assertEquals("Should return current config", testCustomizationConfig, result)
    }
    
    @Test
    fun `getCurrentCustomizationConfig should return default on error`() = runTest {
        // Given
        every { mockDataStore.customizationConfigFlow } returns flowOf()
        
        // When
        val result = repository.getCurrentCustomizationConfig()
        
        // Then
        assertEquals("Should return default config", CustomizationConfig.createDefault(), result)
    }
    
    @Test
    fun `getCurrentUserCustomization should return current customization`() = runTest {
        // Given
        every { mockDataStore.userCustomizationFlow } returns flowOf(testUserCustomization)
        
        // When
        val result = repository.getCurrentUserCustomization()
        
        // Then
        assertEquals("Should return current customization", testUserCustomization, result)
    }
    
    @Test
    fun `saveCustomizationConfig should validate and save config`() = runTest {
        // Given
        coEvery { mockDataStore.saveCustomizationConfig(any()) } returns Result.success(Unit)
        
        // When
        val result = repository.saveCustomizationConfig(testCustomizationConfig)
        
        // Then
        assertTrue("Should succeed for valid config", result.isSuccess)
        coVerify { mockDataStore.saveCustomizationConfig(testCustomizationConfig.validated()) }
    }
    
    @Test
    fun `saveCustomizationConfig should handle DataStore errors`() = runTest {
        // Given
        val dataStoreError = RuntimeException("DataStore error")
        coEvery { mockDataStore.saveCustomizationConfig(any()) } returns Result.failure(dataStoreError)
        
        // When
        val result = repository.saveCustomizationConfig(testCustomizationConfig)
        
        // Then
        assertTrue("Should fail when DataStore fails", result.isFailure)
        assertEquals("Should preserve DataStore error", dataStoreError, result.exceptionOrNull())
    }
    
    @Test
    fun `saveUserCustomization should validate and save customization`() = runTest {
        // Given
        coEvery { mockDataStore.saveUserCustomization(any()) } returns Result.success(Unit)
        
        // When
        val result = repository.saveUserCustomization(testUserCustomization)
        
        // Then
        assertTrue("Should succeed for valid customization", result.isSuccess)
        coVerify { 
            mockDataStore.saveUserCustomization(
                match { it.customizationConfig == testUserCustomization.customizationConfig.validated() }
            )
        }
    }
    
    @Test
    fun `saveUserPreferences should save preferences`() = runTest {
        // Given
        coEvery { mockDataStore.saveUserPreferences(any()) } returns Result.success(Unit)
        
        // When
        val result = repository.saveUserPreferences(testUserPreferences)
        
        // Then
        assertTrue("Should succeed for valid preferences", result.isSuccess)
        coVerify { mockDataStore.saveUserPreferences(testUserPreferences) }
    }
    
    @Test
    fun `updatePermissionStates should update permissions`() = runTest {
        // Given
        val hasAccessibility = true
        val hasOverlay = false
        val isServiceEnabled = true
        coEvery { mockDataStore.updatePermissionStates(any(), any(), any()) } returns Result.success(Unit)
        
        // When
        val result = repository.updatePermissionStates(hasAccessibility, hasOverlay, isServiceEnabled)
        
        // Then
        assertTrue("Should succeed for permission update", result.isSuccess)
        coVerify { mockDataStore.updatePermissionStates(hasAccessibility, hasOverlay, isServiceEnabled) }
    }
    
    @Test
    fun `setFeatureEnabled should update feature state`() = runTest {
        // Given
        val enabled = true
        every { mockDataStore.customizationConfigFlow } returns flowOf(testCustomizationConfig)
        coEvery { mockDataStore.saveCustomizationConfig(any()) } returns Result.success(Unit)
        
        // When
        val result = repository.setFeatureEnabled(enabled)
        
        // Then
        assertTrue("Should succeed for feature enablement", result.isSuccess)
        coVerify { 
            mockDataStore.saveCustomizationConfig(
                match { it.isFeatureEnabled == enabled }
            )
        }
    }
    
    @Test
    fun `recordStyleUsage should update usage history`() = runTest {
        // Given
        val styleId = "test_style"
        every { mockDataStore.userCustomizationFlow } returns flowOf(testUserCustomization)
        coEvery { mockDataStore.saveUserCustomization(any()) } returns Result.success(Unit)
        
        // When
        val result = repository.recordStyleUsage(styleId)
        
        // Then
        assertTrue("Should succeed for style usage recording", result.isSuccess)
        coVerify { 
            mockDataStore.saveUserCustomization(
                match { 
                    it.usageHistory.lastUsedTimestamp > testUserCustomization.usageHistory.lastUsedTimestamp
                }
            )
        }
    }
    
    @Test
    fun `resetToDefaults should reset with preserved preferences`() = runTest {
        // Given
        val preservePreferences = true
        every { mockDataStore.userCustomizationFlow } returns flowOf(testUserCustomization)
        coEvery { mockDataStore.saveUserCustomization(any()) } returns Result.success(Unit)
        
        // When
        val result = repository.resetToDefaults(preservePreferences)
        
        // Then
        assertTrue("Should succeed for reset", result.isSuccess)
        coVerify { 
            mockDataStore.saveUserCustomization(
                match { 
                    it.customizationConfig == CustomizationConfig.createDefault() &&
                    it.userPreferences == testUserCustomization.userPreferences
                }
            )
        }
    }
    
    @Test
    fun `resetToDefaults should reset everything when not preserving preferences`() = runTest {
        // Given
        val preservePreferences = false
        every { mockDataStore.userCustomizationFlow } returns flowOf(testUserCustomization)
        coEvery { mockDataStore.saveUserCustomization(any()) } returns Result.success(Unit)
        
        // When
        val result = repository.resetToDefaults(preservePreferences)
        
        // Then
        assertTrue("Should succeed for complete reset", result.isSuccess)
        coVerify { 
            mockDataStore.saveUserCustomization(UserCustomization.createDefault())
        }
    }
    
    @Test
    fun `clearAllData should clear DataStore`() = runTest {
        // Given
        coEvery { mockDataStore.clearAllData() } returns Result.success(Unit)
        
        // When
        val result = repository.clearAllData()
        
        // Then
        assertTrue("Should succeed for clear all", result.isSuccess)
        coVerify { mockDataStore.clearAllData() }
    }
    
    @Test
    fun `validateAndFixConfiguration should detect and fix issues`() = runTest {
        // Given
        val invalidCustomization = testUserCustomization.copy(
            customizationConfig = testCustomizationConfig.copy(
                selectedStyleId = "", // Invalid
                styleConfig = testStyleConfig.copy(percentageFontSizeDp = -1) // Invalid
            )
        )
        every { mockDataStore.userCustomizationFlow } returns flowOf(invalidCustomization)
        coEvery { mockDataStore.saveUserCustomization(any()) } returns Result.success(Unit)
        
        // When
        val result = repository.validateAndFixConfiguration()
        
        // Then
        assertTrue("Should succeed for validation", result.isSuccess)
        val issues = result.getOrNull()!!
        assertTrue("Should detect issues", issues.isNotEmpty())
        assertTrue("Should contain validation message", issues.any { it.contains("Invalid") })
        coVerify { mockDataStore.saveUserCustomization(any()) }
    }
    
    @Test
    fun `validateAndFixConfiguration should pass for valid configuration`() = runTest {
        // Given
        every { mockDataStore.userCustomizationFlow } returns flowOf(testUserCustomization)
        
        // When
        val result = repository.validateAndFixConfiguration()
        
        // Then
        assertTrue("Should succeed for valid configuration", result.isSuccess)
        val issues = result.getOrNull()!!
        assertTrue("Should have no issues", issues.isEmpty())
        coVerify(exactly = 0) { mockDataStore.saveUserCustomization(any()) }
    }
    
    @Test
    fun `exportCustomizationData should serialize current data`() = runTest {
        // Given
        every { mockDataStore.userCustomizationFlow } returns flowOf(testUserCustomization)
        
        // When
        val result = repository.exportCustomizationData()
        
        // Then
        assertTrue("Should succeed for export", result.isSuccess)
        val exportedData = result.getOrNull()!!
        assertTrue("Should contain serialized data", exportedData.isNotBlank())
        assertTrue("Should contain version info", exportedData.contains("version"))
        assertTrue("Should contain timestamp", exportedData.contains("timestamp"))
    }
    
    @Test
    fun `importCustomizationData should validate and import data`() = runTest {
        // Given
        val exportData = mapOf(
            "version" to 1,
            "timestamp" to System.currentTimeMillis(),
            "userCustomization" to testUserCustomization
        )
        val serializedData = gson.toJson(exportData)
        coEvery { mockDataStore.saveUserCustomization(any()) } returns Result.success(Unit)
        
        // When
        val result = repository.importCustomizationData(serializedData, overwriteExisting = true)
        
        // Then
        assertTrue("Should succeed for valid import data", result.isSuccess)
        coVerify { mockDataStore.saveUserCustomization(any()) }
    }
    
    @Test
    fun `importCustomizationData should fail for invalid version`() = runTest {
        // Given
        val exportData = mapOf(
            "version" to 999, // Unsupported version
            "timestamp" to System.currentTimeMillis(),
            "userCustomization" to testUserCustomization
        )
        val serializedData = gson.toJson(exportData)
        
        // When
        val result = repository.importCustomizationData(serializedData)
        
        // Then
        assertTrue("Should fail for unsupported version", result.isFailure)
        assertTrue("Should contain version error", 
            result.exceptionOrNull()?.message?.contains("version") == true)
    }
    
    @Test
    fun `importCustomizationData should fail for invalid data format`() = runTest {
        // Given
        val invalidData = "invalid json data"
        
        // When
        val result = repository.importCustomizationData(invalidData)
        
        // Then
        assertTrue("Should fail for invalid data", result.isFailure)
    }
}
