package com.tqhit.battery.one.features.stats.charge.repository

import android.util.Log
import com.tqhit.battery.one.features.stats.charge.cache.StatsChargeCache
import com.tqhit.battery.one.features.stats.charge.data.StatsChargeSession
import com.tqhit.battery.one.features.stats.charge.data.StatsChargeStatus
import com.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus
import com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider
import com.tqhit.battery.one.repository.AppRepository
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Interface for the StatsChargeRepository.
 * Provides reactive streams for charge status and session data.
 */
interface StatsChargeRepository {
    
    /**
     * Flow that emits the latest StatsChargeStatus.
     */
    val statsChargeStatusFlow: Flow<StatsChargeStatus>
    
    /**
     * Flow that emits the current active charge session.
     */
    val activeChargeSessionFlow: Flow<StatsChargeSession?>
    
    /**
     * Resets the current charge session.
     */
    suspend fun resetCurrentSession()
}

/**
 * Default implementation of StatsChargeRepository.
 * Maps CoreBatteryStatus to StatsChargeStatus and manages charge sessions.
 */
@Singleton
class DefaultStatsChargeRepository @Inject constructor(
    private val coreBatteryStatsProvider: CoreBatteryStatsProvider,
    private val statsChargeCache: StatsChargeCache,
    private val appRepository: AppRepository
) : StatsChargeRepository {
    
    companion object {
        private const val TAG = "StatsChargeRepository"
    }
    
    // Coroutine scope for repository operations
    private val repositoryScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    // Private mutable flows for internal updates
    private val _statsChargeStatusFlow = MutableStateFlow(StatsChargeStatus.createDefault())
    private val _activeChargeSessionFlow = MutableStateFlow<StatsChargeSession?>(null)
    
    // Public read-only flows for external consumption
    override val statsChargeStatusFlow: StateFlow<StatsChargeStatus> = _statsChargeStatusFlow.asStateFlow()
    override val activeChargeSessionFlow: StateFlow<StatsChargeSession?> = _activeChargeSessionFlow.asStateFlow()
    
    // Track the last core status to detect charging state changes
    private var lastCoreStatus: CoreBatteryStatus? = null
    
    init {
        // Load active session from cache on initialization
        repositoryScope.launch {
            loadActiveSessionFromCache()
        }
        
        // Start collecting CoreBatteryStatus updates
        repositoryScope.launch {
            coreBatteryStatsProvider.coreBatteryStatusFlow.collect { coreStatus ->
                coreStatus?.let { processCoreStatusUpdate(it) }
            }
        }
    }
    
    /**
     * Loads the active charge session from cache.
     */
    private suspend fun loadActiveSessionFromCache() {
        try {
            val activeSession = statsChargeCache.getActiveSession()
            _activeChargeSessionFlow.value = activeSession
            
            if (activeSession != null) {
                Log.d(TAG, "STATS_CHARGE_REPO: Active session loaded from cache - " +
                    "ID=${activeSession.hashCode()}, " +
                    "StartPercent=${activeSession.startPercentage}%, " +
                    "Duration=${activeSession.durationMillis}ms")
            } else {
                Log.d(TAG, "STATS_CHARGE_REPO: No active session in cache")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to load active session from cache", e)
        }
    }
    
    /**
     * Processes updates from CoreBatteryStatus.
     * Maps to StatsChargeStatus and manages charge sessions.
     */
    private suspend fun processCoreStatusUpdate(coreStatus: CoreBatteryStatus) {
        // Map CoreBatteryStatus to StatsChargeStatus and emit
        val statsStatus = mapToStatsChargeStatus(coreStatus)
        _statsChargeStatusFlow.value = statsStatus
        
        // Handle charge session logic
        handleChargeSessionLogic(coreStatus)
        
        // Update last status for next comparison
        lastCoreStatus = coreStatus
    }
    
    /**
     * Maps CoreBatteryStatus to StatsChargeStatus.
     */
    private fun mapToStatsChargeStatus(coreStatus: CoreBatteryStatus): StatsChargeStatus {
        val statsStatus = StatsChargeStatus(
            percentage = coreStatus.percentage,
            isCharging = coreStatus.isCharging,
            currentMicroAmperes = coreStatus.currentMicroAmperes,
            voltageMillivolts = coreStatus.voltageMillivolts,
            temperatureCelsius = coreStatus.temperatureCelsius
        )

        Log.v(TAG, "STATS_CHARGE_REPO: Mapped CoreBatteryStatus to StatsChargeStatus - " +
            "percentage=${statsStatus.percentage}%, " +
            "charging=${statsStatus.isCharging}, " +
            "current=${statsStatus.currentMicroAmperes}µA")

        return statsStatus
    }

    /**
     * Handles charge session start/end logic based on charging state changes.
     */
    private suspend fun handleChargeSessionLogic(coreStatus: CoreBatteryStatus) {
        val currentSession = _activeChargeSessionFlow.value
        val wasCharging = lastCoreStatus?.isCharging ?: false
        val isNowCharging = coreStatus.isCharging

        when {
            // Charging started: Create new session if none exists or previous was inactive
            isNowCharging && (!wasCharging || currentSession == null || !currentSession.isActive) -> {
                startNewChargeSession(coreStatus.percentage)
            }

            // Charging stopped: End active session if it exists
            !isNowCharging && wasCharging && currentSession != null && currentSession.isActive -> {
                endActiveChargeSession(coreStatus.percentage)
            }

            // Still charging: Update session statistics
            isNowCharging && wasCharging && currentSession != null && currentSession.isActive -> {
                updateSessionStatistics(currentSession, coreStatus.percentage)
            }

            // Still not charging: No session action needed
            !isNowCharging && !wasCharging -> {
                Log.v(TAG, "STATS_CHARGE_REPO: Not charging - no session")
            }
        }
    }

    /**
     * Starts a new charge session.
     */
    private suspend fun startNewChargeSession(startPercentage: Int) {
        try {
            val newSession = StatsChargeSession.createNew(startPercentage)
            _activeChargeSessionFlow.value = newSession
            statsChargeCache.saveActiveSession(newSession)

            Log.d(TAG, "STATS_CHARGE_REPO: New session started - " +
                "ID=${newSession.hashCode()}, " +
                "StartPercent=${startPercentage}%")

        } catch (e: Exception) {
            Log.e(TAG, "Failed to start new charge session", e)
        }
    }

    /**
     * Calculates total charge percentage for a session.
     * Formula: current percentage - start percentage
     *
     * @param session The charge session
     * @param currentPercentage Current battery percentage
     * @return Total percentage charged during the session
     */
    private fun calculateTotalChargePercentage(session: StatsChargeSession, currentPercentage: Int): Int {
        val totalCharge = currentPercentage - session.startPercentage
        Log.v(TAG, "STATS_CHARGE_REPO: Total charge percentage calculated - " +
            "current=${currentPercentage}%, start=${session.startPercentage}%, total=${totalCharge}%")
        return maxOf(0, totalCharge) // Ensure non-negative
    }

    /**
     * Calculates total charge mAh for a session.
     * Formula: (percentage gained / 100) × battery capacity
     *
     * @param totalChargePercentage Total percentage charged
     * @return Total mAh charged during the session
     */
    private suspend fun calculateTotalChargeMah(totalChargePercentage: Int): Double {
        val batteryCapacityMah = appRepository.getBatteryCapacity()
        val totalChargeMah = (totalChargePercentage / 100.0) * batteryCapacityMah
        Log.v(TAG, "STATS_CHARGE_REPO: Total charge mAh calculated - " +
            "percentage=${totalChargePercentage}%, capacity=${batteryCapacityMah}mAh, total=${totalChargeMah}mAh")
        return totalChargeMah
    }

    /**
     * Updates session statistics during active charging.
     */
    private suspend fun updateSessionStatistics(session: StatsChargeSession, currentPercentage: Int) {
        try {
            val totalChargePercentage = calculateTotalChargePercentage(session, currentPercentage)
            val totalChargeMah = calculateTotalChargeMah(totalChargePercentage)

            val updatedSession = session.copy(
                totalChargePercentage = totalChargePercentage,
                totalChargeMah = totalChargeMah
            )

            _activeChargeSessionFlow.value = updatedSession
            statsChargeCache.saveActiveSession(updatedSession)

            Log.v(TAG, "STATS_CHARGE_REPO: Session statistics updated - " +
                "ID=${updatedSession.hashCode()}, " +
                "TotalChargePercent=${totalChargePercentage}%, " +
                "TotalChargeMah=${totalChargeMah}mAh")

        } catch (e: Exception) {
            Log.e(TAG, "Failed to update session statistics", e)
        }
    }

    /**
     * Ends the active charge session.
     */
    private suspend fun endActiveChargeSession(endPercentage: Int) {
        try {
            val currentSession = _activeChargeSessionFlow.value
            if (currentSession != null && currentSession.isActive) {
                // Calculate final statistics
                val totalChargePercentage = calculateTotalChargePercentage(currentSession, endPercentage)
                val totalChargeMah = calculateTotalChargeMah(totalChargePercentage)

                // Update session with final statistics before ending
                val sessionWithStats = currentSession.copy(
                    totalChargePercentage = totalChargePercentage,
                    totalChargeMah = totalChargeMah
                )

                val endedSession = StatsChargeSession.endSession(sessionWithStats, endPercentage)
                _activeChargeSessionFlow.value = endedSession

                // Clear from cache since session is no longer active
                statsChargeCache.clearActiveSession()

                Log.d(TAG, "STATS_CHARGE_REPO: Session ended - " +
                    "ID=${endedSession.hashCode()}, " +
                    "EndPercent=${endPercentage}%, " +
                    "Duration=${endedSession.durationMillis}ms, " +
                    "PercentageCharged=${endedSession.percentageCharged}%, " +
                    "TotalChargePercent=${totalChargePercentage}%, " +
                    "TotalChargeMah=${totalChargeMah}mAh")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to end active charge session", e)
        }
    }

    /**
     * Resets the current charge session by clearing it from cache and flow.
     */
    override suspend fun resetCurrentSession() {
        try {
            _activeChargeSessionFlow.value = null
            statsChargeCache.clearActiveSession()
            Log.d(TAG, "STATS_CHARGE_REPO: Current session reset")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to reset current session", e)
        }
    }
}
