package com.tqhit.battery.one.features.stats.apppower.presentation

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.tqhit.battery.one.R
import com.tqhit.battery.one.features.stats.apppower.data.AppPowerConsumptionData

/**
 * RecyclerView adapter for displaying app power consumption data
 */
class AppPowerConsumptionAdapter : ListAdapter<AppPowerConsumptionData, AppPowerConsumptionAdapter.AppViewHolder>(
    AppDiffCallback()
) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): AppViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_app_power_consumption, parent, false)
        return AppViewHolder(view)
    }

    override fun onBindViewHolder(holder: AppViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    class AppViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val appIcon: ImageView = itemView.findViewById(R.id.iv_app_icon)
        private val appName: TextView = itemView.findViewById(R.id.tv_app_name)
        private val usageTime: TextView = itemView.findViewById(R.id.tv_usage_time)
        private val percentage: TextView = itemView.findViewById(R.id.tv_percentage)
        private val powerConsumption: TextView = itemView.findViewById(R.id.tv_power_consumption)

        fun bind(app: AppPowerConsumptionData) {
            appName.text = app.appName
            usageTime.text = app.formattedUsageTime
            percentage.text = app.formattedPercentage
            powerConsumption.text = app.formattedPowerConsumption
            
            // Set app icon or default if null
            if (app.appIcon != null) {
                appIcon.setImageDrawable(app.appIcon)
            } else {
                appIcon.setImageResource(R.drawable.ic_android) // Default app icon
            }
        }
    }

    private class AppDiffCallback : DiffUtil.ItemCallback<AppPowerConsumptionData>() {
        override fun areItemsTheSame(
            oldItem: AppPowerConsumptionData,
            newItem: AppPowerConsumptionData
        ): Boolean {
            return oldItem.packageName == newItem.packageName
        }

        override fun areContentsTheSame(
            oldItem: AppPowerConsumptionData,
            newItem: AppPowerConsumptionData
        ): Boolean {
            return oldItem == newItem
        }
    }
}
