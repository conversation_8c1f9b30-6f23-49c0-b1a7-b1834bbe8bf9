package com.tqhit.battery.one.features.stats.charge.cache

import android.util.Log
import com.google.gson.Gson
import com.tqhit.adlib.sdk.data.local.PreferencesHelper
import com.tqhit.battery.one.features.stats.charge.data.StatsChargeSession
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Interface for caching StatsChargeSession data.
 * Provides methods to save, retrieve, and clear the active charge session.
 */
interface StatsChargeCache {
    
    /**
     * Saves the active charge session to persistent storage.
     *
     * @param session The StatsChargeSession to save
     */
    suspend fun saveActiveSession(session: StatsChargeSession)
    
    /**
     * Retrieves the active charge session from persistent storage.
     *
     * @return The active StatsChargeSession or null if none exists
     */
    suspend fun getActiveSession(): StatsChargeSession?
    
    /**
     * Clears the active charge session from persistent storage.
     */
    suspend fun clearActiveSession()
}

/**
 * SharedPreferences implementation of StatsChargeCache.
 * Uses <PERSON><PERSON> to serialize/deserialize StatsChargeSession objects to/from JSON.
 */
@Singleton
class PrefsStatsChargeCache @Inject constructor(
    private val preferencesHelper: PreferencesHelper,
    private val gson: Gson
) : StatsChargeCache {
    
    companion object {
        private const val TAG = "StatsChargeCache"
        private const val KEY_ACTIVE_CHARGE_SESSION = "active_charge_session"
    }
    
    /**
     * Saves the active charge session to SharedPreferences as JSON.
     *
     * @param session The StatsChargeSession to save
     */
    override suspend fun saveActiveSession(session: StatsChargeSession) {
        try {
            val sessionJson = gson.toJson(session)
            preferencesHelper.saveString(KEY_ACTIVE_CHARGE_SESSION, sessionJson)
            
            Log.d(TAG, "STATS_CHARGE_CACHE: Active session saved - " +
                "ID=${session.hashCode()}, " +
                "StartPercent=${session.startPercentage}%, " +
                "IsActive=${session.isActive}, " +
                "Duration=${session.durationMillis}ms")
                
        } catch (e: Exception) {
            Log.e(TAG, "Failed to save active charge session", e)
        }
    }
    
    /**
     * Retrieves the active charge session from SharedPreferences.
     *
     * @return The active StatsChargeSession or null if none exists or parsing fails
     */
    override suspend fun getActiveSession(): StatsChargeSession? {
        return try {
            val sessionJson = preferencesHelper.getString(KEY_ACTIVE_CHARGE_SESSION, "")
            
            if (sessionJson.isNotEmpty()) {
                val session = gson.fromJson(sessionJson, StatsChargeSession::class.java)
                
                Log.d(TAG, "STATS_CHARGE_CACHE: Active session loaded - " +
                    "ID=${session.hashCode()}, " +
                    "StartPercent=${session.startPercentage}%, " +
                    "IsActive=${session.isActive}, " +
                    "Duration=${session.durationMillis}ms")
                    
                session
            } else {
                Log.d(TAG, "STATS_CHARGE_CACHE: No active session found")
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to load active charge session", e)
            null
        }
    }
    
    /**
     * Clears the active charge session from SharedPreferences.
     */
    override suspend fun clearActiveSession() {
        try {
            preferencesHelper.saveString(KEY_ACTIVE_CHARGE_SESSION, "")
            Log.d(TAG, "STATS_CHARGE_CACHE: Active session cleared")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to clear active charge session", e)
        }
    }
}
