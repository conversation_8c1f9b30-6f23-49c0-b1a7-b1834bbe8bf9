package com.tqhit.battery.one.features.stats.discharge.integration

import com.tqhit.battery.one.features.stats.discharge.data.DischargeSessionData
import org.junit.Test
import org.junit.Assert.*

/**
 * Test to verify that screen time oscillation issues are resolved
 */
class ScreenTimeOscillationTest {

    @Test
    fun `test session duration buffer prevents constraint violations`() {
        // Given: A session that started 30 seconds ago
        val sessionStartTime = System.currentTimeMillis() - 30000L // 30 seconds ago
        val activeSession = createTestSession(
            startTime = sessionStartTime,
            isActive = true
        )

        // When: We calculate the session duration
        val sessionDuration = activeSession.durationMillis
        val realTimeDuration = System.currentTimeMillis() - sessionStartTime

        // Then: Session duration should include buffer
        assertTrue(
            "Session duration should include 2-second buffer",
            sessionDuration >= realTimeDuration + 1500L // At least 1.5 seconds buffer
        )
        
        // And: Buffer should be reasonable (not too large)
        assertTrue(
            "Buffer should not be excessive",
            sessionDuration <= realTimeDuration + 3000L // No more than 3 seconds buffer
        )
    }

    @Test
    fun `test constraint enforcement cooldown logic`() {
        // This test verifies the logic for preventing oscillation
        // by testing the constraint enforcement conditions
        
        val currentTime = System.currentTimeMillis()
        val sessionStartTime = currentTime - 30000L // 30 seconds ago
        val sessionDuration = 32000L // 32 seconds (with buffer)
        
        // Test case 1: Minor violation should not trigger enforcement
        val minorViolationMs = 1000L // 1 second violation
        val minorViolationPercent = (minorViolationMs.toDouble() / sessionDuration.toDouble()) * 100.0
        
        assertFalse(
            "Minor violation should not trigger enforcement",
            minorViolationMs > 2000L && minorViolationPercent > 1.0
        )
        
        // Test case 2: Significant violation should trigger enforcement
        val significantViolationMs = 3000L // 3 second violation
        val significantViolationPercent = (significantViolationMs.toDouble() / sessionDuration.toDouble()) * 100.0
        
        assertTrue(
            "Significant violation should trigger enforcement",
            significantViolationMs > 2000L && significantViolationPercent > 1.0
        )
    }

    @Test
    fun `test proportional scaling maintains ratios`() {
        // Given: Screen times that exceed session duration
        val sessionDuration = 30000L // 30 seconds
        val originalOnTime = 20000L // 20 seconds
        val originalOffTime = 15000L // 15 seconds
        val totalTime = originalOnTime + originalOffTime // 35 seconds (exceeds 30)
        
        // When: We apply proportional scaling with buffer
        val targetDuration = sessionDuration - 1000L // Leave 1 second buffer
        val scaleFactor = targetDuration.toDouble() / totalTime.toDouble()
        val scaledOnTime = (originalOnTime * scaleFactor).toLong()
        val scaledOffTime = (originalOffTime * scaleFactor).toLong()
        val scaledTotal = scaledOnTime + scaledOffTime
        
        // Then: Scaled times should fit within target duration
        assertTrue(
            "Scaled total should not exceed target duration",
            scaledTotal <= targetDuration
        )
        
        // And: Original proportions should be maintained
        val originalOnRatio = originalOnTime.toDouble() / totalTime.toDouble()
        val scaledOnRatio = scaledOnTime.toDouble() / scaledTotal.toDouble()
        val ratioDifference = kotlin.math.abs(originalOnRatio - scaledOnRatio)
        
        assertTrue(
            "Proportions should be maintained (difference: $ratioDifference)",
            ratioDifference < 0.01 // Less than 1% difference
        )
    }

    @Test
    fun `test inactive session duration is fixed`() {
        // Given: An inactive session
        val sessionStartTime = System.currentTimeMillis() - 60000L // 1 minute ago
        val sessionEndTime = System.currentTimeMillis() - 30000L   // 30 seconds ago
        val inactiveSession = createTestSession(
            startTime = sessionStartTime,
            lastUpdateTime = sessionEndTime,
            isActive = false
        )

        // When: We calculate the session duration
        val sessionDuration = inactiveSession.durationMillis
        val expectedDuration = sessionEndTime - sessionStartTime

        // Then: Duration should be fixed (no buffer for inactive sessions)
        assertEquals(
            "Inactive session duration should be fixed",
            expectedDuration, sessionDuration
        )
    }

    @Test
    fun `test constraint enforcement frequency limits`() {
        // Test the constraint enforcement limits to prevent oscillation
        val maxEnforcementsPerMinute = 3
        val cooldownMs = 5000L // 5 seconds
        
        // Simulate multiple constraint violations in quick succession
        val violations = listOf(
            0L,     // First violation - should be enforced
            1000L,  // 1 second later - should be blocked by cooldown
            6000L,  // 6 seconds later - should be enforced (after cooldown)
            7000L,  // 1 second later - should be blocked by cooldown
            12000L, // 5 seconds later - should be enforced (after cooldown)
            13000L, // 1 second later - should be blocked by cooldown
            18000L  // 5 seconds later - should be blocked by max limit
        )
        
        var enforcementCount = 0
        var lastEnforcementTime = 0L
        
        violations.forEach { violationTime ->
            val shouldEnforce = (violationTime - lastEnforcementTime) > cooldownMs &&
                    enforcementCount < maxEnforcementsPerMinute
            
            if (shouldEnforce) {
                enforcementCount++
                lastEnforcementTime = violationTime
            }
        }
        
        // Should have enforced exactly 3 times (the maximum)
        assertEquals(
            "Should enforce exactly max times per minute",
            maxEnforcementsPerMinute, enforcementCount
        )
    }

    @Test
    fun `test gentle scaling vs aggressive scaling`() {
        // Test that gentle scaling is less disruptive than aggressive scaling
        val sessionDuration = 30000L // 30 seconds
        val violatingOnTime = 20000L // 20 seconds
        val violatingOffTime = 12000L // 12 seconds (total: 32 seconds)
        val totalViolatingTime = violatingOnTime + violatingOffTime
        
        // Aggressive scaling (old method): scale to exact session duration
        val aggressiveScaleFactor = sessionDuration.toDouble() / totalViolatingTime.toDouble()
        val aggressiveOnTime = (violatingOnTime * aggressiveScaleFactor).toLong()
        val aggressiveOffTime = (violatingOffTime * aggressiveScaleFactor).toLong()
        
        // Gentle scaling (new method): scale to session duration minus buffer
        val targetDuration = sessionDuration - 1000L // 1 second buffer
        val gentleScaleFactor = targetDuration.toDouble() / totalViolatingTime.toDouble()
        val gentleOnTime = (violatingOnTime * gentleScaleFactor).toLong()
        val gentleOffTime = (violatingOffTime * gentleScaleFactor).toLong()
        
        // Gentle scaling should result in smaller reduction
        assertTrue(
            "Gentle scaling should be less aggressive",
            gentleScaleFactor < aggressiveScaleFactor
        )
        
        // But both should prevent constraint violation
        assertTrue(
            "Aggressive scaling should fit in session duration",
            (aggressiveOnTime + aggressiveOffTime) <= sessionDuration
        )
        
        assertTrue(
            "Gentle scaling should fit in target duration",
            (gentleOnTime + gentleOffTime) <= targetDuration
        )
    }

    private fun createTestSession(
        startTime: Long = System.currentTimeMillis(),
        lastUpdateTime: Long = System.currentTimeMillis(),
        startPercentage: Int = 100,
        currentPercentage: Int = 90,
        isActive: Boolean = true,
        screenOnTimeMs: Long = 0L,
        screenOffTimeMs: Long = 0L
    ): DischargeSessionData {
        return DischargeSessionData(
            startTimeEpochMillis = startTime,
            lastUpdateTimeEpochMillis = lastUpdateTime,
            startPercentage = startPercentage,
            currentPercentage = currentPercentage,
            currentPercentageAtLastUpdate = currentPercentage,
            isActive = isActive,
            screenOnTimeMillis = screenOnTimeMs,
            screenOffTimeMillis = screenOffTimeMs,
            totalMahConsumed = 0.0,
            screenOnMahConsumed = 0.0,
            screenOffMahConsumed = 0.0,
            avgScreenOnDischargeRateMahPerHour = 250.0,
            avgScreenOffDischargeRateMahPerHour = 50.0,
            avgMixedDischargeRateMahPerHour = 120.0,
            avgPercentPerHour = 10.0,
            currentDischargeRate = 8.0
        )
    }
}
