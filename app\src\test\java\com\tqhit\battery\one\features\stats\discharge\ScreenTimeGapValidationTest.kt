package com.tqhit.battery.one.features.stats.discharge

import com.tqhit.battery.one.features.stats.discharge.domain.ScreenStateTimeTracker
import org.junit.Before
import org.junit.Test
import org.junit.Assert.*

/**
 * Test class for validating screen time gap calculation and 60-second tolerance
 * Tests the enhanced screen time tracking fixes for TJ_BatteryOne
 */
class ScreenTimeGapValidationTest {

    private lateinit var screenStateTimeTracker: ScreenStateTimeTracker
    private val sessionStartTime = System.currentTimeMillis()
    
    @Before
    fun setup() {
        screenStateTimeTracker = ScreenStateTimeTracker()
    }

    @Test
    fun `test gap validation within 60 second tolerance`() {
        // Initialize with some screen times
        screenStateTimeTracker.initialize(30000L, 25000L, false) // 30s ON, 25s OFF, Screen OFF
        
        // Apply gap estimation for a 60-second session
        screenStateTimeTracker.applyGapEstimationResults(30000L, sessionStartTime - 60000L)
        
        // Simulate time progression
        Thread.sleep(100) // Small delay to simulate real time
        
        // Get current times
        val (onTime, offTime) = screenStateTimeTracker.incrementCurrentState()
        val totalTime = onTime + offTime
        val sessionDuration = System.currentTimeMillis() - (sessionStartTime - 60000L)
        val gap = kotlin.math.abs(totalTime - sessionDuration)
        
        // Verify gap is within 60 seconds (60000ms)
        assertTrue("Gap should be within 60 seconds, but was ${gap/1000}s", gap <= 60000L)
        
        println("✓ Gap validation test passed: Gap = ${gap/1000}s ≤ 60s")
    }

    @Test
    fun `test simplified calculation during screen OFF periods`() {
        // Initialize tracker
        screenStateTimeTracker.initialize(20000L, 15000L, false) // Screen OFF
        
        // Apply gap estimation
        val testSessionStart = sessionStartTime - 40000L // 40 seconds ago
        screenStateTimeTracker.applyGapEstimationResults(20000L, testSessionStart)
        
        // Increment during Screen OFF period
        val (onTime, offTime) = screenStateTimeTracker.incrementCurrentState()
        
        // Verify simplified calculation: OFF time = Total session time - ON time
        val currentTime = System.currentTimeMillis()
        val expectedTotalTime = currentTime - testSessionStart
        val calculatedTotal = onTime + offTime
        val gap = kotlin.math.abs(calculatedTotal - expectedTotalTime)
        
        // Should be very close (within 5 seconds due to test execution time)
        assertTrue("Simplified calculation should be accurate within 5s, gap was ${gap/1000}s", gap <= 5000L)
        
        println("✓ Simplified calculation test passed: Gap = ${gap/1000}s ≤ 5s")
    }

    @Test
    fun `test gap correction when exceeding 60 second tolerance`() {
        // Initialize with times that would create a large gap
        screenStateTimeTracker.initialize(100000L, 50000L, false) // 100s ON, 50s OFF
        
        // Apply gap estimation for a much shorter session (should trigger correction)
        val testSessionStart = sessionStartTime - 60000L // 60 seconds ago
        screenStateTimeTracker.applyGapEstimationResults(30000L, testSessionStart)
        
        // Force set a problematic OFF time that would exceed tolerance
        screenStateTimeTracker.forceSetScreenOffTime(100000L) // 100 seconds OFF time
        
        // Now increment and check if gap correction is applied
        val (onTime, offTime) = screenStateTimeTracker.incrementCurrentState()
        
        val currentTime = System.currentTimeMillis()
        val sessionDuration = currentTime - testSessionStart
        val totalTime = onTime + offTime
        val gap = kotlin.math.abs(totalTime - sessionDuration)
        
        // After correction, gap should be within tolerance
        assertTrue("Gap should be corrected to within 60s, but was ${gap/1000}s", gap <= 60000L)
        
        println("✓ Gap correction test passed: Gap after correction = ${gap/1000}s ≤ 60s")
    }

    @Test
    fun `test force set screen OFF time functionality`() {
        // Initialize tracker
        screenStateTimeTracker.initialize(10000L, 5000L, false)
        
        val newOffTime = 25000L // 25 seconds
        
        // Force set new OFF time
        screenStateTimeTracker.forceSetScreenOffTime(newOffTime)
        
        // Verify the change was applied
        val (onTime, offTime) = screenStateTimeTracker.getCurrentTimes()
        
        assertEquals("OFF time should be updated to new value", newOffTime, offTime)
        assertEquals("ON time should remain unchanged", 10000L, onTime)
        
        println("✓ Force set OFF time test passed: OFF time = ${offTime/1000}s")
    }

    @Test
    fun `test mathematical constraint validation`() {
        // Test that screen time sum never exceeds session duration + 60s tolerance
        screenStateTimeTracker.initialize(30000L, 20000L, true) // Screen ON
        
        val testSessionStart = sessionStartTime - 45000L // 45 seconds ago
        screenStateTimeTracker.applyGapEstimationResults(30000L, testSessionStart)
        
        // Increment multiple times
        repeat(10) {
            val (onTime, offTime) = screenStateTimeTracker.incrementCurrentState()
            val totalTime = onTime + offTime
            val sessionDuration = System.currentTimeMillis() - testSessionStart
            val maxAllowed = sessionDuration + 60000L // Session duration + 60s tolerance
            
            assertTrue("Total screen time should not exceed session duration + 60s tolerance. " +
                      "Total: ${totalTime/1000}s, Max allowed: ${maxAllowed/1000}s", 
                      totalTime <= maxAllowed)
            
            Thread.sleep(100) // Small delay between increments
        }
        
        println("✓ Mathematical constraint validation test passed")
    }

    @Test
    fun `test edge case - very short session`() {
        // Test behavior with very short sessions (< 30 seconds)
        val testSessionStart = sessionStartTime - 15000L // 15 seconds ago
        
        screenStateTimeTracker.applyGapEstimationResults(5000L, testSessionStart) // 5s ON time
        
        val (onTime, offTime) = screenStateTimeTracker.incrementCurrentState()
        val totalTime = onTime + offTime
        val sessionDuration = System.currentTimeMillis() - testSessionStart
        val gap = kotlin.math.abs(totalTime - sessionDuration)
        
        // Even for short sessions, gap should be reasonable
        assertTrue("Gap for short session should be within 60s, was ${gap/1000}s", gap <= 60000L)
        
        println("✓ Short session test passed: Gap = ${gap/1000}s for ${sessionDuration/1000}s session")
    }
}
