/ Header Record For PersistentHashMapValueStorageN (com.tqhit.adlib.sdk.AdLibHiltApplication$androidx.lifecycle.LifecycleObserver. -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity) (androidx.appcompat.app.AppCompatActivity. -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity. -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity. -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity. -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity. -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity' &androidx.viewpager.widget.PagerAdapter android.widget.ProgressBar, +com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog, +com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog, +com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog, +com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog, +com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog, +com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog, +com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog, +com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog, +com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog, +com.tqhit.adlib.sdk.base.ui.AdLibBaseDialogN Mcom.tqhit.battery.one.features.emoji.domain.repository.BatteryStyleRepositoryO Ncom.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum java.lang.ExceptionX Wcom.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepositoryExceptionX Wcom.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepositoryExceptionX Wcom.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepositoryExceptionX Wcom.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepositoryException java.lang.Exception java.lang.ExceptionK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventK Jcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent androidx.fragment.app.Fragment androidx.lifecycle.ViewModel) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback android.view.ViewN Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEventN Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEventN Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEventN Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEventN Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEventN Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEventN Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEventN Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEventN Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEventN Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEventN Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEventN Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEventN Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEventN Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEventN Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEventN Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEventN Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEventN Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEventN Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEventN Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEventN Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEventN Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEventN Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEventN Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEventN Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEventN Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEventN Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEventN Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEventN Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEventN Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEventJ Icom.tqhit.battery.one.features.emoji.presentation.gallery.NavigationEventJ Icom.tqhit.battery.one.features.emoji.presentation.gallery.NavigationEventJ Icom.tqhit.battery.one.features.emoji.presentation.gallery.NavigationEvent androidx.lifecycle.ViewModel. -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment9 8androidx.recyclerview.widget.RecyclerView.ItemDecoration) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback kotlin.Enum) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback android.app.DialogC Bcom.tqhit.battery.one.features.stats.charge.cache.StatsChargeCache androidx.fragment.app.Fragment androidx.lifecycle.ViewModelM Lcom.tqhit.battery.one.features.stats.charge.repository.StatsChargeRepositoryQ Pcom.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider android.app.ServiceI Hcom.tqhit.battery.one.features.stats.discharge.cache.CurrentSessionCacheI Hcom.tqhit.battery.one.features.stats.discharge.cache.DischargeRatesCache, +androidx.lifecycle.DefaultLifecycleObserver kotlin.EnumG Fcom.tqhit.battery.one.features.stats.discharge.domain.ValidationResultG Fcom.tqhit.battery.one.features.stats.discharge.domain.ValidationResultG Fcom.tqhit.battery.one.features.stats.discharge.domain.ValidationResult kotlin.Enum kotlin.Enum androidx.fragment.app.Fragment androidx.lifecycle.ViewModel android.app.Service> =com.tqhit.battery.one.features.stats.health.cache.HealthCache kotlin.Enum kotlin.Enum androidx.lifecycle.ViewModelH Gcom.tqhit.battery.one.features.stats.health.repository.HealthRepository android.app.Service. -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment. -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment kotlin.Enum. -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment. -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment. -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder9 8androidx.recyclerview.widget.RecyclerView.ItemDecoration2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2com.tqhit.battery.one.manager.graph.HistoryManager3 2com.tqhit.battery.one.manager.graph.HistoryManager android.app.Service android.app.Service androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel