package com.tqhit.battery.one.features.emoji.presentation.customize.view

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Rect
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.util.Log
import android.view.View
import androidx.core.content.ContextCompat
import com.bumptech.glide.Glide
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import com.tqhit.battery.one.R
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfig
import com.tqhit.battery.one.features.emoji.domain.model.CustomizationConfig

/**
 * Custom view for displaying live preview of emoji battery customization.
 * 
 * Renders the battery container, emoji character, and percentage text
 * according to the current customization configuration. Provides real-time
 * preview updates as user modifies settings.
 * 
 * Key features:
 * - Real-time rendering of battery container and emoji
 * - Dynamic text sizing and positioning
 * - Color customization support
 * - Battery level simulation
 * - Performance optimized drawing
 */
class LivePreviewView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {
    
    companion object {
        private const val TAG = "LivePreviewView"
        private const val DEFAULT_BATTERY_LEVEL = 50
        private const val MIN_PREVIEW_SIZE = 100
    }
    
    // Drawing components
    private val textPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val backgroundPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    
    // Current state
    private var currentConfig: CustomizationConfig? = null
    private var batteryLevel: Int = DEFAULT_BATTERY_LEVEL
    private var isCharging: Boolean = false
    
    // Drawable resources
    private var batteryDrawable: Drawable? = null
    private var emojiDrawable: Drawable? = null
    
    // Layout calculations
    private val drawingBounds = Rect()
    private val batteryBounds = Rect()
    private val emojiBounds = Rect()
    private val textBounds = Rect()
    
    // Loading state
    private var isLoading: Boolean = false
    
    init {
        setupPaints()
        Log.d(TAG, "LivePreviewView initialized")
    }
    
    /**
     * Sets up paint objects for drawing.
     */
    private fun setupPaints() {
        textPaint.apply {
            isAntiAlias = true
            textAlign = Paint.Align.CENTER
            color = ContextCompat.getColor(context, android.R.color.white)
        }
        
        backgroundPaint.apply {
            isAntiAlias = true
            color = ContextCompat.getColor(context, R.color.preview_background)
        }
    }
    
    /**
     * Updates the preview with new customization configuration.
     * 
     * @param config The CustomizationConfig to preview
     * @param batteryLevel Current battery level (0-100)
     * @param isCharging Whether device is charging
     */
    fun updatePreview(
        config: CustomizationConfig,
        batteryLevel: Int = this.batteryLevel,
        isCharging: Boolean = this.isCharging
    ) {
        Log.d(TAG, "PREVIEW_UPDATE: Updating preview with battery level: $batteryLevel%")
        
        this.currentConfig = config
        this.batteryLevel = batteryLevel.coerceIn(0, 100)
        this.isCharging = isCharging
        
        // Update text paint with new configuration
        updateTextPaint(config.styleConfig)
        
        // Load new images if URLs changed
        loadImages(config)
        
        // Trigger redraw
        invalidate()
    }
    
    /**
     * Updates text paint properties based on style configuration.
     */
    private fun updateTextPaint(styleConfig: BatteryStyleConfig) {
        textPaint.apply {
            textSize = styleConfig.percentageFontSizeDp * resources.displayMetrics.density
            color = styleConfig.percentageColor
        }
    }
    
    /**
     * Loads battery and emoji images from URLs.
     */
    private fun loadImages(config: CustomizationConfig) {
        isLoading = true
        
        // Load battery container image
        if (config.selectedBatteryImageUrl.isNotBlank()) {
            Glide.with(context)
                .asDrawable()
                .load(config.selectedBatteryImageUrl)
                .into(object : CustomTarget<Drawable>() {
                    override fun onResourceReady(resource: Drawable, transition: Transition<in Drawable>?) {
                        batteryDrawable = resource
                        checkLoadingComplete()
                        invalidate()
                        Log.d(TAG, "PREVIEW_UPDATE: Battery image loaded")
                    }
                    
                    override fun onLoadCleared(placeholder: Drawable?) {
                        batteryDrawable = placeholder
                    }
                    
                    override fun onLoadFailed(errorDrawable: Drawable?) {
                        batteryDrawable = ContextCompat.getDrawable(context, R.drawable.ic_battery_placeholder)
                        checkLoadingComplete()
                        invalidate()
                        Log.w(TAG, "PREVIEW_UPDATE: Failed to load battery image, using placeholder")
                    }
                })
        }
        
        // Load emoji character image
        if (config.selectedEmojiImageUrl.isNotBlank()) {
            Glide.with(context)
                .asDrawable()
                .load(config.selectedEmojiImageUrl)
                .into(object : CustomTarget<Drawable>() {
                    override fun onResourceReady(resource: Drawable, transition: Transition<in Drawable>?) {
                        emojiDrawable = resource
                        checkLoadingComplete()
                        invalidate()
                        Log.d(TAG, "PREVIEW_UPDATE: Emoji image loaded")
                    }
                    
                    override fun onLoadCleared(placeholder: Drawable?) {
                        emojiDrawable = placeholder
                    }
                    
                    override fun onLoadFailed(errorDrawable: Drawable?) {
                        emojiDrawable = ContextCompat.getDrawable(context, R.drawable.ic_emoji_placeholder)
                        checkLoadingComplete()
                        invalidate()
                        Log.w(TAG, "PREVIEW_UPDATE: Failed to load emoji image, using placeholder")
                    }
                })
        }
    }
    
    /**
     * Checks if all images are loaded and updates loading state.
     */
    private fun checkLoadingComplete() {
        val config = currentConfig ?: return
        
        val batteryLoaded = config.selectedBatteryImageUrl.isBlank() || batteryDrawable != null
        val emojiLoaded = config.selectedEmojiImageUrl.isBlank() || emojiDrawable != null
        
        if (batteryLoaded && emojiLoaded) {
            isLoading = false
            Log.d(TAG, "PREVIEW_UPDATE: All images loaded, preview ready")
        }
    }
    
    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        calculateLayout()
        Log.d(TAG, "LAYOUT: Size changed to ${w}x${h}")
    }
    
    /**
     * Calculates layout bounds for all drawing elements.
     */
    private fun calculateLayout() {
        val width = width - paddingLeft - paddingRight
        val height = height - paddingTop - paddingBottom
        
        if (width <= 0 || height <= 0) return
        
        // Calculate drawing bounds
        drawingBounds.set(paddingLeft, paddingTop, paddingLeft + width, paddingTop + height)
        
        // Calculate preview size (maintain aspect ratio)
        val previewSize = minOf(width, height, MIN_PREVIEW_SIZE * 2)
        val centerX = drawingBounds.centerX()
        val centerY = drawingBounds.centerY()
        
        // Battery container bounds (larger)
        val batterySize = (previewSize * 0.8f).toInt()
        batteryBounds.set(
            centerX - batterySize / 2,
            centerY - batterySize / 2,
            centerX + batterySize / 2,
            centerY + batterySize / 2
        )
        
        // Emoji bounds (smaller, positioned inside battery)
        val config = currentConfig
        val emojiScale = config?.styleConfig?.emojiSizeScale ?: 1.0f
        val emojiSize = (batterySize * 0.6f * emojiScale).toInt()
        emojiBounds.set(
            centerX - emojiSize / 2,
            centerY - emojiSize / 2,
            centerX + emojiSize / 2,
            centerY + emojiSize / 2
        )
        
        Log.d(TAG, "LAYOUT: Calculated bounds - battery: $batteryBounds, emoji: $emojiBounds")
    }
    
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        
        val config = currentConfig
        if (config == null) {
            drawPlaceholder(canvas)
            return
        }
        
        if (isLoading) {
            drawLoading(canvas)
            return
        }
        
        // Draw battery container
        batteryDrawable?.let { drawable ->
            drawable.bounds = batteryBounds
            drawable.draw(canvas)
        }
        
        // Draw emoji character if enabled
        if (config.styleConfig.showEmoji) {
            emojiDrawable?.let { drawable ->
                drawable.bounds = emojiBounds
                drawable.draw(canvas)
            }
        }
        
        // Draw percentage text if enabled
        if (config.styleConfig.showPercentage) {
            drawPercentageText(canvas, config.styleConfig)
        }
    }
    
    /**
     * Draws placeholder content when no configuration is available.
     */
    private fun drawPlaceholder(canvas: Canvas) {
        val placeholderText = "Preview"
        textPaint.textSize = 24f * resources.displayMetrics.density
        textPaint.color = ContextCompat.getColor(context, android.R.color.darker_gray)
        
        val centerX = drawingBounds.centerX().toFloat()
        val centerY = drawingBounds.centerY().toFloat()
        
        canvas.drawText(placeholderText, centerX, centerY, textPaint)
    }
    
    /**
     * Draws loading indicator.
     */
    private fun drawLoading(canvas: Canvas) {
        val loadingText = "Loading..."
        textPaint.textSize = 18f * resources.displayMetrics.density
        textPaint.color = ContextCompat.getColor(context, android.R.color.darker_gray)
        
        val centerX = drawingBounds.centerX().toFloat()
        val centerY = drawingBounds.centerY().toFloat()
        
        canvas.drawText(loadingText, centerX, centerY, textPaint)
    }
    
    /**
     * Draws percentage text with current battery level.
     */
    private fun drawPercentageText(canvas: Canvas, styleConfig: BatteryStyleConfig) {
        val percentageText = "${batteryLevel}%"
        
        // Calculate text position (below the emoji/battery)
        val centerX = drawingBounds.centerX().toFloat()
        val textY = emojiBounds.bottom + (textPaint.textSize * 1.2f)
        
        // Ensure text fits within bounds
        val maxY = drawingBounds.bottom - textPaint.descent()
        val finalY = minOf(textY, maxY)
        
        canvas.drawText(percentageText, centerX, finalY, textPaint)
    }
    
    /**
     * Sets the battery level for preview.
     */
    fun setBatteryLevel(level: Int) {
        if (this.batteryLevel != level) {
            this.batteryLevel = level.coerceIn(0, 100)
            invalidate()
            Log.d(TAG, "BATTERY_LEVEL: Updated to $level%")
        }
    }
    
    /**
     * Sets the charging state for preview.
     */
    fun setChargingState(isCharging: Boolean) {
        if (this.isCharging != isCharging) {
            this.isCharging = isCharging
            invalidate()
            Log.d(TAG, "CHARGING_STATE: Updated to $isCharging")
        }
    }
    
    /**
     * Clears the current preview.
     */
    fun clearPreview() {
        currentConfig = null
        batteryDrawable = null
        emojiDrawable = null
        isLoading = false
        invalidate()
        Log.d(TAG, "PREVIEW_CLEAR: Preview cleared")
    }
}
