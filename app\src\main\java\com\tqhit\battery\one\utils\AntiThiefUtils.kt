package com.tqhit.battery.one.utils

import android.app.ActivityManager
import android.content.Context
import android.content.IntentFilter

object AntiThiefUtils {
    fun isEnterPasswordActivityRunning(context: Context): Boolean {
        val am = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val runningTasks = am.getRunningTasks(1)
        if (runningTasks.isNotEmpty()) {
            val topActivity = runningTasks[0].topActivity
            return topActivity?.className == "com.tqhit.battery.one.activity.password.EnterPasswordActivity"
        }
        return false
    }
}
