<?xml version="1.0" encoding="utf-8"?>
<!-- 
 * Status and estimates section
 * This section contains:
 * - Main battery percentage display
 * - Formatted status text
 * - Time estimates for different usage patterns (screen on, mixed usage, screen off)
 */
-->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/statusAndEstimatesRoot"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/white_block"
    android:padding="8dp"
    android:layout_marginStart="9dp"
    android:layout_marginEnd="9dp">

    <!-- Main Percentage Display -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">
        
        <!-- Battery percentage in a circle -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:background="@drawable/circle"
            android:paddingStart="8dp"
            android:paddingEnd="8dp">
            
            <TextView
                android:id="@+id/sae_tv_percentage"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="27dp"
                android:textColor="?attr/colorr"
                android:text="@string/zero"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"/>
                
            <!-- Invisible text to maintain consistent sizing -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="27dp"
                android:textColor="?attr/colorr"
                android:visibility="invisible"
                android:text="100%"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"/>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <!-- Status text and time estimate blocks -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:orientation="vertical">
            
            <!-- Status text (charging/discharging) -->
            <TextView
                android:id="@+id/sae_tv_formatted_status_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/grey_block_line_up_static"
                android:baselineAligned="false"
                android:ellipsize="marquee"
                android:focusableInTouchMode="true"
                android:gravity="center"
                android:marqueeRepeatLimit="marquee_forever"
                android:paddingStart="10dp"
                android:paddingTop="12dp"
                android:paddingEnd="10dp"
                android:paddingBottom="12dp"
                android:singleLine="true"
                android:text="@string/charging"
                android:textColor="?attr/black"
                android:textSize="14sp" />

            <!-- Screen On Estimate -->
            <RelativeLayout
                android:id="@+id/sae_rl_screen_on_estimate"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:background="@drawable/grey_block_line"
                android:clickable="true"
                android:focusable="true">

                <TextView
                    android:id="@+id/sae_tv_screen_on_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="10dp"
                    android:text="@string/zero"
                    android:textColor="?attr/black"
                    android:textSize="14sp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentStart="true"
                    android:layout_marginStart="10dp"
                    android:layout_marginTop="12dp"
                    android:layout_marginEnd="5dp"
                    android:layout_marginBottom="12dp"
                    android:layout_toStartOf="@+id/sae_tv_screen_on_time"
                    android:ellipsize="marquee"
                    android:focusableInTouchMode="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:singleLine="true"
                    android:text="@string/active_mode"
                    android:textAlignment="viewStart"
                    android:textColor="?attr/black"
                    android:textSize="14sp" />
            </RelativeLayout>

            <!-- Mixed Usage Estimate -->
            <RelativeLayout
                android:id="@+id/sae_rl_mixed_usage_estimate"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:background="@drawable/grey_block_line"
                android:clickable="true"
                android:focusable="true">

                <TextView
                    android:id="@+id/sae_tv_mixed_usage_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="10dp"
                    android:text="@string/zero"
                    android:textColor="?attr/black"
                    android:textSize="14sp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentStart="true"
                    android:layout_marginStart="10dp"
                    android:layout_marginTop="12dp"
                    android:layout_marginEnd="5dp"
                    android:layout_marginBottom="12dp"
                    android:layout_toStartOf="@+id/sae_tv_mixed_usage_time"
                    android:ellipsize="marquee"
                    android:focusableInTouchMode="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:singleLine="true"
                    android:text="@string/complex_use"
                    android:textAlignment="viewStart"
                    android:textColor="?attr/black"
                    android:textSize="14sp" />
            </RelativeLayout>

            <!-- Screen Off Estimate -->
            <RelativeLayout
                android:id="@+id/sae_rl_screen_off_estimate"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:background="@drawable/grey_block_line_down"
                android:clickable="true"
                android:focusable="true">

                <TextView
                    android:id="@+id/sae_tv_screen_off_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="10dp"
                    android:text="@string/zero"
                    android:textColor="?attr/black"
                    android:textSize="14sp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentStart="true"
                    android:layout_marginStart="10dp"
                    android:layout_marginTop="12dp"
                    android:layout_marginEnd="5dp"
                    android:layout_marginBottom="12dp"
                    android:layout_toStartOf="@+id/sae_tv_screen_off_time"
                    android:ellipsize="marquee"
                    android:focusableInTouchMode="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:singleLine="true"
                    android:text="@string/screen_off"
                    android:textAlignment="viewStart"
                    android:textColor="?attr/black"
                    android:textSize="14sp" />
            </RelativeLayout>
        </LinearLayout>
    </LinearLayout>
</LinearLayout> 