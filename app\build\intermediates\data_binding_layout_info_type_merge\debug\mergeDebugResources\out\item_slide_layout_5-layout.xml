<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_slide_layout_5" modulePackage="com.tqhit.battery.one" filePath="app\src\main\res\layout\item_slide_layout_5.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.RelativeLayout"><Targets><Target tag="layout/item_slide_layout_5_0" view="RelativeLayout"><Expressions/><location startLine="1" startOffset="0" endLine="159" endOffset="16"/></Target><Target id="@+id/text_percent" view="TextView"><Expressions/><location startLine="15" startOffset="12" endLine="23" endOffset="53"/></Target><Target id="@+id/circularbar" view="me.tankery.lib.circularseekbar.CircularSeekBar"><Expressions/><location startLine="24" startOffset="12" endLine="42" endOffset="37"/></Target><Target id="@+id/textView4" view="TextView"><Expressions/><location startLine="44" startOffset="8" endLine="55" endOffset="44"/></Target><Target id="@+id/p5" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="75" startOffset="12" endLine="117" endOffset="63"/></Target><Target id="@+id/p_11" view="TextView"><Expressions/><location startLine="83" startOffset="16" endLine="101" endOffset="62"/></Target><Target id="@+id/switch_info" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="102" startOffset="16" endLine="116" endOffset="55"/></Target><Target id="@+id/button" view="RelativeLayout"><Expressions/><location startLine="120" startOffset="4" endLine="158" endOffset="20"/></Target><Target id="@+id/next_page" view="Button"><Expressions/><location startLine="141" startOffset="8" endLine="149" endOffset="62"/></Target></Targets></Layout>