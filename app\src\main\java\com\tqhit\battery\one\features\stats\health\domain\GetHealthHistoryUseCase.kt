package com.tqhit.battery.one.features.stats.health.domain

import android.util.Log
import com.github.mikephil.charting.data.Entry
import com.tqhit.battery.one.features.stats.health.data.HealthChartData
import com.tqhit.battery.one.repository.BatteryRepository
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.math.sin
import kotlin.random.Random

/**
 * Use case for retrieving and processing historical health data for charts.
 * Handles data transformation from raw battery history to chart-ready format.
 */
@Singleton
class GetHealthHistoryUseCase @Inject constructor(
    private val batteryRepository: BatteryRepository // Temporary during migration
) {
    
    companion object {
        private const val TAG = "GetHealthHistoryUseCase"
        
        // Chart configuration constants
        private const val MIN_DATA_POINTS = 2
        private const val MAX_DATA_POINTS = 100
        private const val TEMPERATURE_OFFSET = 20.0f // Mock temperature offset for demonstration
    }
    
    /**
     * Gets battery percentage history for the specified time range.
     * 
     * @param timeRangeHours Time range in hours (4, 8, 12, 24)
     * @return List of Entry objects for battery percentage chart
     */
    suspend fun getBatteryPercentageHistory(timeRangeHours: Int): List<Entry> {
        return try {
            val historyData = batteryRepository.getHistoryBatteryForHours(timeRangeHours)
            
            val entries = historyData.mapIndexed { index, historyEntry ->
                Entry(index.toFloat(), historyEntry.value.toFloat().coerceIn(0f, 100f))
            }
            
            Log.d(TAG, "HEALTH_HISTORY: Battery percentage history retrieved - " +
                "timeRange=${timeRangeHours}h, " +
                "dataPoints=${entries.size}")
            
            // Ensure we have reasonable data points
            if (entries.size < MIN_DATA_POINTS) {
                generateSampleBatteryData(timeRangeHours)
            } else {
                entries.take(MAX_DATA_POINTS)
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get battery percentage history", e)
            generateSampleBatteryData(timeRangeHours)
        }
    }
    
    /**
     * Gets temperature history for the specified time range.
     * Note: This is currently using mock data as temperature history is not fully implemented.
     * 
     * @param timeRangeHours Time range in hours (4, 8, 12, 24)
     * @return List of Entry objects for temperature chart
     */
    suspend fun getTemperatureHistory(timeRangeHours: Int): List<Entry> {
        return try {
            // TODO: Replace with actual temperature history when available
            // For now, generate mock temperature data based on battery data
            val batteryData = batteryRepository.getHistoryBatteryForHours(timeRangeHours)
            
            val temperatureEntries = batteryData.mapIndexed { index, historyEntry ->
                // Mock temperature calculation: base temperature + variation based on battery level
                val mockTemperature = TEMPERATURE_OFFSET + (historyEntry.value * 0.2f) +
                    (sin(index * 0.1) * 5).toFloat()
                Entry(index.toFloat(), mockTemperature.coerceIn(15f, 45f))
            }
            
            Log.d(TAG, "HEALTH_HISTORY: Temperature history generated - " +
                "timeRange=${timeRangeHours}h, " +
                "dataPoints=${temperatureEntries.size}")
            
            if (temperatureEntries.size < MIN_DATA_POINTS) {
                generateSampleTemperatureData(timeRangeHours)
            } else {
                temperatureEntries.take(MAX_DATA_POINTS)
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get temperature history", e)
            generateSampleTemperatureData(timeRangeHours)
        }
    }
    
    /**
     * Gets daily wear data for the last 7 days.
     * 
     * @return List of daily wear percentages (7 values)
     */
    suspend fun getDailyWearData(): List<Double> {
        return try {
            val wearData = batteryRepository.getDailyWearData(7)
            
            Log.d(TAG, "HEALTH_HISTORY: Daily wear data retrieved - " +
                "days=${wearData.size}, " +
                "maxWear=${wearData.maxOrNull()}")
            
            // Ensure we have exactly 7 days of data
            when {
                wearData.size == 7 -> wearData
                wearData.size > 7 -> wearData.take(7)
                else -> wearData + List(7 - wearData.size) { 0.0 }
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get daily wear data", e)
            generateSampleDailyWearData()
        }
    }
    
    /**
     * Gets complete chart data for the specified time range.
     * 
     * @param timeRangeHours Time range in hours (4, 8, 12, 24)
     * @return Complete HealthChartData object
     */
    suspend fun getCompleteChartData(timeRangeHours: Int): HealthChartData {
        return try {
            val batteryEntries = getBatteryPercentageHistory(timeRangeHours)
            val temperatureEntries = getTemperatureHistory(timeRangeHours)
            val dailyWearData = getDailyWearData()
            
            val chartData = HealthChartData(
                batteryPercentageEntries = batteryEntries,
                temperatureEntries = temperatureEntries,
                dailyWearData = dailyWearData,
                selectedTimeRangeHours = timeRangeHours
            )
            
            HealthChartData.logCreation(chartData)
            
            chartData
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get complete chart data", e)
            HealthChartData.createSample(timeRangeHours)
        }
    }
    
    /**
     * Generates sample battery percentage data for demonstration.
     * 
     * @param timeRangeHours Time range to generate data for
     * @return List of sample battery percentage entries
     */
    private fun generateSampleBatteryData(timeRangeHours: Int): List<Entry> {
        val dataPoints = timeRangeHours * 4 // 4 points per hour
        val entries = mutableListOf<Entry>()
        
        for (i in 0 until dataPoints) {
            val timeX = i.toFloat()
            // Simulate realistic battery discharge/charge pattern
            val batteryY = (70 + 25 * sin(i * 0.05) + Random.nextDouble() * 10).toFloat()
            entries.add(Entry(timeX, batteryY.coerceIn(0f, 100f)))
        }
        
        Log.d(TAG, "HEALTH_HISTORY: Sample battery data generated - ${entries.size} points")
        return entries
    }
    
    /**
     * Generates sample temperature data for demonstration.
     * 
     * @param timeRangeHours Time range to generate data for
     * @return List of sample temperature entries
     */
    private fun generateSampleTemperatureData(timeRangeHours: Int): List<Entry> {
        val dataPoints = timeRangeHours * 4 // 4 points per hour
        val entries = mutableListOf<Entry>()
        
        for (i in 0 until dataPoints) {
            val timeX = i.toFloat()
            // Simulate realistic temperature variations
            val tempY = (25 + 8 * sin(i * 0.03) + Random.nextDouble() * 4).toFloat()
            entries.add(Entry(timeX, tempY.coerceIn(15f, 40f)))
        }
        
        Log.d(TAG, "HEALTH_HISTORY: Sample temperature data generated - ${entries.size} points")
        return entries
    }
    
    /**
     * Generates sample daily wear data for demonstration.
     * 
     * @return List of 7 daily wear percentages
     */
    private fun generateSampleDailyWearData(): List<Double> {
        val wearData = List(7) {
            (Random.nextDouble() * 15 + 5) // Random wear between 5-20%
        }
        
        Log.d(TAG, "HEALTH_HISTORY: Sample daily wear data generated - 7 days")
        return wearData
    }
    
    /**
     * Validates chart data for completeness and reasonableness.
     * 
     * @param chartData Chart data to validate
     * @return true if data is valid for display
     */
    fun validateChartData(chartData: HealthChartData): Boolean {
        val isValid = chartData.isValid() &&
                     chartData.batteryPercentageEntries.size >= MIN_DATA_POINTS &&
                     chartData.temperatureEntries.size >= MIN_DATA_POINTS &&
                     chartData.dailyWearData.size == 7
        
        Log.v(TAG, "HEALTH_HISTORY: Chart data validation - " +
            "valid=$isValid, " +
            "batteryPoints=${chartData.batteryPercentageEntries.size}, " +
            "tempPoints=${chartData.temperatureEntries.size}, " +
            "wearDays=${chartData.dailyWearData.size}")
        
        return isValid
    }
}
