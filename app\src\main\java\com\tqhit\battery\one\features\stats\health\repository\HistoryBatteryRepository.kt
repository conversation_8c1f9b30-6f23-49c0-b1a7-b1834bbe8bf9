package com.tqhit.battery.one.features.stats.health.repository

import android.util.Log
import com.tqhit.adlib.sdk.data.local.PreferencesHelper
import com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider
import com.tqhit.battery.one.manager.graph.HistoryEntry
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.launch
import java.util.concurrent.ConcurrentLinkedQueue
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Dedicated repository for battery history data collection specifically for health fragment.
 * Integrates with CoreBatteryStatsService to collect real-time battery and temperature data.
 * 
 * This repository follows the stats module architecture pattern and provides clean separation
 * of concerns for health-specific data handling.
 */
@Singleton
class HistoryBatteryRepository @Inject constructor(
    private val coreBatteryStatsProvider: CoreBatteryStatsProvider,
    private val preferencesHelper: PreferencesHelper
) {
    
    companion object {
        private const val TAG = "HistoryBatteryRepository"
        private const val HISTORY_ENTRY_INTERVAL_MS = 60_000L // 1 minute
        private const val MAX_HISTORY_ENTRIES = 1440 // 24 hours worth of minute entries

        // SharedPreferences keys for persistent storage
        private const val KEY_BATTERY_HISTORY = "health_battery_history"
        private const val KEY_TEMPERATURE_HISTORY = "health_temperature_history"
        private const val KEY_LAST_BATTERY_ENTRY_TIME = "health_last_battery_entry_time"
        private const val KEY_LAST_TEMPERATURE_ENTRY_TIME = "health_last_temperature_entry_time"
    }
    
    // Coroutine scope for background data collection
    private val repositoryScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    // Thread-safe collections for storing history data
    private val batteryHistory = ConcurrentLinkedQueue<HistoryEntry<Int>>()
    private val temperatureHistory = ConcurrentLinkedQueue<HistoryEntry<Double>>()
    
    // Track last entry timestamps to avoid duplicate entries
    private var lastBatteryEntryTime = 0L
    private var lastTemperatureEntryTime = 0L
    
    init {
        loadPersistedData()
        startBatteryDataCollection()
    }

    /**
     * Loads persisted data from SharedPreferences on app startup.
     * This ensures data survives app restarts and provides historical context.
     */
    private fun loadPersistedData() {
        try {
            Log.d(TAG, "PERSISTENCE: === LOADING PERSISTED DATA ===")

            // Load battery history
            val batteryHistoryString = preferencesHelper.getString(KEY_BATTERY_HISTORY) ?: ""
            if (batteryHistoryString.isNotEmpty()) {
                val batteryEntries = parseBatteryHistoryString(batteryHistoryString)
                batteryHistory.addAll(batteryEntries)
                Log.d(TAG, "PERSISTENCE: Loaded ${batteryEntries.size} battery history entries")
            } else {
                Log.d(TAG, "PERSISTENCE: No persisted battery history found")
            }

            // Load temperature history
            val temperatureHistoryString = preferencesHelper.getString(KEY_TEMPERATURE_HISTORY) ?: ""
            if (temperatureHistoryString.isNotEmpty()) {
                val temperatureEntries = parseTemperatureHistoryString(temperatureHistoryString)
                temperatureHistory.addAll(temperatureEntries)
                Log.d(TAG, "PERSISTENCE: Loaded ${temperatureEntries.size} temperature history entries")
            } else {
                Log.d(TAG, "PERSISTENCE: No persisted temperature history found")
            }

            // Load last entry timestamps
            lastBatteryEntryTime = preferencesHelper.getLong(KEY_LAST_BATTERY_ENTRY_TIME, 0L)
            lastTemperatureEntryTime = preferencesHelper.getLong(KEY_LAST_TEMPERATURE_ENTRY_TIME, 0L)

            Log.d(TAG, "PERSISTENCE: Last battery entry time: $lastBatteryEntryTime")
            Log.d(TAG, "PERSISTENCE: Last temperature entry time: $lastTemperatureEntryTime")
            Log.d(TAG, "PERSISTENCE: Total loaded - Battery: ${batteryHistory.size}, Temperature: ${temperatureHistory.size}")

        } catch (e: Exception) {
            Log.e(TAG, "PERSISTENCE: Failed to load persisted data", e)
            // Clear corrupted data
            batteryHistory.clear()
            temperatureHistory.clear()
            lastBatteryEntryTime = 0L
            lastTemperatureEntryTime = 0L
        }
    }

    /**
     * Starts collecting battery data from CoreBatteryStatsService.
     * Processes CoreBatteryStatus updates and stores historical data.
     */
    private fun startBatteryDataCollection() {
        repositoryScope.launch {
            Log.d(TAG, "PRODUCTION_TEST: Starting battery data collection for health fragment")
            
            coreBatteryStatsProvider.coreBatteryStatusFlow
                .filterNotNull()
                .collect { coreStatus ->
                    Log.d(TAG, "CHART_DATA: === RECEIVED CORE BATTERY STATUS ===")
                    Log.d(TAG, "CHART_DATA: Status details:")
                    Log.d(TAG, "CHART_DATA:   Percentage: ${coreStatus.percentage}%")
                    Log.d(TAG, "CHART_DATA:   Temperature: ${coreStatus.temperatureCelsius}°C")
                    Log.d(TAG, "CHART_DATA:   Charging: ${coreStatus.isCharging}")
                    Log.d(TAG, "CHART_DATA:   Timestamp: ${java.text.SimpleDateFormat("HH:mm:ss", java.util.Locale.getDefault()).format(java.util.Date(coreStatus.timestampEpochMillis))}")

                    val currentTime = coreStatus.timestampEpochMillis
                    val timeSinceLastBattery = currentTime - lastBatteryEntryTime
                    val timeSinceLastTemp = currentTime - lastTemperatureEntryTime

                    Log.d(TAG, "CHART_DATA: Time since last entries - Battery: ${timeSinceLastBattery}ms, Temp: ${timeSinceLastTemp}ms")
                    Log.d(TAG, "CHART_DATA: Interval threshold: ${HISTORY_ENTRY_INTERVAL_MS}ms")

                    // Add battery percentage entry if enough time has passed
                    if (timeSinceLastBattery >= HISTORY_ENTRY_INTERVAL_MS) {
                        Log.d(TAG, "CHART_DATA: ✅ Adding battery history entry (interval met)")
                        addBatteryHistoryEntry(currentTime, coreStatus.percentage)
                        lastBatteryEntryTime = currentTime
                    } else {
                        Log.d(TAG, "CHART_DATA: ⏳ Skipping battery entry (interval not met: ${timeSinceLastBattery}ms < ${HISTORY_ENTRY_INTERVAL_MS}ms)")
                    }

                    // Add temperature entry if enough time has passed
                    if (timeSinceLastTemp >= HISTORY_ENTRY_INTERVAL_MS) {
                        Log.d(TAG, "CHART_DATA: ✅ Adding temperature history entry (interval met)")
                        addTemperatureHistoryEntry(currentTime, coreStatus.temperatureCelsius.toDouble())
                        lastTemperatureEntryTime = currentTime
                    } else {
                        Log.d(TAG, "CHART_DATA: ⏳ Skipping temperature entry (interval not met: ${timeSinceLastTemp}ms < ${HISTORY_ENTRY_INTERVAL_MS}ms)")
                    }

                    Log.d(TAG, "CHART_DATA: Current collection sizes - Battery: ${batteryHistory.size}, Temperature: ${temperatureHistory.size}")
                }
        }
    }
    
    /**
     * Adds a battery percentage history entry.
     */
    private fun addBatteryHistoryEntry(timestamp: Long, percentage: Int) {
        val entry = HistoryEntry(timestamp, percentage)
        batteryHistory.add(entry)

        // Remove old entries to maintain size limit
        val removedEntries = mutableListOf<HistoryEntry<Int>>()
        while (batteryHistory.size > MAX_HISTORY_ENTRIES) {
            batteryHistory.poll()?.let { removedEntries.add(it) }
        }

        Log.d(TAG, "CHART_DATA: ➕ BATTERY ENTRY ADDED")
        Log.d(TAG, "CHART_DATA:   Percentage: $percentage%")
        Log.d(TAG, "CHART_DATA:   Timestamp: ${java.text.SimpleDateFormat("HH:mm:ss", java.util.Locale.getDefault()).format(java.util.Date(timestamp))}")
        Log.d(TAG, "CHART_DATA:   Total entries: ${batteryHistory.size}")
        if (removedEntries.isNotEmpty()) {
            Log.d(TAG, "CHART_DATA:   Removed ${removedEntries.size} old entries to maintain limit")
        }
        Log.d(TAG, "CHART_DATA: This should trigger HealthFragment chart updates")
    }
    
    /**
     * Adds a temperature history entry.
     */
    private fun addTemperatureHistoryEntry(timestamp: Long, temperature: Double) {
        val entry = HistoryEntry(timestamp, temperature)
        temperatureHistory.add(entry)

        // Remove old entries to maintain size limit
        val removedEntries = mutableListOf<HistoryEntry<Double>>()
        while (temperatureHistory.size > MAX_HISTORY_ENTRIES) {
            temperatureHistory.poll()?.let { removedEntries.add(it) }
        }

        Log.d(TAG, "CHART_DATA: ➕ TEMPERATURE ENTRY ADDED")
        Log.d(TAG, "CHART_DATA:   Temperature: $temperature°C")
        Log.d(TAG, "CHART_DATA:   Timestamp: ${java.text.SimpleDateFormat("HH:mm:ss", java.util.Locale.getDefault()).format(java.util.Date(timestamp))}")
        Log.d(TAG, "CHART_DATA:   Total entries: ${temperatureHistory.size}")
        if (removedEntries.isNotEmpty()) {
            Log.d(TAG, "CHART_DATA:   Removed ${removedEntries.size} old entries to maintain limit")
        }
        Log.d(TAG, "CHART_DATA: This should trigger HealthFragment chart updates")

        // Save data to persistence after adding new entry
        savePersistedData()
    }

    /**
     * Saves current data to SharedPreferences for persistence across app restarts.
     */
    private fun savePersistedData() {
        try {
            // Save battery history
            val batteryHistoryString = batteryHistory.joinToString("|") { "${it.timestamp},${it.value}" }
            preferencesHelper.saveString(KEY_BATTERY_HISTORY, batteryHistoryString)

            // Save temperature history
            val temperatureHistoryString = temperatureHistory.joinToString("|") { "${it.timestamp},${it.value}" }
            preferencesHelper.saveString(KEY_TEMPERATURE_HISTORY, temperatureHistoryString)

            // Save last entry timestamps
            preferencesHelper.saveLong(KEY_LAST_BATTERY_ENTRY_TIME, lastBatteryEntryTime)
            preferencesHelper.saveLong(KEY_LAST_TEMPERATURE_ENTRY_TIME, lastTemperatureEntryTime)

            Log.v(TAG, "PERSISTENCE: Data saved - Battery: ${batteryHistory.size}, Temperature: ${temperatureHistory.size}")

        } catch (e: Exception) {
            Log.e(TAG, "PERSISTENCE: Failed to save data", e)
        }
    }

    /**
     * Parses battery history string from SharedPreferences.
     */
    private fun parseBatteryHistoryString(historyString: String): List<HistoryEntry<Int>> {
        return try {
            historyString.split("|").mapNotNull { entry ->
                val parts = entry.split(",")
                if (parts.size == 2) {
                    val timestamp = parts[0].toLongOrNull()
                    val value = parts[1].toIntOrNull()
                    if (timestamp != null && value != null) {
                        HistoryEntry(timestamp, value)
                    } else null
                } else null
            }
        } catch (e: Exception) {
            Log.e(TAG, "PERSISTENCE: Failed to parse battery history string", e)
            emptyList()
        }
    }

    /**
     * Parses temperature history string from SharedPreferences.
     */
    private fun parseTemperatureHistoryString(historyString: String): List<HistoryEntry<Double>> {
        return try {
            historyString.split("|").mapNotNull { entry ->
                val parts = entry.split(",")
                if (parts.size == 2) {
                    val timestamp = parts[0].toLongOrNull()
                    val value = parts[1].toDoubleOrNull()
                    if (timestamp != null && value != null) {
                        HistoryEntry(timestamp, value)
                    } else null
                } else null
            }
        } catch (e: Exception) {
            Log.e(TAG, "PERSISTENCE: Failed to parse temperature history string", e)
            emptyList()
        }
    }

    /**
     * Gets battery percentage history for the specified number of hours.
     * 
     * @param hours Number of hours of history to retrieve
     * @return List of battery percentage history entries
     */
    fun getHistoryBatteryForHours(hours: Int): List<HistoryEntry<Int>> {
        val cutoffTime = System.currentTimeMillis() - (hours * 60 * 60 * 1000L)
        val filteredEntries = batteryHistory.filter { it.timestamp >= cutoffTime }
        
        Log.d(TAG, "PRODUCTION_TEST: Retrieved ${filteredEntries.size} battery entries for ${hours}h (cutoff: $cutoffTime)")
        
        return filteredEntries.toList()
    }
    
    /**
     * Gets temperature history for the specified number of hours.
     * 
     * @param hours Number of hours of history to retrieve
     * @return List of temperature history entries
     */
    fun getHistoryTemperatureForHours(hours: Int): List<HistoryEntry<Double>> {
        val cutoffTime = System.currentTimeMillis() - (hours * 60 * 60 * 1000L)
        val filteredEntries = temperatureHistory.filter { it.timestamp >= cutoffTime }
        
        Log.d(TAG, "PRODUCTION_TEST: Retrieved ${filteredEntries.size} temperature entries for ${hours}h (cutoff: $cutoffTime)")
        
        return filteredEntries.toList()
    }
    
    /**
     * Gets daily wear data (placeholder implementation for compatibility).
     * In a real implementation, this would calculate battery wear based on charge cycles.
     * 
     * @param days Number of days of wear data to retrieve
     * @return List of daily wear percentages
     */
    fun getDailyWearData(days: Int): List<Double> {
        // For now, return empty list since this is primarily used for sample data
        // Real implementation would calculate wear based on charging sessions
        Log.d(TAG, "PRODUCTION_TEST: getDailyWearData called for $days days - returning empty list")
        return emptyList()
    }
    
    /**
     * Gets the current number of battery history entries.
     * Useful for debugging and monitoring data collection.
     */
    fun getBatteryHistoryCount(): Int = batteryHistory.size

    /**
     * Gets the current number of temperature history entries.
     * Useful for debugging and monitoring data collection.
     */
    fun getTemperatureHistoryCount(): Int = temperatureHistory.size
    
    /**
     * Clears all history data including persisted data.
     * Useful for testing or resetting data collection.
     */
    fun clearHistory() {
        batteryHistory.clear()
        temperatureHistory.clear()
        lastBatteryEntryTime = 0L
        lastTemperatureEntryTime = 0L

        // Clear persisted data as well
        preferencesHelper.saveString(KEY_BATTERY_HISTORY, "")
        preferencesHelper.saveString(KEY_TEMPERATURE_HISTORY, "")
        preferencesHelper.saveLong(KEY_LAST_BATTERY_ENTRY_TIME, 0L)
        preferencesHelper.saveLong(KEY_LAST_TEMPERATURE_ENTRY_TIME, 0L)

        Log.d(TAG, "PRODUCTION_TEST: History data cleared (including persisted data)")
    }

    /**
     * Generates sample data for testing time range buttons when insufficient real data exists.
     * This is only for debugging/testing purposes.
     */
    fun generateSampleDataForTesting(hours: Int) {
        Log.d(TAG, "SAMPLE_DATA: Generating sample data for ${hours}h testing")

        val currentTime = System.currentTimeMillis()
        val intervalMs = 60_000L // 1 minute intervals
        val totalEntries = hours * 60 // One entry per minute

        // Clear existing data first
        clearHistory()

        // Generate battery percentage data (declining from 100% to 20%)
        for (i in 0 until totalEntries) {
            val timestamp = currentTime - ((totalEntries - i - 1) * intervalMs)
            val percentage = 100 - ((i * 80) / totalEntries) // Decline from 100% to 20%
            val entry = HistoryEntry(timestamp, percentage)
            batteryHistory.add(entry)
        }

        // Generate temperature data (varying between 25°C and 40°C)
        for (i in 0 until totalEntries) {
            val timestamp = currentTime - ((totalEntries - i - 1) * intervalMs)
            val temperature = 25.0 + (15.0 * kotlin.math.sin(i * 0.1)) // Sine wave between 25-40°C
            val entry = HistoryEntry(timestamp, temperature)
            temperatureHistory.add(entry)
        }

        // Update timestamps
        lastBatteryEntryTime = currentTime
        lastTemperatureEntryTime = currentTime

        // Save the sample data
        savePersistedData()

        Log.d(TAG, "SAMPLE_DATA: Generated ${batteryHistory.size} battery entries and ${temperatureHistory.size} temperature entries for ${hours}h")
    }
}
