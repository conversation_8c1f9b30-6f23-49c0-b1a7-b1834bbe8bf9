package com.tqhit.battery.one.features.emoji.presentation.customize

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfig
import com.tqhit.battery.one.features.emoji.domain.model.OverlayPosition
import com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase
import com.tqhit.battery.one.features.emoji.domain.use_case.SaveCustomizationUseCase
import com.tqhit.battery.one.features.emoji.domain.use_case.ResetCustomizationUseCase
import com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for the customization screen.
 * 
 * Manages the complete state and business logic for the emoji battery customization
 * interface. Integrates with use cases for data operations and CoreBatteryStatsProvider
 * for real-time battery monitoring.
 * 
 * Key responsibilities:
 * - Manage UI state and user interactions
 * - Coordinate between multiple use cases
 * - Provide real-time preview with battery data
 * - Handle configuration validation and saving
 * - Manage permission states and feature enablement
 * - Provide reactive data streams for UI updates
 */
@HiltViewModel
class CustomizeViewModel @Inject constructor(
    private val loadCustomizationUseCase: LoadCustomizationUseCase,
    private val saveCustomizationUseCase: SaveCustomizationUseCase,
    private val resetCustomizationUseCase: ResetCustomizationUseCase,
    private val coreBatteryStatsProvider: CoreBatteryStatsProvider
) : ViewModel() {
    
    companion object {
        private const val TAG = "CustomizeViewModel"
        private const val PREVIEW_UPDATE_DELAY_MS = 300L
        private const val AUTO_SAVE_DELAY_MS = 2000L
    }
    
    // Internal mutable state
    private val _uiState = MutableStateFlow(CustomizeState.createInitial())
    
    // Public state exposure
    val uiState: StateFlow<CustomizeState> = _uiState.asStateFlow()
    
    // Jobs for managing coroutines
    private var previewUpdateJob: Job? = null
    private var autoSaveJob: Job? = null
    private var dataLoadingJob: Job? = null
    
    init {
        Log.d(TAG, "INIT: CustomizeViewModel initialized")
        startDataObservation()
        loadInitialData()
    }
    
    /**
     * Handles user events from the UI.
     */
    fun handleEvent(event: CustomizeEvent) {
        Log.d(TAG, "EVENT: Handling event: ${event::class.simpleName}")
        
        when (event) {
            // Lifecycle Events
            is CustomizeEvent.OnScreenEnter -> handleScreenEnter()
            is CustomizeEvent.OnScreenExit -> handleScreenExit()
            is CustomizeEvent.OnResume -> handleResume()
            is CustomizeEvent.OnPause -> handlePause()
            
            // Style Selection Events
            is CustomizeEvent.SelectBatteryStyle -> handleSelectBatteryStyle(event.style)
            is CustomizeEvent.SelectBatteryContainer -> handleSelectBatteryContainer(event.style)
            is CustomizeEvent.SelectEmojiCharacter -> handleSelectEmojiCharacter(event.style)
            is CustomizeEvent.BrowseMoreStyles -> handleBrowseMoreStyles()
            
            // Configuration Editing Events
            is CustomizeEvent.ToggleEmojiVisibility -> handleToggleEmojiVisibility(event.showEmoji)
            is CustomizeEvent.TogglePercentageVisibility -> handleTogglePercentageVisibility(event.showPercentage)
            is CustomizeEvent.ChangePercentageFontSize -> handleChangePercentageFontSize(event.sizeDp)
            is CustomizeEvent.ChangeEmojiSize -> handleChangeEmojiSize(event.scale)
            is CustomizeEvent.ChangePercentageColor -> handleChangePercentageColor(event.color)
            is CustomizeEvent.ChangeOverlayPosition -> handleChangeOverlayPosition(event.position)
            
            // Save and Apply Events
            is CustomizeEvent.SaveConfiguration -> handleSaveConfiguration()
            is CustomizeEvent.ApplyAndEnable -> handleApplyAndEnable()
            is CustomizeEvent.ResetToDefaults -> handleResetToDefaults()
            is CustomizeEvent.ConfirmReset -> handleConfirmReset()
            is CustomizeEvent.DiscardChanges -> handleDiscardChanges()
            
            // UI Interaction Events
            is CustomizeEvent.OpenColorPicker -> handleOpenColorPicker()
            is CustomizeEvent.CloseColorPicker -> handleCloseColorPicker()
            is CustomizeEvent.OpenPositionSelector -> handleOpenPositionSelector()
            is CustomizeEvent.ClosePositionSelector -> handleClosePositionSelector()
            is CustomizeEvent.ToggleLivePreview -> handleToggleLivePreview(event.showPreview)
            is CustomizeEvent.ChangePreviewBatteryLevel -> handleChangePreviewBatteryLevel(event.level)
            is CustomizeEvent.RefreshPreview -> handleRefreshPreview()
            
            // Permission and Feature Events
            is CustomizeEvent.RequestPermissions -> handleRequestPermissions()
            is CustomizeEvent.EnableAccessibilityService -> handleEnableAccessibilityService()
            is CustomizeEvent.ToggleFeatureEnabled -> handleToggleFeatureEnabled(event.enabled)
            
            // Error Handling Events
            is CustomizeEvent.RetryOperation -> handleRetryOperation()
            is CustomizeEvent.DismissError -> handleDismissError()
            is CustomizeEvent.ValidateConfiguration -> handleValidateConfiguration()
            
            // Navigation Events
            is CustomizeEvent.NavigateBack -> handleNavigateBack()
            is CustomizeEvent.NavigateToSettings -> handleNavigateToSettings()
            is CustomizeEvent.NavigateToHelp -> handleNavigateToHelp()
            
            // Data Events
            is CustomizeEvent.CustomizationDataLoaded -> handleCustomizationDataLoaded(event.success)
            is CustomizeEvent.StylesDataLoaded -> handleStylesDataLoaded(event.success)
            is CustomizeEvent.ConfigurationSaved -> handleConfigurationSaved()
            is CustomizeEvent.ConfigurationSaveFailed -> handleConfigurationSaveFailed(event.error)
            is CustomizeEvent.BatteryLevelChanged -> handleBatteryLevelChanged(event.level, event.isCharging)
            
            // Premium Events (Phase 5)
            is CustomizeEvent.UnlockPremiumStyle -> handleUnlockPremiumStyle(event.style)
            is CustomizeEvent.WatchAdToUnlock -> handleWatchAdToUnlock(event.style)
            is CustomizeEvent.PurchasePremiumAccess -> handlePurchasePremiumAccess()
            
            // Backup Events
            is CustomizeEvent.ExportConfiguration -> handleExportConfiguration()
            is CustomizeEvent.ImportConfiguration -> handleImportConfiguration(event.data)
            is CustomizeEvent.ShareConfiguration -> handleShareConfiguration()
        }
    }
    
    /**
     * Starts observing data changes from use cases and battery provider.
     */
    private fun startDataObservation() {
        Log.d(TAG, "DATA_OBSERVATION: Starting data observation")
        
        // Observe enriched customization data
        viewModelScope.launch {
            loadCustomizationUseCase.userCustomizationFlow
                .catch { exception ->
                    Log.e(TAG, "DATA_OBSERVATION: Error in customization flow", exception)
                    _uiState.value = _uiState.value.withError("Failed to load customization data")
                }
                .collect { enrichedCustomization ->
                    Log.d(TAG, "DATA_OBSERVATION: Received enriched customization update")
                    _uiState.value = _uiState.value.withEnrichedCustomization(enrichedCustomization)
                }
        }
        
        // Observe battery status for live preview
        viewModelScope.launch {
            coreBatteryStatsProvider.coreBatteryStatusFlow
                .catch { exception ->
                    Log.e(TAG, "DATA_OBSERVATION: Error in battery status flow", exception)
                }
                .collect { batteryStatus ->
                    val currentState = _uiState.value
                    if (currentState.showLivePreview) {
                        handleEvent(CustomizeEvent.BatteryLevelChanged(
                            batteryStatus.batteryLevel,
                            batteryStatus.isCharging
                        ))
                    }
                }
        }
    }
    
    /**
     * Loads initial data for the screen.
     */
    private fun loadInitialData() {
        Log.d(TAG, "LOAD_INITIAL: Loading initial data")
        
        dataLoadingJob?.cancel()
        dataLoadingJob = viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.withLoadingState(isLoading = true, isLoadingStyles = true)
                
                // Load current customization and available styles
                val enrichedCustomization = loadCustomizationUseCase.getCurrentEnrichedUserCustomization()
                
                _uiState.value = _uiState.value
                    .withEnrichedCustomization(enrichedCustomization)
                    .withLoadingState(isLoading = false, isLoadingStyles = false)
                
                Log.d(TAG, "LOAD_INITIAL: Initial data loaded successfully")
                handleEvent(CustomizeEvent.CustomizationDataLoaded(true))
                handleEvent(CustomizeEvent.StylesDataLoaded(true))
            } catch (e: Exception) {
                Log.e(TAG, "LOAD_INITIAL: Failed to load initial data", e)
                _uiState.value = _uiState.value.withError("Failed to load customization data: ${e.message}")
                handleEvent(CustomizeEvent.CustomizationDataLoaded(false))
                handleEvent(CustomizeEvent.StylesDataLoaded(false))
            }
        }
    }
    
    // Lifecycle Event Handlers
    private fun handleScreenEnter() {
        Log.d(TAG, "LIFECYCLE: Screen enter")
        loadInitialData()
    }
    
    private fun handleScreenExit() {
        Log.d(TAG, "LIFECYCLE: Screen exit")
        // Save any pending changes
        val currentState = _uiState.value
        if (currentState.hasUnsavedChanges && currentState.canSave()) {
            viewModelScope.launch {
                saveCurrentConfiguration()
            }
        }
    }
    
    private fun handleResume() {
        Log.d(TAG, "LIFECYCLE: Resume")
        // Refresh data and check permissions
        loadInitialData()
    }
    
    private fun handlePause() {
        Log.d(TAG, "LIFECYCLE: Pause")
        // Cancel any pending operations
        previewUpdateJob?.cancel()
        autoSaveJob?.cancel()
    }
    
    // Style Selection Event Handlers
    private fun handleSelectBatteryStyle(style: BatteryStyle) {
        Log.d(TAG, "STYLE_SELECTION: Selected battery style: ${style.name}")
        
        viewModelScope.launch {
            try {
                val result = saveCustomizationUseCase.saveCustomizationFromStyle(style, enableFeature = false)
                if (result.isSuccess) {
                    Log.d(TAG, "STYLE_SELECTION: Successfully saved style selection")
                    schedulePreviewUpdate()
                } else {
                    Log.e(TAG, "STYLE_SELECTION: Failed to save style selection", result.exceptionOrNull())
                    _uiState.value = _uiState.value.withError("Failed to select style: ${result.exceptionOrNull()?.message}")
                }
            } catch (e: Exception) {
                Log.e(TAG, "STYLE_SELECTION: Exception selecting style", e)
                _uiState.value = _uiState.value.withError("Error selecting style: ${e.message}")
            }
        }
    }
    
    private fun handleSelectBatteryContainer(style: BatteryStyle) {
        Log.d(TAG, "STYLE_SELECTION: Selected battery container from style: ${style.name}")
        
        val currentState = _uiState.value
        val updatedState = currentState.withStyleSelection(batteryStyleId = style.id)
        _uiState.value = updatedState
        
        scheduleAutoSave()
        schedulePreviewUpdate()
    }
    
    private fun handleSelectEmojiCharacter(style: BatteryStyle) {
        Log.d(TAG, "STYLE_SELECTION: Selected emoji character from style: ${style.name}")
        
        val currentState = _uiState.value
        val updatedState = currentState.withStyleSelection(emojiStyleId = style.id)
        _uiState.value = updatedState
        
        scheduleAutoSave()
        schedulePreviewUpdate()
    }
    
    private fun handleBrowseMoreStyles() {
        Log.d(TAG, "NAVIGATION: Browse more styles requested")
        // This will be handled by the fragment for navigation
    }

    // Configuration Editing Event Handlers
    private fun handleToggleEmojiVisibility(showEmoji: Boolean) {
        Log.d(TAG, "CONFIG_EDIT: Toggle emoji visibility: $showEmoji")
        updateStyleConfig { it.copy(showEmoji = showEmoji) }
    }

    private fun handleTogglePercentageVisibility(showPercentage: Boolean) {
        Log.d(TAG, "CONFIG_EDIT: Toggle percentage visibility: $showPercentage")
        updateStyleConfig { it.copy(showPercentage = showPercentage) }
    }

    private fun handleChangePercentageFontSize(sizeDp: Int) {
        Log.d(TAG, "CONFIG_EDIT: Change percentage font size: ${sizeDp}dp")
        val clampedSize = sizeDp.coerceIn(5, 40)
        updateStyleConfig { it.copy(percentageFontSizeDp = clampedSize) }
    }

    private fun handleChangeEmojiSize(scale: Float) {
        Log.d(TAG, "CONFIG_EDIT: Change emoji size: ${scale}x")
        val clampedScale = scale.coerceIn(0.5f, 2.0f)
        updateStyleConfig { it.copy(emojiSizeScale = clampedScale) }
    }

    private fun handleChangePercentageColor(color: Int) {
        Log.d(TAG, "CONFIG_EDIT: Change percentage color: $color")
        updateStyleConfig { it.copy(percentageColor = color) }
    }

    private fun handleChangeOverlayPosition(position: OverlayPosition) {
        Log.d(TAG, "CONFIG_EDIT: Change overlay position: ${position.displayName}")
        // This updates the main configuration, not just style config
        viewModelScope.launch {
            try {
                val currentConfig = loadCustomizationUseCase.getCurrentCustomizationConfig()
                val updatedConfig = currentConfig.copy(overlayPosition = position)
                saveCustomizationUseCase.saveCustomizationConfig(updatedConfig)
            } catch (e: Exception) {
                Log.e(TAG, "CONFIG_EDIT: Failed to update overlay position", e)
                _uiState.value = _uiState.value.withError("Failed to update position: ${e.message}")
            }
        }
    }

    // Save and Apply Event Handlers
    private fun handleSaveConfiguration() {
        Log.d(TAG, "SAVE: Save configuration requested")

        viewModelScope.launch {
            saveCurrentConfiguration()
        }
    }

    private fun handleApplyAndEnable() {
        Log.d(TAG, "APPLY: Apply and enable requested")

        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.withLoadingState(isSaving = true)

                // Save current configuration
                val saveResult = saveCurrentConfiguration()
                if (saveResult.isFailure) {
                    return@launch
                }

                // Enable the feature
                val enableResult = saveCustomizationUseCase.setFeatureEnabled(true)
                if (enableResult.isSuccess) {
                    Log.d(TAG, "APPLY: Successfully applied and enabled feature")
                    _uiState.value = _uiState.value.withLoadingState(isSaving = false)
                    handleEvent(CustomizeEvent.ConfigurationSaved)
                } else {
                    Log.e(TAG, "APPLY: Failed to enable feature", enableResult.exceptionOrNull())
                    _uiState.value = _uiState.value.withError("Failed to enable feature: ${enableResult.exceptionOrNull()?.message}")
                }
            } catch (e: Exception) {
                Log.e(TAG, "APPLY: Exception applying and enabling", e)
                _uiState.value = _uiState.value.withError("Error applying changes: ${e.message}")
            }
        }
    }

    private fun handleResetToDefaults() {
        Log.d(TAG, "RESET: Reset to defaults requested")
        // This should show confirmation dialog in UI
    }

    private fun handleConfirmReset() {
        Log.d(TAG, "RESET: Reset confirmed")

        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.withLoadingState(isLoading = true)

                val result = resetCustomizationUseCase.resetCustomizationConfig()
                if (result.isSuccess) {
                    Log.d(TAG, "RESET: Successfully reset configuration")
                    _uiState.value = _uiState.value.withLoadingState(isLoading = false)
                    loadInitialData() // Reload to reflect changes
                } else {
                    Log.e(TAG, "RESET: Failed to reset configuration", result.exceptionOrNull())
                    _uiState.value = _uiState.value.withError("Failed to reset: ${result.exceptionOrNull()?.message}")
                }
            } catch (e: Exception) {
                Log.e(TAG, "RESET: Exception resetting configuration", e)
                _uiState.value = _uiState.value.withError("Error resetting: ${e.message}")
            }
        }
    }

    private fun handleDiscardChanges() {
        Log.d(TAG, "DISCARD: Discard changes requested")
        loadInitialData() // Reload original data
    }

    // UI Interaction Event Handlers
    private fun handleOpenColorPicker() {
        Log.d(TAG, "UI: Open color picker")
        _uiState.value = _uiState.value.withUIState(showColorPicker = true)
    }

    private fun handleCloseColorPicker() {
        Log.d(TAG, "UI: Close color picker")
        _uiState.value = _uiState.value.withUIState(showColorPicker = false)
    }

    private fun handleOpenPositionSelector() {
        Log.d(TAG, "UI: Open position selector")
        _uiState.value = _uiState.value.withUIState(showPositionSelector = true)
    }

    private fun handleClosePositionSelector() {
        Log.d(TAG, "UI: Close position selector")
        _uiState.value = _uiState.value.withUIState(showPositionSelector = false)
    }

    private fun handleToggleLivePreview(showPreview: Boolean) {
        Log.d(TAG, "UI: Toggle live preview: $showPreview")
        _uiState.value = _uiState.value.withUIState(showLivePreview = showPreview)
    }

    private fun handleChangePreviewBatteryLevel(level: Int) {
        Log.d(TAG, "UI: Change preview battery level: $level%")
        val clampedLevel = level.coerceIn(0, 100)
        _uiState.value = _uiState.value.withUIState(previewBatteryLevel = clampedLevel)
    }

    private fun handleRefreshPreview() {
        Log.d(TAG, "UI: Refresh preview")
        schedulePreviewUpdate()
    }

    // Permission and Feature Event Handlers
    private fun handleRequestPermissions() {
        Log.d(TAG, "PERMISSIONS: Request permissions")
        // This will be handled by the fragment
    }

    private fun handleEnableAccessibilityService() {
        Log.d(TAG, "PERMISSIONS: Enable accessibility service")
        // This will be handled by the fragment
    }

    private fun handleToggleFeatureEnabled(enabled: Boolean) {
        Log.d(TAG, "FEATURE: Toggle feature enabled: $enabled")

        viewModelScope.launch {
            try {
                val result = saveCustomizationUseCase.setFeatureEnabled(enabled)
                if (result.isFailure) {
                    Log.e(TAG, "FEATURE: Failed to toggle feature", result.exceptionOrNull())
                    _uiState.value = _uiState.value.withError("Failed to toggle feature: ${result.exceptionOrNull()?.message}")
                }
            } catch (e: Exception) {
                Log.e(TAG, "FEATURE: Exception toggling feature", e)
                _uiState.value = _uiState.value.withError("Error toggling feature: ${e.message}")
            }
        }
    }

    // Error Handling Event Handlers
    private fun handleRetryOperation() {
        Log.d(TAG, "ERROR: Retry operation")
        _uiState.value = _uiState.value.withClearedErrors()
        loadInitialData()
    }

    private fun handleDismissError() {
        Log.d(TAG, "ERROR: Dismiss error")
        _uiState.value = _uiState.value.withClearedErrors()
    }

    private fun handleValidateConfiguration() {
        Log.d(TAG, "VALIDATE: Validate configuration")

        viewModelScope.launch {
            try {
                val issues = loadCustomizationUseCase.validateCurrentConfiguration()
                if (issues.isNotEmpty()) {
                    _uiState.value = _uiState.value.withError(validationErrors = issues)
                } else {
                    Log.d(TAG, "VALIDATE: Configuration is valid")
                }
            } catch (e: Exception) {
                Log.e(TAG, "VALIDATE: Exception validating configuration", e)
                _uiState.value = _uiState.value.withError("Validation error: ${e.message}")
            }
        }
    }

    // Navigation Event Handlers (handled by fragment)
    private fun handleNavigateBack() {
        Log.d(TAG, "NAVIGATION: Navigate back")
    }

    private fun handleNavigateToSettings() {
        Log.d(TAG, "NAVIGATION: Navigate to settings")
    }

    private fun handleNavigateToHelp() {
        Log.d(TAG, "NAVIGATION: Navigate to help")
    }

    // Data Event Handlers
    private fun handleCustomizationDataLoaded(success: Boolean) {
        Log.d(TAG, "DATA: Customization data loaded: $success")
        if (!success) {
            _uiState.value = _uiState.value.withError("Failed to load customization data")
        }
    }

    private fun handleStylesDataLoaded(success: Boolean) {
        Log.d(TAG, "DATA: Styles data loaded: $success")
        if (!success) {
            _uiState.value = _uiState.value.withError("Failed to load styles data")
        }
    }

    private fun handleConfigurationSaved() {
        Log.d(TAG, "DATA: Configuration saved successfully")
        _uiState.value = _uiState.value.copy(hasUnsavedChanges = false, isConfigModified = false)
    }

    private fun handleConfigurationSaveFailed(error: String) {
        Log.e(TAG, "DATA: Configuration save failed: $error")
        _uiState.value = _uiState.value.withError("Save failed: $error")
    }

    private fun handleBatteryLevelChanged(level: Int, isCharging: Boolean) {
        val currentState = _uiState.value
        if (currentState.showLivePreview && currentState.previewBatteryLevel != level) {
            _uiState.value = currentState.withUIState(previewBatteryLevel = level)
        }
    }

    // Premium Event Handlers (Phase 5 placeholders)
    private fun handleUnlockPremiumStyle(style: BatteryStyle) {
        Log.d(TAG, "PREMIUM: Unlock premium style: ${style.name}")
        // TODO: Implement in Phase 5
    }

    private fun handleWatchAdToUnlock(style: BatteryStyle) {
        Log.d(TAG, "PREMIUM: Watch ad to unlock: ${style.name}")
        // TODO: Implement in Phase 5
    }

    private fun handlePurchasePremiumAccess() {
        Log.d(TAG, "PREMIUM: Purchase premium access")
        // TODO: Implement in Phase 5
    }

    // Backup Event Handlers
    private fun handleExportConfiguration() {
        Log.d(TAG, "BACKUP: Export configuration")
        // TODO: Implement export functionality
    }

    private fun handleImportConfiguration(data: String) {
        Log.d(TAG, "BACKUP: Import configuration")
        // TODO: Implement import functionality
    }

    private fun handleShareConfiguration() {
        Log.d(TAG, "BACKUP: Share configuration")
        // TODO: Implement share functionality
    }

    // Utility Methods

    /**
     * Updates the current style configuration and triggers preview update.
     */
    private fun updateStyleConfig(update: (BatteryStyleConfig) -> BatteryStyleConfig) {
        val currentState = _uiState.value
        val updatedConfig = update(currentState.editingConfig)
        val validatedConfig = updatedConfig.validated()

        _uiState.value = currentState.withEditingConfig(validatedConfig)

        scheduleAutoSave()
        schedulePreviewUpdate()
    }

    /**
     * Saves the current configuration.
     */
    private suspend fun saveCurrentConfiguration(): Result<Unit> {
        return try {
            _uiState.value = _uiState.value.withLoadingState(isSaving = true)

            val currentState = _uiState.value
            val currentConfig = currentState.currentCustomization.customizationConfig
            val updatedConfig = currentConfig.withStyleConfig(currentState.editingConfig)

            val result = saveCustomizationUseCase.saveCustomizationConfig(updatedConfig)

            if (result.isSuccess) {
                Log.d(TAG, "SAVE: Configuration saved successfully")
                _uiState.value = _uiState.value.withLoadingState(isSaving = false)
                handleEvent(CustomizeEvent.ConfigurationSaved)
            } else {
                Log.e(TAG, "SAVE: Failed to save configuration", result.exceptionOrNull())
                _uiState.value = _uiState.value.withError("Save failed: ${result.exceptionOrNull()?.message}")
            }

            result
        } catch (e: Exception) {
            Log.e(TAG, "SAVE: Exception saving configuration", e)
            _uiState.value = _uiState.value.withError("Save error: ${e.message}")
            Result.failure(e)
        }
    }

    /**
     * Schedules a preview update with debouncing.
     */
    private fun schedulePreviewUpdate() {
        previewUpdateJob?.cancel()
        previewUpdateJob = viewModelScope.launch {
            delay(PREVIEW_UPDATE_DELAY_MS)
            _uiState.value = _uiState.value.copy(previewUpdateTimestamp = System.currentTimeMillis())
            Log.d(TAG, "PREVIEW: Preview updated")
        }
    }

    /**
     * Schedules auto-save with debouncing.
     */
    private fun scheduleAutoSave() {
        autoSaveJob?.cancel()
        autoSaveJob = viewModelScope.launch {
            delay(AUTO_SAVE_DELAY_MS)
            val currentState = _uiState.value
            if (currentState.canSave()) {
                Log.d(TAG, "AUTO_SAVE: Performing auto-save")
                saveCurrentConfiguration()
            }
        }
    }

    override fun onCleared() {
        super.onCleared()
        Log.d(TAG, "LIFECYCLE: ViewModel cleared")

        // Cancel all jobs
        previewUpdateJob?.cancel()
        autoSaveJob?.cancel()
        dataLoadingJob?.cancel()
    }
}
