package com.tqhit.battery.one.features.stats.discharge.domain

import android.util.Log
import com.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus
import com.tqhit.battery.one.features.stats.discharge.data.DischargeSessionData
import java.util.Date
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Calculator for session metrics
 */
@Singleton
class SessionMetricsCalculator @Inject constructor(
    private val timeConverter: TimeConverter,
    private val dischargeRateCalculator: DischargeRateCalculator
) {
    private val TAG = "SessionMetricsCalc"

    /**
     * Data class to hold session calculation metrics
     */
    data class SessionMetrics(
        val startTimeFormatted: String,
        val totalSessionDurationMillis: Long,
        val totalSessionDurationHours: Double,
        val startPercentage: Int,
        val currentPercentage: Int,
        val totalPercentageDropped: Int,
        val totalMahConsumed: Double
    )

    /**
     * Calculates session metrics from cached session and live status
     */
    fun calculateSessionMetrics(
        cachedSession: DischargeSessionData,
        liveStatus: CoreBatteryStatus,
        effectiveCapacityMah: Double
    ): SessionMetrics {
        val totalSessionDurationMillis = liveStatus.timestampEpochMillis - cachedSession.startTimeEpochMillis
        val totalSessionDurationHours = timeConverter.millisToHours(totalSessionDurationMillis)
        val totalPercentageDropped = cachedSession.startPercentage - liveStatus.percentage
        val totalMahConsumed = dischargeRateCalculator.calculateMahConsumed(
            totalPercentageDropped,
            effectiveCapacityMah
        )
        
        val startTimeFormatted = timeConverter.formatDateTime(cachedSession.startTimeEpochMillis)
        
        return SessionMetrics(
            startTimeFormatted = startTimeFormatted,
            totalSessionDurationMillis = totalSessionDurationMillis,
            totalSessionDurationHours = totalSessionDurationHours,
            startPercentage = cachedSession.startPercentage,
            currentPercentage = liveStatus.percentage,
            totalPercentageDropped = totalPercentageDropped,
            totalMahConsumed = totalMahConsumed
        )
    }
    
    /**
     * Logs session metrics in a readable format
     */
    fun logSessionMetrics(metrics: SessionMetrics) {
        Log.d(TAG, "Session: started ${metrics.startTimeFormatted} (${metrics.totalSessionDurationMillis/60000}m ago), " +
              "battery: ${metrics.startPercentage}% → ${metrics.currentPercentage}% (${metrics.totalPercentageDropped}% drop)")
    }
    
    /**
     * Determines if calculation is needed based on session metrics
     */
    fun isCalculationNeeded(metrics: SessionMetrics): Boolean {
        val isNeeded = metrics.totalSessionDurationMillis >= 10000 && metrics.totalPercentageDropped > 0
        if (!isNeeded) {
            Log.d(TAG, "Skipping calculation - session too short or no percentage drop")
        }
        return isNeeded
    }

    /**
     * Calculates mAh consumed since last update based on percentage drop
     */
    fun calculateMahConsumed(
        currentStatus: CoreBatteryStatus,
        lastStatus: CoreBatteryStatus,
        effectiveCapacityMah: Double
    ): Double {
        val currentCapacityMah = (currentStatus.percentage / 100.0) * effectiveCapacityMah
        val previousCapacityMah = (lastStatus.percentage / 100.0) * effectiveCapacityMah
        return kotlin.math.max(0.0, previousCapacityMah - currentCapacityMah)
    }

    /**
     * Logs session milestones
     */
    fun logSessionMilestones(
        updatedSession: DischargeSessionData,
        previousSession: DischargeSessionData
    ) {
        val percentDropped = updatedSession.startPercentage - updatedSession.currentPercentage
        if (percentDropped > 0 && (percentDropped % 5 == 0 || 
                updatedSession.durationMillis - previousSession.durationMillis > 10 * 60 * 1000)) {
            Log.d(TAG, "TC2.2: Session milestone - Duration: ${updatedSession.durationMillis/60000}m, " +
                   "Battery: ${updatedSession.startPercentage}% → ${updatedSession.currentPercentage}% (${percentDropped}% drop), " +
                   "Total consumed: ${String.format("%.1f", updatedSession.totalMahConsumed)} mAh")
        }
    }
    
    /**
     * Logs detailed information about a session
     */
    fun logDetailedSessionInfo(session: DischargeSessionData) {
        // Format timestamps for better readability
        val startTimeFormatted = timeConverter.formatDateTime(session.startTimeEpochMillis)
        val lastUpdateFormatted = timeConverter.formatDateTime(session.lastUpdateTimeEpochMillis)
        
        // Calculate absolute session duration
        val totalSessionDurationMillis = System.currentTimeMillis() - session.startTimeEpochMillis
        
        Log.d(TAG, "Resetting discharge session - Detailed session information:")
        Log.d(TAG, "  - Session started: $startTimeFormatted (${totalSessionDurationMillis/60000} minutes ago)")
        Log.d(TAG, "  - Last update time: $lastUpdateFormatted")
        Log.d(TAG, "  - Total session duration: ${totalSessionDurationMillis/60000} minutes")
        Log.d(TAG, "  - Battery from start: ${session.startPercentage}% → ${session.currentPercentage}% (${session.percentageDropped}% drop)")
        Log.d(TAG, "  - Screen time: ON: ${session.screenOnTimeMillis/60000}m, OFF: ${session.screenOffTimeMillis/60000}m")
        Log.d(TAG, "  - Total consumed: ${String.format("%.1f", session.totalMahConsumed)} mAh")
        Log.d(TAG, "  - Rates - Screen ON: ${String.format("%.1f", session.avgScreenOnDischargeRateMahPerHour)} mAh/h, " +
                "Screen OFF: ${String.format("%.1f", session.avgScreenOffDischargeRateMahPerHour)} mAh/h")
    }
}
