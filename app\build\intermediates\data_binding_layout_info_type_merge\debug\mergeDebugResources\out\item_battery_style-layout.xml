<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_battery_style" modulePackage="com.tqhit.battery.one" filePath="app\src\main\res\layout\item_battery_style.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView" rootNodeViewId="@+id/styleCard"><Targets><Target id="@+id/styleCard" tag="layout/item_battery_style_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="212" endOffset="51"/></Target><Target id="@+id/previewContainer" view="FrameLayout"><Expressions/><location startLine="23" startOffset="8" endLine="165" endOffset="21"/></Target><Target id="@+id/batteryImageView" view="ImageView"><Expressions/><location startLine="32" startOffset="12" endLine="39" endOffset="62"/></Target><Target id="@+id/emojiImageView" view="ImageView"><Expressions/><location startLine="42" startOffset="12" endLine="51" endOffset="60"/></Target><Target id="@+id/percentagePreview" view="TextView"><Expressions/><location startLine="54" startOffset="12" endLine="63" endOffset="42"/></Target><Target id="@+id/premiumBadge" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="66" startOffset="12" endLine="103" endOffset="63"/></Target><Target id="@+id/popularBadge" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="106" startOffset="12" endLine="143" endOffset="63"/></Target><Target id="@+id/imageLoadingIndicator" view="com.google.android.material.progressindicator.CircularProgressIndicator"><Expressions/><location startLine="146" startOffset="12" endLine="152" endOffset="43"/></Target><Target id="@+id/imageErrorIndicator" view="ImageView"><Expressions/><location startLine="155" startOffset="12" endLine="163" endOffset="44"/></Target><Target id="@+id/styleNameText" view="TextView"><Expressions/><location startLine="174" startOffset="12" endLine="183" endOffset="44"/></Target><Target id="@+id/categoryText" view="TextView"><Expressions/><location startLine="186" startOffset="12" endLine="195" endOffset="43"/></Target><Target id="@+id/actionButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="200" startOffset="8" endLine="208" endOffset="40"/></Target></Targets></Layout>