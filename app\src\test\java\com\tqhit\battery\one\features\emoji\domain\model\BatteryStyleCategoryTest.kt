package com.tqhit.battery.one.features.emoji.domain.model

import org.junit.Assert.*
import org.junit.Test

/**
 * Unit tests for BatteryStyleCategory enum.
 * Tests category properties, utility methods, and string conversion.
 */
class BatteryStyleCategoryTest {
    
    @Test
    fun `test all categories have required properties`() {
        // When & Then
        BatteryStyleCategory.values().forEach { category ->
            assertNotNull("Display name should not be null", category.displayName)
            assertTrue("Display name should not be empty", category.displayName.isNotEmpty())
            assertNotNull("Emoji should not be null", category.emoji)
            assertTrue("Emoji should not be empty", category.emoji.isNotEmpty())
            assertTrue("Sort order should be non-negative", category.sortOrder >= 0)
        }
    }
    
    @Test
    fun `test HOT category properties`() {
        // Given
        val hotCategory = BatteryStyleCategory.HOT
        
        // Then
        assertEquals("HOT", hotCategory.displayName)
        assertEquals("🔥", hotCategory.emoji)
        assertEquals(0, hotCategory.sortOrder)
    }
    
    @Test
    fun `test CHARACTER category properties`() {
        // Given
        val characterCategory = BatteryStyleCategory.CHARACTER
        
        // Then
        assertEquals("Character", characterCategory.displayName)
        assertEquals("👾", characterCategory.emoji)
        assertEquals(1, characterCategory.sortOrder)
    }
    
    @Test
    fun `test HEART category properties`() {
        // Given
        val heartCategory = BatteryStyleCategory.HEART
        
        // Then
        assertEquals("Heart", heartCategory.displayName)
        assertEquals("❤️", heartCategory.emoji)
        assertEquals(2, heartCategory.sortOrder)
    }
    
    @Test
    fun `test getDisplayText returns formatted text`() {
        // Given
        val category = BatteryStyleCategory.CUTE
        
        // When
        val displayText = category.getDisplayText()
        
        // Then
        assertEquals("🥰 Cute", displayText)
        assertTrue(displayText.contains(category.emoji))
        assertTrue(displayText.contains(category.displayName))
    }
    
    @Test
    fun `test isFeatured returns true only for HOT category`() {
        // When & Then
        assertTrue("HOT should be featured", BatteryStyleCategory.HOT.isFeatured())
        assertFalse("CHARACTER should not be featured", BatteryStyleCategory.CHARACTER.isFeatured())
        assertFalse("HEART should not be featured", BatteryStyleCategory.HEART.isFeatured())
        assertFalse("CUTE should not be featured", BatteryStyleCategory.CUTE.isFeatured())
        assertFalse("ANIMAL should not be featured", BatteryStyleCategory.ANIMAL.isFeatured())
        assertFalse("FOOD should not be featured", BatteryStyleCategory.FOOD.isFeatured())
        assertFalse("NATURE should not be featured", BatteryStyleCategory.NATURE.isFeatured())
        assertFalse("GAMING should not be featured", BatteryStyleCategory.GAMING.isFeatured())
        assertFalse("SEASONAL should not be featured", BatteryStyleCategory.SEASONAL.isFeatured())
        assertFalse("MINIMAL should not be featured", BatteryStyleCategory.MINIMAL.isFeatured())
    }
    
    @Test
    fun `test getAllSorted returns categories in sort order`() {
        // When
        val sortedCategories = BatteryStyleCategory.getAllSorted()
        
        // Then
        assertEquals(BatteryStyleCategory.values().size, sortedCategories.size)
        
        // Verify sort order is ascending
        for (i in 0 until sortedCategories.size - 1) {
            assertTrue(
                "Categories should be sorted by sortOrder",
                sortedCategories[i].sortOrder <= sortedCategories[i + 1].sortOrder
            )
        }
        
        // Verify HOT is first (sortOrder = 0)
        assertEquals(BatteryStyleCategory.HOT, sortedCategories.first())
    }
    
    @Test
    fun `test getMainFilterCategories returns expected categories`() {
        // When
        val mainCategories = BatteryStyleCategory.getMainFilterCategories()
        
        // Then
        val expectedCategories = listOf(
            BatteryStyleCategory.HOT,
            BatteryStyleCategory.CHARACTER,
            BatteryStyleCategory.HEART,
            BatteryStyleCategory.CUTE,
            BatteryStyleCategory.ANIMAL,
            BatteryStyleCategory.FOOD
        )
        
        assertEquals(expectedCategories.size, mainCategories.size)
        expectedCategories.forEach { expectedCategory ->
            assertTrue(
                "Main categories should contain $expectedCategory",
                mainCategories.contains(expectedCategory)
            )
        }
        
        // Verify excluded categories are not in main filter
        assertFalse("NATURE should not be in main filter", mainCategories.contains(BatteryStyleCategory.NATURE))
        assertFalse("GAMING should not be in main filter", mainCategories.contains(BatteryStyleCategory.GAMING))
        assertFalse("SEASONAL should not be in main filter", mainCategories.contains(BatteryStyleCategory.SEASONAL))
        assertFalse("MINIMAL should not be in main filter", mainCategories.contains(BatteryStyleCategory.MINIMAL))
    }
    
    @Test
    fun `test findByDisplayName with exact match`() {
        // When & Then
        assertEquals(BatteryStyleCategory.HOT, BatteryStyleCategory.findByDisplayName("HOT"))
        assertEquals(BatteryStyleCategory.CHARACTER, BatteryStyleCategory.findByDisplayName("Character"))
        assertEquals(BatteryStyleCategory.HEART, BatteryStyleCategory.findByDisplayName("Heart"))
        assertEquals(BatteryStyleCategory.CUTE, BatteryStyleCategory.findByDisplayName("Cute"))
    }
    
    @Test
    fun `test findByDisplayName with case insensitive match`() {
        // When & Then
        assertEquals(BatteryStyleCategory.HOT, BatteryStyleCategory.findByDisplayName("hot"))
        assertEquals(BatteryStyleCategory.CHARACTER, BatteryStyleCategory.findByDisplayName("character"))
        assertEquals(BatteryStyleCategory.HEART, BatteryStyleCategory.findByDisplayName("HEART"))
        assertEquals(BatteryStyleCategory.CUTE, BatteryStyleCategory.findByDisplayName("cUtE"))
    }
    
    @Test
    fun `test findByDisplayName with non-matching name returns null`() {
        // When & Then
        assertNull(BatteryStyleCategory.findByDisplayName("NonExistent"))
        assertNull(BatteryStyleCategory.findByDisplayName("Unknown"))
        assertNull(BatteryStyleCategory.findByDisplayName(""))
    }
    
    @Test
    fun `test getDefault returns CHARACTER`() {
        // When
        val defaultCategory = BatteryStyleCategory.getDefault()
        
        // Then
        assertEquals(BatteryStyleCategory.CHARACTER, defaultCategory)
    }
    
    @Test
    fun `test fromString with valid enum name`() {
        // When & Then
        assertEquals(BatteryStyleCategory.HOT, BatteryStyleCategory.fromString("HOT"))
        assertEquals(BatteryStyleCategory.CHARACTER, BatteryStyleCategory.fromString("CHARACTER"))
        assertEquals(BatteryStyleCategory.HEART, BatteryStyleCategory.fromString("HEART"))
        assertEquals(BatteryStyleCategory.CUTE, BatteryStyleCategory.fromString("CUTE"))
    }
    
    @Test
    fun `test fromString with lowercase enum name`() {
        // When & Then
        assertEquals(BatteryStyleCategory.HOT, BatteryStyleCategory.fromString("hot"))
        assertEquals(BatteryStyleCategory.CHARACTER, BatteryStyleCategory.fromString("character"))
        assertEquals(BatteryStyleCategory.HEART, BatteryStyleCategory.fromString("heart"))
        assertEquals(BatteryStyleCategory.CUTE, BatteryStyleCategory.fromString("cute"))
    }
    
    @Test
    fun `test fromString with display name fallback`() {
        // When & Then
        assertEquals(BatteryStyleCategory.CHARACTER, BatteryStyleCategory.fromString("Character"))
        assertEquals(BatteryStyleCategory.HEART, BatteryStyleCategory.fromString("Heart"))
        assertEquals(BatteryStyleCategory.CUTE, BatteryStyleCategory.fromString("Cute"))
        assertEquals(BatteryStyleCategory.ANIMAL, BatteryStyleCategory.fromString("Animal"))
    }
    
    @Test
    fun `test fromString with null returns default`() {
        // When
        val result = BatteryStyleCategory.fromString(null)
        
        // Then
        assertEquals(BatteryStyleCategory.getDefault(), result)
    }
    
    @Test
    fun `test fromString with empty string returns default`() {
        // When
        val result = BatteryStyleCategory.fromString("")
        
        // Then
        assertEquals(BatteryStyleCategory.getDefault(), result)
    }
    
    @Test
    fun `test fromString with blank string returns default`() {
        // When
        val result = BatteryStyleCategory.fromString("   ")
        
        // Then
        assertEquals(BatteryStyleCategory.getDefault(), result)
    }
    
    @Test
    fun `test fromString with invalid value returns default`() {
        // When
        val result = BatteryStyleCategory.fromString("InvalidCategory")
        
        // Then
        assertEquals(BatteryStyleCategory.getDefault(), result)
    }
    
    @Test
    fun `test all categories have unique sort orders`() {
        // Given
        val categories = BatteryStyleCategory.values()
        
        // When
        val sortOrders = categories.map { it.sortOrder }
        val uniqueSortOrders = sortOrders.toSet()
        
        // Then
        assertEquals(
            "All categories should have unique sort orders",
            categories.size,
            uniqueSortOrders.size
        )
    }
}
