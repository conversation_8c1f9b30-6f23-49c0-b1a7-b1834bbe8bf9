package com.tqhit.battery.one.features.emoji.domain.model

import org.junit.Assert.*
import org.junit.Test

/**
 * Unit tests for BatteryStyle domain model.
 * Tests data class creation, validation, and utility methods.
 */
class BatteryStyleTest {
    
    @Test
    fun `test valid battery style creation`() {
        // Given
        val id = "test_style"
        val name = "Test Style"
        val category = BatteryStyleCategory.HEART
        val batteryImageUrl = "https://example.com/battery.png"
        val emojiImageUrl = "https://example.com/emoji.png"
        val isPremium = true
        val isPopular = false
        
        // When
        val batteryStyle = BatteryStyle(
            id = id,
            name = name,
            category = category,
            batteryImageUrl = batteryImageUrl,
            emojiImageUrl = emojiImageUrl,
            isPremium = isPremium,
            isPopular = isPopular
        )
        
        // Then
        assertEquals(id, batteryStyle.id)
        assertEquals(name, batteryStyle.name)
        assertEquals(category, batteryStyle.category)
        assertEquals(batteryImageUrl, batteryStyle.batteryImageUrl)
        assertEquals(emojiImageUrl, batteryStyle.emojiImageUrl)
        assertEquals(isPremium, batteryStyle.isPremium)
        assertEquals(isPopular, batteryStyle.isPopular)
        assertTrue(batteryStyle.timestampEpochMillis > 0)
    }
    
    @Test
    fun `test battery style with default values`() {
        // Given
        val id = "default_test"
        val name = "Default Test"
        val category = BatteryStyleCategory.CUTE
        val batteryImageUrl = "https://example.com/battery.png"
        val emojiImageUrl = "https://example.com/emoji.png"
        
        // When
        val batteryStyle = BatteryStyle(
            id = id,
            name = name,
            category = category,
            batteryImageUrl = batteryImageUrl,
            emojiImageUrl = emojiImageUrl
        )
        
        // Then
        assertFalse(batteryStyle.isPremium) // Default should be false
        assertFalse(batteryStyle.isPopular) // Default should be false
        assertNotNull(batteryStyle.defaultConfig) // Should have default config
        assertTrue(batteryStyle.timestampEpochMillis > 0)
    }
    
    @Test
    fun `test isValid returns true for valid style`() {
        // Given
        val validStyle = BatteryStyle(
            id = "valid_style",
            name = "Valid Style",
            category = BatteryStyleCategory.CHARACTER,
            batteryImageUrl = "https://example.com/battery.png",
            emojiImageUrl = "https://example.com/emoji.png"
        )
        
        // When & Then
        assertTrue(validStyle.isValid())
    }
    
    @Test
    fun `test isValid returns false for empty id`() {
        // Given
        val invalidStyle = BatteryStyle(
            id = "",
            name = "Valid Name",
            category = BatteryStyleCategory.CHARACTER,
            batteryImageUrl = "https://example.com/battery.png",
            emojiImageUrl = "https://example.com/emoji.png"
        )
        
        // When & Then
        assertFalse(invalidStyle.isValid())
    }
    
    @Test
    fun `test isValid returns false for blank name`() {
        // Given
        val invalidStyle = BatteryStyle(
            id = "valid_id",
            name = "   ",
            category = BatteryStyleCategory.CHARACTER,
            batteryImageUrl = "https://example.com/battery.png",
            emojiImageUrl = "https://example.com/emoji.png"
        )
        
        // When & Then
        assertFalse(invalidStyle.isValid())
    }
    
    @Test
    fun `test isValid returns false for empty battery image URL`() {
        // Given
        val invalidStyle = BatteryStyle(
            id = "valid_id",
            name = "Valid Name",
            category = BatteryStyleCategory.CHARACTER,
            batteryImageUrl = "",
            emojiImageUrl = "https://example.com/emoji.png"
        )
        
        // When & Then
        assertFalse(invalidStyle.isValid())
    }
    
    @Test
    fun `test isValid returns false for empty emoji image URL`() {
        // Given
        val invalidStyle = BatteryStyle(
            id = "valid_id",
            name = "Valid Name",
            category = BatteryStyleCategory.CHARACTER,
            batteryImageUrl = "https://example.com/battery.png",
            emojiImageUrl = ""
        )
        
        // When & Then
        assertFalse(invalidStyle.isValid())
    }
    
    @Test
    fun `test getPreviewId generates unique identifier`() {
        // Given
        val style1 = BatteryStyle(
            id = "style1",
            name = "Style 1",
            category = BatteryStyleCategory.HEART,
            batteryImageUrl = "https://example.com/battery1.png",
            emojiImageUrl = "https://example.com/emoji1.png"
        )
        
        val style2 = BatteryStyle(
            id = "style2",
            name = "Style 2",
            category = BatteryStyleCategory.CUTE,
            batteryImageUrl = "https://example.com/battery2.png",
            emojiImageUrl = "https://example.com/emoji2.png"
        )
        
        // When
        val previewId1 = style1.getPreviewId()
        val previewId2 = style2.getPreviewId()
        
        // Then
        assertNotEquals(previewId1, previewId2)
        assertTrue(previewId1.contains("style1"))
        assertTrue(previewId2.contains("style2"))
    }
    
    @Test
    fun `test matchesSearch with matching name`() {
        // Given
        val style = BatteryStyle(
            id = "heart_style",
            name = "Heart Battery",
            category = BatteryStyleCategory.HEART,
            batteryImageUrl = "https://example.com/battery.png",
            emojiImageUrl = "https://example.com/emoji.png"
        )
        
        // When & Then
        assertTrue(style.matchesSearch("heart"))
        assertTrue(style.matchesSearch("Heart"))
        assertTrue(style.matchesSearch("HEART"))
        assertTrue(style.matchesSearch("battery"))
        assertTrue(style.matchesSearch("Heart Battery"))
    }
    
    @Test
    fun `test matchesSearch with matching category`() {
        // Given
        val style = BatteryStyle(
            id = "cute_style",
            name = "Cute Panda",
            category = BatteryStyleCategory.CUTE,
            batteryImageUrl = "https://example.com/battery.png",
            emojiImageUrl = "https://example.com/emoji.png"
        )
        
        // When & Then
        assertTrue(style.matchesSearch("cute"))
        assertTrue(style.matchesSearch("CUTE"))
        assertTrue(style.matchesSearch("Cute"))
    }
    
    @Test
    fun `test matchesSearch with non-matching query`() {
        // Given
        val style = BatteryStyle(
            id = "heart_style",
            name = "Heart Battery",
            category = BatteryStyleCategory.HEART,
            batteryImageUrl = "https://example.com/battery.png",
            emojiImageUrl = "https://example.com/emoji.png"
        )
        
        // When & Then
        assertFalse(style.matchesSearch("gaming"))
        assertFalse(style.matchesSearch("robot"))
        assertFalse(style.matchesSearch("xyz"))
    }
    
    @Test
    fun `test matchesSearch with empty query returns true`() {
        // Given
        val style = BatteryStyle(
            id = "any_style",
            name = "Any Style",
            category = BatteryStyleCategory.CHARACTER,
            batteryImageUrl = "https://example.com/battery.png",
            emojiImageUrl = "https://example.com/emoji.png"
        )
        
        // When & Then
        assertTrue(style.matchesSearch(""))
        assertTrue(style.matchesSearch("   "))
    }
    
    @Test
    fun `test createDefault returns valid default style`() {
        // When
        val defaultStyle = BatteryStyle.createDefault()
        
        // Then
        assertEquals("default", defaultStyle.id)
        assertEquals("Default Style", defaultStyle.name)
        assertEquals(BatteryStyleCategory.CHARACTER, defaultStyle.category)
        assertEquals("", defaultStyle.batteryImageUrl)
        assertEquals("", defaultStyle.emojiImageUrl)
        assertFalse(defaultStyle.isPremium)
        assertFalse(defaultStyle.isPopular)
        assertNotNull(defaultStyle.defaultConfig)
        assertTrue(defaultStyle.timestampEpochMillis > 0)
    }
}
