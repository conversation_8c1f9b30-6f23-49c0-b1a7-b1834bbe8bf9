package com.tqhit.battery.one.features.stats.apppower.repository

import android.app.usage.UsageStats
import android.app.usage.UsageStatsManager
import android.content.Context
import android.content.pm.ApplicationInfo
import android.content.pm.PackageManager
import android.util.Log
import com.tqhit.battery.one.features.stats.apppower.data.AppPowerConsumptionData
import com.tqhit.battery.one.features.stats.apppower.data.AppPowerConsumptionSummary
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.math.max

/**
 * Repository for retrieving app usage statistics and calculating power consumption estimates
 */
@Singleton
class AppUsageStatsRepository @Inject constructor(
    @ApplicationContext private val context: Context
) {
    companion object {
        private const val TAG = "AppUsageStatsRepository"
        private const val MIN_USAGE_TIME_MS = 5000L // 5 seconds minimum to be included
        
        // Power consumption estimates (mAh per hour of usage)
        // These are rough estimates based on typical app behavior
        private const val DEFAULT_POWER_CONSUMPTION_MAH_PER_HOUR = 50.0
        private const val GAME_POWER_CONSUMPTION_MAH_PER_HOUR = 150.0
        private const val VIDEO_POWER_CONSUMPTION_MAH_PER_HOUR = 120.0
        private const val SOCIAL_POWER_CONSUMPTION_MAH_PER_HOUR = 80.0
        private const val SYSTEM_POWER_CONSUMPTION_MAH_PER_HOUR = 30.0
    }
    
    private val usageStatsManager = context.getSystemService(Context.USAGE_STATS_SERVICE) as? UsageStatsManager
    private val packageManager = context.packageManager
    
    /**
     * Retrieves app power consumption data for the specified time range
     */
    suspend fun getAppPowerConsumption(
        startTime: Long,
        endTime: Long,
        batteryCapacityMah: Int,
        screenOnTimeMillis: Long,
        screenOffTimeMillis: Long
    ): AppPowerConsumptionSummary? {
        Log.d(TAG, "getAppPowerConsumption() called with:")
        Log.d(TAG, "  startTime: $startTime (${java.util.Date(startTime)})")
        Log.d(TAG, "  endTime: $endTime (${java.util.Date(endTime)})")
        Log.d(TAG, "  batteryCapacityMah: $batteryCapacityMah")
        Log.d(TAG, "  screenOnTimeMillis: $screenOnTimeMillis")
        Log.d(TAG, "  screenOffTimeMillis: $screenOffTimeMillis")

        if (usageStatsManager == null) {
            Log.e(TAG, "UsageStatsManager not available - this indicates a system issue")
            return null
        }

        // Check permission first
        val hasPermission = hasUsageStatsPermission()
        Log.d(TAG, "Permission check result: $hasPermission")
        if (!hasPermission) {
            Log.w(TAG, "Usage stats permission not granted")
            return null
        }

        try {
            Log.d(TAG, "Querying usage stats from $startTime to $endTime")

            // Get usage stats for the specified time range
            val usageStats = usageStatsManager.queryUsageStats(
                UsageStatsManager.INTERVAL_BEST,
                startTime,
                endTime
            )

            Log.d(TAG, "UsageStatsManager.queryUsageStats() returned: ${usageStats?.size ?: "null"} entries")

            if (usageStats.isNullOrEmpty()) {
                Log.w(TAG, "No usage stats available for the specified time range")
                Log.w(TAG, "This could mean:")
                Log.w(TAG, "  1. No apps were used during this time period")
                Log.w(TAG, "  2. The time range is too short")
                Log.w(TAG, "  3. Permission is not actually granted")
                Log.w(TAG, "  4. UsageStatsManager is not working properly")
                return createEmptySummary(startTime, endTime, screenOnTimeMillis, screenOffTimeMillis)
            }

            Log.d(TAG, "Found ${usageStats.size} apps with usage data")

            // Log details about the usage stats
            usageStats.forEachIndexed { index, stats ->
                if (index < 5) { // Log first 5 for debugging
                    Log.d(TAG, "  App $index: ${stats.packageName}, foregroundTime: ${stats.totalTimeInForeground}ms")
                }
            }
            
            // Filter and process apps
            Log.d(TAG, "Filtering apps with minimum usage time: ${MIN_USAGE_TIME_MS}ms")
            val filteredStats = usageStats.filter { it.totalTimeInForeground >= MIN_USAGE_TIME_MS }
            Log.d(TAG, "After filtering: ${filteredStats.size} apps meet minimum usage criteria")

            val appDataList = filteredStats.mapNotNull { usageStats ->
                try {
                    val appData = createAppPowerConsumptionData(usageStats, batteryCapacityMah)
                    if (appData != null) {
                        Log.v(TAG, "Created app data: ${appData.appName}, power: ${appData.estimatedPowerConsumptionMah}mAh")
                    } else {
                        Log.v(TAG, "Failed to create app data for: ${usageStats.packageName}")
                    }
                    appData
                } catch (e: Exception) {
                    Log.w(TAG, "Exception creating app data for ${usageStats.packageName}", e)
                    null
                }
            }.sortedByDescending { it.estimatedPowerConsumptionMah }

            Log.d(TAG, "Successfully created app data for ${appDataList.size} apps")

            val totalPowerConsumption = appDataList.sumOf { it.estimatedPowerConsumptionMah }
            Log.d(TAG, "Total estimated power consumption: ${totalPowerConsumption}mAh")

            // Calculate percentages
            val appDataWithPercentages = appDataList.map { app ->
                val percentage = if (totalPowerConsumption > 0) {
                    (app.estimatedPowerConsumptionMah / totalPowerConsumption) * 100.0
                } else {
                    0.0
                }
                app.copy(percentageOfTotalUsage = percentage)
            }

            Log.d(TAG, "Final result: ${appDataWithPercentages.size} apps with power consumption data")
            
            return AppPowerConsumptionSummary(
                sessionStartTime = startTime,
                sessionEndTime = endTime,
                totalSessionDurationMillis = endTime - startTime,
                totalEstimatedPowerConsumptionMah = totalPowerConsumption,
                apps = appDataWithPercentages,
                screenOnTimeMillis = screenOnTimeMillis,
                screenOffTimeMillis = screenOffTimeMillis
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "Error retrieving app usage stats", e)
            Log.e(TAG, "Exception type: ${e.javaClass.simpleName}")
            Log.e(TAG, "Exception message: ${e.message}")
            Log.e(TAG, "Stack trace: ${e.stackTraceToString()}")
            return null
        }
    }
    
    /**
     * Creates AppPowerConsumptionData from UsageStats
     */
    private fun createAppPowerConsumptionData(
        usageStats: UsageStats,
        batteryCapacityMah: Int
    ): AppPowerConsumptionData? {
        return try {
            val packageName = usageStats.packageName
            val applicationInfo = packageManager.getApplicationInfo(packageName, 0)
            val appName = packageManager.getApplicationLabel(applicationInfo).toString()
            val appIcon = packageManager.getApplicationIcon(applicationInfo)
            val isSystemApp = (applicationInfo.flags and ApplicationInfo.FLAG_SYSTEM) != 0
            
            // Calculate estimated power consumption based on app category and usage time
            val usageTimeHours = usageStats.totalTimeInForeground / (1000.0 * 60.0 * 60.0)
            val powerConsumptionRate = estimatePowerConsumptionRate(packageName, applicationInfo)
            val estimatedPowerConsumption = usageTimeHours * powerConsumptionRate
            
            // Cap the power consumption to a reasonable percentage of battery capacity
            val cappedPowerConsumption = max(0.0, estimatedPowerConsumption.coerceAtMost(batteryCapacityMah * 0.5))
            
            AppPowerConsumptionData(
                packageName = packageName,
                appName = appName,
                appIcon = appIcon,
                usageTimeMillis = usageStats.totalTimeInForeground,
                estimatedPowerConsumptionMah = cappedPowerConsumption,
                percentageOfTotalUsage = 0.0, // Will be calculated later
                isSystemApp = isSystemApp
            )
        } catch (e: Exception) {
            Log.w(TAG, "Failed to get app info for ${usageStats.packageName}", e)
            null
        }
    }
    
    /**
     * Estimates power consumption rate based on app characteristics
     */
    private fun estimatePowerConsumptionRate(packageName: String, applicationInfo: ApplicationInfo): Double {
        return when {
            // System apps typically use less power
            (applicationInfo.flags and ApplicationInfo.FLAG_SYSTEM) != 0 -> SYSTEM_POWER_CONSUMPTION_MAH_PER_HOUR
            
            // Games typically use more power
            isGameApp(packageName) -> GAME_POWER_CONSUMPTION_MAH_PER_HOUR
            
            // Video/streaming apps use significant power
            isVideoApp(packageName) -> VIDEO_POWER_CONSUMPTION_MAH_PER_HOUR
            
            // Social media apps use moderate power
            isSocialApp(packageName) -> SOCIAL_POWER_CONSUMPTION_MAH_PER_HOUR
            
            // Default for other apps
            else -> DEFAULT_POWER_CONSUMPTION_MAH_PER_HOUR
        }
    }
    
    private fun isGameApp(packageName: String): Boolean {
        val gameKeywords = listOf("game", "play", "puzzle", "arcade", "action", "adventure")
        return gameKeywords.any { packageName.lowercase().contains(it) }
    }
    
    private fun isVideoApp(packageName: String): Boolean {
        val videoKeywords = listOf("youtube", "netflix", "video", "stream", "media", "player")
        return videoKeywords.any { packageName.lowercase().contains(it) }
    }
    
    private fun isSocialApp(packageName: String): Boolean {
        val socialKeywords = listOf("facebook", "instagram", "twitter", "tiktok", "snapchat", "whatsapp", "telegram")
        return socialKeywords.any { packageName.lowercase().contains(it) }
    }
    
    private fun createEmptySummary(
        startTime: Long,
        endTime: Long,
        screenOnTimeMillis: Long,
        screenOffTimeMillis: Long
    ): AppPowerConsumptionSummary {
        return AppPowerConsumptionSummary(
            sessionStartTime = startTime,
            sessionEndTime = endTime,
            totalSessionDurationMillis = endTime - startTime,
            totalEstimatedPowerConsumptionMah = 0.0,
            apps = emptyList(),
            screenOnTimeMillis = screenOnTimeMillis,
            screenOffTimeMillis = screenOffTimeMillis
        )
    }
    
    /**
     * Checks if the app has usage stats permission
     */
    fun hasUsageStatsPermission(): Boolean {
        Log.d(TAG, "hasUsageStatsPermission() called")

        if (usageStatsManager == null) {
            Log.e(TAG, "UsageStatsManager is null - permission check failed")
            return false
        }

        return try {
            val endTime = System.currentTimeMillis()
            val startTime = endTime - 60000 // 1 minute ago
            Log.d(TAG, "Testing permission with query from $startTime to $endTime")

            val stats = usageStatsManager.queryUsageStats(UsageStatsManager.INTERVAL_DAILY, startTime, endTime)
            val hasPermission = stats?.isNotEmpty() == true

            Log.d(TAG, "Permission test result: $hasPermission (found ${stats?.size ?: 0} usage stats)")

            if (!hasPermission) {
                Log.w(TAG, "Permission check failed - this could mean:")
                Log.w(TAG, "  1. PACKAGE_USAGE_STATS permission not granted")
                Log.w(TAG, "  2. No apps were used in the last minute")
                Log.w(TAG, "  3. UsageStatsManager is not working properly")
            }

            hasPermission
        } catch (e: Exception) {
            Log.e(TAG, "Exception during permission check", e)
            false
        }
    }
}
