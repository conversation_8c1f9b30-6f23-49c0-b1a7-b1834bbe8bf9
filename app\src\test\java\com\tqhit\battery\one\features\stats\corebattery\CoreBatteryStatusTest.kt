package com.tqhit.battery.one.features.stats.corebattery

import com.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus
import org.junit.Test
import org.junit.Assert.*

/**
 * Unit tests for CoreBatteryStatus data class.
 * Tests basic instantiation, logging, and default creation.
 */
class CoreBatteryStatusTest {
    
    @Test
    fun `test CoreBatteryStatus creation with valid data`() {
        // Given
        val percentage = 85
        val isCharging = true
        val pluggedSource = 2 // USB
        val currentMicroAmperes = 1500000L // 1.5A charging
        val voltageMillivolts = 4200
        val temperatureCelsius = 25.5f
        val timestamp = System.currentTimeMillis()
        
        // When
        val status = CoreBatteryStatus(
            percentage = percentage,
            isCharging = isCharging,
            pluggedSource = pluggedSource,
            currentMicroAmperes = currentMicroAmperes,
            voltageMillivolts = voltageMillivolts,
            temperatureCelsius = temperatureCelsius,
            timestampEpochMillis = timestamp
        )
        
        // Then
        assertEquals(percentage, status.percentage)
        assertEquals(isCharging, status.isCharging)
        assertEquals(pluggedSource, status.pluggedSource)
        assertEquals(currentMicroAmperes, status.currentMicroAmperes)
        assertEquals(voltageMillivolts, status.voltageMillivolts)
        assertEquals(temperatureCelsius, status.temperatureCelsius, 0.01f)
        assertEquals(timestamp, status.timestampEpochMillis)
    }
    
    @Test
    fun `test CoreBatteryStatus creation with boundary values`() {
        // Given - boundary values
        val percentage = 0
        val isCharging = false
        val pluggedSource = 0 // UNPLUGGED
        val currentMicroAmperes = -2000000L // 2A discharging
        val voltageMillivolts = 3000
        val temperatureCelsius = -10.0f
        val timestamp = 0L
        
        // When
        val status = CoreBatteryStatus(
            percentage = percentage,
            isCharging = isCharging,
            pluggedSource = pluggedSource,
            currentMicroAmperes = currentMicroAmperes,
            voltageMillivolts = voltageMillivolts,
            temperatureCelsius = temperatureCelsius,
            timestampEpochMillis = timestamp
        )
        
        // Then
        assertEquals(0, status.percentage)
        assertFalse(status.isCharging)
        assertEquals(0, status.pluggedSource)
        assertEquals(-2000000L, status.currentMicroAmperes)
        assertEquals(3000, status.voltageMillivolts)
        assertEquals(-10.0f, status.temperatureCelsius, 0.01f)
        assertEquals(0L, status.timestampEpochMillis)
    }
    
    @Test
    fun `test CoreBatteryStatus createDefault method`() {
        // When
        val defaultStatus = CoreBatteryStatus.createDefault()
        
        // Then
        assertEquals(0, defaultStatus.percentage)
        assertFalse(defaultStatus.isCharging)
        assertEquals(0, defaultStatus.pluggedSource)
        assertEquals(0L, defaultStatus.currentMicroAmperes)
        assertEquals(0, defaultStatus.voltageMillivolts)
        assertEquals(25.0f, defaultStatus.temperatureCelsius, 0.01f) // Room temperature
        assertTrue(defaultStatus.timestampEpochMillis > 0) // Should have a valid timestamp
    }
    
    @Test
    fun `test CoreBatteryStatus data class equality`() {
        // Given
        val timestamp = System.currentTimeMillis()
        val status1 = CoreBatteryStatus(
            percentage = 50,
            isCharging = true,
            pluggedSource = 1,
            currentMicroAmperes = 1000000L,
            voltageMillivolts = 4000,
            temperatureCelsius = 30.0f,
            timestampEpochMillis = timestamp
        )
        
        val status2 = CoreBatteryStatus(
            percentage = 50,
            isCharging = true,
            pluggedSource = 1,
            currentMicroAmperes = 1000000L,
            voltageMillivolts = 4000,
            temperatureCelsius = 30.0f,
            timestampEpochMillis = timestamp
        )
        
        // Then
        assertEquals(status1, status2)
        assertEquals(status1.hashCode(), status2.hashCode())
    }
    
    @Test
    fun `test CoreBatteryStatus data class inequality`() {
        // Given
        val timestamp = System.currentTimeMillis()
        val status1 = CoreBatteryStatus(
            percentage = 50,
            isCharging = true,
            pluggedSource = 1,
            currentMicroAmperes = 1000000L,
            voltageMillivolts = 4000,
            temperatureCelsius = 30.0f,
            timestampEpochMillis = timestamp
        )
        
        val status2 = CoreBatteryStatus(
            percentage = 51, // Different percentage
            isCharging = true,
            pluggedSource = 1,
            currentMicroAmperes = 1000000L,
            voltageMillivolts = 4000,
            temperatureCelsius = 30.0f,
            timestampEpochMillis = timestamp
        )
        
        // Then
        assertNotEquals(status1, status2)
    }
    
    @Test
    fun `test CoreBatteryStatus toString contains key information`() {
        // Given
        val status = CoreBatteryStatus(
            percentage = 75,
            isCharging = true,
            pluggedSource = 2,
            currentMicroAmperes = 1500000L,
            voltageMillivolts = 4100,
            temperatureCelsius = 28.5f,
            timestampEpochMillis = 1234567890L
        )
        
        // When
        val statusString = status.toString()
        
        // Then
        assertTrue(statusString.contains("75"))
        assertTrue(statusString.contains("true"))
        assertTrue(statusString.contains("1500000"))
        assertTrue(statusString.contains("4100"))
        assertTrue(statusString.contains("28.5"))
    }
}
