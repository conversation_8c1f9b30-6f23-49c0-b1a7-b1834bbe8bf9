<resources>
    <string name="app_name">Battery Charging Animation 3D</string>

    <string name="DontKillMyApp">Don\'t Kill My App</string>
    <string name="Dutch_lang">Nederlands</string>
    <string name="English_lang">English</string>
    <string name="French_lang">Français</string>
    <string name="Germany_lang">Deutsch</string>
    <string name="Hungarian_lang">Magyar</string>
    <string name="Italy_lang">Italiano</string>
    <string name="Poland_lang"><PERSON><PERSON></string>
    <string name="Portuguese_lang">Português</string>
    <string name="Privacy_Policy">Privacy Policy</string>
    <string name="Romanian_lang">Română</string>
    <string name="Russian_lang">Русский</string>
    <string name="Spanish_lang">Español</string>
    <string name="Turkish_lang">Türkçe</string>
    <string name="Ukraine_lang">Українська</string>
    <string name="abc_action_menu_overflow_description">More options</string>
    <string name="abc_action_mode_done">Done</string>
    <string name="abc_capital_off">OFF</string>
    <string name="abc_capital_on">ON</string>
    <string name="abc_search_hint">Search…</string>
    <string name="abc_searchview_description_clear">Clear query</string>
    <string name="abc_searchview_description_search">Search</string>
    <string name="abc_searchview_description_submit">Submit query</string>
    <string name="abc_searchview_description_voice">Voice search</string>
    <string name="abc_toolbar_collapse_description">Collapse</string>
    <string name="about_translations">About Translations</string>
    <string name="active_mode">Screen on</string>
    <string name="all">Total</string>
    <string name="all_info">This block displays how long the device will work from the current battery level, taking into account your usage style.</string>
    <string name="all_time_charge">Total time</string>
    <string name="alternative_charge_level">Alternative charge level method</string>
    <string name="amoled">Amoled</string>
    <string name="amoled_inverted">Amoled Inverted</string>
    <string name="amperage_current_session">Current session amperage graph</string>
    <string name="app">Application</string>
    <string name="arabic_lang">العربية</string>
    <string name="auto">Auto</string>
    <string name="auto_stab_database">Automatic stabilization of database</string>
    <string name="average_speed">Average speed</string>
    <string name="average_speed_charge">Charging speed average</string>
    <string name="avg_charge_info">This block displays the average charging speed for all sessions. You have the option to reset this data by clearing the database in settings.\n\nScreen on is the average speed with the screen on.\nScreen off is the average speed with the screen off.\nCombined use is the average speed based on how you use your phone while charging.</string>
    <string name="avg_discharge_info">This block displays the average discharging speed for all sessions. You have the option to reset this data by clearing the database in settings.\n\nScreen on is the average speed with the screen on.\nScreen off is the average speed with the screen off.\nCombined use is the average speed based on how you use your phone while discharging.</string>
    <string name="awake">Awake</string>
    <string name="battery_info">Battery information (Android)</string>
    <string name="battery_level_alert">Battery Level Alert</string>
    <string name="battery_notify_text_start">Choose whether you want to receive a notification every time your battery reaches the selected percentage. You can turn this feature off at any time.</string>
    <string name="battery_wear_info">Every time you charge your device, the battery wears out. In this block, you can roughly find out how much damage your battery will take when charged to the selected percentage. The higher the wear cycle ratio of the battery, the more damage the battery takes.</string>
    <string name="battery_wear_info_up">What is battery wear?</string>
    <string name="bla">Full Screen Alert</string>
    <string name="black">Dark</string>
    <string name="black_inverted">Dark Inverted</string>
    <string name="buy_advanced_access">Get Advanced Access</string>
    <string name="by_TJ">designed by TJ</string>
    <string name="calculated_capacity">Calculated capacity</string>
    <string name="calibration_going">Waiting for diagnostics</string>
    <string name="cancel">Cancel</string>
    <string name="change_design_capacity">Change design capacity</string>
    <string name="charge">Charge</string>
    <string name="charge_alarm">Charging completion notification</string>
    <string name="charge_alarm_text">By enabling this feature, you will receive a notification each time your battery is charged to the selected level.</string>
    <string name="charge_notification">Charge notification</string>
    <string name="charge_notification_switch">Notify when the device is charging</string>
    <string name="charge_notification_text">Your device is charging</string>
    <string name="charge_reached">The battery is charged!</string>
    <string name="charge_to">Charging up to %1$s%% leads to %2$s wear cycles</string>
    <string name="charging">The charge of %1$s%% is enough for</string>
    <string name="choose_color">Choose the color</string>
    <string name="choose_theme">Choose the theme</string>
    <string name="clear_database">Clear database</string>
    <string name="complex_use">Combined use</string>
    <string name="confirm">Confirm</string>
    <string name="confirm_and_continue">Accept and continue</string>
    <string name="confirmed">The terms of the privacy policy are accepted. Thanks!</string>
    <string name="cumulative">Cumulative</string>
    <string name="cumulative_text">This method is more convenient because it does not require the user to fully charge. But, it may be inferior in accuracy to the singular method. To update the statistics, charge the device at least 15%. Attention, with a low number of sessions, the level of battery wear can differ significantly from the real ones.</string>
    <string name="current_charging">Amperage</string>
    <string name="current_session">Current session</string>
    <string name="current_session_info">This block displays information about the current or last charging session. If the battery is charging, the current session will be displayed. If the battery is discharging, it will display the last session during which at least 1% was charged. You have the option to reset this data immediately using Manual session reset. You can enable this feature in the settings.\n\nAverage Speed is the overall charging speed.\nScreen on is the charging speed with the screen on.\nScreen off is the charging speed with the screen off.</string>
    <string name="current_session_info_discharge">This block displays information about the current or last discharging session. If the battery is discharging, the current session will be displayed. If the battery is charging, it will display the last session during which at least 1% was discharged. You have the option to reset this data immediately using Manual session reset. You can enable this feature in the settings.\n\nAverage Speed is the overall discharging speed.\nScreen on is the discharging speed with the screen on.\nScreen off is the discharging speed with the screen off.</string>
    <string name="damage_of_battery_charge">Battery wear</string>
    <string name="data_base">Database</string>
    <string name="day_info">This block displays how long the device will work with the SCREEN ON from the current battery level.</string>
    <string name="dead_time">Battery wear prediction</string>
    <string name="dead_time_text_down" formatted="false">\tTo reduce the rate of wear and increase battery life, it is recommended to keep the battery charge level between 20% and 80%. \n\tWhen the battery reaches more than 20% wear, it is recommended to replace the battery with a new one.</string>
    <string name="deep_sleep">Deep sleep</string>
    <string name="degree_of_wear_info">This block displays approximate information about the state of your battery.\n\nThe design capacity is the original capacity of the battery, which was specified by the manufacturer.\nThe сalculated capacity is the capacity of the battery that was calculated by this program.\n\nAttention. If your Battery health is less than 50%, then you probably have a dual-cell battery. To fix capacity display you need to enable in settings for dual-cell batteries.</string>
    <string name="design_capacity">Design capacity</string>
    <string name="design_capacity_text_dialog">The design capacity is the battery capacity declared by the manufacturer in mAh.</string>
    <string name="design_capacity_text_error">Invalid value. It\'s important to specify the correct capacity as it affects some features.</string>
    <string name="design_capacity_text_incorrect">The design capacity was determined automatically. If it is not true, you can change it.</string>
    <string name="desing_capacity">Design capacity:</string>
    <string name="device">Device:</string>
    <string name="discharge">Discharge</string>
    <string name="discharge_notification">Discharge notification</string>
    <string name="discharge_notification_switch">Notify when the device is discharging</string>
    <string name="discharge_notification_text">Your device is not charging</string>
    <string name="discharging">Discharging.</string>
    <string name="disconnect_charge">Disconnect the device from charging. Diagnostics occurs only when the device is discharging.</string>
    <string name="display_charging_amperage_graph">Display charging amperage graph</string>
    <string name="do_not_disturb">Don\'t disturb</string>
    <string name="do_not_disturb_from">Don\'t disturb from</string>
    <string name="do_not_disturb_until">Don\'t disturb until</string>
    <string name="dont_kill_my_app1">In order to work properly and display the correct data, BatteryApp must be constantly running in the background. If BatteryApp is not running in the background, it will not be able to perform functions such as battery level alerts or other important functions.</string>
    <string name="dont_kill_my_app2">Unfortunately, on many devices, the operating system stops the service that collects the necessary battery data. Don\'t Kill My App is a site that helps users solve this problem for various devices.</string>
    <string name="dont_kill_my_app3">For versions of Android 11 and above, it has become critical to grant permission to run continuously in the background. Please grant this permission.</string>
    <string name="dont_kill_my_app4">Please enable Autorun for BatteryApp so that if the service is killed, it can restart itself.</string>
    <string name="dont_kill_my_app_permission">Work in background permission</string>
    <string name="dual_battery">Setting for dual-cell battery</string>

    <!-- Background Permission Dialog -->
    <string name="background_permission_dialog_title">Important information!</string>
    <string name="background_permission_dialog_message">In order to work properly and display correct data, BatteryApp must be constantly running in the background. If BatteryApp is not running in the background, it will not be able to perform functions such as battery level alerts or other important functions.\n\nUnfortunately, on many devices, the operating system stops the service that collects the necessary battery data. Don\'t Kill My App is a site that helps users solve this problem for various devices.\n\nFor versions of Android 11 and above, it has become critical to grant permission to run continuously in the background. Please grant this permission.</string>
    <string name="background_permission_allow">Allow</string>
    <string name="background_permission_close">Close</string>
    <string name="background_permission_dont_kill_link">Don\'t Kill My App</string>
    <string name="enable_notification">Enable Notification</string>
    <string name="enable_vibration">Vibration</string>
    <string name="error">Error</string>
    <string name="error_accessing_file">Error accessing the file. Try again.</string>
    <string name="export_database">Export Database</string>
    <string name="for_time_up_charge">000h 000m</string>
    <string name="full_battery_info">This block displays approximate information about the duration of the device from a full battery. This information is based on previously collected data about your use of the device.</string>
    <string name="full_time">All Time</string>
    <string name="fullbattery_top">The battery is fully charged!</string>
    <string name="graph">Battery wear chart</string>
    <string name="graph_percentage_text_down">\tThis graph shows how the battery percentage has changed over the time period you selected.</string>
    <string name="graph_temp">Temperature graph</string>
    <string name="graph_temp_text_down">\tThis graph shows how the battery temperature has changed over the time period you select.</string>
    <string name="graph_text_down">\tThis graph displays the wear rate of the battery over last week. Each column is one day. Height column is the degree of wear that the battery has received on a specific day.</string>
    <string name="grey">Grey</string>
    <string name="grey_inverted">Grey Inverted</string>
    <string name="h_16">16h</string>
    <string name="h_24">24h</string>
    <string name="h_4">4h</string>
    <string name="h_8">8h</string>
    <string name="hand_reset_sessions">Manual session reset</string>
    <string name="health">Health</string>
    <string name="history">Usage history</string>
    <string name="icon">Application icon</string>
    <string name="import_database">Import Database</string>
    <string name="important_information">Important information!</string>
    <string name="info_in_current_session">Loss of charge</string>
    <string name="info_text">Show hints</string>
    <string name="language">Language</string>
    <string name="light">Light</string>
    <string name="light__nverted_res_0x7f130141">Light Inverted</string>
    <string name="loss_charge_info">This block displays the percentage of charge and mah that were spent during the current or last session. If the battery is discharging, the current session will be displayed. If the battery is charging, it will display the last session during which at least 1% was discharged.\n\nScreen on is the amount of interest spent with the screen on.\nScreen off is the amount of interest spent with the screen turned off.</string>
    <string name="low_battery">Low Battery</string>
    <string name="low_battery_alarm">Low battery notification</string>
    <string name="low_battery_alarm_text">By enabling this function, you will receive a notification every time the battery level drops to the selected level.</string>
    <string name="low_battery_text_1">The battery level has reached </string>
    <string name="low_battery_text_2">. Charge your device.</string>
    <string name="m36">36m</string>
    <string name="m8">8m</string>
    <string name="mA">\u0020mA</string>
    <string name="ma">\u0020mAh </string>
    <string name="ma_in_medium">\u0020mA average</string>
    <string name="maximum_capacity">Battery health</string>
    <string name="measurement_parameter">Measured parameter:</string>
    <string name="my_subscribe">My subscription</string>
    <string name="needed_advanced_access">Advanced Access required</string>
    <string name="night_info">This block displays how long the device will work with the SCREEN OFF from the current battery level.</string>
    <string name="no">No</string>
    <string name="not_identified">Not identified</string>
    <string name="notification">Notification</string>
    <string name="notification_for_tempBat">Notify when battery overheats</string>
    <string name="notifications_permission_confirm">Allow</string>
    <string name="notifications_permission_decline">Don\'t allow</string>
    <string name="notifications_permission_title">Allow app to send you notifications?</string>
    <string name="not_charging_message">Device is not currently charging. Connect a charger to see charging statistics.</string>
    <string name="not_charging">Not charging. Battery is discharging.</string>
    <string name="notify_access">In order to use this feature, you must grant access to display notifications.</string>
    <string name="notify_full">Notify when fully charged</string>
    <string name="now">Right now</string>
    <string name="operating_time">Operating time with screen off</string>
    <string name="operating_time_info">In this block you can see the operating time of your device with the screen turned off.\n\nAwake is the total amount of time the phone used the processor to complete tasks.\nDeep sleep is the total amount of time the phone\'s processor is idle.</string>
    <string name="percent">% </string>
    <string name="percent_in_hour">\u0020%/h</string>
    <string name="percent_without_tab">%</string>
    <string name="percentage_graph">Percentage graph</string>
    <string name="permission_granted">Permission has already been granted. Thank you!</string>
    <string name="pers">Personalization</string>
    <string name="please_disconnect_charger">Please turn off the charge.</string>
    <string name="polarity">Polarity:</string>
    <string name="power">Power</string>
    <string name="privacy_policy_starting">Please read and accept the privacy policy to continue.</string>
    <string name="projected_time_charge_to_100">Expected charging time up to 100%</string>
    <string name="projected_time_charge_to_var">Expected charging time up to %1$s%%</string>
    <string name="rate">Rate and review</string>
    <string name="real_capacity">Actual battery capacity</string>
    <string name="real_capacity_text">Using two kinds of algorithms, with BatteryApp, you can calculate the actual capacity of a battery and predict how long it will take for it to become unusable.</string>
    <string name="remaining_time_charge">Remaining charging time</string>
    <string name="remove_add">Remove advertising</string>
    <string name="reset_sessions">Reset session</string>
    <string name="screen_off">Screen off</string>
    <string name="second_color">Second theme color</string>
    <string name="setting_notify">Setting up notifications via Android</string>
    <string name="setting_notify_battery_service">Displaying information in a notification</string>
    <string name="setting_notify_rate">Notification update rate</string>
    <string name="settings">Settings</string>
    <string name="settings_autostart">Autorun settings</string>
    <string name="show_on_lockscreen">Show on lock screen</string>
    <string name="since">from %1$s</string>
    <string name="since_above">from %1$s to %2$s</string>
    <string name="singular">Singular</string>
    <string name="singular_no_data" formatted="false">Charge from less than 15% to 100% to display data.</string>
    <string name="singular_text" formatted="false">This method is potentially more accurate because it only takes into account the last full charge of the battery. But, it is more demanding for the user, as it requires charging from less than 15% to 100%. To increase data accuracy, do not stop charging until you are notified that it is fully charged.</string>
    <string name="stab_database">Manual database stabilization</string>
    <string name="state_empty">Empty</string>
    <string name="status_charge">Charging status</string>
    <string name="status_icon">Notification icon in status bar</string>
    <string name="support_us">Support the project</string>
    <string name="temperature">Temperature</string>
    <string name="theme">Theme</string>
    <string name="time_work_on_us">Time works for us!</string>
    <string name="time_work_on_us_text">BatteryApp collects data gradually to show you the most accurate statistics. It\'s simple, the longer you use BatteryApp, the more accurate the statistics become!</string>
    <string name="timework_on_fullbaterry">Full battery time estimates</string>
    <string name="unexpected_error">Unexpected error!</string>
    <string name="update_downloading">Downloading update…</string>
    <string name="update_install">Install</string>
    <string name="using_baterry_middle">Average battery usage</string>
    <string name="using_energy">App power consumption</string>
    <string name="v">\u0020V</string>
    <string name="versions">Application version: </string>
    <string name="view_temp">Temperature display</string>
    <string name="voltage">Voltage</string>
    <string name="watt">\u0020W</string>

    <!-- App Power Consumption Dialog -->
    <string name="close">Close</string>
    <string name="current_session_summary">Current Session Summary</string>
    <string name="discharge_session_duration">Duration:</string>
    <string name="estimated_total_consumption">Est. Total:</string>
    <string name="app_power_consumption_error">Unable to load app power consumption data</string>
    <string name="no_app_usage_data">No app usage data available for this session</string>
    <string name="usage_access_required">Usage Access Required</string>
    <string name="usage_access_explanation">To show which apps are consuming battery power, this app needs access to usage statistics. This helps estimate battery consumption during your current session.</string>
    <string name="grant_permission">Grant Permission</string>
    <string name="skip_for_now">Skip for now</string>
    <string name="estimated">estimated</string>
    <string name="permission_status_checking">Checking permission status...</string>
    <string name="permission_status_navigating">Opening settings page...</string>
    <string name="permission_status_waiting">Waiting for permission grant...</string>
    <string name="wear_rate">Degree of wear</string>
    <string name="what_time">What is this time?</string>
    <string name="work_in_background">Work in the background</string>
    <string name="write_me">Contact us</string>
    <string name="yes">Yes</string>
    <string name="zero">0</string>
    <string name="zero_seconds">0s</string>
    <string name="_hinese_traditional_lang_res_0x7f130305">中文 (繁體)</string>
    <!-- TODO: Remove or change this placeholder text -->
    <string name="calculated_for">Calculated for %s sessions</string>
    <string name="premium">Premium</string>
    <string name="animation">Animation</string>
    <string name="no_video_url_provided">No video URL provided</string>
    <string name="cannot_load_video">Cannot load video</string>
    <string name="apply">Apply</string>
    <string name="overlay_permission_title">Overlay Permission Required</string>
    <string name="overlay_permission_message">We need permission to display the animation over other apps, including on the lock screen while charging.</string>
    <string name="overlay_permission_denied">Overlay permission denied. Cannot proceed.</string>
    <string name="saving_video">Saving video, please wait...</string>
    <string name="charging_animation">Charging animation</string>
    <string name="video_saved">Video saved</string>
    <string name="animation_info_desc">Animation is a feature to display a custom battery charging animation. It appears when your device is charging and can be dismissed by tapping on it. You can also disable this feature in Settings.</string>
    <string name="show_date_and_time">Show date and time</string>
    <string name="overlay_will_appear_when_charging">Overlay will appear when charging.</string>
    <string name="anti_thief">Anti-Theft</string>
    <string name="play_warning_sound">Play warning sound</string>
    <string name="anti_thief_info">The Anti-Theft feature helps protect your device while charging. If the charger is disconnected and the correct password is not entered within 5 seconds, a loud warning sound will play to alert you. You can enable or disable this feature in the app settings.</string>
    <string name="password">Password</string>
    <string name="set_a_password_to_turn_off_the_anti_thief_alert_when_charging_is_disconnected">Set a password to turn off the anti-theft alert when charging is disconnected.</string>
    <string name="enter_password">Enter password</string>
    <string name="incorrect_password">Incorrect password!</string>
    <string name="privacy_setting">Privacy Setting</string>
    <string name="apply_for_24hrs">Apply for 24hrs</string>
    <string name="time_remaining">Time remaining</string>
    <string name="applied">Applied</string>
    <string name="ads_not_available">Ads not available</string>
    <string name="charging_indicator"> (Charging)</string>
    
    <!-- New Charge feature string resources -->
    <string name="battery_wear">Battery Wear</string>
    <string name="information">Information</string>
    <string name="target_percentage">Target percentage:</string>
    <string name="charge_to_percent_better">Charging to a lower percentage helps extend battery life.</string>
    <string name="charging_status">Charging Status</string>
    <string name="voltage_label">Voltage:</string>
    <string name="power_label">Power:</string>
    <string name="amperage_label">Amperage:</string>
    <string name="temperature_label">Temperature:</string>
    <string name="current_charging_speed">Current charging speed:</string>
    <string name="remaining_charging_time">Remaining Charging Time</string>
    <string name="time_to_target_percent" formatted="false">Time to %d%:</string>
    <string name="time_to_full">Time to full (100%):</string>
    <string name="current_charge_session">Current Session</string>
    <string name="session_duration">Session duration:</string>
    <string name="current_rate">Current rate:</string>
    <string name="avg_speed_screen_on">Avg. speed (screen on):</string>
    <string name="avg_speed_screen_off">Avg. speed (screen off):</string>
    <string name="avg_speed_mixed">Avg. speed (mixed):</string>
    <string name="total_charged_percent">Total charged:</string>
    <string name="total_charged_mah">Total charged (mAh):</string>
    <string name="screen_on_time">Screen on time:</string>
    <string name="screen_off_time">Screen off time:</string>
    <string name="reset_session">Reset Session</string>
    
    <!-- New Discharge feature string resources -->
    <string name="charging_message">Device is currently charging. Discharge statistics will be available when the device is unplugged.</string>

    <!-- Emoji Battery feature string resources -->
    <string name="emoji_battery_feature">Emoji Battery</string>
    <string name="emoji_battery_description">Replace your status bar battery icon with fun emoji styles</string>
    <string name="permissions_required">Permissions required to enable this feature</string>
    <string name="grant_permissions">Grant</string>
    <string name="all_styles">All</string>
    <string name="popular">Popular</string>
    <string name="free">Free</string>
    <string name="hot">HOT</string>
    <string name="search_styles">Search styles...</string>
    <string name="loading_styles">Loading styles...</string>
    <string name="no_styles_found">No styles found</string>
    <string name="no_styles_message">Check your internet connection and try again</string>
    <string name="no_styles_match_filter">No styles match your filter</string>
    <string name="try_different_filter">Try adjusting your search or filter settings</string>
    <string name="retry">Retry</string>
    <string name="customize">Customize</string>
    <string name="battery_image">Battery image</string>
    <string name="emoji_image">Emoji image</string>

    <!-- Phase 3 Customization Screen strings -->
    <string name="live_preview">Live Preview</string>
    <string name="preview_battery_level">Preview Battery Level</string>
    <string name="style_selection">Style Selection</string>
    <string name="battery_container">Battery Container</string>
    <string name="emoji_character">Emoji Character</string>
    <string name="customization_options">Customization Options</string>
    <string name="show_emoji">Show Emoji</string>
    <string name="show_percentage">Show Percentage</string>
    <string name="emoji_size">Emoji Size</string>
    <string name="percentage_font_size">Percentage Font Size</string>
    <string name="percentage_color">Percentage Color</string>
    <string name="reset">Reset</string>
    <string name="save">Save</string>
    <string name="loading">Loading</string>
    <string name="battery_container_image">Battery container image</string>
    <string name="selected_indicator">Selected indicator</string>
    <string name="premium_content">Premium content</string>
    <string name="emoji_character_image">Emoji character image</string>
</resources>