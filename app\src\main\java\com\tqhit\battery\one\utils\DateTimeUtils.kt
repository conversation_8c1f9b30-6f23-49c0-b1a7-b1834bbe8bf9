package com.tqhit.battery.one.utils

import java.text.SimpleDateFormat
import java.util.*

object DateTimeUtils {
    fun getCurrentTimeString(): String {
        val sdf = SimpleDateFormat("HH:mm", Locale.getDefault())
        return sdf.format(Date())
    }

    fun getCurrentDateString(): String {
        val sdf = SimpleDateFormat("EEE, d MMM", Locale.getDefault())
        return sdf.format(Date())
    }

    fun formatMillisToTimeString(milliseconds: Long): String {
        val totalSeconds = milliseconds / 1000
        val hours = totalSeconds / 3600
        val minutes = (totalSeconds % 3600) / 60
        val seconds = totalSeconds % 60

        return String.format("%02d:%02d:%02ds", hours, minutes, seconds)
    }
}