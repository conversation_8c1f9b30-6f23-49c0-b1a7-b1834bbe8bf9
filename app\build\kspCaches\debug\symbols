{"src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\cache\\CurrentSessionCache.kt": ["CurrentSessionCache:com.tqhit.battery.one.features.stats.discharge.cache", "getCurrentSession:com.tqhit.battery.one.features.stats.discharge.cache.CurrentSessionCache", "clearCurrentSession:com.tqhit.battery.one.features.stats.discharge.cache.CurrentSessionCache", "saveCurrentSession:com.tqhit.battery.one.features.stats.discharge.cache.CurrentSessionCache"], "src\\main\\java\\com\\tqhit\\battery\\one\\dialog\\utils\\NotificationDialog.kt": ["initWindow:com.tqhit.battery.one.dialog.utils.NotificationDialog", "setupListener:com.tqhit.battery.one.dialog.utils.NotificationDialog", "binding:com.tqhit.battery.one.dialog.utils.NotificationDialog", "setupUI:com.tqhit.battery.one.dialog.utils.NotificationDialog", "NotificationDialog:com.tqhit.battery.one.dialog.utils"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_features_stats_discharge_presentation_DischargeViewModel_HiltModules_BindsModule.java": ["_com_tqhit_battery_one_features_stats_discharge_presentation_DischargeViewModel_HiltModules_BindsModule:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_tqhit_battery_one_features_stats_discharge_presentation_DischargeViewModel_HiltModules_BindsModule"], "src\\main\\java\\com\\tqhit\\battery\\one\\activity\\splash\\SplashActivity.kt": ["setupData:com.tqhit.battery.one.activity.splash.SplashActivity", "binding:com.tqhit.battery.one.activity.splash.SplashActivity", "SplashActivity:com.tqhit.battery.one.activity.splash", "<init>:com.tqhit.battery.one.activity.splash.SplashActivity", "<init>:com.tqhit.battery.one.activity.splash.SplashActivity.Companion", "onCreate:com.tqhit.battery.one.activity.splash.SplashActivity", "Companion:com.tqhit.battery.one.activity.splash.SplashActivity"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\repository\\AppRepository_Factory.java": ["AppRepository_Factory:com.tqhit.battery.one.repository", "create:com.tqhit.battery.one.repository.AppRepository_Factory", "get:com.tqhit.battery.one.repository.AppRepository_Factory", "newInstance:com.tqhit.battery.one.repository.AppRepository_Factory", "<init>:com.tqhit.battery.one.repository.AppRepository_Factory"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\service\\Hilt_EnhancedDischargeTimerService.java": ["<init>:com.tqhit.battery.one.features.stats.discharge.service.Hilt_EnhancedDischargeTimerService", "generatedComponent:com.tqhit.battery.one.features.stats.discharge.service.Hilt_EnhancedDischargeTimerService", "componentManager:com.tqhit.battery.one.features.stats.discharge.service.Hilt_EnhancedDischargeTimerService", "inject:com.tqhit.battery.one.features.stats.discharge.service.Hilt_EnhancedDischargeTimerService", "Hilt_EnhancedDischargeTimerService:com.tqhit.battery.one.features.stats.discharge.service", "onCreate:com.tqhit.battery.one.features.stats.discharge.service.Hilt_EnhancedDischargeTimerService", "createComponentManager:com.tqhit.battery.one.features.stats.discharge.service.Hilt_EnhancedDischargeTimerService"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\tqhit\\battery\\one\\viewmodel\\AppViewModel_HiltModules_KeyModule_ProvideFactory.java": ["create:com.tqhit.battery.one.viewmodel.AppViewModel_HiltModules_KeyModule_ProvideFactory", "AppViewModel_HiltModules_KeyModule_ProvideFactory:com.tqhit.battery.one.viewmodel", "get:com.tqhit.battery.one.viewmodel.AppViewModel_HiltModules_KeyModule_ProvideFactory", "INSTANCE:com.tqhit.battery.one.viewmodel.AppViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder", "<init>:com.tqhit.battery.one.viewmodel.AppViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder", "<init>:com.tqhit.battery.one.viewmodel.AppViewModel_HiltModules_KeyModule_ProvideFactory", "provide:com.tqhit.battery.one.viewmodel.AppViewModel_HiltModules_KeyModule_ProvideFactory"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\emoji\\presentation\\gallery\\BatteryGalleryViewModel.kt": ["clearNavigationEvent:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModel", "<init>:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModel.Companion", "handleEvent:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModel", "BatteryGalleryViewModel:com.tqhit.battery.one.features.emoji.presentation.gallery", "uiState:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModel", "Companion:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModel"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\tqhit\\battery\\one\\viewmodel\\animation\\AnimationViewModel_HiltModules_KeyModule_Provide_LazyMapKey.java": ["<init>:com.tqhit.battery.one.viewmodel.animation.AnimationViewModel_HiltModules_KeyModule_Provide_LazyMapKey", "lazyClassKeyName:com.tqhit.battery.one.viewmodel.animation.AnimationViewModel_HiltModules_KeyModule_Provide_LazyMapKey", "AnimationViewModel_HiltModules_KeyModule_Provide_LazyMapKey:com.tqhit.battery.one.viewmodel.animation", "keepFieldType:com.tqhit.battery.one.viewmodel.animation.AnimationViewModel_HiltModules_KeyModule_Provide_LazyMapKey"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\tqhit\\battery\\one\\databinding\\ActivityTestBinding.java": ["checkServiceButton:com.tqhit.battery.one.databinding.ActivityTestBinding", "bind:com.tqhit.battery.one.databinding.ActivityTestBinding", "startServiceButton:com.tqhit.battery.one.databinding.ActivityTestBinding", "inflate:com.tqhit.battery.one.databinding.ActivityTestBinding", "serviceStatusText:com.tqhit.battery.one.databinding.ActivityTestBinding", "stopServiceButton:com.tqhit.battery.one.databinding.ActivityTestBinding", "fragmentContainer:com.tqhit.battery.one.databinding.ActivityTestBinding", "testControlsLayout:com.tqhit.battery.one.databinding.ActivityTestBinding", "ActivityTestBinding:com.tqhit.battery.one.databinding", "getRoot:com.tqhit.battery.one.databinding.ActivityTestBinding"], "src\\main\\java\\com\\tqhit\\battery\\one\\activity\\animation\\AnimationActivity.kt": ["applovinRewardedAdManager:com.tqhit.battery.one.activity.animation.AnimationActivity", "setupData:com.tqhit.battery.one.activity.animation.AnimationActivity", "appRepository:com.tqhit.battery.one.activity.animation.AnimationActivity", "AnimationActivity:com.tqhit.battery.one.activity.animation", "remoteConfigHelper:com.tqhit.battery.one.activity.animation.AnimationActivity", "setupListener:com.tqhit.battery.one.activity.animation.AnimationActivity", "binding:com.tqhit.battery.one.activity.animation.AnimationActivity", "onDestroy:com.tqhit.battery.one.activity.animation.AnimationActivity", "setupUI:com.tqhit.battery.one.activity.animation.AnimationActivity", "<init>:com.tqhit.battery.one.activity.animation.AnimationActivity"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_fragment_main_HealthFragment_GeneratedInjector.java": ["_com_tqhit_battery_one_fragment_main_HealthFragment_GeneratedInjector:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_tqhit_battery_one_fragment_main_HealthFragment_GeneratedInjector"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\health\\domain\\GetHealthHistoryUseCase.kt": ["getTemperatureHistory:com.tqhit.battery.one.features.stats.health.domain.GetHealthHistoryUseCase", "<init>:com.tqhit.battery.one.features.stats.health.domain.GetHealthHistoryUseCase.Companion", "getDailyWearData:com.tqhit.battery.one.features.stats.health.domain.GetHealthHistoryUseCase", "getCompleteChartData:com.tqhit.battery.one.features.stats.health.domain.GetHealthHistoryUseCase", "Companion:com.tqhit.battery.one.features.stats.health.domain.GetHealthHistoryUseCase", "GetHealthHistoryUseCase:com.tqhit.battery.one.features.stats.health.domain", "getBatteryPercentageHistory:com.tqhit.battery.one.features.stats.health.domain.GetHealthHistoryUseCase", "validateChartData:com.tqhit.battery.one.features.stats.health.domain.GetHealthHistoryUseCase"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\activity\\starting\\StartingActivity_GeneratedInjector.java": ["StartingActivity_GeneratedInjector:com.tqhit.battery.one.activity.starting", "injectStartingActivity:com.tqhit.battery.one.activity.starting.StartingActivity_GeneratedInjector"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\health\\domain\\CalculateBatteryHealthUseCase.kt": ["calculateDegradationRate:com.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCase", "<init>:com.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCase.Companion", "calculateSingularHealth:com.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCase", "isFullChargeCycle:com.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCase", "calculateEffectiveCapacity:com.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCase", "calculateHealthStatus:com.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCase", "calculateCumulativeHealth:com.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCase", "Companion:com.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCase", "CalculateBatteryHealthUseCase:com.tqhit.battery.one.features.stats.health.domain", "estimateRemainingLifespan:com.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCase"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_activity_animation_AnimationActivity_GeneratedInjector.java": ["<init>:hilt_aggregated_deps._com_tqhit_battery_one_activity_animation_AnimationActivity_GeneratedInjector", "_com_tqhit_battery_one_activity_animation_AnimationActivity_GeneratedInjector:hilt_aggregated_deps"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\tqhit\\battery\\one\\databinding\\ActivitySplashBinding.java": ["inflate:com.tqhit.battery.one.databinding.ActivitySplashBinding", "tvAppName:com.tqhit.battery.one.databinding.ActivitySplashBinding", "bind:com.tqhit.battery.one.databinding.ActivitySplashBinding", "ivLogo:com.tqhit.battery.one.databinding.ActivitySplashBinding", "ActivitySplashBinding:com.tqhit.battery.one.databinding", "getRoot:com.tqhit.battery.one.databinding.ActivitySplashBinding"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\tqhit\\battery\\one\\databinding\\ItemSlideLayout1Binding.java": ["resetSessionChargeLayout:com.tqhit.battery.one.databinding.ItemSlideLayout1Binding", "textView14:com.tqhit.battery.one.databinding.ItemSlideLayout1Binding", "ItemSlideLayout1Binding:com.tqhit.battery.one.databinding", "bind:com.tqhit.battery.one.databinding.ItemSlideLayout1Binding", "nextPage:com.tqhit.battery.one.databinding.ItemSlideLayout1Binding", "button:com.tqhit.battery.one.databinding.ItemSlideLayout1Binding", "main:com.tqhit.battery.one.databinding.ItemSlideLayout1Binding", "inflate:com.tqhit.battery.one.databinding.ItemSlideLayout1Binding", "text5:com.tqhit.battery.one.databinding.ItemSlideLayout1Binding", "textView:com.tqhit.battery.one.databinding.ItemSlideLayout1Binding", "lenghtLayout:com.tqhit.battery.one.databinding.ItemSlideLayout1Binding", "textViewResetCharge:com.tqhit.battery.one.databinding.ItemSlideLayout1Binding", "logo:com.tqhit.battery.one.databinding.ItemSlideLayout1Binding", "textView4:com.tqhit.battery.one.databinding.ItemSlideLayout1Binding", "display:com.tqhit.battery.one.databinding.ItemSlideLayout1Binding", "getRoot:com.tqhit.battery.one.databinding.ItemSlideLayout1Binding", "textView2:com.tqhit.battery.one.databinding.ItemSlideLayout1Binding"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\corebattery\\data\\CoreBatteryStatus.kt": ["currentMicroAmperes:com.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus", "voltageMillivolts:com.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus", "temperatureCelsius:com.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus", "timestampEpochMillis:com.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus", "percentage:com.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus", "isCharging:com.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus", "<init>:com.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus.Companion", "createDefault:com.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus.Companion", "Companion:com.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus", "pluggedSource:com.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus", "logCreation:com.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus.Companion", "CoreBatteryStatus:com.tqhit.battery.one.features.stats.corebattery.data"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\emoji\\data\\datastore\\CustomizationDataStore.kt": ["Companion:com.tqhit.battery.one.features.emoji.data.datastore.CustomizationDataStore", "userCustomizationFlow:com.tqhit.battery.one.features.emoji.data.datastore.CustomizationDataStore", "userPreferencesFlow:com.tqhit.battery.one.features.emoji.data.datastore.CustomizationDataStore", "saveUserPreferences:com.tqhit.battery.one.features.emoji.data.datastore.CustomizationDataStore", "<init>:com.tqhit.battery.one.features.emoji.data.datastore.CustomizationDataStore.Companion", "clearAllData:com.tqhit.battery.one.features.emoji.data.datastore.CustomizationDataStore", "saveCustomizationConfig:com.tqhit.battery.one.features.emoji.data.datastore.CustomizationDataStore", "customizationConfigFlow:com.tqhit.battery.one.features.emoji.data.datastore.CustomizationDataStore", "saveUserCustomization:com.tqhit.battery.one.features.emoji.data.datastore.CustomizationDataStore", "CustomizationDataStore:com.tqhit.battery.one.features.emoji.data.datastore", "updatePermissionStates:com.tqhit.battery.one.features.emoji.data.datastore.CustomizationDataStore"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\tqhit\\battery\\one\\databinding\\DialogSelectThemeBinding.java": ["rg2rtwjk:com.tqhit.battery.one.databinding.DialogSelectThemeBinding", "czsx:com.tqhit.battery.one.databinding.DialogSelectThemeBinding", "rgrtwjk:com.tqhit.battery.one.databinding.DialogSelectThemeBinding", "amoledTheme:com.tqhit.battery.one.databinding.DialogSelectThemeBinding", "rgrtjk:com.tqhit.battery.one.databinding.DialogSelectThemeBinding", "textView20:com.tqhit.battery.one.databinding.DialogSelectThemeBinding", "rgrtwj1k:com.tqhit.battery.one.databinding.DialogSelectThemeBinding", "darkThemeInverted:com.tqhit.battery.one.databinding.DialogSelectThemeBinding", "strelka:com.tqhit.battery.one.databinding.DialogSelectThemeBinding", "amoledThemeInverted:com.tqhit.battery.one.databinding.DialogSelectThemeBinding", "dfsdfb1:com.tqhit.battery.one.databinding.DialogSelectThemeBinding", "f1221:com.tqhit.battery.one.databinding.DialogSelectThemeBinding", "greyThemeInverted:com.tqhit.battery.one.databinding.DialogSelectThemeBinding", "DialogSelectThemeBinding:com.tqhit.battery.one.databinding", "dgdsfg:com.tqhit.battery.one.databinding.DialogSelectThemeBinding", "sdfgsdf1g:com.tqhit.battery.one.databinding.DialogSelectThemeBinding", "exitTheme:com.tqhit.battery.one.databinding.DialogSelectThemeBinding", "sdfgsdfg:com.tqhit.battery.one.databinding.DialogSelectThemeBinding", "inflate:com.tqhit.battery.one.databinding.DialogSelectThemeBinding", "lightThemeInverted:com.tqhit.battery.one.databinding.DialogSelectThemeBinding", "darkTheme:com.tqhit.battery.one.databinding.DialogSelectThemeBinding", "getRoot:com.tqhit.battery.one.databinding.DialogSelectThemeBinding", "bind:com.tqhit.battery.one.databinding.DialogSelectThemeBinding", "greyTheme:com.tqhit.battery.one.databinding.DialogSelectThemeBinding", "auto:com.tqhit.battery.one.databinding.DialogSelectThemeBinding", "dfsdfb11:com.tqhit.battery.one.databinding.DialogSelectThemeBinding", "czsux:com.tqhit.battery.one.databinding.DialogSelectThemeBinding", "lightTheme:com.tqhit.battery.one.databinding.DialogSelectThemeBinding"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\domain\\TimeConverter.kt": ["formatDate:com.tqhit.battery.one.features.stats.discharge.domain.TimeConverter", "formatMillisToHoursMinutesSeconds:com.tqhit.battery.one.features.stats.discharge.domain.TimeConverter", "formatDateTime:com.tqhit.battery.one.features.stats.discharge.domain.TimeConverter", "TimeConverter:com.tqhit.battery.one.features.stats.discharge.domain", "formatMillisToMinutes:com.tqhit.battery.one.features.stats.discharge.domain.TimeConverter", "hoursToMillis:com.tqhit.battery.one.features.stats.discharge.domain.TimeConverter", "formatMillisToHoursMinutes:com.tqhit.battery.one.features.stats.discharge.domain.TimeConverter", "formatTimestamp:com.tqhit.battery.one.features.stats.discharge.domain.TimeConverter", "millisToHours:com.tqhit.battery.one.features.stats.discharge.domain.TimeConverter"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\tqhit\\battery\\one\\databinding\\ItemCategoryBinding.java": ["inflate:com.tqhit.battery.one.databinding.ItemCategoryBinding", "getRoot:com.tqhit.battery.one.databinding.ItemCategoryBinding", "categoryBlock:com.tqhit.battery.one.databinding.ItemCategoryBinding", "categoryName:com.tqhit.battery.one.databinding.ItemCategoryBinding", "bind:com.tqhit.battery.one.databinding.ItemCategoryBinding", "ItemCategoryBinding:com.tqhit.battery.one.databinding"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\tqhit\\battery\\one\\databinding\\ItemSlideLayout5Binding.java": ["getRoot:com.tqhit.battery.one.databinding.ItemSlideLayout5Binding", "textPercent:com.tqhit.battery.one.databinding.ItemSlideLayout5Binding", "ItemSlideLayout5Binding:com.tqhit.battery.one.databinding", "textView4:com.tqhit.battery.one.databinding.ItemSlideLayout5Binding", "circularbar:com.tqhit.battery.one.databinding.ItemSlideLayout5Binding", "bind:com.tqhit.battery.one.databinding.ItemSlideLayout5Binding", "p5:com.tqhit.battery.one.databinding.ItemSlideLayout5Binding", "button:com.tqhit.battery.one.databinding.ItemSlideLayout5Binding", "switchInfo:com.tqhit.battery.one.databinding.ItemSlideLayout5Binding", "nextPage:com.tqhit.battery.one.databinding.ItemSlideLayout5Binding", "p11:com.tqhit.battery.one.databinding.ItemSlideLayout5Binding", "inflate:com.tqhit.battery.one.databinding.ItemSlideLayout5Binding"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\tqhit\\battery\\one\\features\\emoji\\presentation\\customize\\CustomizeViewModel_HiltModules_BindsModule_Binds_LazyMapKey.java": ["keepFieldType:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModel_HiltModules_BindsModule_Binds_LazyMapKey", "CustomizeViewModel_HiltModules_BindsModule_Binds_LazyMapKey:com.tqhit.battery.one.features.emoji.presentation.customize", "lazyClassKeyName:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModel_HiltModules_BindsModule_Binds_LazyMapKey", "<init>:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModel_HiltModules_BindsModule_Binds_LazyMapKey"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\presentation\\DischargeUiUpdater.kt": ["getStalenessMetrics:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeUiUpdater", "DischargeUiUpdater:com.tqhit.battery.one.features.stats.discharge.presentation", "updateCurrentSessionDetails:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeUiUpdater", "onFragmentPaused:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeUiUpdater", "onFragmentResumed:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeUiUpdater", "Companion:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeUiUpdater", "updateLossOfCharge:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeUiUpdater", "<init>:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeUiUpdater.Companion", "updateStatusAndEstimates:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeUiUpdater"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\emoji\\data\\repository\\CustomizationRepositoryImpl_Factory.java": ["<init>:com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImpl_Factory", "newInstance:com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImpl_Factory", "create:com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImpl_Factory", "CustomizationRepositoryImpl_Factory:com.tqhit.battery.one.features.emoji.data.repository", "get:com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImpl_Factory"], "src\\main\\java\\com\\tqhit\\battery\\one\\manager\\theme\\ThemeManager.kt": ["applyTheme:com.tqhit.battery.one.manager.theme.ThemeManager", "saveColor:com.tqhit.battery.one.manager.theme.ThemeManager", "saveTheme:com.tqhit.battery.one.manager.theme.ThemeManager", "<init>:com.tqhit.battery.one.manager.theme.ThemeManager", "ThemeManager:com.tqhit.battery.one.manager.theme", "initialize:com.tqhit.battery.one.manager.theme.ThemeManager", "getSelectedTheme:com.tqhit.battery.one.manager.theme.ThemeManager", "getThemeResourceId:com.tqhit.battery.one.manager.theme.ThemeManager", "getSelectedColor:com.tqhit.battery.one.manager.theme.ThemeManager"], "src\\main\\java\\com\\tqhit\\battery\\one\\dialog\\theme\\SelectColorDialog.kt": ["setupListener:com.tqhit.battery.one.dialog.theme.SelectColorDialog", "SelectColorDialog:com.tqhit.battery.one.dialog.theme", "initWindow:com.tqhit.battery.one.dialog.theme.SelectColorDialog", "binding:com.tqhit.battery.one.dialog.theme.SelectColorDialog"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\service\\EnhancedDischargeTimerServiceHelper.kt": ["stopService:com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerServiceHelper", "isServiceRunning:com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerServiceHelper", "startService:com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerServiceHelper", "<init>:com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerServiceHelper.Companion", "restartService:com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerServiceHelper", "Companion:com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerServiceHelper", "EnhancedDischargeTimerServiceHelper:com.tqhit.battery.one.features.stats.discharge.service", "getServiceStatus:com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerServiceHelper"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\dialog\\alarm\\SelectBatteryAlarmDialog_Factory.java": ["<init>:com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmDialog_Factory", "get:com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmDialog_Factory", "newInstance:com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmDialog_Factory", "SelectBatteryAlarmDialog_Factory:com.tqhit.battery.one.dialog.alarm", "create:com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmDialog_Factory"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\emoji\\domain\\use_case\\GetBatteryStylesUseCase_Factory.java": ["get:com.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCase_Factory", "GetBatteryStylesUseCase_Factory:com.tqhit.battery.one.features.emoji.domain.use_case", "newInstance:com.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCase_Factory", "create:com.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCase_Factory", "<init>:com.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCase_Factory"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\corebattery\\domain\\DefaultCoreBatteryStatsProvider_Factory.java": ["<init>:com.tqhit.battery.one.features.stats.corebattery.domain.DefaultCoreBatteryStatsProvider_Factory", "newInstance:com.tqhit.battery.one.features.stats.corebattery.domain.DefaultCoreBatteryStatsProvider_Factory", "get:com.tqhit.battery.one.features.stats.corebattery.domain.DefaultCoreBatteryStatsProvider_Factory", "DefaultCoreBatteryStatsProvider_Factory:com.tqhit.battery.one.features.stats.corebattery.domain", "create:com.tqhit.battery.one.features.stats.corebattery.domain.DefaultCoreBatteryStatsProvider_Factory", "INSTANCE:com.tqhit.battery.one.features.stats.corebattery.domain.DefaultCoreBatteryStatsProvider_Factory.InstanceHolder", "<init>:com.tqhit.battery.one.features.stats.corebattery.domain.DefaultCoreBatteryStatsProvider_Factory.InstanceHolder"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_activity_overlay_ChargingOverlayActivity_GeneratedInjector.java": ["_com_tqhit_battery_one_activity_overlay_ChargingOverlayActivity_GeneratedInjector:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_tqhit_battery_one_activity_overlay_ChargingOverlayActivity_GeneratedInjector"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\corebattery\\service\\CoreBatteryServiceHelper.kt": ["isServiceRunning:com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryServiceHelper", "stopService:com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryServiceHelper", "CoreBatteryServiceHelper:com.tqhit.battery.one.features.stats.corebattery.service", "<init>:com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryServiceHelper.Companion", "Companion:com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryServiceHelper", "startService:com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryServiceHelper", "restartService:com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryServiceHelper"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\service\\ChargingOverlayService_MembersInjector.java": ["injectAppRepository:com.tqhit.battery.one.service.ChargingOverlayService_MembersInjector", "create:com.tqhit.battery.one.service.ChargingOverlayService_MembersInjector", "<init>:com.tqhit.battery.one.service.ChargingOverlayService_MembersInjector", "injectMembers:com.tqhit.battery.one.service.ChargingOverlayService_MembersInjector", "injectAnimationRepository:com.tqhit.battery.one.service.ChargingOverlayService_MembersInjector", "ChargingOverlayService_MembersInjector:com.tqhit.battery.one.service"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\domain\\TimeConverter_Factory.java": ["<init>:com.tqhit.battery.one.features.stats.discharge.domain.TimeConverter_Factory.InstanceHolder", "INSTANCE:com.tqhit.battery.one.features.stats.discharge.domain.TimeConverter_Factory.InstanceHolder", "TimeConverter_Factory:com.tqhit.battery.one.features.stats.discharge.domain", "get:com.tqhit.battery.one.features.stats.discharge.domain.TimeConverter_Factory", "newInstance:com.tqhit.battery.one.features.stats.discharge.domain.TimeConverter_Factory", "<init>:com.tqhit.battery.one.features.stats.discharge.domain.TimeConverter_Factory", "create:com.tqhit.battery.one.features.stats.discharge.domain.TimeConverter_Factory"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\tqhit\\battery\\one\\features\\stats\\charge\\presentation\\StatsChargeViewModel_HiltModules_KeyModule_ProvideFactory.java": ["get:com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel_HiltModules_KeyModule_ProvideFactory", "provide:com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel_HiltModules_KeyModule_ProvideFactory", "<init>:com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel_HiltModules_KeyModule_ProvideFactory", "<init>:com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder", "INSTANCE:com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder", "create:com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel_HiltModules_KeyModule_ProvideFactory", "StatsChargeViewModel_HiltModules_KeyModule_ProvideFactory:com.tqhit.battery.one.features.stats.charge.presentation"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\tqhit\\battery\\one\\databinding\\LayoutDischargeSectionCurrentSessionDetailsBinding.java": ["csdTvCurrentRateValue:com.tqhit.battery.one.databinding.LayoutDischargeSectionCurrentSessionDetailsBinding", "csdTvAvgSpeedMahValue:com.tqhit.battery.one.databinding.LayoutDischargeSectionCurrentSessionDetailsBinding", "csdClCurrentRate:com.tqhit.battery.one.databinding.LayoutDischargeSectionCurrentSessionDetailsBinding", "csdTvScreenOnMahValue:com.tqhit.battery.one.databinding.LayoutDischargeSectionCurrentSessionDetailsBinding", "csdTvScreenOffPercentValue:com.tqhit.battery.one.databinding.LayoutDischargeSectionCurrentSessionDetailsBinding", "csdTvTotalTimeLabel:com.tqhit.battery.one.databinding.LayoutDischargeSectionCurrentSessionDetailsBinding", "csdClTotalConsumed:com.tqhit.battery.one.databinding.LayoutDischargeSectionCurrentSessionDetailsBinding", "csdTvSessionStartTime:com.tqhit.battery.one.databinding.LayoutDischargeSectionCurrentSessionDetailsBinding", "csdTvTitle:com.tqhit.battery.one.databinding.LayoutDischargeSectionCurrentSessionDetailsBinding", "csdTvScreenOffStatsLabel:com.tqhit.battery.one.databinding.LayoutDischargeSectionCurrentSessionDetailsBinding", "csdIvInfoButton:com.tqhit.battery.one.databinding.LayoutDischargeSectionCurrentSessionDetailsBinding", "inflate:com.tqhit.battery.one.databinding.LayoutDischargeSectionCurrentSessionDetailsBinding", "csdTvTotalTimeValue:com.tqhit.battery.one.databinding.LayoutDischargeSectionCurrentSessionDetailsBinding", "csdTvScreenOffMahValue:com.tqhit.battery.one.databinding.LayoutDischargeSectionCurrentSessionDetailsBinding", "csdTvScreenOnStatsLabel:com.tqhit.battery.one.databinding.LayoutDischargeSectionCurrentSessionDetailsBinding", "csdTvTotalConsumedPercentValue:com.tqhit.battery.one.databinding.LayoutDischargeSectionCurrentSessionDetailsBinding", "currentSessionDetailsRoot:com.tqhit.battery.one.databinding.LayoutDischargeSectionCurrentSessionDetailsBinding", "csdClScreenOnStats:com.tqhit.battery.one.databinding.LayoutDischargeSectionCurrentSessionDetailsBinding", "csdClTotalTime:com.tqhit.battery.one.databinding.LayoutDischargeSectionCurrentSessionDetailsBinding", "bind:com.tqhit.battery.one.databinding.LayoutDischargeSectionCurrentSessionDetailsBinding", "getRoot:com.tqhit.battery.one.databinding.LayoutDischargeSectionCurrentSessionDetailsBinding", "csdTvTotalConsumedMahValue:com.tqhit.battery.one.databinding.LayoutDischargeSectionCurrentSessionDetailsBinding", "csdTvTotalConsumedLabel:com.tqhit.battery.one.databinding.LayoutDischargeSectionCurrentSessionDetailsBinding", "csdTvAvgSpeedPercentValue:com.tqhit.battery.one.databinding.LayoutDischargeSectionCurrentSessionDetailsBinding", "csdTvTotalConsumedPercentUnitAndRange:com.tqhit.battery.one.databinding.LayoutDischargeSectionCurrentSessionDetailsBinding", "csdClScreenOffStats:com.tqhit.battery.one.databinding.LayoutDischargeSectionCurrentSessionDetailsBinding", "csdTvScreenOnPercentValue:com.tqhit.battery.one.databinding.LayoutDischargeSectionCurrentSessionDetailsBinding", "csdTvCurrentRateLabel:com.tqhit.battery.one.databinding.LayoutDischargeSectionCurrentSessionDetailsBinding", "csdTvAvgSpeedLabel:com.tqhit.battery.one.databinding.LayoutDischargeSectionCurrentSessionDetailsBinding", "LayoutDischargeSectionCurrentSessionDetailsBinding:com.tqhit.battery.one.databinding", "csdClAvgSpeed:com.tqhit.battery.one.databinding.LayoutDischargeSectionCurrentSessionDetailsBinding"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\service\\EnhancedDischargeTimerService_MembersInjector.java": ["EnhancedDischargeTimerService_MembersInjector:com.tqhit.battery.one.features.stats.discharge.service", "injectAppLifecycleManager:com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerService_MembersInjector", "injectDischargeSessionRepository:com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerService_MembersInjector", "injectMembers:com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerService_MembersInjector", "create:com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerService_MembersInjector", "injectScreenTimeValidationService:com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerService_MembersInjector", "<init>:com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerService_MembersInjector"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\tqhit\\battery\\one\\viewmodel\\AppViewModel_HiltModules_BindsModule_Binds_LazyMapKey.java": ["lazyClassKeyName:com.tqhit.battery.one.viewmodel.AppViewModel_HiltModules_BindsModule_Binds_LazyMapKey", "<init>:com.tqhit.battery.one.viewmodel.AppViewModel_HiltModules_BindsModule_Binds_LazyMapKey", "keepFieldType:com.tqhit.battery.one.viewmodel.AppViewModel_HiltModules_BindsModule_Binds_LazyMapKey", "AppViewModel_HiltModules_BindsModule_Binds_LazyMapKey:com.tqhit.battery.one.viewmodel"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\hilt_aggregated_deps\\_com_tqhit_battery_one_features_stats_discharge_di_StatsDischargeProvidersModule.java": ["_com_tqhit_battery_one_features_stats_discharge_di_StatsDischargeProvidersModule:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_tqhit_battery_one_features_stats_discharge_di_StatsDischargeProvidersModule"], "src\\main\\java\\com\\tqhit\\battery\\one\\dialog\\alarm\\SelectBatteryAlarmDialog.kt": ["binding:com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmDialog", "initWindow:com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmDialog", "SelectBatteryAlarmDialog:com.tqhit.battery.one.dialog.alarm", "setupListener:com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmDialog", "setupUI:com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmDialog"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\charge\\repository\\DefaultStatsChargeRepository_Factory.java": ["DefaultStatsChargeRepository_Factory:com.tqhit.battery.one.features.stats.charge.repository", "<init>:com.tqhit.battery.one.features.stats.charge.repository.DefaultStatsChargeRepository_Factory", "create:com.tqhit.battery.one.features.stats.charge.repository.DefaultStatsChargeRepository_Factory", "newInstance:com.tqhit.battery.one.features.stats.charge.repository.DefaultStatsChargeRepository_Factory", "get:com.tqhit.battery.one.features.stats.charge.repository.DefaultStatsChargeRepository_Factory"], "src\\main\\java\\com\\tqhit\\battery\\one\\repository\\AnimationRepository.kt": ["<init>:com.tqhit.battery.one.repository.AnimationRepository.Companion", "getApplied:com.tqhit.battery.one.repository.AnimationRepository", "mediaOriginal:com.tqhit.battery.one.repository.AnimationRepository.AnimationApplyEntry", "AnimationRepository:com.tqhit.battery.one.repository", "getTimeRemaining:com.tqhit.battery.one.repository.AnimationRepository", "getTrialEndTime:com.tqhit.battery.one.repository.AnimationRepository", "setApplied:com.tqhit.battery.one.repository.AnimationRepository", "getApplyList:com.tqhit.battery.one.repository.AnimationRepository", "setTrialEndTime:com.tqhit.battery.one.repository.AnimationRepository", "isApplied:com.tqhit.battery.one.repository.AnimationRepository", "AnimationApplyEntry:com.tqhit.battery.one.repository.AnimationRepository", "clearAnimation:com.tqhit.battery.one.repository.AnimationRepository", "setApplyList:com.tqhit.battery.one.repository.AnimationRepository", "endTime:com.tqhit.battery.one.repository.AnimationRepository.AnimationApplyEntry", "Companion:com.tqhit.battery.one.repository.AnimationRepository", "applyAnimation:com.tqhit.battery.one.repository.AnimationRepository", "getEntry:com.tqhit.battery.one.repository.AnimationRepository", "isExpired:com.tqhit.battery.one.repository.AnimationRepository"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\tqhit\\battery\\one\\databinding\\SectionChargeStatusDetailsBinding.java": ["chargeStatusDetailsRoot:com.tqhit.battery.one.databinding.SectionChargeStatusDetailsBinding", "inflate:com.tqhit.battery.one.databinding.SectionChargeStatusDetailsBinding", "valVoltage:com.tqhit.battery.one.databinding.SectionChargeStatusDetailsBinding", "getRoot:com.tqhit.battery.one.databinding.SectionChargeStatusDetailsBinding", "valAmperage:com.tqhit.battery.one.databinding.SectionChargeStatusDetailsBinding", "valTemperature:com.tqhit.battery.one.databinding.SectionChargeStatusDetailsBinding", "valChargingSpeed:com.tqhit.battery.one.databinding.SectionChargeStatusDetailsBinding", "bind:com.tqhit.battery.one.databinding.SectionChargeStatusDetailsBinding", "valPower:com.tqhit.battery.one.databinding.SectionChargeStatusDetailsBinding", "SectionChargeStatusDetailsBinding:com.tqhit.battery.one.databinding"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\emoji\\presentation\\customize\\Hilt_CustomizeFragment.java": ["generatedComponent:com.tqhit.battery.one.features.emoji.presentation.customize.Hilt_CustomizeFragment", "createComponentManager:com.tqhit.battery.one.features.emoji.presentation.customize.Hilt_CustomizeFragment", "inject:com.tqhit.battery.one.features.emoji.presentation.customize.Hilt_CustomizeFragment", "componentManager:com.tqhit.battery.one.features.emoji.presentation.customize.Hilt_CustomizeFragment", "Hilt_CustomizeFragment:com.tqhit.battery.one.features.emoji.presentation.customize", "getDefaultViewModelProviderFactory:com.tqhit.battery.one.features.emoji.presentation.customize.Hilt_CustomizeFragment", "onAttach:com.tqhit.battery.one.features.emoji.presentation.customize.Hilt_CustomizeFragment", "<init>:com.tqhit.battery.one.features.emoji.presentation.customize.Hilt_CustomizeFragment", "getContext:com.tqhit.battery.one.features.emoji.presentation.customize.Hilt_CustomizeFragment", "onGetLayoutInflater:com.tqhit.battery.one.features.emoji.presentation.customize.Hilt_CustomizeFragment"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\health\\di\\HealthDIModule.kt": ["<init>:com.tqhit.battery.one.features.stats.health.di.HealthDIModule", "bindHealthRepository:com.tqhit.battery.one.features.stats.health.di.HealthDIModule", "HealthDIModule:com.tqhit.battery.one.features.stats.health.di", "bindHealthCache:com.tqhit.battery.one.features.stats.health.di.HealthDIModule"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\manager\\graph\\BatteryHistoryManager_Factory.java": ["BatteryHistoryManager_Factory:com.tqhit.battery.one.manager.graph", "create:com.tqhit.battery.one.manager.graph.BatteryHistoryManager_Factory", "<init>:com.tqhit.battery.one.manager.graph.BatteryHistoryManager_Factory", "get:com.tqhit.battery.one.manager.graph.BatteryHistoryManager_Factory", "newInstance:com.tqhit.battery.one.manager.graph.BatteryHistoryManager_Factory"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\tqhit\\battery\\one\\features\\stats\\charge\\presentation\\StatsChargeViewModel_HiltModules_KeyModule_Provide_LazyMapKey.java": ["lazyClassKeyName:com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel_HiltModules_KeyModule_Provide_LazyMapKey", "keepFieldType:com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel_HiltModules_KeyModule_Provide_LazyMapKey", "StatsChargeViewModel_HiltModules_KeyModule_Provide_LazyMapKey:com.tqhit.battery.one.features.stats.charge.presentation", "<init>:com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel_HiltModules_KeyModule_Provide_LazyMapKey"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\charge\\presentation\\StatsChargeViewModel_HiltModules.java": ["binds:com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel_HiltModules.BindsModule", "StatsChargeViewModel_HiltModules:com.tqhit.battery.one.features.stats.charge.presentation", "BindsModule:com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel_HiltModules", "KeyModule:com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel_HiltModules", "provide:com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel_HiltModules.KeyModule"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\presentation\\DischargeViewModel_HiltModules.java": ["BindsModule:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeViewModel_HiltModules", "KeyModule:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeViewModel_HiltModules", "DischargeViewModel_HiltModules:com.tqhit.battery.one.features.stats.discharge.presentation", "provide:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeViewModel_HiltModules.KeyModule", "binds:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeViewModel_HiltModules.BindsModule"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\fragment\\main\\SettingsFragment_GeneratedInjector.java": ["SettingsFragment_GeneratedInjector:com.tqhit.battery.one.fragment.main", "injectSettingsFragment:com.tqhit.battery.one.fragment.main.SettingsFragment_GeneratedInjector"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\tqhit\\battery\\one\\databinding\\ActivityChargingOverlayBinding.java": ["textDate:com.tqhit.battery.one.databinding.ActivityChargingOverlayBinding", "batteryPercent:com.tqhit.battery.one.databinding.ActivityChargingOverlayBinding", "dateTimeContainer:com.tqhit.battery.one.databinding.ActivityChargingOverlayBinding", "bind:com.tqhit.battery.one.databinding.ActivityChargingOverlayBinding", "playerView:com.tqhit.battery.one.databinding.ActivityChargingOverlayBinding", "getRoot:com.tqhit.battery.one.databinding.ActivityChargingOverlayBinding", "ActivityChargingOverlayBinding:com.tqhit.battery.one.databinding", "textTime:com.tqhit.battery.one.databinding.ActivityChargingOverlayBinding", "inflate:com.tqhit.battery.one.databinding.ActivityChargingOverlayBinding"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\service\\BatteryMonitorService_GeneratedInjector.java": ["injectBatteryMonitorService:com.tqhit.battery.one.service.BatteryMonitorService_GeneratedInjector", "BatteryMonitorService_GeneratedInjector:com.tqhit.battery.one.service"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\navigation\\FragmentLifecycleOptimizer.kt": ["resumeCount:com.tqhit.battery.one.features.navigation.FragmentLifecycleOptimizer.FragmentState", "registerFragment:com.tqhit.battery.one.features.navigation.FragmentLifecycleOptimizer", "lastActiveTime:com.tqhit.battery.one.features.navigation.FragmentLifecycleOptimizer.FragmentState", "<init>:com.tqhit.battery.one.features.navigation.FragmentLifecycleOptimizer.Companion", "unregisterFragment:com.tqhit.battery.one.features.navigation.FragmentLifecycleOptimizer", "onFragmentVisible:com.tqhit.battery.one.features.navigation.FragmentLifecycleOptimizer", "FragmentLifecycleOptimizer:com.tqhit.battery.one.features.navigation", "clear:com.tqhit.battery.one.features.navigation.FragmentLifecycleOptimizer", "onFragmentHidden:com.tqhit.battery.one.features.navigation.FragmentLifecycleOptimizer", "isVisible:com.tqhit.battery.one.features.navigation.FragmentLifecycleOptimizer.FragmentState", "getLifecycleStats:com.tqhit.battery.one.features.navigation.FragmentLifecycleOptimizer", "Companion:com.tqhit.battery.one.features.navigation.FragmentLifecycleOptimizer", "<init>:com.tqhit.battery.one.features.navigation.FragmentLifecycleOptimizer", "pauseCount:com.tqhit.battery.one.features.navigation.FragmentLifecycleOptimizer.FragmentState"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\tqhit\\battery\\one\\databinding\\ItemBatteryOptionBinding.java": ["ItemBatteryOptionBinding:com.tqhit.battery.one.databinding", "loadingIndicator:com.tqhit.battery.one.databinding.ItemBatteryOptionBinding", "getRoot:com.tqhit.battery.one.databinding.ItemBatteryOptionBinding", "inflate:com.tqhit.battery.one.databinding.ItemBatteryOptionBinding", "batteryImageView:com.tqhit.battery.one.databinding.ItemBatteryOptionBinding", "premiumBadge:com.tqhit.battery.one.databinding.ItemBatteryOptionBinding", "bind:com.tqhit.battery.one.databinding.ItemBatteryOptionBinding", "selectionIndicator:com.tqhit.battery.one.databinding.ItemBatteryOptionBinding"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\activity\\password\\Hilt_EnterPasswordActivity.java": ["componentManager:com.tqhit.battery.one.activity.password.Hilt_EnterPasswordActivity", "onDestroy:com.tqhit.battery.one.activity.password.Hilt_EnterPasswordActivity", "inject:com.tqhit.battery.one.activity.password.Hilt_EnterPasswordActivity", "<init>:com.tqhit.battery.one.activity.password.Hilt_EnterPasswordActivity", "onCreate:com.tqhit.battery.one.activity.password.Hilt_EnterPasswordActivity", "getDefaultViewModelProviderFactory:com.tqhit.battery.one.activity.password.Hilt_EnterPasswordActivity", "Hilt_EnterPasswordActivity:com.tqhit.battery.one.activity.password", "generatedComponent:com.tqhit.battery.one.activity.password.Hilt_EnterPasswordActivity", "createComponentManager:com.tqhit.battery.one.activity.password.Hilt_EnterPasswordActivity"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\fragment\\main\\animation\\AnimationGridFragment_MembersInjector.java": ["<init>:com.tqhit.battery.one.fragment.main.animation.AnimationGridFragment_MembersInjector", "injectApplovinInterstitialAdManager:com.tqhit.battery.one.fragment.main.animation.AnimationGridFragment_MembersInjector", "injectMembers:com.tqhit.battery.one.fragment.main.animation.AnimationGridFragment_MembersInjector", "injectAppRepository:com.tqhit.battery.one.fragment.main.animation.AnimationGridFragment_MembersInjector", "AnimationGridFragment_MembersInjector:com.tqhit.battery.one.fragment.main.animation", "create:com.tqhit.battery.one.fragment.main.animation.AnimationGridFragment_MembersInjector", "injectRemoteConfigHelper:com.tqhit.battery.one.fragment.main.animation.AnimationGridFragment_MembersInjector"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\service\\Hilt_BatteryMonitorService.java": ["generatedComponent:com.tqhit.battery.one.service.Hilt_BatteryMonitorService", "<init>:com.tqhit.battery.one.service.Hilt_BatteryMonitorService", "onCreate:com.tqhit.battery.one.service.Hilt_BatteryMonitorService", "componentManager:com.tqhit.battery.one.service.Hilt_BatteryMonitorService", "inject:com.tqhit.battery.one.service.Hilt_BatteryMonitorService", "createComponentManager:com.tqhit.battery.one.service.Hilt_BatteryMonitorService", "Hilt_BatteryMonitorService:com.tqhit.battery.one.service"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\apppower\\permission\\UsageStatsPermissionManager_Factory.java": ["create:com.tqhit.battery.one.features.stats.apppower.permission.UsageStatsPermissionManager_Factory", "newInstance:com.tqhit.battery.one.features.stats.apppower.permission.UsageStatsPermissionManager_Factory", "get:com.tqhit.battery.one.features.stats.apppower.permission.UsageStatsPermissionManager_Factory", "UsageStatsPermissionManager_Factory:com.tqhit.battery.one.features.stats.apppower.permission", "<init>:com.tqhit.battery.one.features.stats.apppower.permission.UsageStatsPermissionManager_Factory"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\fragment\\main\\HealthFragment_GeneratedInjector.java": ["injectHealthFragment:com.tqhit.battery.one.fragment.main.HealthFragment_GeneratedInjector", "HealthFragment_GeneratedInjector:com.tqhit.battery.one.fragment.main"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\corebattery\\domain\\CoreBatteryStatsProvider.kt": ["DefaultCoreBatteryStatsProvider:com.tqhit.battery.one.features.stats.corebattery.domain", "updateStatus:com.tqhit.battery.one.features.stats.corebattery.domain.DefaultCoreBatteryStatsProvider", "CoreBatteryStatsProvider:com.tqhit.battery.one.features.stats.corebattery.domain", "coreBatteryStatusFlow:com.tqhit.battery.one.features.stats.corebattery.domain.DefaultCoreBatteryStatsProvider", "getCurrentStatus:com.tqhit.battery.one.features.stats.corebattery.domain.DefaultCoreBatteryStatsProvider", "Companion:com.tqhit.battery.one.features.stats.corebattery.domain.DefaultCoreBatteryStatsProvider", "coreBatteryStatusFlow:com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider", "getCurrentStatus:com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider", "updateStatus:com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider", "<init>:com.tqhit.battery.one.features.stats.corebattery.domain.DefaultCoreBatteryStatsProvider.Companion"], "src\\main\\java\\com\\tqhit\\battery\\one\\activity\\starting\\StartingActivity.kt": ["StartingActivity:com.tqhit.battery.one.activity.starting", "binding:com.tqhit.battery.one.activity.starting.StartingActivity", "setupData:com.tqhit.battery.one.activity.starting.StartingActivity", "<init>:com.tqhit.battery.one.activity.starting.StartingActivity", "setupListener:com.tqhit.battery.one.activity.starting.StartingActivity", "setupUI:com.tqhit.battery.one.activity.starting.StartingActivity", "applovinInterstitialAdManager:com.tqhit.battery.one.activity.starting.StartingActivity", "onResume:com.tqhit.battery.one.activity.starting.StartingActivity"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_service_ChargingOverlayService_GeneratedInjector.java": ["_com_tqhit_battery_one_service_ChargingOverlayService_GeneratedInjector:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_tqhit_battery_one_service_ChargingOverlayService_GeneratedInjector"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\health\\data\\HealthStatus.kt": ["calculationMode:com.tqhit.battery.one.features.stats.health.data.HealthStatus", "getDisplayName:com.tqhit.battery.one.features.stats.health.data.HealthCalculationMode", "HealthStatus:com.tqhit.battery.one.features.stats.health.data", "getDescription:com.tqhit.battery.one.features.stats.health.data.HealthCalculationMode", "effectiveCapacityMah:com.tqhit.battery.one.features.stats.health.data.HealthStatus", "CUMULATIVE:com.tqhit.battery.one.features.stats.health.data.HealthCalculationMode", "<init>:com.tqhit.battery.one.features.stats.health.data.HealthCalculationMode.SINGULAR", "getCapacityLossMah:com.tqhit.battery.one.features.stats.health.data.HealthStatus", "logCreation:com.tqhit.battery.one.features.stats.health.data.HealthStatus.Companion", "Companion:com.tqhit.battery.one.features.stats.health.data.HealthStatus", "SINGULAR:com.tqhit.battery.one.features.stats.health.data.HealthCalculationMode", "createDefault:com.tqhit.battery.one.features.stats.health.data.HealthStatus.Companion", "healthPercentage:com.tqhit.battery.one.features.stats.health.data.HealthStatus", "totalSessions:com.tqhit.battery.one.features.stats.health.data.HealthStatus", "HealthCalculationMode:com.tqhit.battery.one.features.stats.health.data", "isValid:com.tqhit.battery.one.features.stats.health.data.HealthStatus", "getDegradationPercentage:com.tqhit.battery.one.features.stats.health.data.HealthStatus", "<init>:com.tqhit.battery.one.features.stats.health.data.HealthCalculationMode.CUMULATIVE", "designCapacityMah:com.tqhit.battery.one.features.stats.health.data.HealthStatus", "<init>:com.tqhit.battery.one.features.stats.health.data.HealthStatus.Companion", "timestampEpochMillis:com.tqhit.battery.one.features.stats.health.data.HealthStatus", "createCalculated:com.tqhit.battery.one.features.stats.health.data.HealthStatus.Companion"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\emoji\\domain\\use_case\\SaveCustomizationUseCase_Factory.java": ["create:com.tqhit.battery.one.features.emoji.domain.use_case.SaveCustomizationUseCase_Factory", "SaveCustomizationUseCase_Factory:com.tqhit.battery.one.features.emoji.domain.use_case", "<init>:com.tqhit.battery.one.features.emoji.domain.use_case.SaveCustomizationUseCase_Factory", "newInstance:com.tqhit.battery.one.features.emoji.domain.use_case.SaveCustomizationUseCase_Factory", "get:com.tqhit.battery.one.features.emoji.domain.use_case.SaveCustomizationUseCase_Factory"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\health\\data\\HealthChartData.kt": ["getAverageDailyWear:com.tqhit.battery.one.features.stats.health.data.HealthChartData", "<init>:com.tqhit.battery.one.features.stats.health.data.HealthChartTimeRange.Companion", "hours:com.tqhit.battery.one.features.stats.health.data.HealthChartTimeRange", "<init>:com.tqhit.battery.one.features.stats.health.data.HealthChartTimeRange.EIGHT_HOURS", "TWELVE_HOURS:com.tqhit.battery.one.features.stats.health.data.HealthChartTimeRange", "HealthChartData:com.tqhit.battery.one.features.stats.health.data", "FOUR_HOURS:com.tqhit.battery.one.features.stats.health.data.HealthChartTimeRange", "getMaxTemperature:com.tqhit.battery.one.features.stats.health.data.HealthChartData", "logCreation:com.tqhit.battery.one.features.stats.health.data.HealthChartData.Companion", "getMinTemperature:com.tqhit.battery.one.features.stats.health.data.HealthChartData", "EIGHT_HOURS:com.tqhit.battery.one.features.stats.health.data.HealthChartTimeRange", "temperatureEntries:com.tqhit.battery.one.features.stats.health.data.HealthChartData", "getMinBatteryPercentage:com.tqhit.battery.one.features.stats.health.data.HealthChartData", "getMaxDailyWear:com.tqhit.battery.one.features.stats.health.data.HealthChartData", "TWENTY_FOUR_HOURS:com.tqhit.battery.one.features.stats.health.data.HealthChartTimeRange", "batteryPercentageEntries:com.tqhit.battery.one.features.stats.health.data.HealthChartData", "selectedTimeRangeHours:com.tqhit.battery.one.features.stats.health.data.HealthChartData", "<init>:com.tqhit.battery.one.features.stats.health.data.HealthChartData.Companion", "createEmpty:com.tqhit.battery.one.features.stats.health.data.HealthChartData.Companion", "isValid:com.tqhit.battery.one.features.stats.health.data.HealthChartData", "dailyWearData:com.tqhit.battery.one.features.stats.health.data.HealthChartData", "Companion:com.tqhit.battery.one.features.stats.health.data.HealthChartData", "getAllRanges:com.tqhit.battery.one.features.stats.health.data.HealthChartTimeRange.Companion", "<init>:com.tqhit.battery.one.features.stats.health.data.HealthChartTimeRange.TWENTY_FOUR_HOURS", "fromHours:com.tqhit.battery.one.features.stats.health.data.HealthChartTimeRange.Companion", "HealthChartTimeRange:com.tqhit.battery.one.features.stats.health.data", "displayName:com.tqhit.battery.one.features.stats.health.data.HealthChartTimeRange", "getMaxBatteryPercentage:com.tqhit.battery.one.features.stats.health.data.HealthChartData", "<init>:com.tqhit.battery.one.features.stats.health.data.HealthChartTimeRange.FOUR_HOURS", "<init>:com.tqhit.battery.one.features.stats.health.data.HealthChartTimeRange.TWELVE_HOURS", "timestampEpochMillis:com.tqhit.battery.one.features.stats.health.data.HealthChartData", "Companion:com.tqhit.battery.one.features.stats.health.data.HealthChartTimeRange", "createSample:com.tqhit.battery.one.features.stats.health.data.HealthChartData.Companion"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\tqhit\\battery\\one\\viewmodel\\animation\\AnimationViewModel_HiltModules_BindsModule_Binds_LazyMapKey.java": ["keepFieldType:com.tqhit.battery.one.viewmodel.animation.AnimationViewModel_HiltModules_BindsModule_Binds_LazyMapKey", "<init>:com.tqhit.battery.one.viewmodel.animation.AnimationViewModel_HiltModules_BindsModule_Binds_LazyMapKey", "AnimationViewModel_HiltModules_BindsModule_Binds_LazyMapKey:com.tqhit.battery.one.viewmodel.animation", "lazyClassKeyName:com.tqhit.battery.one.viewmodel.animation.AnimationViewModel_HiltModules_BindsModule_Binds_LazyMapKey"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\viewmodel\\animation\\AnimationViewModel_Factory.java": ["newInstance:com.tqhit.battery.one.viewmodel.animation.AnimationViewModel_Factory", "AnimationViewModel_Factory:com.tqhit.battery.one.viewmodel.animation", "<init>:com.tqhit.battery.one.viewmodel.animation.AnimationViewModel_Factory", "create:com.tqhit.battery.one.viewmodel.animation.AnimationViewModel_Factory", "get:com.tqhit.battery.one.viewmodel.animation.AnimationViewModel_Factory"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\charge\\domain\\CalculateSimpleChargeEstimateUseCase.kt": ["calculateTimeToTarget:com.tqhit.battery.one.features.stats.charge.domain.CalculateSimpleChargeEstimateUseCase", "Companion:com.tqhit.battery.one.features.stats.charge.domain.CalculateSimpleChargeEstimateUseCase", "calculateTimeToFull:com.tqhit.battery.one.features.stats.charge.domain.CalculateSimpleChargeEstimateUseCase", "execute:com.tqhit.battery.one.features.stats.charge.domain.CalculateSimpleChargeEstimateUseCase", "CalculateSimpleChargeEstimateUseCase:com.tqhit.battery.one.features.stats.charge.domain", "<init>:com.tqhit.battery.one.features.stats.charge.domain.CalculateSimpleChargeEstimateUseCase.Companion"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\health\\presentation\\HealthViewModel_Factory.java": ["HealthViewModel_Factory:com.tqhit.battery.one.features.stats.health.presentation", "<init>:com.tqhit.battery.one.features.stats.health.presentation.HealthViewModel_Factory", "newInstance:com.tqhit.battery.one.features.stats.health.presentation.HealthViewModel_Factory", "get:com.tqhit.battery.one.features.stats.health.presentation.HealthViewModel_Factory", "create:com.tqhit.battery.one.features.stats.health.presentation.HealthViewModel_Factory"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\tqhit\\battery\\one\\databinding\\ItemBatteryStyleBinding.java": ["actionButton:com.tqhit.battery.one.databinding.ItemBatteryStyleBinding", "percentagePreview:com.tqhit.battery.one.databinding.ItemBatteryStyleBinding", "previewContainer:com.tqhit.battery.one.databinding.ItemBatteryStyleBinding", "styleCard:com.tqhit.battery.one.databinding.ItemBatteryStyleBinding", "ItemBatteryStyleBinding:com.tqhit.battery.one.databinding", "styleNameText:com.tqhit.battery.one.databinding.ItemBatteryStyleBinding", "getRoot:com.tqhit.battery.one.databinding.ItemBatteryStyleBinding", "batteryImageView:com.tqhit.battery.one.databinding.ItemBatteryStyleBinding", "bind:com.tqhit.battery.one.databinding.ItemBatteryStyleBinding", "emojiImageView:com.tqhit.battery.one.databinding.ItemBatteryStyleBinding", "imageLoadingIndicator:com.tqhit.battery.one.databinding.ItemBatteryStyleBinding", "categoryText:com.tqhit.battery.one.databinding.ItemBatteryStyleBinding", "popularBadge:com.tqhit.battery.one.databinding.ItemBatteryStyleBinding", "premiumBadge:com.tqhit.battery.one.databinding.ItemBatteryStyleBinding", "inflate:com.tqhit.battery.one.databinding.ItemBatteryStyleBinding", "imageErrorIndicator:com.tqhit.battery.one.databinding.ItemBatteryStyleBinding"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\tqhit\\battery\\one\\databinding\\ActivityEnterPasswordBinding.java": ["bind:com.tqhit.battery.one.databinding.ActivityEnterPasswordBinding", "ActivityEnterPasswordBinding:com.tqhit.battery.one.databinding", "w22:com.tqhit.battery.one.databinding.ActivityEnterPasswordBinding", "getRoot:com.tqhit.battery.one.databinding.ActivityEnterPasswordBinding", "inflate:com.tqhit.battery.one.databinding.ActivityEnterPasswordBinding", "textInputEdit:com.tqhit.battery.one.databinding.ActivityEnterPasswordBinding", "confirmChangeCapacity:com.tqhit.battery.one.databinding.ActivityEnterPasswordBinding", "textView20:com.tqhit.battery.one.databinding.ActivityEnterPasswordBinding"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\service\\VibrationService_Factory.java": ["VibrationService_Factory:com.tqhit.battery.one.service", "create:com.tqhit.battery.one.service.VibrationService_Factory", "get:com.tqhit.battery.one.service.VibrationService_Factory", "newInstance:com.tqhit.battery.one.service.VibrationService_Factory", "<init>:com.tqhit.battery.one.service.VibrationService_Factory"], "src\\main\\java\\com\\tqhit\\battery\\one\\component\\progress\\VerticalProgressBar.kt": ["onSizeChanged:com.tqhit.battery.one.component.progress.VerticalProgressBar", "drawableStateChanged:com.tqhit.battery.one.component.progress.VerticalProgressBar", "onDraw:com.tqhit.battery.one.component.progress.VerticalProgressBar", "setProgress:com.tqhit.battery.one.component.progress.VerticalProgressBar", "onMeasure:com.tqhit.battery.one.component.progress.VerticalProgressBar", "VerticalProgressBar:com.tqhit.battery.one.component.progress"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\charge\\presentation\\StatsChargeFragment_MembersInjector.java": ["StatsChargeFragment_MembersInjector:com.tqhit.battery.one.features.stats.charge.presentation", "<init>:com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment_MembersInjector", "injectVibrationService:com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment_MembersInjector", "injectMembers:com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment_MembersInjector", "create:com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment_MembersInjector"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\tqhit\\battery\\one\\features\\stats\\health\\presentation\\HealthViewModel_HiltModules_BindsModule_Binds_LazyMapKey.java": ["<init>:com.tqhit.battery.one.features.stats.health.presentation.HealthViewModel_HiltModules_BindsModule_Binds_LazyMapKey", "keepFieldType:com.tqhit.battery.one.features.stats.health.presentation.HealthViewModel_HiltModules_BindsModule_Binds_LazyMapKey", "lazyClassKeyName:com.tqhit.battery.one.features.stats.health.presentation.HealthViewModel_HiltModules_BindsModule_Binds_LazyMapKey", "HealthViewModel_HiltModules_BindsModule_Binds_LazyMapKey:com.tqhit.battery.one.features.stats.health.presentation"], "src\\main\\java\\com\\tqhit\\battery\\one\\ads\\core\\ApplovinBannerAdManager.kt": ["ApplovinBannerAdManager:com.tqhit.battery.one.ads.core"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\fragment\\main\\DischargeFragment_GeneratedInjector.java": ["DischargeFragment_GeneratedInjector:com.tqhit.battery.one.fragment.main", "injectDischargeFragment:com.tqhit.battery.one.fragment.main.DischargeFragment_GeneratedInjector"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\tqhit\\battery\\one\\databinding\\ActivityDebugBinding.java": ["getRoot:com.tqhit.battery.one.databinding.ActivityDebugBinding", "inflate:com.tqhit.battery.one.databinding.ActivityDebugBinding", "ActivityDebugBinding:com.tqhit.battery.one.databinding", "btnTestDischarge:com.tqhit.battery.one.databinding.ActivityDebugBinding", "bind:com.tqhit.battery.one.databinding.ActivityDebugBinding", "btnTestCharge:com.tqhit.battery.one.databinding.ActivityDebugBinding"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\domain\\DischargeCalculator.kt": ["DischargeCalculator:com.tqhit.battery.one.features.stats.discharge.domain", "Companion:com.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculator", "DEFAULT_EFFECTIVE_CAPACITY_MAH:com.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculator.Companion", "MIXED_USAGE_SCREEN_OFF_WEIGHT:com.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculator.Companion", "calculateMixedDischargeRate:com.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculator", "DEFAULT_AVG_SCREEN_OFF_CURRENT_MA:com.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculator.Companion", "estimateTimeRemainingMillis:com.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculator", "DEFAULT_AVG_SCREEN_ON_CURRENT_MA:com.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculator.Companion", "<init>:com.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculator.Companion", "calculateCurrentCapacityMah:com.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculator", "MIXED_USAGE_SCREEN_ON_WEIGHT:com.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculator.Companion", "shouldSkipUpdate:com.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculator"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\tqhit\\battery\\one\\viewmodel\\battery\\BatteryViewModel_HiltModules_KeyModule_Provide_LazyMapKey.java": ["BatteryViewModel_HiltModules_KeyModule_Provide_LazyMapKey:com.tqhit.battery.one.viewmodel.battery", "<init>:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel_HiltModules_KeyModule_Provide_LazyMapKey", "lazyClassKeyName:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel_HiltModules_KeyModule_Provide_LazyMapKey", "keepFieldType:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel_HiltModules_KeyModule_Provide_LazyMapKey"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\tqhit\\battery\\one\\databinding\\LayoutDischargeSectionLossOfChargeBinding.java": ["locTvScreenOnPercentageUnit:com.tqhit.battery.one.databinding.LayoutDischargeSectionLossOfChargeBinding", "locTvScreenOnPercentageDropped:com.tqhit.battery.one.databinding.LayoutDischargeSectionLossOfChargeBinding", "locTvTitle:com.tqhit.battery.one.databinding.LayoutDischargeSectionLossOfChargeBinding", "lossOfChargeRoot:com.tqhit.battery.one.databinding.LayoutDischargeSectionLossOfChargeBinding", "locTvScreenOnMahConsumed:com.tqhit.battery.one.databinding.LayoutDischargeSectionLossOfChargeBinding", "locTvScreenOffPercentageUnit:com.tqhit.battery.one.databinding.LayoutDischargeSectionLossOfChargeBinding", "inflate:com.tqhit.battery.one.databinding.LayoutDischargeSectionLossOfChargeBinding", "bind:com.tqhit.battery.one.databinding.LayoutDischargeSectionLossOfChargeBinding", "locTvScreenOffPercentageDropped:com.tqhit.battery.one.databinding.LayoutDischargeSectionLossOfChargeBinding", "locBtnAppPowerConsumption:com.tqhit.battery.one.databinding.LayoutDischargeSectionLossOfChargeBinding", "locTvScreenOffTitle:com.tqhit.battery.one.databinding.LayoutDischargeSectionLossOfChargeBinding", "locTvScreenOnTitle:com.tqhit.battery.one.databinding.LayoutDischargeSectionLossOfChargeBinding", "locClScreenOnConsumption:com.tqhit.battery.one.databinding.LayoutDischargeSectionLossOfChargeBinding", "locTvScreenOnMahUnit:com.tqhit.battery.one.databinding.LayoutDischargeSectionLossOfChargeBinding", "locIvInfoButton:com.tqhit.battery.one.databinding.LayoutDischargeSectionLossOfChargeBinding", "locTvScreenOnTime:com.tqhit.battery.one.databinding.LayoutDischargeSectionLossOfChargeBinding", "locClScreenOffConsumption:com.tqhit.battery.one.databinding.LayoutDischargeSectionLossOfChargeBinding", "getRoot:com.tqhit.battery.one.databinding.LayoutDischargeSectionLossOfChargeBinding", "LayoutDischargeSectionLossOfChargeBinding:com.tqhit.battery.one.databinding", "locTvScreenOffMahConsumed:com.tqhit.battery.one.databinding.LayoutDischargeSectionLossOfChargeBinding", "locTvScreenOffTime:com.tqhit.battery.one.databinding.LayoutDischargeSectionLossOfChargeBinding", "locTvScreenOffMahUnit:com.tqhit.battery.one.databinding.LayoutDischargeSectionLossOfChargeBinding"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\emoji\\data\\repository\\BatteryStyleRepositoryImpl_Factory.java": ["newInstance:com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl_Factory", "get:com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl_Factory", "create:com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl_Factory", "BatteryStyleRepositoryImpl_Factory:com.tqhit.battery.one.features.emoji.data.repository", "<init>:com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl_Factory"], "src\\main\\java\\com\\tqhit\\battery\\one\\utils\\BatteryUtils.kt": ["getBatteryPolarity:com.tqhit.battery.one.utils.BatteryUtils", "BatteryUtils:com.tqhit.battery.one.utils", "formatTimeToHoursMinutesFromSeconds:com.tqhit.battery.one.utils.BatteryUtils", "isCharging:com.tqhit.battery.one.utils.BatteryUtils", "formatTimeToHoursMinutesFromMillis:com.tqhit.battery.one.utils.BatteryUtils", "formatElapsedTime:com.tqhit.battery.one.utils.BatteryUtils", "getBatteryCapacity:com.tqhit.battery.one.utils.BatteryUtils", "isIgnoringBatteryOptimizations:com.tqhit.battery.one.utils.BatteryUtils", "getTotalMinutesFromMillis:com.tqhit.battery.one.utils.BatteryUtils", "formatSessionTimeRange:com.tqhit.battery.one.utils.BatteryUtils", "calculateWearCycles:com.tqhit.battery.one.utils.BatteryUtils", "<init>:com.tqhit.battery.one.utils.BatteryUtils"], "src\\main\\java\\com\\tqhit\\battery\\one\\service\\VibrationService.kt": ["hasVibrator:com.tqhit.battery.one.service.VibrationService", "VibrationService:com.tqhit.battery.one.service", "vibrateTick:com.tqhit.battery.one.service.VibrationService", "PATTERN_WARNING:com.tqhit.battery.one.service.VibrationService.Companion", "vibrateClick:com.tqhit.battery.one.service.VibrationService", "VIBRATION_DURATION_SHORT:com.tqhit.battery.one.service.VibrationService.Companion", "VIBRATION_DURATION_MEDIUM:com.tqhit.battery.one.service.VibrationService.Companion", "vibrateError:com.tqhit.battery.one.service.VibrationService", "vibrateDoubleClick:com.tqhit.battery.one.service.VibrationService", "AMPLITUDE_MEDIUM:com.tqhit.battery.one.service.VibrationService.Companion", "PATTERN_TICK:com.tqhit.battery.one.service.VibrationService.Companion", "cancel:com.tqhit.battery.one.service.VibrationService", "PATTERN_SUCCESS:com.tqhit.battery.one.service.VibrationService.Companion", "AMPLITUDE_HEAVY:com.tqhit.battery.one.service.VibrationService.Companion", "vibrate:com.tqhit.battery.one.service.VibrationService", "Companion:com.tqhit.battery.one.service.VibrationService", "vibratePattern:com.tqhit.battery.one.service.VibrationService", "PATTERN_CLICK:com.tqhit.battery.one.service.VibrationService.Companion", "isVibrationAvailable:com.tqhit.battery.one.service.VibrationService", "vibrateSuccess:com.tqhit.battery.one.service.VibrationService", "vibrateCustomEffect:com.tqhit.battery.one.service.VibrationService", "vibrateWarning:com.tqhit.battery.one.service.VibrationService", "<init>:com.tqhit.battery.one.service.VibrationService.Companion", "isVibrationEnabled:com.tqhit.battery.one.service.VibrationService", "PATTERN_DOUBLE_CLICK:com.tqhit.battery.one.service.VibrationService.Companion", "VIBRATION_DURATION_LONG:com.tqhit.battery.one.service.VibrationService.Companion", "AMPLITUDE_LIGHT:com.tqhit.battery.one.service.VibrationService.Companion", "PATTERN_ERROR:com.tqhit.battery.one.service.VibrationService.Companion"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\domain\\ScreenStateTimeTracker.kt": ["Companion:com.tqhit.battery.one.features.stats.discharge.domain.ScreenStateTimeTracker", "getCurrentTimes:com.tqhit.battery.one.features.stats.discharge.domain.ScreenStateTimeTracker", "forceSetScreenState:com.tqhit.battery.one.features.stats.discharge.domain.ScreenStateTimeTracker", "reset:com.tqhit.battery.one.features.stats.discharge.domain.ScreenStateTimeTracker", "ScreenStateTimeTracker:com.tqhit.battery.one.features.stats.discharge.domain", "<init>:com.tqhit.battery.one.features.stats.discharge.domain.ScreenStateTimeTracker.Companion", "initialize:com.tqhit.battery.one.features.stats.discharge.domain.ScreenStateTimeTracker", "incrementCurrentState:com.tqhit.battery.one.features.stats.discharge.domain.ScreenStateTimeTracker", "forceSetScreenOffTime:com.tqhit.battery.one.features.stats.discharge.domain.ScreenStateTimeTracker", "applyGapEstimationResults:com.tqhit.battery.one.features.stats.discharge.domain.ScreenStateTimeTracker", "getDebugInfo:com.tqhit.battery.one.features.stats.discharge.domain.ScreenStateTimeTracker", "screenOffTimeUI:com.tqhit.battery.one.features.stats.discharge.domain.ScreenStateTimeTracker", "screenOnTimeUI:com.tqhit.battery.one.features.stats.discharge.domain.ScreenStateTimeTracker", "logCurrentStatus:com.tqhit.battery.one.features.stats.discharge.domain.ScreenStateTimeTracker", "handleScreenStateChange:com.tqhit.battery.one.features.stats.discharge.domain.ScreenStateTimeTracker", "<init>:com.tqhit.battery.one.features.stats.discharge.domain.ScreenStateTimeTracker", "forceSetTimes:com.tqhit.battery.one.features.stats.discharge.domain.ScreenStateTimeTracker"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\emoji\\di\\EmojiBatteryDIModule_Companion_ProvideGsonFactory.java": ["<init>:com.tqhit.battery.one.features.emoji.di.EmojiBatteryDIModule_Companion_ProvideGsonFactory", "EmojiBatteryDIModule_Companion_ProvideGsonFactory:com.tqhit.battery.one.features.emoji.di", "get:com.tqhit.battery.one.features.emoji.di.EmojiBatteryDIModule_Companion_ProvideGsonFactory", "provideGson:com.tqhit.battery.one.features.emoji.di.EmojiBatteryDIModule_Companion_ProvideGsonFactory", "INSTANCE:com.tqhit.battery.one.features.emoji.di.EmojiBatteryDIModule_Companion_ProvideGsonFactory.InstanceHolder", "<init>:com.tqhit.battery.one.features.emoji.di.EmojiBatteryDIModule_Companion_ProvideGsonFactory.InstanceHolder", "create:com.tqhit.battery.one.features.emoji.di.EmojiBatteryDIModule_Companion_ProvideGsonFactory"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_features_stats_corebattery_service_CoreBatteryStatsService_GeneratedInjector.java": ["<init>:hilt_aggregated_deps._com_tqhit_battery_one_features_stats_corebattery_service_CoreBatteryStatsService_GeneratedInjector", "_com_tqhit_battery_one_features_stats_corebattery_service_CoreBatteryStatsService_GeneratedInjector:hilt_aggregated_deps"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\service\\Hilt_ChargingOverlayService.java": ["Hilt_ChargingOverlayService:com.tqhit.battery.one.service", "inject:com.tqhit.battery.one.service.Hilt_ChargingOverlayService", "componentManager:com.tqhit.battery.one.service.Hilt_ChargingOverlayService", "generatedComponent:com.tqhit.battery.one.service.Hilt_ChargingOverlayService", "createComponentManager:com.tqhit.battery.one.service.Hilt_ChargingOverlayService", "onCreate:com.tqhit.battery.one.service.Hilt_ChargingOverlayService", "<init>:com.tqhit.battery.one.service.Hilt_ChargingOverlayService"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\domain\\FullSessionReEstimator_Factory.java": ["newInstance:com.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimator_Factory", "<init>:com.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimator_Factory", "create:com.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimator_Factory", "FullSessionReEstimator_Factory:com.tqhit.battery.one.features.stats.discharge.domain", "get:com.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimator_Factory"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\tqhit\\battery\\one\\databinding\\FragmentDischargeBinding.java": ["s6:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "timeDisSessionStart:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "operationSessionInfo:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "te99:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "stTexeet:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "updateViewBtn:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "allBlock:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "averageInfo:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "mah2:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "buttonDisschargeUsing:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "textSpeedDisNightSession:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "full3:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "infoDaySpeedSession:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "dayBlock:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "i2:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "dischargeNight:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "dischargeAll:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "te88:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "s5:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "textPercentDisNightSessionAwake:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "infoDayPercentSession:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "infoNightPercentSession:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "text226:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "dischargeSessionInfo:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "mah3:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "full2:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "textViewPercent:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "timeDaySession:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "f3:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "i1:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "s4:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "te77:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "textView9:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "textPercentDisSessionLast:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "getRoot:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "awakePercent:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "resetSessionDischargeButton:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "infoNightSpeedSession:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "stText:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "f2:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "textPercentDisDaySessionDeep:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "dischargeSpeedPercentAll:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "updateView:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "dischargeSessionPercent:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "dischargeSpeedMahAll:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "textSpeedDisDaySession:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "s3:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "scrollView:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "textNowDisSession:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "i21:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "textSpeedDisAllSession:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "dischargeFulltimeRemainingAll:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "dischargeTextPercent3:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "f1:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "dischargeSpeedMahNight:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "dischargeTextPercent:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "te7722:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "textView7:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "dischargeSpeedMahDay:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "iir:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "s2:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "dischargeSpeedPercentNight:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "textFulltimeDisSession:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "i12:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "batteryAlarmBtn:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "nightBlock:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "bind:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "te0:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "textPercentDisNightSession:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "timeNightSession:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "s1:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "indentDown:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "i11:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "fText:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "fullPercentInfo:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "i332:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "per1:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "dischargeFulltimeRemainingDay:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "textSpeedDisDaySession2:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "deepPercent:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "dischargeFulltimeRemainingNight:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "te88ww:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "FragmentDischargeBinding:com.tqhit.battery.one.databinding", "iText:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "full1:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "per2:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "dischargeRateInfo:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "s8:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "s7:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "mah1:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "inflate:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "dischargeSpeedPercentDay:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "i93:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "textPercentDisDaySession:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "dischargeSun:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "per3:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "iT:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "textPercentDisAllSession:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "i3:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "textSpeedDisNightSessionAwake:com.tqhit.battery.one.databinding.FragmentDischargeBinding", "textView6:com.tqhit.battery.one.databinding.FragmentDischargeBinding"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\charge\\repository\\StatsChargeRepository.kt": ["statsChargeStatusFlow:com.tqhit.battery.one.features.stats.charge.repository.DefaultStatsChargeRepository", "activeChargeSessionFlow:com.tqhit.battery.one.features.stats.charge.repository.StatsChargeRepository", "DefaultStatsChargeRepository:com.tqhit.battery.one.features.stats.charge.repository", "resetCurrentSession:com.tqhit.battery.one.features.stats.charge.repository.StatsChargeRepository", "statsChargeStatusFlow:com.tqhit.battery.one.features.stats.charge.repository.StatsChargeRepository", "<init>:com.tqhit.battery.one.features.stats.charge.repository.DefaultStatsChargeRepository.Companion", "activeChargeSessionFlow:com.tqhit.battery.one.features.stats.charge.repository.DefaultStatsChargeRepository", "StatsChargeRepository:com.tqhit.battery.one.features.stats.charge.repository", "Companion:com.tqhit.battery.one.features.stats.charge.repository.DefaultStatsChargeRepository", "resetCurrentSession:com.tqhit.battery.one.features.stats.charge.repository.DefaultStatsChargeRepository"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\tqhit\\battery\\one\\databinding\\ActivityVideoPlayerBinding.java": ["ActivityVideoPlayerBinding:com.tqhit.battery.one.databinding", "videoView:com.tqhit.battery.one.databinding.ActivityVideoPlayerBinding", "getRoot:com.tqhit.battery.one.databinding.ActivityVideoPlayerBinding", "inflate:com.tqhit.battery.one.databinding.ActivityVideoPlayerBinding", "bind:com.tqhit.battery.one.databinding.ActivityVideoPlayerBinding"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\charge\\data\\StatsChargeSession.kt": ["createNew:com.tqhit.battery.one.features.stats.charge.data.StatsChargeSession.Companion", "startPercentage:com.tqhit.battery.one.features.stats.charge.data.StatsChargeSession", "isActive:com.tqhit.battery.one.features.stats.charge.data.StatsChargeSession", "<init>:com.tqhit.battery.one.features.stats.charge.data.StatsChargeSession.Companion", "totalChargeMah:com.tqhit.battery.one.features.stats.charge.data.StatsChargeSession", "endPercentage:com.tqhit.battery.one.features.stats.charge.data.StatsChargeSession", "Companion:com.tqhit.battery.one.features.stats.charge.data.StatsChargeSession", "logUpdate:com.tqhit.battery.one.features.stats.charge.data.StatsChargeSession.Companion", "getPercentageCharged:com.tqhit.battery.one.features.stats.charge.data.StatsChargeSession", "totalChargePercentage:com.tqhit.battery.one.features.stats.charge.data.StatsChargeSession", "durationMillis:com.tqhit.battery.one.features.stats.charge.data.StatsChargeSession", "startTimeEpochMillis:com.tqhit.battery.one.features.stats.charge.data.StatsChargeSession", "endTimeEpochMillis:com.tqhit.battery.one.features.stats.charge.data.StatsChargeSession", "percentageCharged:com.tqhit.battery.one.features.stats.charge.data.StatsChargeSession", "StatsChargeSession:com.tqhit.battery.one.features.stats.charge.data", "logCreation:com.tqhit.battery.one.features.stats.charge.data.StatsChargeSession.Companion", "endSession:com.tqhit.battery.one.features.stats.charge.data.StatsChargeSession.Companion"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\repository\\BatteryRepository_Factory.java": ["<init>:com.tqhit.battery.one.features.stats.discharge.repository.BatteryRepository_Factory", "create:com.tqhit.battery.one.features.stats.discharge.repository.BatteryRepository_Factory", "BatteryRepository_Factory:com.tqhit.battery.one.features.stats.discharge.repository", "get:com.tqhit.battery.one.features.stats.discharge.repository.BatteryRepository_Factory", "newInstance:com.tqhit.battery.one.features.stats.discharge.repository.BatteryRepository_Factory"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\tqhit\\battery\\one\\databinding\\DialogChangeCapacityBinding.java": ["confirmChangeCapacity:com.tqhit.battery.one.databinding.DialogChangeCapacityBinding", "exitChangeCapacity:com.tqhit.battery.one.databinding.DialogChangeCapacityBinding", "DialogChangeCapacityBinding:com.tqhit.battery.one.databinding", "textInputEdit:com.tqhit.battery.one.databinding.DialogChangeCapacityBinding", "strelka:com.tqhit.battery.one.databinding.DialogChangeCapacityBinding", "bind:com.tqhit.battery.one.databinding.DialogChangeCapacityBinding", "sdfgsdfg1:com.tqhit.battery.one.databinding.DialogChangeCapacityBinding", "cancelChangeCapacity:com.tqhit.battery.one.databinding.DialogChangeCapacityBinding", "textView20:com.tqhit.battery.one.databinding.DialogChangeCapacityBinding", "w22:com.tqhit.battery.one.databinding.DialogChangeCapacityBinding", "getRoot:com.tqhit.battery.one.databinding.DialogChangeCapacityBinding", "inflate:com.tqhit.battery.one.databinding.DialogChangeCapacityBinding"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\emoji\\presentation\\customize\\view\\LivePreviewView.kt": ["onDraw:com.tqhit.battery.one.features.emoji.presentation.customize.view.LivePreviewView", "Companion:com.tqhit.battery.one.features.emoji.presentation.customize.view.LivePreviewView", "setBatteryLevel:com.tqhit.battery.one.features.emoji.presentation.customize.view.LivePreviewView", "clearPreview:com.tqhit.battery.one.features.emoji.presentation.customize.view.LivePreviewView", "updatePreview:com.tqhit.battery.one.features.emoji.presentation.customize.view.LivePreviewView", "<init>:com.tqhit.battery.one.features.emoji.presentation.customize.view.LivePreviewView.Companion", "setChargingState:com.tqhit.battery.one.features.emoji.presentation.customize.view.LivePreviewView", "LivePreviewView:com.tqhit.battery.one.features.emoji.presentation.customize.view", "onSizeChanged:com.tqhit.battery.one.features.emoji.presentation.customize.view.LivePreviewView"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\domain\\ScreenTimeCalculator.kt": ["offMahConsumed:com.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeCalculator.ScreenTimes", "BatteryRates:com.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeCalculator", "solveScreenTimes:com.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeCalculator", "onTimeMillis:com.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeCalculator.ScreenTimes", "screenOnTimeMillis:com.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeCalculator.ScreenTimeDeltas", "ScreenTimeDeltas:com.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeCalculator", "screenOffRate:com.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeCalculator.BatteryRates", "screenOffTimeMillis:com.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeCalculator.ScreenTimeDeltas", "ScreenTimes:com.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeCalculator", "offTimeHours:com.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeCalculator.ScreenTimes", "calculateScreenTimeDeltas:com.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeCalculator", "onMahConsumed:com.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeCalculator.ScreenTimes", "ScreenTimeCalculator:com.tqhit.battery.one.features.stats.discharge.domain", "calculateScreenTimes:com.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeCalculator", "onTimeHours:com.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeCalculator.ScreenTimes", "offTimeMillis:com.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeCalculator.ScreenTimes", "ratio:com.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeCalculator.BatteryRates", "screenOnRate:com.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeCalculator.BatteryRates"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\cache\\PrefsDischargeRatesCache.kt": ["getAverageScreenOnRateMah:com.tqhit.battery.one.features.stats.discharge.cache.PrefsDischargeRatesCache", "getAverageScreenOffRateMah:com.tqhit.battery.one.features.stats.discharge.cache.PrefsDischargeRatesCache", "PrefsDischargeRatesCache:com.tqhit.battery.one.features.stats.discharge.cache", "saveAverageScreenOffRateMah:com.tqhit.battery.one.features.stats.discharge.cache.PrefsDischargeRatesCache", "<init>:com.tqhit.battery.one.features.stats.discharge.cache.PrefsDischargeRatesCache.Companion", "saveAverageScreenOnRateMah:com.tqhit.battery.one.features.stats.discharge.cache.PrefsDischargeRatesCache", "Companion:com.tqhit.battery.one.features.stats.discharge.cache.PrefsDischargeRatesCache"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\repository\\DischargeSessionRepository.kt": ["processBatteryStatus:com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository", "cleanup:com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository", "<init>:com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository.Companion", "Companion:com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository", "screenOnTimeUI:com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository", "applyScreenTimeGapCorrection:com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository", "screenOffTimeUI:com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository", "incrementScreenTimeForUI:com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository", "forceCheckScreenState:com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository", "DischargeSessionRepository:com.tqhit.battery.one.features.stats.discharge.repository", "currentSession:com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository", "clearActiveSessionData:com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\emoji\\di\\EmojiBatteryDIModule.kt": ["EmojiBatteryDIModule:com.tqhit.battery.one.features.emoji.di", "Companion:com.tqhit.battery.one.features.emoji.di.EmojiBatteryDIModule", "<init>:com.tqhit.battery.one.features.emoji.di.EmojiBatteryDIModule.Companion", "bindBatteryStyleRepository:com.tqhit.battery.one.features.emoji.di.EmojiBatteryDIModule", "bindCustomizationRepository:com.tqhit.battery.one.features.emoji.di.EmojiBatteryDIModule", "<init>:com.tqhit.battery.one.features.emoji.di.EmojiBatteryDIModule"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\presentation\\DischargeViewModel_HiltModules_BindsModule_Binds_LazyMapKey.java": ["lazyClassKeyName:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeViewModel_HiltModules_BindsModule_Binds_LazyMapKey", "<init>:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeViewModel_HiltModules_BindsModule_Binds_LazyMapKey", "DischargeViewModel_HiltModules_BindsModule_Binds_LazyMapKey:com.tqhit.battery.one.features.stats.discharge.presentation", "keepFieldType:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeViewModel_HiltModules_BindsModule_Binds_LazyMapKey"], "src\\main\\java\\com\\tqhit\\battery\\one\\utils\\DeviceUtils.kt": ["<init>:com.tqhit.battery.one.utils.DeviceUtils", "isXiaomiDevice:com.tqhit.battery.one.utils.DeviceUtils", "applyDeviceSpecificAdjustments:com.tqhit.battery.one.utils.DeviceUtils", "handleMiuiAnimationCallback:com.tqhit.battery.one.utils.DeviceUtils", "DeviceUtils:com.tqhit.battery.one.utils"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\domain\\DischargeRateCalculator_Factory.java": ["DischargeRateCalculator_Factory:com.tqhit.battery.one.features.stats.discharge.domain", "create:com.tqhit.battery.one.features.stats.discharge.domain.DischargeRateCalculator_Factory", "<init>:com.tqhit.battery.one.features.stats.discharge.domain.DischargeRateCalculator_Factory", "newInstance:com.tqhit.battery.one.features.stats.discharge.domain.DischargeRateCalculator_Factory", "get:com.tqhit.battery.one.features.stats.discharge.domain.DischargeRateCalculator_Factory"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\viewmodel\\AppViewModel_Factory.java": ["create:com.tqhit.battery.one.viewmodel.AppViewModel_Factory", "AppViewModel_Factory:com.tqhit.battery.one.viewmodel", "<init>:com.tqhit.battery.one.viewmodel.AppViewModel_Factory", "get:com.tqhit.battery.one.viewmodel.AppViewModel_Factory", "newInstance:com.tqhit.battery.one.viewmodel.AppViewModel_Factory"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\service\\BatteryMonitorService_MembersInjector.java": ["injectAppRepository:com.tqhit.battery.one.service.BatteryMonitorService_MembersInjector", "create:com.tqhit.battery.one.service.BatteryMonitorService_MembersInjector", "BatteryMonitorService_MembersInjector:com.tqhit.battery.one.service", "<init>:com.tqhit.battery.one.service.BatteryMonitorService_MembersInjector", "injectMembers:com.tqhit.battery.one.service.BatteryMonitorService_MembersInjector", "injectBatteryRepository:com.tqhit.battery.one.service.BatteryMonitorService_MembersInjector"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\cache\\PrefsCurrentSessionCache.kt": ["PrefsCurrentSessionCache:com.tqhit.battery.one.features.stats.discharge.cache", "getCurrentSession:com.tqhit.battery.one.features.stats.discharge.cache.PrefsCurrentSessionCache", "clearCurrentSession:com.tqhit.battery.one.features.stats.discharge.cache.PrefsCurrentSessionCache", "saveCurrentSession:com.tqhit.battery.one.features.stats.discharge.cache.PrefsCurrentSessionCache", "Companion:com.tqhit.battery.one.features.stats.discharge.cache.PrefsCurrentSessionCache", "<init>:com.tqhit.battery.one.features.stats.discharge.cache.PrefsCurrentSessionCache.Companion"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\cache\\PrefsCurrentSessionCache_Factory.java": ["get:com.tqhit.battery.one.features.stats.discharge.cache.PrefsCurrentSessionCache_Factory", "newInstance:com.tqhit.battery.one.features.stats.discharge.cache.PrefsCurrentSessionCache_Factory", "<init>:com.tqhit.battery.one.features.stats.discharge.cache.PrefsCurrentSessionCache_Factory", "create:com.tqhit.battery.one.features.stats.discharge.cache.PrefsCurrentSessionCache_Factory", "PrefsCurrentSessionCache_Factory:com.tqhit.battery.one.features.stats.discharge.cache"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\ads\\core\\ApplovinInterstitialAdManager_Factory.java": ["ApplovinInterstitialAdManager_Factory:com.tqhit.battery.one.ads.core", "<init>:com.tqhit.battery.one.ads.core.ApplovinInterstitialAdManager_Factory", "create:com.tqhit.battery.one.ads.core.ApplovinInterstitialAdManager_Factory", "newInstance:com.tqhit.battery.one.ads.core.ApplovinInterstitialAdManager_Factory", "get:com.tqhit.battery.one.ads.core.ApplovinInterstitialAdManager_Factory"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\service\\EnhancedDischargeTimerService.kt": ["ACTION_START_SERVICE:com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerService.Companion", "appLifecycleManager:com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerService", "dischargeSessionRepository:com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerService", "<init>:com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerService.Companion", "onDestroy:com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerService", "onBind:com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerService", "ACTION_STOP_SERVICE:com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerService.Companion", "screenTimeValidationService:com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerService", "onCreate:com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerService", "EnhancedDischargeTimerService:com.tqhit.battery.one.features.stats.discharge.service", "Companion:com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerService", "onStartCommand:com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerService", "<init>:com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerService"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\activity\\animation\\AnimationActivity_MembersInjector.java": ["AnimationActivity_MembersInjector:com.tqhit.battery.one.activity.animation", "create:com.tqhit.battery.one.activity.animation.AnimationActivity_MembersInjector", "injectMembers:com.tqhit.battery.one.activity.animation.AnimationActivity_MembersInjector", "<init>:com.tqhit.battery.one.activity.animation.AnimationActivity_MembersInjector", "injectRemoteConfigHelper:com.tqhit.battery.one.activity.animation.AnimationActivity_MembersInjector", "injectApplovinRewardedAdManager:com.tqhit.battery.one.activity.animation.AnimationActivity_MembersInjector", "injectAppRepository:com.tqhit.battery.one.activity.animation.AnimationActivity_MembersInjector"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_features_stats_notifications_UnifiedBatteryNotificationService_GeneratedInjector.java": ["_com_tqhit_battery_one_features_stats_notifications_UnifiedBatteryNotificationService_GeneratedInjector:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_tqhit_battery_one_features_stats_notifications_UnifiedBatteryNotificationService_GeneratedInjector"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\tqhit\\battery\\one\\databinding\\ActivityMainBinding.java": ["ActivityMainBinding:com.tqhit.battery.one.databinding", "mainL:com.tqhit.battery.one.databinding.ActivityMainBinding", "panel:com.tqhit.battery.one.databinding.ActivityMainBinding", "bind:com.tqhit.battery.one.databinding.ActivityMainBinding", "updateViewText:com.tqhit.battery.one.databinding.ActivityMainBinding", "adsBanerLayout:com.tqhit.battery.one.databinding.ActivityMainBinding", "updateViewBtn:com.tqhit.battery.one.databinding.ActivityMainBinding", "bottomView:com.tqhit.battery.one.databinding.ActivityMainBinding", "navHostFragment:com.tqhit.battery.one.databinding.ActivityMainBinding", "updateView:com.tqhit.battery.one.databinding.ActivityMainBinding", "getRoot:com.tqhit.battery.one.databinding.ActivityMainBinding", "inflate:com.tqhit.battery.one.databinding.ActivityMainBinding"], "src\\main\\java\\com\\tqhit\\battery\\one\\utils\\NotificationUtils.kt": ["createNotificationChannel:com.tqhit.battery.one.utils.NotificationUtils", "<init>:com.tqhit.battery.one.utils.NotificationUtils", "NotificationUtils:com.tqhit.battery.one.utils", "sendNotification:com.tqhit.battery.one.utils.NotificationUtils", "createChargingOverlayNotification:com.tqhit.battery.one.utils.NotificationUtils"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_features_emoji_presentation_customize_CustomizeViewModel_HiltModules_KeyModule.java": ["_com_tqhit_battery_one_features_emoji_presentation_customize_CustomizeViewModel_HiltModules_KeyModule:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_tqhit_battery_one_features_emoji_presentation_customize_CustomizeViewModel_HiltModules_KeyModule"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\charge\\cache\\PrefsStatsChargeCache_Factory.java": ["get:com.tqhit.battery.one.features.stats.charge.cache.PrefsStatsChargeCache_Factory", "newInstance:com.tqhit.battery.one.features.stats.charge.cache.PrefsStatsChargeCache_Factory", "create:com.tqhit.battery.one.features.stats.charge.cache.PrefsStatsChargeCache_Factory", "<init>:com.tqhit.battery.one.features.stats.charge.cache.PrefsStatsChargeCache_Factory", "PrefsStatsChargeCache_Factory:com.tqhit.battery.one.features.stats.charge.cache"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\emoji\\presentation\\customize\\CustomizeFragment_GeneratedInjector.java": ["CustomizeFragment_GeneratedInjector:com.tqhit.battery.one.features.emoji.presentation.customize", "injectCustomizeFragment:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeFragment_GeneratedInjector"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\tqhit\\battery\\one\\viewmodel\\AppViewModel_HiltModules_KeyModule_Provide_LazyMapKey.java": ["AppViewModel_HiltModules_KeyModule_Provide_LazyMapKey:com.tqhit.battery.one.viewmodel", "keepFieldType:com.tqhit.battery.one.viewmodel.AppViewModel_HiltModules_KeyModule_Provide_LazyMapKey", "<init>:com.tqhit.battery.one.viewmodel.AppViewModel_HiltModules_KeyModule_Provide_LazyMapKey", "lazyClassKeyName:com.tqhit.battery.one.viewmodel.AppViewModel_HiltModules_KeyModule_Provide_LazyMapKey"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\charge\\presentation\\StatsChargeFragment.kt": ["Companion:com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment", "onCreateView:com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment", "newInstance:com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment.Companion", "<init>:com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment.Companion", "StatsChargeFragment:com.tqhit.battery.one.features.stats.charge.presentation", "vibrationService:com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment", "<init>:com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment", "onViewCreated:com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment"], "src\\main\\java\\com\\tqhit\\battery\\one\\manager\\discharge\\DischargeSession.kt": ["DischargeSession:com.tqhit.battery.one.manager.discharge", "screenOffPercent:com.tqhit.battery.one.manager.discharge.DischargeSession", "averageSpeedMilliAmperes:com.tqhit.battery.one.manager.discharge.DischargeSession", "endPercent:com.tqhit.battery.one.manager.discharge.DischargeSession", "screenOnMilliAmperes:com.tqhit.battery.one.manager.discharge.DischargeSession", "Companion:com.tqhit.battery.one.manager.discharge.DischargeSession", "averageSpeed:com.tqhit.battery.one.manager.discharge.DischargeSession", "<init>:com.tqhit.battery.one.manager.discharge.DischargeSession.Companion", "totalMilliAmperes:com.tqhit.battery.one.manager.discharge.DischargeSession", "fromString:com.tqhit.battery.one.manager.discharge.DischargeSession.Companion", "startPercent:com.tqhit.battery.one.manager.discharge.DischargeSession", "screenOnPercent:com.tqhit.battery.one.manager.discharge.DischargeSession", "toString:com.tqhit.battery.one.manager.discharge.DischargeSession", "screenOffMilliAmperes:com.tqhit.battery.one.manager.discharge.DischargeSession", "startTime:com.tqhit.battery.one.manager.discharge.DischargeSession", "endTime:com.tqhit.battery.one.manager.discharge.DischargeSession"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\data\\DischargeSessionData.kt": ["avgPercentPerHour:com.tqhit.battery.one.features.stats.discharge.data.DischargeSessionData", "isActive:com.tqhit.battery.one.features.stats.discharge.data.DischargeSessionData", "screenOffTimeMillis:com.tqhit.battery.one.features.stats.discharge.data.DischargeSessionData", "screenOnTimeMillis:com.tqhit.battery.one.features.stats.discharge.data.DischargeSessionData", "screenOnPercentageDropped:com.tqhit.battery.one.features.stats.discharge.data.DischargeSessionData", "currentDischargeRate:com.tqhit.battery.one.features.stats.discharge.data.DischargeSessionData", "currentPercentageAtLastUpdate:com.tqhit.battery.one.features.stats.discharge.data.DischargeSessionData", "lastUpdateTimeEpochMillis:com.tqhit.battery.one.features.stats.discharge.data.DischargeSessionData", "currentPercentage:com.tqhit.battery.one.features.stats.discharge.data.DischargeSessionData", "avgMixedDischargeRateMahPerHour:com.tqhit.battery.one.features.stats.discharge.data.DischargeSessionData", "avgScreenOffDischargeRatePercentPerHour:com.tqhit.battery.one.features.stats.discharge.data.DischargeSessionData", "totalMahConsumed:com.tqhit.battery.one.features.stats.discharge.data.DischargeSessionData", "DischargeSessionData:com.tqhit.battery.one.features.stats.discharge.data", "screenOffMahConsumed:com.tqhit.battery.one.features.stats.discharge.data.DischargeSessionData", "startTimeEpochMillis:com.tqhit.battery.one.features.stats.discharge.data.DischargeSessionData", "avgScreenOnDischargeRateMahPerHour:com.tqhit.battery.one.features.stats.discharge.data.DischargeSessionData", "durationMillis:com.tqhit.battery.one.features.stats.discharge.data.DischargeSessionData", "percentageDropped:com.tqhit.battery.one.features.stats.discharge.data.DischargeSessionData", "startPercentage:com.tqhit.battery.one.features.stats.discharge.data.DischargeSessionData", "screenOnMahConsumed:com.tqhit.battery.one.features.stats.discharge.data.DischargeSessionData", "screenOffPercentageDropped:com.tqhit.battery.one.features.stats.discharge.data.DischargeSessionData", "avgScreenOnDischargeRatePercentPerHour:com.tqhit.battery.one.features.stats.discharge.data.DischargeSessionData", "avgScreenOffDischargeRateMahPerHour:com.tqhit.battery.one.features.stats.discharge.data.DischargeSessionData"], "src\\main\\java\\com\\tqhit\\battery\\one\\BatteryApplication.kt": ["coreBatteryServiceHelper:com.tqhit.battery.one.BatteryApplication", "<init>:com.tqhit.battery.one.BatteryApplication", "preferencesHelper:com.tqhit.battery.one.BatteryApplication", "applovinRewardedAdManager:com.tqhit.battery.one.BatteryApplication", "onCreateExt:com.tqhit.battery.one.BatteryApplication", "Companion:com.tqhit.battery.one.BatteryApplication", "onCreate:com.tqhit.battery.one.BatteryApplication", "appSession:com.tqhit.battery.one.BatteryApplication.Companion", "appRepository:com.tqhit.battery.one.BatteryApplication", "onActivityResumed:com.tqhit.battery.one.BatteryApplication", "applovinAppOpenAdManager:com.tqhit.battery.one.BatteryApplication", "applovinNativeAdManager:com.tqhit.battery.one.BatteryApplication", "onAppForegrounded:com.tqhit.battery.one.BatteryApplication", "appOpenTime:com.tqhit.battery.one.BatteryApplication.Companion", "BatteryApplication:com.tqhit.battery.one", "onActivityPreCreated:com.tqhit.battery.one.BatteryApplication", "onActivityPaused:com.tqhit.battery.one.BatteryApplication", "<init>:com.tqhit.battery.one.BatteryApplication.Companion", "applovinBannerAdManager:com.tqhit.battery.one.BatteryApplication", "applovinInterstitialAdManager:com.tqhit.battery.one.BatteryApplication"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\presentation\\InfoButtonManager_Factory.java": ["InfoButtonManager_Factory:com.tqhit.battery.one.features.stats.discharge.presentation", "get:com.tqhit.battery.one.features.stats.discharge.presentation.InfoButtonManager_Factory", "newInstance:com.tqhit.battery.one.features.stats.discharge.presentation.InfoButtonManager_Factory", "<init>:com.tqhit.battery.one.features.stats.discharge.presentation.InfoButtonManager_Factory", "create:com.tqhit.battery.one.features.stats.discharge.presentation.InfoButtonManager_Factory"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\health\\repository\\HistoryBatteryRepository_Factory.java": ["create:com.tqhit.battery.one.features.stats.health.repository.HistoryBatteryRepository_Factory", "<init>:com.tqhit.battery.one.features.stats.health.repository.HistoryBatteryRepository_Factory", "HistoryBatteryRepository_Factory:com.tqhit.battery.one.features.stats.health.repository", "get:com.tqhit.battery.one.features.stats.health.repository.HistoryBatteryRepository_Factory", "newInstance:com.tqhit.battery.one.features.stats.health.repository.HistoryBatteryRepository_Factory"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\tqhit\\battery\\one\\databinding\\FragmentHealthBinding.java": ["degreeWearInfo:com.tqhit.battery.one.databinding.FragmentHealthBinding", "day2Percent:com.tqhit.battery.one.databinding.FragmentHealthBinding", "healthPercentDamageSingular:com.tqhit.battery.one.databinding.FragmentHealthBinding", "singularCalculated:com.tqhit.battery.one.databinding.FragmentHealthBinding", "underGraphTemp:com.tqhit.battery.one.databinding.FragmentHealthBinding", "day3Temp:com.tqhit.battery.one.databinding.FragmentHealthBinding", "damageBarPercentCurrentSingular:com.tqhit.battery.one.databinding.FragmentHealthBinding", "graphPercent:com.tqhit.battery.one.databinding.FragmentHealthBinding", "deadTimeUpSingular:com.tqhit.battery.one.databinding.FragmentHealthBinding", "textTopDeadTime111:com.tqhit.battery.one.databinding.FragmentHealthBinding", "singular31:com.tqhit.battery.one.databinding.FragmentHealthBinding", "day6Percent:com.tqhit.battery.one.databinding.FragmentHealthBinding", "progbar4:com.tqhit.battery.one.databinding.FragmentHealthBinding", "day2:com.tqhit.battery.one.databinding.FragmentHealthBinding", "day4Temp:com.tqhit.battery.one.databinding.FragmentHealthBinding", "t7:com.tqhit.battery.one.databinding.FragmentHealthBinding", "grahpTempViewgroup:com.tqhit.battery.one.databinding.FragmentHealthBinding", "btn3T:com.tqhit.battery.one.databinding.FragmentHealthBinding", "singularBtn:com.tqhit.battery.one.databinding.FragmentHealthBinding", "proggersBarDamage:com.tqhit.battery.one.databinding.FragmentHealthBinding", "updateViewBtn:com.tqhit.battery.one.databinding.FragmentHealthBinding", "cumulativeCalculated:com.tqhit.battery.one.databinding.FragmentHealthBinding", "historyDatabase:com.tqhit.battery.one.databinding.FragmentHealthBinding", "wearRate3:com.tqhit.battery.one.databinding.FragmentHealthBinding", "cumulative1:com.tqhit.battery.one.databinding.FragmentHealthBinding", "seekBarSingular:com.tqhit.battery.one.databinding.FragmentHealthBinding", "percentDamageDeadSingular:com.tqhit.battery.one.databinding.FragmentHealthBinding", "proggersBarDamageCumulative:com.tqhit.battery.one.databinding.FragmentHealthBinding", "percentDamage:com.tqhit.battery.one.databinding.FragmentHealthBinding", "percentGraphChange:com.tqhit.battery.one.databinding.FragmentHealthBinding", "day6Temp:com.tqhit.battery.one.databinding.FragmentHealthBinding", "singular32:com.tqhit.battery.one.databinding.FragmentHealthBinding", "progbar5:com.tqhit.battery.one.databinding.FragmentHealthBinding", "singular1:com.tqhit.battery.one.databinding.FragmentHealthBinding", "t6:com.tqhit.battery.one.databinding.FragmentHealthBinding", "day3:com.tqhit.battery.one.databinding.FragmentHealthBinding", "t8Temp:com.tqhit.battery.one.databinding.FragmentHealthBinding", "textRemainVarCumulative:com.tqhit.battery.one.databinding.FragmentHealthBinding", "whyINeedToDoThis:com.tqhit.battery.one.databinding.FragmentHealthBinding", "wearRate2:com.tqhit.battery.one.databinding.FragmentHealthBinding", "day5:com.tqhit.battery.one.databinding.FragmentHealthBinding", "healthAccess2:com.tqhit.battery.one.databinding.FragmentHealthBinding", "textDead:com.tqhit.battery.one.databinding.FragmentHealthBinding", "progbar1:com.tqhit.battery.one.databinding.FragmentHealthBinding", "day1Percent:com.tqhit.battery.one.databinding.FragmentHealthBinding", "chartPercent:com.tqhit.battery.one.databinding.FragmentHealthBinding", "healthPercentDamageCumulative:com.tqhit.battery.one.databinding.FragmentHealthBinding", "percentCumulative:com.tqhit.battery.one.databinding.FragmentHealthBinding", "t6Temp:com.tqhit.battery.one.databinding.FragmentHealthBinding", "blurViewDeadTop:com.tqhit.battery.one.databinding.FragmentHealthBinding", "progbar2:com.tqhit.battery.one.databinding.FragmentHealthBinding", "FragmentHealthBinding:com.tqhit.battery.one.databinding", "day1Temp:com.tqhit.battery.one.databinding.FragmentHealthBinding", "day4:com.tqhit.battery.one.databinding.FragmentHealthBinding", "day5Percent:com.tqhit.battery.one.databinding.FragmentHealthBinding", "t5:com.tqhit.battery.one.databinding.FragmentHealthBinding", "day7Temp:com.tqhit.battery.one.databinding.FragmentHealthBinding", "btn2T:com.tqhit.battery.one.databinding.FragmentHealthBinding", "day6:com.tqhit.battery.one.databinding.FragmentHealthBinding", "healthCountOfSessionsCumulative:com.tqhit.battery.one.databinding.FragmentHealthBinding", "damageBarSeekwhite:com.tqhit.battery.one.databinding.FragmentHealthBinding", "graph:com.tqhit.battery.one.databinding.FragmentHealthBinding", "t9Temp:com.tqhit.battery.one.databinding.FragmentHealthBinding", "cumulativeSessionInfo:com.tqhit.battery.one.databinding.FragmentHealthBinding", "cumulative31:com.tqhit.battery.one.databinding.FragmentHealthBinding", "t4:com.tqhit.battery.one.databinding.FragmentHealthBinding", "progbar3:com.tqhit.battery.one.databinding.FragmentHealthBinding", "textTopDeadTime111Cumulative:com.tqhit.battery.one.databinding.FragmentHealthBinding", "singularSessionInfo:com.tqhit.battery.one.databinding.FragmentHealthBinding", "indentDown:com.tqhit.battery.one.databinding.FragmentHealthBinding", "btn0:com.tqhit.battery.one.databinding.FragmentHealthBinding", "getRoot:com.tqhit.battery.one.databinding.FragmentHealthBinding", "dT:com.tqhit.battery.one.databinding.FragmentHealthBinding", "scrollView:com.tqhit.battery.one.databinding.FragmentHealthBinding", "day7:com.tqhit.battery.one.databinding.FragmentHealthBinding", "deadTimeUp:com.tqhit.battery.one.databinding.FragmentHealthBinding", "sdfsd:com.tqhit.battery.one.databinding.FragmentHealthBinding", "d21:com.tqhit.battery.one.databinding.FragmentHealthBinding", "percentDamageDead:com.tqhit.battery.one.databinding.FragmentHealthBinding", "cumulative32:com.tqhit.battery.one.databinding.FragmentHealthBinding", "t3Temp:com.tqhit.battery.one.databinding.FragmentHealthBinding", "graphTemp:com.tqhit.battery.one.databinding.FragmentHealthBinding", "textDeadSingular:com.tqhit.battery.one.databinding.FragmentHealthBinding", "t3:com.tqhit.battery.one.databinding.FragmentHealthBinding", "deadTimeUpCummulative:com.tqhit.battery.one.databinding.FragmentHealthBinding", "day4Percent:com.tqhit.battery.one.databinding.FragmentHealthBinding", "singularCapacityNi:com.tqhit.battery.one.databinding.FragmentHealthBinding", "damageBarSeekwhiteSingular:com.tqhit.battery.one.databinding.FragmentHealthBinding", "cumulativeCapacityNi:com.tqhit.battery.one.databinding.FragmentHealthBinding", "btn1T:com.tqhit.battery.one.databinding.FragmentHealthBinding", "btn1:com.tqhit.battery.one.databinding.FragmentHealthBinding", "d2:com.tqhit.battery.one.databinding.FragmentHealthBinding", "timeGraphChange:com.tqhit.battery.one.databinding.FragmentHealthBinding", "cumulativeBtn:com.tqhit.battery.one.databinding.FragmentHealthBinding", "damageBarPercentCurrent:com.tqhit.battery.one.databinding.FragmentHealthBinding", "d22:com.tqhit.battery.one.databinding.FragmentHealthBinding", "day7Percent:com.tqhit.battery.one.databinding.FragmentHealthBinding", "wearRatePercent:com.tqhit.battery.one.databinding.FragmentHealthBinding", "updateView:com.tqhit.battery.one.databinding.FragmentHealthBinding", "t2:com.tqhit.battery.one.databinding.FragmentHealthBinding", "chart1Percent:com.tqhit.battery.one.databinding.FragmentHealthBinding", "t2Temp:com.tqhit.battery.one.databinding.FragmentHealthBinding", "timeDeadViewgroup:com.tqhit.battery.one.databinding.FragmentHealthBinding", "bind:com.tqhit.battery.one.databinding.FragmentHealthBinding", "healthFirstProgressbarCumulative:com.tqhit.battery.one.databinding.FragmentHealthBinding", "t5Temp:com.tqhit.battery.one.databinding.FragmentHealthBinding", "healthCountOfSessionsSingular:com.tqhit.battery.one.databinding.FragmentHealthBinding", "healthFullBateryCapacity:com.tqhit.battery.one.databinding.FragmentHealthBinding", "btn2:com.tqhit.battery.one.databinding.FragmentHealthBinding", "day3Percent:com.tqhit.battery.one.databinding.FragmentHealthBinding", "textRemainVarSingular:com.tqhit.battery.one.databinding.FragmentHealthBinding", "healthCheckedBateryCapacityCumulative:com.tqhit.battery.one.databinding.FragmentHealthBinding", "progbar6:com.tqhit.battery.one.databinding.FragmentHealthBinding", "inflate:com.tqhit.battery.one.databinding.FragmentHealthBinding", "chart1L:com.tqhit.battery.one.databinding.FragmentHealthBinding", "t1:com.tqhit.battery.one.databinding.FragmentHealthBinding", "chart1:com.tqhit.battery.one.databinding.FragmentHealthBinding", "t9:com.tqhit.battery.one.databinding.FragmentHealthBinding", "day5Temp:com.tqhit.battery.one.databinding.FragmentHealthBinding", "day2Temp:com.tqhit.battery.one.databinding.FragmentHealthBinding", "percentSingular:com.tqhit.battery.one.databinding.FragmentHealthBinding", "underGraphPercent:com.tqhit.battery.one.databinding.FragmentHealthBinding", "btn0T:com.tqhit.battery.one.databinding.FragmentHealthBinding", "methodText:com.tqhit.battery.one.databinding.FragmentHealthBinding", "degreeOfWear:com.tqhit.battery.one.databinding.FragmentHealthBinding", "predictionWearInfo:com.tqhit.battery.one.databinding.FragmentHealthBinding", "t7Temp:com.tqhit.battery.one.databinding.FragmentHealthBinding", "btn3:com.tqhit.battery.one.databinding.FragmentHealthBinding", "healthCheckedBateryCapacitySingular:com.tqhit.battery.one.databinding.FragmentHealthBinding", "methodTextSingular:com.tqhit.battery.one.databinding.FragmentHealthBinding", "t1Temp:com.tqhit.battery.one.databinding.FragmentHealthBinding", "t4Temp:com.tqhit.battery.one.databinding.FragmentHealthBinding", "progbar7:com.tqhit.battery.one.databinding.FragmentHealthBinding", "percentDamageCumulative:com.tqhit.battery.one.databinding.FragmentHealthBinding", "underGraph:com.tqhit.battery.one.databinding.FragmentHealthBinding", "t8:com.tqhit.battery.one.databinding.FragmentHealthBinding", "day1:com.tqhit.battery.one.databinding.FragmentHealthBinding", "seekBar:com.tqhit.battery.one.databinding.FragmentHealthBinding", "healthFirstProgressbarSingular:com.tqhit.battery.one.databinding.FragmentHealthBinding", "deadTimeText:com.tqhit.battery.one.databinding.FragmentHealthBinding"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\navigation\\DynamicNavigationManager.kt": ["stateChanges:com.tqhit.battery.one.features.navigation.DynamicNavigationManager", "<init>:com.tqhit.battery.one.features.navigation.DynamicNavigationManager.Companion", "initialize:com.tqhit.battery.one.features.navigation.DynamicNavigationManager", "DynamicNavigationManager:com.tqhit.battery.one.features.navigation", "getCurrentState:com.tqhit.battery.one.features.navigation.DynamicNavigationManager", "Companion:com.tqhit.battery.one.features.navigation.DynamicNavigationManager", "isInitialized:com.tqhit.battery.one.features.navigation.DynamicNavigationManager", "navigationState:com.tqhit.battery.one.features.navigation.DynamicNavigationManager", "getPerformanceStats:com.tqhit.battery.one.features.navigation.DynamicNavigationManager", "handleUserNavigation:com.tqhit.battery.one.features.navigation.DynamicNavigationManager"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\apppower\\presentation\\AppPowerConsumptionDialog.kt": ["AppPowerConsumptionDialog:com.tqhit.battery.one.features.stats.apppower.presentation", "Companion:com.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionDialog", "<init>:com.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionDialog.Companion", "onCreate:com.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionDialog", "onDetachedFromWindow:com.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionDialog"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\activity\\overlay\\Hilt_ChargingOverlayActivity.java": ["onDestroy:com.tqhit.battery.one.activity.overlay.Hilt_ChargingOverlayActivity", "<init>:com.tqhit.battery.one.activity.overlay.Hilt_ChargingOverlayActivity", "onCreate:com.tqhit.battery.one.activity.overlay.Hilt_ChargingOverlayActivity", "getDefaultViewModelProviderFactory:com.tqhit.battery.one.activity.overlay.Hilt_ChargingOverlayActivity", "generatedComponent:com.tqhit.battery.one.activity.overlay.Hilt_ChargingOverlayActivity", "Hilt_ChargingOverlayActivity:com.tqhit.battery.one.activity.overlay", "createComponentManager:com.tqhit.battery.one.activity.overlay.Hilt_ChargingOverlayActivity", "inject:com.tqhit.battery.one.activity.overlay.Hilt_ChargingOverlayActivity", "componentManager:com.tqhit.battery.one.activity.overlay.Hilt_ChargingOverlayActivity"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\emoji\\presentation\\gallery\\BatteryGalleryViewModel_Factory.java": ["BatteryGalleryViewModel_Factory:com.tqhit.battery.one.features.emoji.presentation.gallery", "get:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModel_Factory", "newInstance:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModel_Factory", "<init>:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModel_Factory", "create:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModel_Factory"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\corebattery\\service\\Hilt_CoreBatteryStatsService.java": ["<init>:com.tqhit.battery.one.features.stats.corebattery.service.Hilt_CoreBatteryStatsService", "generatedComponent:com.tqhit.battery.one.features.stats.corebattery.service.Hilt_CoreBatteryStatsService", "componentManager:com.tqhit.battery.one.features.stats.corebattery.service.Hilt_CoreBatteryStatsService", "inject:com.tqhit.battery.one.features.stats.corebattery.service.Hilt_CoreBatteryStatsService", "onCreate:com.tqhit.battery.one.features.stats.corebattery.service.Hilt_CoreBatteryStatsService", "createComponentManager:com.tqhit.battery.one.features.stats.corebattery.service.Hilt_CoreBatteryStatsService", "Hilt_CoreBatteryStatsService:com.tqhit.battery.one.features.stats.corebattery.service"], "src\\main\\java\\com\\tqhit\\battery\\one\\manager\\charge\\ChargingSessionManager.kt": ["Companion:com.tqhit.battery.one.manager.charge.ChargingSessionManager", "getAverageSpeed:com.tqhit.battery.one.manager.charge.ChargingSessionManager", "getAllSessions:com.tqhit.battery.one.manager.charge.ChargingSessionManager", "addSampleSessionsIfEmpty:com.tqhit.battery.one.manager.charge.ChargingSessionManager", "clearSessions:com.tqhit.battery.one.manager.charge.ChargingSessionManager", "getAverageScreenOnSpeed:com.tqhit.battery.one.manager.charge.ChargingSessionManager", "ChargingSessionManager:com.tqhit.battery.one.manager.charge", "getTotalSessions:com.tqhit.battery.one.manager.charge.ChargingSessionManager", "addSession:com.tqhit.battery.one.manager.charge.ChargingSessionManager", "getAverageScreenOffMilliAmperes:com.tqhit.battery.one.manager.charge.ChargingSessionManager", "getAverageMilliAmperes:com.tqhit.battery.one.manager.charge.ChargingSessionManager", "getAverageScreenOffSpeed:com.tqhit.battery.one.manager.charge.ChargingSessionManager", "disableSampleGeneration:com.tqhit.battery.one.manager.charge.ChargingSessionManager", "simulateNewChargingSession:com.tqhit.battery.one.manager.charge.ChargingSessionManager", "enableSampleGeneration:com.tqhit.battery.one.manager.charge.ChargingSessionManager", "getAverageScreenOnMilliAmperes:com.tqhit.battery.one.manager.charge.ChargingSessionManager", "<init>:com.tqhit.battery.one.manager.charge.ChargingSessionManager.Companion"], "src\\main\\java\\com\\tqhit\\battery\\one\\fragment\\main\\animation\\adapter\\AnimationAdapter.kt": ["getItemCount:com.tqhit.battery.one.fragment.main.animation.adapter.AnimationAdapter", "onCreateViewHolder:com.tqhit.battery.one.fragment.main.animation.adapter.AnimationAdapter", "bind:com.tqhit.battery.one.fragment.main.animation.adapter.AnimationViewHolder", "GridSpacingItemDecoration:com.tqhit.battery.one.fragment.main.animation.adapter", "AnimationViewHolder:com.tqhit.battery.one.fragment.main.animation.adapter", "getItemOffsets:com.tqhit.battery.one.fragment.main.animation.adapter.GridSpacingItemDecoration", "updateItems:com.tqhit.battery.one.fragment.main.animation.adapter.AnimationAdapter", "onBindViewHolder:com.tqhit.battery.one.fragment.main.animation.adapter.AnimationAdapter", "AnimationAdapter:com.tqhit.battery.one.fragment.main.animation.adapter"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\notifications\\UnifiedBatteryNotificationService.kt": ["UnifiedBatteryNotificationService:com.tqhit.battery.one.features.stats.notifications", "onDestroy:com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationService", "Companion:com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationService", "onStartCommand:com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationService", "<init>:com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationService", "onCreate:com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationService", "<init>:com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationService.Companion", "appRepository:com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationService", "onBind:com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationService", "coreBatteryStatsProvider:com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationService"], "src\\main\\java\\com\\tqhit\\battery\\one\\dialog\\theme\\SelectThemeDialog.kt": ["SelectThemeDialog:com.tqhit.battery.one.dialog.theme", "setupListener:com.tqhit.battery.one.dialog.theme.SelectThemeDialog", "binding:com.tqhit.battery.one.dialog.theme.SelectThemeDialog", "initWindow:com.tqhit.battery.one.dialog.theme.SelectThemeDialog"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\charge\\presentation\\StatsChargeFragment_GeneratedInjector.java": ["StatsChargeFragment_GeneratedInjector:com.tqhit.battery.one.features.stats.charge.presentation", "injectStatsChargeFragment:com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment_GeneratedInjector"], "src\\main\\java\\com\\tqhit\\battery\\one\\ads\\core\\ApplovinInterstitialAdManager.kt": ["loadInterstitialAd:com.tqhit.battery.one.ads.core.ApplovinInterstitialAdManager", "showInterstitialAd:com.tqhit.battery.one.ads.core.ApplovinInterstitialAdManager", "ApplovinInterstitialAdManager:com.tqhit.battery.one.ads.core"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\notifications\\UnifiedBatteryNotificationServiceHelper.kt": ["isServiceRunning:com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationServiceHelper", "UnifiedBatteryNotificationServiceHelper:com.tqhit.battery.one.features.stats.notifications", "startService:com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationServiceHelper", "stopService:com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationServiceHelper", "<init>:com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationServiceHelper.Companion", "Companion:com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationServiceHelper"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\domain\\ScreenTimeCalculator_Factory.java": ["create:com.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeCalculator_Factory", "ScreenTimeCalculator_Factory:com.tqhit.battery.one.features.stats.discharge.domain", "<init>:com.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeCalculator_Factory", "get:com.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeCalculator_Factory", "newInstance:com.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeCalculator_Factory"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\tqhit\\battery\\one\\databinding\\ActivityTestNewDischargeBinding.java": ["ActivityTestNewDischargeBinding:com.tqhit.battery.one.databinding", "bind:com.tqhit.battery.one.databinding.ActivityTestNewDischargeBinding", "getRoot:com.tqhit.battery.one.databinding.ActivityTestNewDischargeBinding", "inflate:com.tqhit.battery.one.databinding.ActivityTestNewDischargeBinding", "fragmentContainer:com.tqhit.battery.one.databinding.ActivityTestNewDischargeBinding"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\data\\ScreenStateChangeEvent.kt": ["ScreenStateChangeEvent:com.tqhit.battery.one.features.stats.discharge.data", "timestamp:com.tqhit.battery.one.features.stats.discharge.data.ScreenStateChangeEvent", "isScreenOn:com.tqhit.battery.one.features.stats.discharge.data.ScreenStateChangeEvent"], "src\\main\\java\\com\\tqhit\\battery\\one\\utils\\AntiThiefUtils.kt": ["isEnterPasswordActivityRunning:com.tqhit.battery.one.utils.AntiThiefUtils", "<init>:com.tqhit.battery.one.utils.AntiThiefUtils", "AntiThiefUtils:com.tqhit.battery.one.utils"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\health\\cache\\HealthCache.kt": ["isCachedDataRecent:com.tqhit.battery.one.features.stats.health.cache", "getHealthStatus:com.tqhit.battery.one.features.stats.health.cache.DefaultHealthCache", "getHealthStatus:com.tqhit.battery.one.features.stats.health.cache.HealthCache", "HealthCache:com.tqhit.battery.one.features.stats.health.cache", "<init>:com.tqhit.battery.one.features.stats.health.cache.DefaultHealthCache.Companion", "DefaultHealthCache:com.tqhit.battery.one.features.stats.health.cache", "getCalculationMode:com.tqhit.battery.one.features.stats.health.cache.DefaultHealthCache", "getCachedDataAgeMinutes:com.tqhit.battery.one.features.stats.health.cache", "Companion:com.tqhit.battery.one.features.stats.health.cache.DefaultHealthCache", "clearAll:com.tqhit.battery.one.features.stats.health.cache.DefaultHealthCache", "clearAll:com.tqhit.battery.one.features.stats.health.cache.HealthCache", "saveHealthStatus:com.tqhit.battery.one.features.stats.health.cache.DefaultHealthCache", "saveCalculationMode:com.tqhit.battery.one.features.stats.health.cache.HealthCache", "getCalculationMode:com.tqhit.battery.one.features.stats.health.cache.HealthCache", "saveHealthStatus:com.tqhit.battery.one.features.stats.health.cache.HealthCache", "saveCalculationMode:com.tqhit.battery.one.features.stats.health.cache.DefaultHealthCache"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\emoji\\domain\\use_case\\ResetCustomizationUseCase_Factory.java": ["get:com.tqhit.battery.one.features.emoji.domain.use_case.ResetCustomizationUseCase_Factory", "newInstance:com.tqhit.battery.one.features.emoji.domain.use_case.ResetCustomizationUseCase_Factory", "<init>:com.tqhit.battery.one.features.emoji.domain.use_case.ResetCustomizationUseCase_Factory", "ResetCustomizationUseCase_Factory:com.tqhit.battery.one.features.emoji.domain.use_case", "create:com.tqhit.battery.one.features.emoji.domain.use_case.ResetCustomizationUseCase_Factory"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\emoji\\presentation\\gallery\\EmojiBatteryFragment_GeneratedInjector.java": ["injectEmojiBatteryFragment:com.tqhit.battery.one.features.emoji.presentation.gallery.EmojiBatteryFragment_GeneratedInjector", "EmojiBatteryFragment_GeneratedInjector:com.tqhit.battery.one.features.emoji.presentation.gallery"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\notifications\\UnifiedBatteryNotificationServiceHelper_Factory.java": ["get:com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationServiceHelper_Factory", "create:com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationServiceHelper_Factory", "newInstance:com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationServiceHelper_Factory", "<init>:com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationServiceHelper_Factory", "UnifiedBatteryNotificationServiceHelper_Factory:com.tqhit.battery.one.features.stats.notifications"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\emoji\\domain\\model\\BatteryStyle.kt": ["getPreviewId:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle", "Companion:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle", "batteryImageUrl:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle", "BatteryStyle:com.tqhit.battery.one.features.emoji.domain.model", "showEmoji:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfig", "matchesSearch:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle", "name:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle", "<init>:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle.Companion", "isValid:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfig", "validated:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfig", "id:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle", "Companion:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfig", "timestampEpochMillis:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle", "emojiImageUrl:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle", "createDefault:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle.Companion", "showPercentage:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfig", "category:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle", "percentageFontSizeDp:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfig", "createDefault:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfig.Companion", "BatteryStyleConfig:com.tqhit.battery.one.features.emoji.domain.model", "isPopular:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle", "<init>:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfig.Companion", "isPremium:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle", "percentageColor:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfig", "defaultConfig:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle", "emojiSizeScale:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfig", "isValid:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\datasource\\ScreenStateReceiver.kt": ["screenStateFlow:com.tqhit.battery.one.features.stats.discharge.datasource.ScreenStateReceiver", "Companion:com.tqhit.battery.one.features.stats.discharge.datasource.ScreenStateReceiver", "forceCheckScreenState:com.tqhit.battery.one.features.stats.discharge.datasource.ScreenStateReceiver", "<init>:com.tqhit.battery.one.features.stats.discharge.datasource.ScreenStateReceiver.Companion", "register:com.tqhit.battery.one.features.stats.discharge.datasource.ScreenStateReceiver", "unregister:com.tqhit.battery.one.features.stats.discharge.datasource.ScreenStateReceiver", "ScreenStateReceiver:com.tqhit.battery.one.features.stats.discharge.datasource"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_activity_main_MainActivity_GeneratedInjector.java": ["_com_tqhit_battery_one_activity_main_MainActivity_GeneratedInjector:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_tqhit_battery_one_activity_main_MainActivity_GeneratedInjector"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\hilt_aggregated_deps\\_com_tqhit_battery_one_features_stats_corebattery_di_CoreBatteryDIModule.java": ["<init>:hilt_aggregated_deps._com_tqhit_battery_one_features_stats_corebattery_di_CoreBatteryDIModule", "_com_tqhit_battery_one_features_stats_corebattery_di_CoreBatteryDIModule:hilt_aggregated_deps"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\presentation\\DischargeViewModel_HiltModules_KeyModule_Provide_LazyMapKey.java": ["keepFieldType:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeViewModel_HiltModules_KeyModule_Provide_LazyMapKey", "DischargeViewModel_HiltModules_KeyModule_Provide_LazyMapKey:com.tqhit.battery.one.features.stats.discharge.presentation", "<init>:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeViewModel_HiltModules_KeyModule_Provide_LazyMapKey", "lazyClassKeyName:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeViewModel_HiltModules_KeyModule_Provide_LazyMapKey"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\cache\\PrefsDischargeRatesCache_Factory.java": ["get:com.tqhit.battery.one.features.stats.discharge.cache.PrefsDischargeRatesCache_Factory", "newInstance:com.tqhit.battery.one.features.stats.discharge.cache.PrefsDischargeRatesCache_Factory", "create:com.tqhit.battery.one.features.stats.discharge.cache.PrefsDischargeRatesCache_Factory", "<init>:com.tqhit.battery.one.features.stats.discharge.cache.PrefsDischargeRatesCache_Factory", "PrefsDischargeRatesCache_Factory:com.tqhit.battery.one.features.stats.discharge.cache"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_features_stats_health_presentation_HealthViewModel_HiltModules_KeyModule.java": ["_com_tqhit_battery_one_features_stats_health_presentation_HealthViewModel_HiltModules_KeyModule:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_tqhit_battery_one_features_stats_health_presentation_HealthViewModel_HiltModules_KeyModule"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\emoji\\data\\datastore\\CustomizationDataStore_Factory.java": ["CustomizationDataStore_Factory:com.tqhit.battery.one.features.emoji.data.datastore", "<init>:com.tqhit.battery.one.features.emoji.data.datastore.CustomizationDataStore_Factory", "newInstance:com.tqhit.battery.one.features.emoji.data.datastore.CustomizationDataStore_Factory", "get:com.tqhit.battery.one.features.emoji.data.datastore.CustomizationDataStore_Factory", "create:com.tqhit.battery.one.features.emoji.data.datastore.CustomizationDataStore_Factory"], "src\\main\\java\\com\\tqhit\\battery\\one\\viewmodel\\AppViewModel.kt": ["setDontDisturbDischargeFromTime:com.tqhit.battery.one.viewmodel.AppViewModel", "setChargeAlarmPercent:com.tqhit.battery.one.viewmodel.AppViewModel", "getDoNotKillMyAppUrl:com.tqhit.battery.one.viewmodel.AppViewModel", "clearAntiThiefPassword:com.tqhit.battery.one.viewmodel.AppViewModel", "setShowedStartPage:com.tqhit.battery.one.viewmodel.AppViewModel", "isPrivacyPolicyAccepted:com.tqhit.battery.one.viewmodel.AppViewModel", "getDontDisturbChargeFromTime:com.tqhit.battery.one.viewmodel.AppViewModel", "setVibrationChargeEnabled:com.tqhit.battery.one.viewmodel.AppViewModel", "setAntiThiefSoundEnabled:com.tqhit.battery.one.viewmodel.AppViewModel", "setDontDisturbChargeEnabled:com.tqhit.battery.one.viewmodel.AppViewModel", "isShowedStartPage:com.tqhit.battery.one.viewmodel.AppViewModel", "getDefaultLanguage:com.tqhit.battery.one.viewmodel.AppViewModel", "isVibrationDischargeEnabled:com.tqhit.battery.one.viewmodel.AppViewModel", "setDontDisturbChargeFromTime:com.tqhit.battery.one.viewmodel.AppViewModel", "setNotifyFullChargeEnabled:com.tqhit.battery.one.viewmodel.AppViewModel", "isVibrationEnabled:com.tqhit.battery.one.viewmodel.AppViewModel", "isAntiThiefSoundEnabled:com.tqhit.battery.one.viewmodel.AppViewModel", "isDischargeAlarmEnabled:com.tqhit.battery.one.viewmodel.AppViewModel", "isVibrationChargeEnabled:com.tqhit.battery.one.viewmodel.AppViewModel", "acceptPrivacyPolicy:com.tqhit.battery.one.viewmodel.AppViewModel", "getDontDisturbChargeUntilTime:com.tqhit.battery.one.viewmodel.AppViewModel", "setConsentFlowUserGeography:com.tqhit.battery.one.viewmodel.AppViewModel", "isAntiThiefEnabled:com.tqhit.battery.one.viewmodel.AppViewModel", "isNotifyFullChargeEnabled:com.tqhit.battery.one.viewmodel.AppViewModel", "setAntiThiefPassword:com.tqhit.battery.one.viewmodel.AppViewModel", "setVibrationEnabled:com.tqhit.battery.one.viewmodel.AppViewModel", "getDontDisturbDischargeUntilTime:com.tqhit.battery.one.viewmodel.AppViewModel", "setLocale:com.tqhit.battery.one.viewmodel.AppViewModel", "getChargeAlarmPercent:com.tqhit.battery.one.viewmodel.AppViewModel", "setDischargeAlarmEnabled:com.tqhit.battery.one.viewmodel.AppViewModel", "setLanguage:com.tqhit.battery.one.viewmodel.AppViewModel", "setDontDisturbDischargeUntilTime:com.tqhit.battery.one.viewmodel.AppViewModel", "getPrivacyPolicyUrl:com.tqhit.battery.one.viewmodel.AppViewModel", "getDontDisturbDischargeFromTime:com.tqhit.battery.one.viewmodel.AppViewModel", "setAnimationOverlayEnabled:com.tqhit.battery.one.viewmodel.AppViewModel", "setDischargeNotificationEnabled:com.tqhit.battery.one.viewmodel.AppViewModel", "setDontDisturbChargeUntilTime:com.tqhit.battery.one.viewmodel.AppViewModel", "isDontDisturbChargeEnabled:com.tqhit.battery.one.viewmodel.AppViewModel", "setAntiThiefEnabled:com.tqhit.battery.one.viewmodel.AppViewModel", "isConsentFlowUserGeography:com.tqhit.battery.one.viewmodel.AppViewModel", "isDischargeNotificationEnabled:com.tqhit.battery.one.viewmodel.AppViewModel", "getAntiThiefPassword:com.tqhit.battery.one.viewmodel.AppViewModel", "setChargeAlarmEnabled:com.tqhit.battery.one.viewmodel.AppViewModel", "isDontDisturbDischargeEnabled:com.tqhit.battery.one.viewmodel.AppViewModel", "getDischargeAlarmPercent:com.tqhit.battery.one.viewmodel.AppViewModel", "setDischargeAlarmPercent:com.tqhit.battery.one.viewmodel.AppViewModel", "setChargeNotificationEnabled:com.tqhit.battery.one.viewmodel.AppViewModel", "setVibrationDischargeEnabled:com.tqhit.battery.one.viewmodel.AppViewModel", "isChargeAlarmEnabled:com.tqhit.battery.one.viewmodel.AppViewModel", "isChargeNotificationEnabled:com.tqhit.battery.one.viewmodel.AppViewModel", "isAnimationOverlayTimeEnabled:com.tqhit.battery.one.viewmodel.AppViewModel", "isAntiThiefPasswordSet:com.tqhit.battery.one.viewmodel.AppViewModel", "getLanguage:com.tqhit.battery.one.viewmodel.AppViewModel", "isAnimationOverlayEnabled:com.tqhit.battery.one.viewmodel.AppViewModel", "setAnimationOverlayTimeEnabled:com.tqhit.battery.one.viewmodel.AppViewModel", "AppViewModel:com.tqhit.battery.one.viewmodel", "setDontDisturbDischargeEnabled:com.tqhit.battery.one.viewmodel.AppViewModel"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\tqhit\\battery\\one\\databinding\\SectionChargeBatteryWearBinding.java": ["targetPercentValue:com.tqhit.battery.one.databinding.SectionChargeBatteryWearBinding", "SectionChargeBatteryWearBinding:com.tqhit.battery.one.databinding", "batteryWearInfo:com.tqhit.battery.one.databinding.SectionChargeBatteryWearBinding", "batteryWearTitle:com.tqhit.battery.one.databinding.SectionChargeBatteryWearBinding", "inflate:com.tqhit.battery.one.databinding.SectionChargeBatteryWearBinding", "targetPercentLabel:com.tqhit.battery.one.databinding.SectionChargeBatteryWearBinding", "targetPercentSeekbar:com.tqhit.battery.one.databinding.SectionChargeBatteryWearBinding", "dividerWear:com.tqhit.battery.one.databinding.SectionChargeBatteryWearBinding", "batteryAlarmBtn:com.tqhit.battery.one.databinding.SectionChargeBatteryWearBinding", "bind:com.tqhit.battery.one.databinding.SectionChargeBatteryWearBinding", "batteryWearRoot:com.tqhit.battery.one.databinding.SectionChargeBatteryWearBinding", "getRoot:com.tqhit.battery.one.databinding.SectionChargeBatteryWearBinding", "batteryWearDescription:com.tqhit.battery.one.databinding.SectionChargeBatteryWearBinding"], "src\\main\\java\\com\\tqhit\\battery\\one\\dialog\\alarm\\SelectBatteryAlarmLowDialog.kt": ["binding:com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmLowDialog", "initWindow:com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmLowDialog", "setupListener:com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmLowDialog", "SelectBatteryAlarmLowDialog:com.tqhit.battery.one.dialog.alarm", "setupUI:com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmLowDialog"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\activity\\animation\\Hilt_AnimationActivity.java": ["onDestroy:com.tqhit.battery.one.activity.animation.Hilt_AnimationActivity", "generatedComponent:com.tqhit.battery.one.activity.animation.Hilt_AnimationActivity", "<init>:com.tqhit.battery.one.activity.animation.Hilt_AnimationActivity", "componentManager:com.tqhit.battery.one.activity.animation.Hilt_AnimationActivity", "createComponentManager:com.tqhit.battery.one.activity.animation.Hilt_AnimationActivity", "onCreate:com.tqhit.battery.one.activity.animation.Hilt_AnimationActivity", "getDefaultViewModelProviderFactory:com.tqhit.battery.one.activity.animation.Hilt_AnimationActivity", "inject:com.tqhit.battery.one.activity.animation.Hilt_AnimationActivity", "Hilt_AnimationActivity:com.tqhit.battery.one.activity.animation"], "src\\main\\java\\com\\tqhit\\battery\\one\\manager\\charge\\ChargeSession.kt": ["totalMilliAmperes:com.tqhit.battery.one.manager.charge.ChargeSession", "startTime:com.tqhit.battery.one.manager.charge.ChargeSession", "endTime:com.tqhit.battery.one.manager.charge.ChargeSession", "toString:com.tqhit.battery.one.manager.charge.ChargeSession", "screenOffPercent:com.tqhit.battery.one.manager.charge.ChargeSession", "screenOnPercent:com.tqhit.battery.one.manager.charge.ChargeSession", "screenOffMilliAmperes:com.tqhit.battery.one.manager.charge.ChargeSession", "averageSpeed:com.tqhit.battery.one.manager.charge.ChargeSession", "startPercent:com.tqhit.battery.one.manager.charge.ChargeSession", "ChargeSession:com.tqhit.battery.one.manager.charge", "<init>:com.tqhit.battery.one.manager.charge.ChargeSession.Companion", "screenOnMilliAmperes:com.tqhit.battery.one.manager.charge.ChargeSession", "averageSpeedMilliAmperes:com.tqhit.battery.one.manager.charge.ChargeSession", "fromString:com.tqhit.battery.one.manager.charge.ChargeSession.Companion", "endPercent:com.tqhit.battery.one.manager.charge.ChargeSession", "Companion:com.tqhit.battery.one.manager.charge.ChargeSession"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\tqhit\\battery\\one\\features\\emoji\\presentation\\customize\\CustomizeViewModel_HiltModules_KeyModule_Provide_LazyMapKey.java": ["<init>:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModel_HiltModules_KeyModule_Provide_LazyMapKey", "CustomizeViewModel_HiltModules_KeyModule_Provide_LazyMapKey:com.tqhit.battery.one.features.emoji.presentation.customize", "keepFieldType:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModel_HiltModules_KeyModule_Provide_LazyMapKey", "lazyClassKeyName:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModel_HiltModules_KeyModule_Provide_LazyMapKey"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\tqhit\\battery\\one\\features\\emoji\\presentation\\gallery\\BatteryGalleryViewModel_HiltModules_KeyModule_Provide_LazyMapKey.java": ["lazyClassKeyName:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModel_HiltModules_KeyModule_Provide_LazyMapKey", "<init>:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModel_HiltModules_KeyModule_Provide_LazyMapKey", "BatteryGalleryViewModel_HiltModules_KeyModule_Provide_LazyMapKey:com.tqhit.battery.one.features.emoji.presentation.gallery", "keepFieldType:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModel_HiltModules_KeyModule_Provide_LazyMapKey"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\apppower\\permission\\UsageStatsPermissionManager.kt": ["getPermissionExplanation:com.tqhit.battery.one.features.stats.apppower.permission.UsageStatsPermissionManager", "getPermissionRequestTitle:com.tqhit.battery.one.features.stats.apppower.permission.UsageStatsPermissionManager", "getPermissionDeniedMessage:com.tqhit.battery.one.features.stats.apppower.permission.UsageStatsPermissionManager", "initializePermissionLauncher:com.tqhit.battery.one.features.stats.apppower.permission.UsageStatsPermissionManager", "hasPermission:com.tqhit.battery.one.features.stats.apppower.permission.UsageStatsPermissionManager", "createUsageStatsSettingsIntent:com.tqhit.battery.one.features.stats.apppower.permission.UsageStatsPermissionManager", "requestPermissionFallback:com.tqhit.battery.one.features.stats.apppower.permission.UsageStatsPermissionManager", "UsageStatsPermissionManager:com.tqhit.battery.one.features.stats.apppower.permission", "getManualPermissionInstructions:com.tqhit.battery.one.features.stats.apppower.permission.UsageStatsPermissionManager", "requestPermission:com.tqhit.battery.one.features.stats.apppower.permission.UsageStatsPermissionManager", "<init>:com.tqhit.battery.one.features.stats.apppower.permission.UsageStatsPermissionManager.Companion", "isUsageStatsSupported:com.tqhit.battery.one.features.stats.apppower.permission.UsageStatsPermissionManager", "Companion:com.tqhit.battery.one.features.stats.apppower.permission.UsageStatsPermissionManager"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\corebattery\\service\\CoreBatteryStatsService_GeneratedInjector.java": ["injectCoreBatteryStatsService:com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService_GeneratedInjector", "CoreBatteryStatsService_GeneratedInjector:com.tqhit.battery.one.features.stats.corebattery.service"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\fragment\\main\\DischargeFragment_MembersInjector.java": ["injectMembers:com.tqhit.battery.one.fragment.main.DischargeFragment_MembersInjector", "create:com.tqhit.battery.one.fragment.main.DischargeFragment_MembersInjector", "<init>:com.tqhit.battery.one.fragment.main.DischargeFragment_MembersInjector", "DischargeFragment_MembersInjector:com.tqhit.battery.one.fragment.main", "injectVibrationService:com.tqhit.battery.one.fragment.main.DischargeFragment_MembersInjector", "injectApplovinInterstitialAdManager:com.tqhit.battery.one.fragment.main.DischargeFragment_MembersInjector"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_viewmodel_battery_BatteryViewModel_HiltModules_BindsModule.java": ["_com_tqhit_battery_one_viewmodel_battery_BatteryViewModel_HiltModules_BindsModule:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_tqhit_battery_one_viewmodel_battery_BatteryViewModel_HiltModules_BindsModule"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\tqhit\\battery\\one\\databinding\\DialogSetupPasswordBinding.java": ["sdfgsdfg1:com.tqhit.battery.one.databinding.DialogSetupPasswordBinding", "strelka:com.tqhit.battery.one.databinding.DialogSetupPasswordBinding", "DialogSetupPasswordBinding:com.tqhit.battery.one.databinding", "confirmChangeCapacity:com.tqhit.battery.one.databinding.DialogSetupPasswordBinding", "exitChangeCapacity:com.tqhit.battery.one.databinding.DialogSetupPasswordBinding", "bind:com.tqhit.battery.one.databinding.DialogSetupPasswordBinding", "textInputEdit:com.tqhit.battery.one.databinding.DialogSetupPasswordBinding", "cancelChangeCapacity:com.tqhit.battery.one.databinding.DialogSetupPasswordBinding", "textView20:com.tqhit.battery.one.databinding.DialogSetupPasswordBinding", "w22:com.tqhit.battery.one.databinding.DialogSetupPasswordBinding", "getRoot:com.tqhit.battery.one.databinding.DialogSetupPasswordBinding", "inflate:com.tqhit.battery.one.databinding.DialogSetupPasswordBinding"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\tqhit\\battery\\one\\databinding\\FragmentNewChargeBinding.java": ["chargeCurrentSessionSection:com.tqhit.battery.one.databinding.FragmentNewChargeBinding", "chargeOverallAverageSection:com.tqhit.battery.one.databinding.FragmentNewChargeBinding", "bind:com.tqhit.battery.one.databinding.FragmentNewChargeBinding", "chargeStatusDetailsSection:com.tqhit.battery.one.databinding.FragmentNewChargeBinding", "newChargeNotChargingMessage:com.tqhit.battery.one.databinding.FragmentNewChargeBinding", "getRoot:com.tqhit.battery.one.databinding.FragmentNewChargeBinding", "chargeRemainingTimeSection:com.tqhit.battery.one.databinding.FragmentNewChargeBinding", "newChargeScrollView:com.tqhit.battery.one.databinding.FragmentNewChargeBinding", "chargeBatteryWearSection:com.tqhit.battery.one.databinding.FragmentNewChargeBinding", "promoContainer:com.tqhit.battery.one.databinding.FragmentNewChargeBinding", "FragmentNewChargeBinding:com.tqhit.battery.one.databinding", "inflate:com.tqhit.battery.one.databinding.FragmentNewChargeBinding", "chargeMainDisplaySection:com.tqhit.battery.one.databinding.FragmentNewChargeBinding"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\manager\\graph\\TemperatureHistoryManager_Factory.java": ["TemperatureHistoryManager_Factory:com.tqhit.battery.one.manager.graph", "create:com.tqhit.battery.one.manager.graph.TemperatureHistoryManager_Factory", "get:com.tqhit.battery.one.manager.graph.TemperatureHistoryManager_Factory", "<init>:com.tqhit.battery.one.manager.graph.TemperatureHistoryManager_Factory", "newInstance:com.tqhit.battery.one.manager.graph.TemperatureHistoryManager_Factory"], "build\\generated\\data_binding_trigger\\debug\\com\\tqhit\\battery\\one\\DataBindingTriggerClass.java": ["<init>:com.tqhit.battery.one.DataBindingTriggerClass", "DataBindingTriggerClass:com.tqhit.battery.one"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\health\\repository\\HealthRepository.kt": ["switchCalculationMode:com.tqhit.battery.one.features.stats.health.repository.HealthRepository", "getCurrentHealthStatus:com.tqhit.battery.one.features.stats.health.repository.DefaultHealthRepository", "healthChartDataFlow:com.tqhit.battery.one.features.stats.health.repository.DefaultHealthRepository", "DefaultHealthRepository:com.tqhit.battery.one.features.stats.health.repository", "getCurrentChartData:com.tqhit.battery.one.features.stats.health.repository.DefaultHealthRepository", "Companion:com.tqhit.battery.one.features.stats.health.repository.DefaultHealthRepository", "healthStatusFlow:com.tqhit.battery.one.features.stats.health.repository.DefaultHealthRepository", "clearCachedSessionData:com.tqhit.battery.one.features.stats.health.repository.DefaultHealthRepository", "healthChartDataFlow:com.tqhit.battery.one.features.stats.health.repository.HealthRepository", "updateChartData:com.tqhit.battery.one.features.stats.health.repository.HealthRepository", "getCurrentChartData:com.tqhit.battery.one.features.stats.health.repository.HealthRepository", "generateSampleSessionsIfEmpty:com.tqhit.battery.one.features.stats.health.repository.HealthRepository", "switchCalculationMode:com.tqhit.battery.one.features.stats.health.repository.DefaultHealthRepository", "resetSessionClearingFlag:com.tqhit.battery.one.features.stats.health.repository.DefaultHealthRepository", "getCurrentCalculationMode:com.tqhit.battery.one.features.stats.health.repository.DefaultHealthRepository", "updateChartData:com.tqhit.battery.one.features.stats.health.repository.DefaultHealthRepository", "getCurrentHealthStatus:com.tqhit.battery.one.features.stats.health.repository.HealthRepository", "generateSampleSessionsIfEmpty:com.tqhit.battery.one.features.stats.health.repository.DefaultHealthRepository", "getCurrentCalculationMode:com.tqhit.battery.one.features.stats.health.repository.HealthRepository", "<init>:com.tqhit.battery.one.features.stats.health.repository.DefaultHealthRepository.Companion", "HealthRepository:com.tqhit.battery.one.features.stats.health.repository", "healthStatusFlow:com.tqhit.battery.one.features.stats.health.repository.HealthRepository"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\ads\\core\\ApplovinNativeAdManager_Factory.java": ["ApplovinNativeAdManager_Factory:com.tqhit.battery.one.ads.core", "<init>:com.tqhit.battery.one.ads.core.ApplovinNativeAdManager_Factory", "get:com.tqhit.battery.one.ads.core.ApplovinNativeAdManager_Factory", "create:com.tqhit.battery.one.ads.core.ApplovinNativeAdManager_Factory", "newInstance:com.tqhit.battery.one.ads.core.ApplovinNativeAdManager_Factory"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\tqhit\\battery\\one\\databinding\\DialogAppPowerConsumptionBinding.java": ["rvAppsList:com.tqhit.battery.one.databinding.DialogAppPowerConsumptionBinding", "llPermissionRequest:com.tqhit.battery.one.databinding.DialogAppPowerConsumptionBinding", "btnGrantPermission:com.tqhit.battery.one.databinding.DialogAppPowerConsumptionBinding", "tvTotalConsumption:com.tqhit.battery.one.databinding.DialogAppPowerConsumptionBinding", "tvDialogTitle:com.tqhit.battery.one.databinding.DialogAppPowerConsumptionBinding", "DialogAppPowerConsumptionBinding:com.tqhit.battery.one.databinding", "tvSessionDuration:com.tqhit.battery.one.databinding.DialogAppPowerConsumptionBinding", "ivCloseDialog:com.tqhit.battery.one.databinding.DialogAppPowerConsumptionBinding", "bind:com.tqhit.battery.one.databinding.DialogAppPowerConsumptionBinding", "tvNoDataMessage:com.tqhit.battery.one.databinding.DialogAppPowerConsumptionBinding", "tvPermissionStatus:com.tqhit.battery.one.databinding.DialogAppPowerConsumptionBinding", "getRoot:com.tqhit.battery.one.databinding.DialogAppPowerConsumptionBinding", "tvSkipPermission:com.tqhit.battery.one.databinding.DialogAppPowerConsumptionBinding", "tvErrorMessage:com.tqhit.battery.one.databinding.DialogAppPowerConsumptionBinding", "inflate:com.tqhit.battery.one.databinding.DialogAppPowerConsumptionBinding", "progressLoading:com.tqhit.battery.one.databinding.DialogAppPowerConsumptionBinding"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\notifications\\Hilt_UnifiedBatteryNotificationService.java": ["generatedComponent:com.tqhit.battery.one.features.stats.notifications.Hilt_UnifiedBatteryNotificationService", "<init>:com.tqhit.battery.one.features.stats.notifications.Hilt_UnifiedBatteryNotificationService", "Hilt_UnifiedBatteryNotificationService:com.tqhit.battery.one.features.stats.notifications", "onCreate:com.tqhit.battery.one.features.stats.notifications.Hilt_UnifiedBatteryNotificationService", "componentManager:com.tqhit.battery.one.features.stats.notifications.Hilt_UnifiedBatteryNotificationService", "inject:com.tqhit.battery.one.features.stats.notifications.Hilt_UnifiedBatteryNotificationService", "createComponentManager:com.tqhit.battery.one.features.stats.notifications.Hilt_UnifiedBatteryNotificationService"], "src\\main\\java\\com\\tqhit\\battery\\one\\utils\\BackgroundPermissionManager.kt": ["recordDialogDismissal:com.tqhit.battery.one.utils.BackgroundPermissionManager", "shouldShowBackgroundPermissionDialogIgnoreCooldown:com.tqhit.battery.one.utils.BackgroundPermissionManager", "isInCooldownPeriod:com.tqhit.battery.one.utils.BackgroundPermissionManager", "<init>:com.tqhit.battery.one.utils.BackgroundPermissionManager", "isIgnoringBatteryOptimizations:com.tqhit.battery.one.utils.BackgroundPermissionManager", "getRemainingCooldownTime:com.tqhit.battery.one.utils.BackgroundPermissionManager", "BackgroundPermissionManager:com.tqhit.battery.one.utils", "requestBatteryOptimizationPermission:com.tqhit.battery.one.utils.BackgroundPermissionManager", "openDontKillMyAppWebsite:com.tqhit.battery.one.utils.BackgroundPermissionManager", "shouldShowBackgroundPermissionDialog:com.tqhit.battery.one.utils.BackgroundPermissionManager"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\emoji\\domain\\model\\UserCustomization.kt": ["MONOCHROME:com.tqhit.battery.one.features.emoji.domain.model.ColorPalette", "UsageHistory:com.tqhit.battery.one.features.emoji.domain.model", "lastUsedTimestamp:com.tqhit.battery.one.features.emoji.domain.model.UsageHistory", "<init>:com.tqhit.battery.one.features.emoji.domain.model.ColorPalette.NEON", "customizationConfig:com.tqhit.battery.one.features.emoji.domain.model.UserCustomization", "<init>:com.tqhit.battery.one.features.emoji.domain.model.UserPreferences.Companion", "DEFAULT:com.tqhit.battery.one.features.emoji.domain.model.ColorPalette", "ColorPalette:com.tqhit.battery.one.features.emoji.domain.model", "<init>:com.tqhit.battery.one.features.emoji.domain.model.FeatureStatus.PERMISSIONS_REQUIRED", "displayName:com.tqhit.battery.one.features.emoji.domain.model.ColorPalette", "<init>:com.tqhit.battery.one.features.emoji.domain.model.FeatureStatus.SERVICE_DISABLED", "getDefault:com.tqhit.battery.one.features.emoji.domain.model.ColorPalette.Companion", "PASTEL:com.tqhit.battery.one.features.emoji.domain.model.ColorPalette", "usageHistory:com.tqhit.battery.one.features.emoji.domain.model.UserCustomization", "<init>:com.tqhit.battery.one.features.emoji.domain.model.ColorPalette.PASTEL", "UserCustomization:com.tqhit.battery.one.features.emoji.domain.model", "Companion:com.tqhit.battery.one.features.emoji.domain.model.UserCustomization", "enableHapticFeedback:com.tqhit.battery.one.features.emoji.domain.model.UserPreferences", "<init>:com.tqhit.battery.one.features.emoji.domain.model.ColorPalette.MONOCHROME", "<init>:com.tqhit.battery.one.features.emoji.domain.model.FeatureStatus.ACTIVE", "featureEnabledDurationMs:com.tqhit.battery.one.features.emoji.domain.model.UsageHistory", "FeatureStatus:com.tqhit.battery.one.features.emoji.domain.model", "autoSaveChanges:com.tqhit.battery.one.features.emoji.domain.model.UserPreferences", "hasAllRequiredPermissions:com.tqhit.battery.one.features.emoji.domain.model.UserCustomization", "<init>:com.tqhit.battery.one.features.emoji.domain.model.FeatureStatus.FEATURE_DISABLED", "createDefault:com.tqhit.battery.one.features.emoji.domain.model.UserCustomization.Companion", "ACTIVE:com.tqhit.battery.one.features.emoji.domain.model.FeatureStatus", "VIBRANT:com.tqhit.battery.one.features.emoji.domain.model.ColorPalette", "isAccessibilityServiceEnabled:com.tqhit.battery.one.features.emoji.domain.model.UserCustomization", "NEON:com.tqhit.battery.one.features.emoji.domain.model.ColorPalette", "isFeatureReady:com.tqhit.battery.one.features.emoji.domain.model.UserCustomization", "totalConfigurationChanges:com.tqhit.battery.one.features.emoji.domain.model.UsageHistory", "showPreviewInGallery:com.tqhit.battery.one.features.emoji.domain.model.UserPreferences", "Companion:com.tqhit.battery.one.features.emoji.domain.model.ColorPalette", "<init>:com.tqhit.battery.one.features.emoji.domain.model.ColorPalette.Companion", "UserPreferences:com.tqhit.battery.one.features.emoji.domain.model", "favoriteStyleIds:com.tqhit.battery.one.features.emoji.domain.model.UsageHistory", "getDescription:com.tqhit.battery.one.features.emoji.domain.model.FeatureStatus", "showOnboardingTips:com.tqhit.battery.one.features.emoji.domain.model.UserPreferences", "PERMISSIONS_REQUIRED:com.tqhit.battery.one.features.emoji.domain.model.FeatureStatus", "Companion:com.tqhit.battery.one.features.emoji.domain.model.UserPreferences", "FEATURE_DISABLED:com.tqhit.battery.one.features.emoji.domain.model.FeatureStatus", "<init>:com.tqhit.battery.one.features.emoji.domain.model.ColorPalette.VIBRANT", "withStyleUsed:com.tqhit.battery.one.features.emoji.domain.model.UsageHistory", "createDefault:com.tqhit.battery.one.features.emoji.domain.model.UserPreferences.Companion", "<init>:com.tqhit.battery.one.features.emoji.domain.model.UserCustomization.Companion", "withConfigurationChange:com.tqhit.battery.one.features.emoji.domain.model.UsageHistory", "userPreferences:com.tqhit.battery.one.features.emoji.domain.model.UserCustomization", "getFeatureStatus:com.tqhit.battery.one.features.emoji.domain.model.UserCustomization", "displayName:com.tqhit.battery.one.features.emoji.domain.model.FeatureStatus", "<init>:com.tqhit.battery.one.features.emoji.domain.model.FeatureStatus.CONFIGURATION_INVALID", "CONFIGURATION_INVALID:com.tqhit.battery.one.features.emoji.domain.model.FeatureStatus", "withCustomizationConfig:com.tqhit.battery.one.features.emoji.domain.model.UserCustomization", "hasOverlayPermission:com.tqhit.battery.one.features.emoji.domain.model.UserCustomization", "isOperational:com.tqhit.battery.one.features.emoji.domain.model.FeatureStatus", "SERVICE_DISABLED:com.tqhit.battery.one.features.emoji.domain.model.FeatureStatus", "withPermissions:com.tqhit.battery.one.features.emoji.domain.model.UserCustomization", "preferredColorPalette:com.tqhit.battery.one.features.emoji.domain.model.UserPreferences", "hasAccessibilityPermission:com.tqhit.battery.one.features.emoji.domain.model.UserCustomization", "totalStylesUsed:com.tqhit.battery.one.features.emoji.domain.model.UsageHistory", "<init>:com.tqhit.battery.one.features.emoji.domain.model.ColorPalette.DEFAULT"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\emoji\\data\\repository\\CustomizationRepositoryImpl.kt": ["getCurrentUserCustomization:com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImpl", "customizationConfigFlow:com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImpl", "getCurrentCustomizationConfig:com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImpl", "saveUserCustomization:com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImpl", "setFeatureEnabled:com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImpl", "validateAndFixConfiguration:com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImpl", "CustomizationRepositoryImpl:com.tqhit.battery.one.features.emoji.data.repository", "userPreferencesFlow:com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImpl", "saveUserPreferences:com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImpl", "importCustomizationData:com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImpl", "Companion:com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImpl", "recordStyleUsage:com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImpl", "saveCustomizationConfig:com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImpl", "resetToDefaults:com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImpl", "<init>:com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImpl.Companion", "userCustomizationFlow:com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImpl", "clearAllData:com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImpl", "exportCustomizationData:com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImpl", "updatePermissionStates:com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImpl"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\tqhit\\battery\\one\\databinding\\FragmentSettingsBinding.java": ["supportMeButton:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "importDatabase:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "versionApp:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "debugTitle:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "n2:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "p6:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "batteryInfo:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "inflate:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "buttonChangeNotifyIcon:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "buttonChangeNotify:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "heandStabDatabase:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "switchVibration:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "currentSessionAmperageButton:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "d1:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "switchAntiThiefSoundBlock:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "antiThiefTitleBlock:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "switchAntiThiefTitle:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "n121:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "buttonChangeFrequency:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "FragmentSettingsBinding:com.tqhit.battery.one.databinding", "antiThiefInfo:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "currentSessionAmperageSession:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "p5:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "switchAntiThiefBlock:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "switchAnimationBlock:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "buttonSettingsNotify:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "writeMe:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "workInBackgoundButton:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "testNewDischarge:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "bind:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "resetPurchasesButton:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "switchEnableAnimation:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "aboutTranslations:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "removeAds:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "ertretr:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "switchAnimationTimeBlock:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "privacyButton:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "handResetButton:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "switchInfo:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "n11:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "p12334:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "changeLang:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "changeIcon:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "switchEnableAnimationTime:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "switchEnableAntiThief:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "privacySettingButton:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "versionAppButton:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "clearDatabase:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "exportDatabase:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "switchAnimationTimeTitle:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "changeCapacity:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "aT:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "mediationDebuggerButton:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "n12:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "switchIsChargeNotify:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "switchIsDischargeNotify:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "switchHandResetSession:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "scrollView:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "antiThiefBlock:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "n111:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "getRoot:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "indentDown:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "switchAnimationTitle:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "autoStabDatabase:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "switchAntiThiefSoundTitle:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "buyAdvanceAccess:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "switchEnableAntiThiefSound:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "n3:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "changeTemp:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "pT:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "switchBLM:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "nT:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "animationTitleBlock:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "changeDualBattery:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "changeTheme:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "changeSecondColorTheme:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "p11:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "rateButton:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "debuggerButton:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "n21:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "changeBLMButton:com.tqhit.battery.one.databinding.FragmentSettingsBinding", "switchIsShowedOnLockscreen:com.tqhit.battery.one.databinding.FragmentSettingsBinding"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\domain\\SessionMetricsCalculator.kt": ["SessionMetricsCalculator:com.tqhit.battery.one.features.stats.discharge.domain", "isCalculationNeeded:com.tqhit.battery.one.features.stats.discharge.domain.SessionMetricsCalculator", "logSessionMilestones:com.tqhit.battery.one.features.stats.discharge.domain.SessionMetricsCalculator", "logDetailedSessionInfo:com.tqhit.battery.one.features.stats.discharge.domain.SessionMetricsCalculator", "totalSessionDurationMillis:com.tqhit.battery.one.features.stats.discharge.domain.SessionMetricsCalculator.SessionMetrics", "currentPercentage:com.tqhit.battery.one.features.stats.discharge.domain.SessionMetricsCalculator.SessionMetrics", "startTimeFormatted:com.tqhit.battery.one.features.stats.discharge.domain.SessionMetricsCalculator.SessionMetrics", "logSessionMetrics:com.tqhit.battery.one.features.stats.discharge.domain.SessionMetricsCalculator", "SessionMetrics:com.tqhit.battery.one.features.stats.discharge.domain.SessionMetricsCalculator", "totalMahConsumed:com.tqhit.battery.one.features.stats.discharge.domain.SessionMetricsCalculator.SessionMetrics", "totalPercentageDropped:com.tqhit.battery.one.features.stats.discharge.domain.SessionMetricsCalculator.SessionMetrics", "calculateSessionMetrics:com.tqhit.battery.one.features.stats.discharge.domain.SessionMetricsCalculator", "totalSessionDurationHours:com.tqhit.battery.one.features.stats.discharge.domain.SessionMetricsCalculator.SessionMetrics", "calculateMahConsumed:com.tqhit.battery.one.features.stats.discharge.domain.SessionMetricsCalculator", "startPercentage:com.tqhit.battery.one.features.stats.discharge.domain.SessionMetricsCalculator.SessionMetrics"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\emoji\\domain\\use_case\\ResetCustomizationUseCase.kt": ["clearAllData:com.tqhit.battery.one.features.emoji.domain.use_case.ResetCustomizationUseCase", "ResetCustomizationException:com.tqhit.battery.one.features.emoji.domain.use_case", "<init>:com.tqhit.battery.one.features.emoji.domain.use_case.ResetCustomizationUseCase.Companion", "resetCustomizationConfig:com.tqhit.battery.one.features.emoji.domain.use_case.ResetCustomizationUseCase", "disableFeature:com.tqhit.battery.one.features.emoji.domain.use_case.ResetCustomizationUseCase", "safeResetWithBackup:com.tqhit.battery.one.features.emoji.domain.use_case.ResetCustomizationUseCase", "Companion:com.tqhit.battery.one.features.emoji.domain.use_case.ResetCustomizationUseCase", "resetToDefaults:com.tqhit.battery.one.features.emoji.domain.use_case.ResetCustomizationUseCase", "ResetCustomizationUseCase:com.tqhit.battery.one.features.emoji.domain.use_case", "resetUserPreferences:com.tqhit.battery.one.features.emoji.domain.use_case.ResetCustomizationUseCase", "createBackupBeforeReset:com.tqhit.battery.one.features.emoji.domain.use_case.ResetCustomizationUseCase", "restoreFromBackup:com.tqhit.battery.one.features.emoji.domain.use_case.ResetCustomizationUseCase"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\emoji\\data\\repository\\BatteryStyleRepositoryImpl.kt": ["getStylesByCategory:com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl", "emojiImageUrl:com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl.BatteryStyleData", "searchStyles:com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl", "<init>:com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl.Companion", "isLoadingFlow:com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl", "id:com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl.BatteryStyleData", "category:com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl.BatteryStyleData", "batteryImageUrl:com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl.BatteryStyleData", "isPopular:com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl.BatteryStyleData", "isPremium:com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl.BatteryStyleData", "refreshStyles:com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl", "BatteryStyleRepositoryImpl:com.tqhit.battery.one.features.emoji.data.repository", "getStyleById:com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl", "getLastFetchTimestamp:com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl", "name:com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl.BatteryStyleData", "Companion:com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl", "getAllStyles:com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl", "getPremiumStyles:com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl", "batteryStylesFlow:com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl", "getFreeStyles:com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl", "getCurrentStyles:com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl", "clearCache:com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl", "getPopularStyles:com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl", "hasCachedData:com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_features_emoji_presentation_gallery_BatteryGalleryViewModel_HiltModules_BindsModule.java": ["_com_tqhit_battery_one_features_emoji_presentation_gallery_BatteryGalleryViewModel_HiltModules_BindsModule:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_tqhit_battery_one_features_emoji_presentation_gallery_BatteryGalleryViewModel_HiltModules_BindsModule"], "src\\main\\java\\com\\tqhit\\battery\\one\\fragment\\main\\HealthFragment.kt": ["binding:com.tqhit.battery.one.fragment.main.HealthFragment", "clearCachedSessionDataForTesting:com.tqhit.battery.one.fragment.main.HealthFragment", "chargingSessionManager:com.tqhit.battery.one.fragment.main.HealthFragment", "setupData:com.tqhit.battery.one.fragment.main.HealthFragment", "runComprehensiveHealthFragmentTest:com.tqhit.battery.one.fragment.main.HealthFragment", "onStart:com.tqhit.battery.one.fragment.main.HealthFragment", "onStop:com.tqhit.battery.one.fragment.main.HealthFragment", "HistoryType:com.tqhit.battery.one.fragment.main", "applovinInterstitialAdManager:com.tqhit.battery.one.fragment.main.HealthFragment", "historyBatteryRepository:com.tqhit.battery.one.fragment.main.HealthFragment", "<init>:com.tqhit.battery.one.fragment.main.HistoryType.TEMPERATURE", "setupListener:com.tqhit.battery.one.fragment.main.HealthFragment", "<init>:com.tqhit.battery.one.fragment.main.HistoryType.PERCENTAGE", "coreBatteryStatsProvider:com.tqhit.battery.one.fragment.main.HealthFragment", "TEMPERATURE:com.tqhit.battery.one.fragment.main.HistoryType", "PERCENTAGE:com.tqhit.battery.one.fragment.main.HistoryType", "testTemperatureYAxisLabels:com.tqhit.battery.one.fragment.main.HealthFragment", "<init>:com.tqhit.battery.one.fragment.main.HealthFragment", "setupUI:com.tqhit.battery.one.fragment.main.HealthFragment", "HealthFragment:com.tqhit.battery.one.fragment.main"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_features_stats_health_presentation_HealthViewModel_HiltModules_BindsModule.java": ["_com_tqhit_battery_one_features_stats_health_presentation_HealthViewModel_HiltModules_BindsModule:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_tqhit_battery_one_features_stats_health_presentation_HealthViewModel_HiltModules_BindsModule"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\presentation\\Hilt_DischargeFragment.java": ["getDefaultViewModelProviderFactory:com.tqhit.battery.one.features.stats.discharge.presentation.Hilt_DischargeFragment", "Hilt_DischargeFragment:com.tqhit.battery.one.features.stats.discharge.presentation", "<init>:com.tqhit.battery.one.features.stats.discharge.presentation.Hilt_DischargeFragment", "onAttach:com.tqhit.battery.one.features.stats.discharge.presentation.Hilt_DischargeFragment", "getContext:com.tqhit.battery.one.features.stats.discharge.presentation.Hilt_DischargeFragment", "onGetLayoutInflater:com.tqhit.battery.one.features.stats.discharge.presentation.Hilt_DischargeFragment", "inject:com.tqhit.battery.one.features.stats.discharge.presentation.Hilt_DischargeFragment", "generatedComponent:com.tqhit.battery.one.features.stats.discharge.presentation.Hilt_DischargeFragment", "componentManager:com.tqhit.battery.one.features.stats.discharge.presentation.Hilt_DischargeFragment", "createComponentManager:com.tqhit.battery.one.features.stats.discharge.presentation.Hilt_DischargeFragment"], "src\\main\\java\\com\\tqhit\\battery\\one\\ads\\core\\ApplovinAppOpenAdManager.kt": ["showAppOpenAd:com.tqhit.battery.one.ads.core.ApplovinAppOpenAdManager", "ApplovinAppOpenAdManager:com.tqhit.battery.one.ads.core", "loadAppOpenAd:com.tqhit.battery.one.ads.core.ApplovinAppOpenAdManager"], "src\\main\\java\\com\\tqhit\\battery\\one\\ads\\core\\ApplovinRewardedAdManager.kt": ["ApplovinRewardedAdManager:com.tqhit.battery.one.ads.core", "loadRewardedAd:com.tqhit.battery.one.ads.core.ApplovinRewardedAdManager", "showRewardedAd:com.tqhit.battery.one.ads.core.ApplovinRewardedAdManager"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_features_stats_charge_presentation_StatsChargeViewModel_HiltModules_KeyModule.java": ["_com_tqhit_battery_one_features_stats_charge_presentation_StatsChargeViewModel_HiltModules_KeyModule:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_tqhit_battery_one_features_stats_charge_presentation_StatsChargeViewModel_HiltModules_KeyModule"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\tqhit\\battery\\one\\databinding\\FragmentCustomizeBinding.java": ["livePreviewView:com.tqhit.battery.one.databinding.FragmentCustomizeBinding", "emojiSizeSlider:com.tqhit.battery.one.databinding.FragmentCustomizeBinding", "applyButton:com.tqhit.battery.one.databinding.FragmentCustomizeBinding", "previewContainer:com.tqhit.battery.one.databinding.FragmentCustomizeBinding", "inflate:com.tqhit.battery.one.databinding.FragmentCustomizeBinding", "batteryOptionsRecyclerView:com.tqhit.battery.one.databinding.FragmentCustomizeBinding", "colorPickerButton:com.tqhit.battery.one.databinding.FragmentCustomizeBinding", "emojiOptionsRecyclerView:com.tqhit.battery.one.databinding.FragmentCustomizeBinding", "bind:com.tqhit.battery.one.databinding.FragmentCustomizeBinding", "resetButton:com.tqhit.battery.one.databinding.FragmentCustomizeBinding", "showPercentageSwitch:com.tqhit.battery.one.databinding.FragmentCustomizeBinding", "FragmentCustomizeBinding:com.tqhit.battery.one.databinding", "loadingOverlay:com.tqhit.battery.one.databinding.FragmentCustomizeBinding", "loadingText:com.tqhit.battery.one.databinding.FragmentCustomizeBinding", "previewBatterySlider:com.tqhit.battery.one.databinding.FragmentCustomizeBinding", "percentageFontSizeSlider:com.tqhit.battery.one.databinding.FragmentCustomizeBinding", "previewBatteryText:com.tqhit.battery.one.databinding.FragmentCustomizeBinding", "previewLoadingIndicator:com.tqhit.battery.one.databinding.FragmentCustomizeBinding", "saveButton:com.tqhit.battery.one.databinding.FragmentCustomizeBinding", "showEmojiSwitch:com.tqhit.battery.one.databinding.FragmentCustomizeBinding", "getRoot:com.tqhit.battery.one.databinding.FragmentCustomizeBinding"], "src\\main\\java\\com\\tqhit\\battery\\one\\activity\\starting\\StartingViewAdapter.kt": ["destroyItem:com.tqhit.battery.one.activity.starting.StartingViewAdapter", "isViewFromObject:com.tqhit.battery.one.activity.starting.StartingViewAdapter", "instantiateItem:com.tqhit.battery.one.activity.starting.StartingViewAdapter", "getCount:com.tqhit.battery.one.activity.starting.StartingViewAdapter", "StartingViewAdapter:com.tqhit.battery.one.activity.starting"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\health\\presentation\\HealthViewModel.kt": ["getFormattedSessionText:com.tqhit.battery.one.features.stats.health.presentation.HealthViewModel", "<init>:com.tqhit.battery.one.features.stats.health.presentation.HealthViewModel.Companion", "getCurrentChartData:com.tqhit.battery.one.features.stats.health.presentation.HealthViewModel", "Companion:com.tqhit.battery.one.features.stats.health.presentation.HealthViewModel", "isLoading:com.tqhit.battery.one.features.stats.health.presentation.HealthUiState", "getEffectiveCapacityMah:com.tqhit.battery.one.features.stats.health.presentation.HealthUiState", "shouldShowSingularNoData:com.tqhit.battery.one.features.stats.health.presentation.HealthUiState", "isSingularMode:com.tqhit.battery.one.features.stats.health.presentation.HealthViewModel", "getDesignCapacityMah:com.tqhit.battery.one.features.stats.health.presentation.HealthUiState", "getSupportedTimeRanges:com.tqhit.battery.one.features.stats.health.presentation.HealthViewModel", "HealthViewModel:com.tqhit.battery.one.features.stats.health.presentation", "chartData:com.tqhit.battery.one.features.stats.health.presentation.HealthUiState", "errorMessage:com.tqhit.battery.one.features.stats.health.presentation.HealthUiState", "HealthUiState:com.tqhit.battery.one.features.stats.health.presentation", "getTotalSessions:com.tqhit.battery.one.features.stats.health.presentation.HealthUiState", "getCurrentHealthStatus:com.tqhit.battery.one.features.stats.health.presentation.HealthViewModel", "getHealthPercentage:com.tqhit.battery.one.features.stats.health.presentation.HealthUiState", "calculationMode:com.tqhit.battery.one.features.stats.health.presentation.HealthUiState", "hasValidData:com.tqhit.battery.one.features.stats.health.presentation.HealthUiState", "uiState:com.tqhit.battery.one.features.stats.health.presentation.HealthViewModel", "refreshHealthData:com.tqhit.battery.one.features.stats.health.presentation.HealthViewModel", "switchCalculationMode:com.tqhit.battery.one.features.stats.health.presentation.HealthViewModel", "updateChartTimeRange:com.tqhit.battery.one.features.stats.health.presentation.HealthViewModel", "selectedTimeRangeHours:com.tqhit.battery.one.features.stats.health.presentation.HealthUiState", "healthStatus:com.tqhit.battery.one.features.stats.health.presentation.HealthUiState", "isCumulativeMode:com.tqhit.battery.one.features.stats.health.presentation.HealthViewModel", "getCurrentModeExplanation:com.tqhit.battery.one.features.stats.health.presentation.HealthViewModel"], "src\\main\\java\\com\\tqhit\\battery\\one\\fragment\\main\\DischargeFragment.kt": ["vibrationService:com.tqhit.battery.one.fragment.main.DischargeFragment", "setupListener:com.tqhit.battery.one.fragment.main.DischargeFragment", "<init>:com.tqhit.battery.one.fragment.main.DischargeFragment", "DischargeFragment:com.tqhit.battery.one.fragment.main", "<init>:com.tqhit.battery.one.fragment.main.DischargeFragment.Companion", "applovinInterstitialAdManager:com.tqhit.battery.one.fragment.main.DischargeFragment", "setupData:com.tqhit.battery.one.fragment.main.DischargeFragment", "binding:com.tqhit.battery.one.fragment.main.DischargeFragment", "Companion:com.tqhit.battery.one.fragment.main.DischargeFragment"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\domain\\SessionManager_Factory.java": ["<init>:com.tqhit.battery.one.features.stats.discharge.domain.SessionManager_Factory", "get:com.tqhit.battery.one.features.stats.discharge.domain.SessionManager_Factory", "newInstance:com.tqhit.battery.one.features.stats.discharge.domain.SessionManager_Factory", "SessionManager_Factory:com.tqhit.battery.one.features.stats.discharge.domain", "create:com.tqhit.battery.one.features.stats.discharge.domain.SessionManager_Factory"], "src\\main\\java\\com\\tqhit\\battery\\one\\fragment\\main\\animation\\adapter\\CategoryAdapter.kt": ["onCreateViewHolder:com.tqhit.battery.one.fragment.main.animation.adapter.CategoryAdapter", "setSelectedIndex:com.tqhit.battery.one.fragment.main.animation.adapter.CategoryAdapter", "CategoryViewHolder:com.tqhit.battery.one.fragment.main.animation.adapter.CategoryAdapter", "getItemCount:com.tqhit.battery.one.fragment.main.animation.adapter.CategoryAdapter", "onBindViewHolder:com.tqhit.battery.one.fragment.main.animation.adapter.CategoryAdapter", "bind:com.tqhit.battery.one.fragment.main.animation.adapter.CategoryAdapter.CategoryViewHolder", "CategoryAdapter:com.tqhit.battery.one.fragment.main.animation.adapter"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\repository\\AnimationRepository_Factory.java": ["newInstance:com.tqhit.battery.one.repository.AnimationRepository_Factory", "AnimationRepository_Factory:com.tqhit.battery.one.repository", "get:com.tqhit.battery.one.repository.AnimationRepository_Factory", "<init>:com.tqhit.battery.one.repository.AnimationRepository_Factory", "create:com.tqhit.battery.one.repository.AnimationRepository_Factory"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\activity\\main\\MainActivity_GeneratedInjector.java": ["injectMainActivity:com.tqhit.battery.one.activity.main.MainActivity_GeneratedInjector", "MainActivity_GeneratedInjector:com.tqhit.battery.one.activity.main"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\emoji\\presentation\\customize\\CustomizeEvent.kt": ["ConfirmReset:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent", "isCharging:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.BatteryLevelChanged", "level:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.ChangePreviewBatteryLevel", "ChangePercentageFontSize:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent", "<init>:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent", "<init>:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.SaveConfiguration", "showPercentage:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.TogglePercentageVisibility", "NavigateToSettings:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent", "<init>:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.OnResume", "<init>:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.ConfirmReset", "<init>:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.ResetToDefaults", "<init>:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.DismissError", "<init>:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.OpenPositionSelector", "color:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.ChangePercentageColor", "ShareConfiguration:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent", "<init>:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.NavigateToHelp", "<init>:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.RetryOperation", "ChangePreviewBatteryLevel:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent", "ApplyAndEnable:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent", "level:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.BatteryLevelChanged", "<init>:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.PurchasePremiumAccess", "isUIInteraction:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventUtils", "SelectBatteryStyle:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent", "ToggleFeatureEnabled:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent", "ChangePercentageColor:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent", "<init>:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.BrowseMoreStyles", "NavigateToHelp:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent", "CustomizeEventUtils:com.tqhit.battery.one.features.emoji.presentation.customize", "RequestPermissions:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent", "TogglePercentageVisibility:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent", "success:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.CustomizationDataLoaded", "position:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.ChangeOverlayPosition", "ValidateConfiguration:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent", "ResetToDefaults:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent", "OnPause:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent", "UnlockPremiumStyle:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent", "ToggleLivePreview:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent", "RefreshPreview:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent", "PurchasePremiumAccess:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent", "CloseColorPicker:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent", "OpenColorPicker:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent", "<init>:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.DiscardChanges", "NavigateBack:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent", "style:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.UnlockPremiumStyle", "<init>:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.NavigateBack", "scale:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.ChangeEmojiSize", "ConfigurationSaved:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent", "error:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.ConfigurationSaveFailed", "data:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.ImportConfiguration", "<init>:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.ConfigurationSaved", "style:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.SelectEmojiCharacter", "EnableAccessibilityService:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent", "ImportConfiguration:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent", "CustomizationDataLoaded:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent", "style:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.SelectBatteryContainer", "BrowseMoreStyles:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent", "OnResume:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent", "ExportConfiguration:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent", "<init>:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.ClosePositionSelector", "isNavigation:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventUtils", "<init>:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.ApplyAndEnable", "<init>:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.RequestPermissions", "WatchAdToUnlock:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent", "StylesDataLoaded:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent", "style:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.SelectBatteryStyle", "<init>:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventUtils", "SaveConfiguration:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent", "showEmoji:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.ToggleEmojiVisibility", "<init>:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.OnScreenExit", "success:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.StylesDataLoaded", "<init>:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.CloseColorPicker", "OnScreenExit:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent", "CustomizeEvent:com.tqhit.battery.one.features.emoji.presentation.customize", "<init>:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.OnScreenEnter", "enabled:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.ToggleFeatureEnabled", "ToggleEmojiVisibility:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent", "<init>:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.NavigateToSettings", "DiscardChanges:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent", "OpenPositionSelector:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent", "SelectEmojiCharacter:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent", "ChangeOverlayPosition:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent", "<init>:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.OnPause", "BatteryLevelChanged:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent", "<init>:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.ShareConfiguration", "<init>:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.ValidateConfiguration", "<init>:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.ExportConfiguration", "SelectBatteryContainer:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent", "ClosePositionSelector:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent", "<init>:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.OpenColorPicker", "ConfigurationSaveFailed:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent", "ChangeEmojiSize:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent", "<init>:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.RefreshPreview", "OnScreenEnter:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent", "<init>:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.EnableAccessibilityService", "showPreview:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.ToggleLivePreview", "DismissError:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent", "style:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.WatchAdToUnlock", "RetryOperation:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent", "requiresSaving:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventUtils", "sizeDp:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEvent.ChangePercentageFontSize"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\viewmodel\\battery\\BatteryViewModel_Factory.java": ["<init>:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel_Factory", "BatteryViewModel_Factory:com.tqhit.battery.one.viewmodel.battery", "create:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel_Factory", "get:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel_Factory", "newInstance:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel_Factory"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\domain\\SessionMetricsCalculator_Factory.java": ["<init>:com.tqhit.battery.one.features.stats.discharge.domain.SessionMetricsCalculator_Factory", "get:com.tqhit.battery.one.features.stats.discharge.domain.SessionMetricsCalculator_Factory", "newInstance:com.tqhit.battery.one.features.stats.discharge.domain.SessionMetricsCalculator_Factory", "create:com.tqhit.battery.one.features.stats.discharge.domain.SessionMetricsCalculator_Factory", "SessionMetricsCalculator_Factory:com.tqhit.battery.one.features.stats.discharge.domain"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\activity\\overlay\\ChargingOverlayActivity_MembersInjector.java": ["<init>:com.tqhit.battery.one.activity.overlay.ChargingOverlayActivity_MembersInjector", "injectMembers:com.tqhit.battery.one.activity.overlay.ChargingOverlayActivity_MembersInjector", "create:com.tqhit.battery.one.activity.overlay.ChargingOverlayActivity_MembersInjector", "injectAppRepository:com.tqhit.battery.one.activity.overlay.ChargingOverlayActivity_MembersInjector", "ChargingOverlayActivity_MembersInjector:com.tqhit.battery.one.activity.overlay"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\apppower\\data\\AppPowerConsumptionData.kt": ["screenOnTimeMillis:com.tqhit.battery.one.features.stats.apppower.data.AppPowerConsumptionSummary", "formattedPowerConsumption:com.tqhit.battery.one.features.stats.apppower.data.AppPowerConsumptionData", "apps:com.tqhit.battery.one.features.stats.apppower.data.AppPowerConsumptionSummary", "packageName:com.tqhit.battery.one.features.stats.apppower.data.AppPowerConsumptionData", "AppPowerConsumptionSummary:com.tqhit.battery.one.features.stats.apppower.data", "appIcon:com.tqhit.battery.one.features.stats.apppower.data.AppPowerConsumptionData", "usageTimeMillis:com.tqhit.battery.one.features.stats.apppower.data.AppPowerConsumptionData", "sessionStartTime:com.tqhit.battery.one.features.stats.apppower.data.AppPowerConsumptionSummary", "getAppsByUsageTime:com.tqhit.battery.one.features.stats.apppower.data.AppPowerConsumptionSummary", "totalSessionDurationMillis:com.tqhit.battery.one.features.stats.apppower.data.AppPowerConsumptionSummary", "appName:com.tqhit.battery.one.features.stats.apppower.data.AppPowerConsumptionData", "screenOffTimeMillis:com.tqhit.battery.one.features.stats.apppower.data.AppPowerConsumptionSummary", "AppPowerConsumptionData:com.tqhit.battery.one.features.stats.apppower.data", "totalEstimatedPowerConsumptionMah:com.tqhit.battery.one.features.stats.apppower.data.AppPowerConsumptionSummary", "formattedSessionDuration:com.tqhit.battery.one.features.stats.apppower.data.AppPowerConsumptionSummary", "formattedUsageTime:com.tqhit.battery.one.features.stats.apppower.data.AppPowerConsumptionData", "percentageOfTotalUsage:com.tqhit.battery.one.features.stats.apppower.data.AppPowerConsumptionData", "sessionEndTime:com.tqhit.battery.one.features.stats.apppower.data.AppPowerConsumptionSummary", "formattedTotalPowerConsumption:com.tqhit.battery.one.features.stats.apppower.data.AppPowerConsumptionSummary", "getTopApps:com.tqhit.battery.one.features.stats.apppower.data.AppPowerConsumptionSummary", "estimatedPowerConsumptionMah:com.tqhit.battery.one.features.stats.apppower.data.AppPowerConsumptionData", "formattedPercentage:com.tqhit.battery.one.features.stats.apppower.data.AppPowerConsumptionData", "isSystemApp:com.tqhit.battery.one.features.stats.apppower.data.AppPowerConsumptionData"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\apppower\\repository\\AppUsageStatsRepository_Factory.java": ["AppUsageStatsRepository_Factory:com.tqhit.battery.one.features.stats.apppower.repository", "create:com.tqhit.battery.one.features.stats.apppower.repository.AppUsageStatsRepository_Factory", "get:com.tqhit.battery.one.features.stats.apppower.repository.AppUsageStatsRepository_Factory", "newInstance:com.tqhit.battery.one.features.stats.apppower.repository.AppUsageStatsRepository_Factory", "<init>:com.tqhit.battery.one.features.stats.apppower.repository.AppUsageStatsRepository_Factory"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\charge\\presentation\\StatsChargeViewModel.kt": ["isLoading:com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeUiState", "getCurrentSession:com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel", "session:com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeUiState", "uiState:com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel", "StatsChargeViewModel:com.tqhit.battery.one.features.stats.charge.presentation", "StatsChargeUiState:com.tqhit.battery.one.features.stats.charge.presentation", "formatCurrent:com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel", "calculatePower:com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel", "status:com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeUiState", "powerWatts:com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeUiState", "isCharging:com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel", "resetChargeSession:com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel", "Companion:com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel", "timeToFullMillis:com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeUiState", "<init>:com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel.Companion", "getCurrentStatus:com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel", "formatTime:com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel", "formatPower:com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel", "timeToTargetMillis:com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeUiState", "targetPercentage:com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeUiState", "formatVoltage:com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel", "setTargetChargePercentage:com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel", "formatTemperature:com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\tqhit\\battery\\one\\features\\emoji\\presentation\\customize\\CustomizeViewModel_HiltModules_KeyModule_ProvideFactory.java": ["provide:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModel_HiltModules_KeyModule_ProvideFactory", "get:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModel_HiltModules_KeyModule_ProvideFactory", "create:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModel_HiltModules_KeyModule_ProvideFactory", "<init>:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModel_HiltModules_KeyModule_ProvideFactory", "INSTANCE:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder", "<init>:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder", "CustomizeViewModel_HiltModules_KeyModule_ProvideFactory:com.tqhit.battery.one.features.emoji.presentation.customize"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_features_stats_discharge_presentation_DischargeFragment_GeneratedInjector.java": ["_com_tqhit_battery_one_features_stats_discharge_presentation_DischargeFragment_GeneratedInjector:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_tqhit_battery_one_features_stats_discharge_presentation_DischargeFragment_GeneratedInjector"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\tqhit\\battery\\one\\databinding\\SectionChargeCurrentSessionBinding.java": ["chargeCurrentSessionRoot:com.tqhit.battery.one.databinding.SectionChargeCurrentSessionBinding", "valAvgSpeedMixed:com.tqhit.battery.one.databinding.SectionChargeCurrentSessionBinding", "SectionChargeCurrentSessionBinding:com.tqhit.battery.one.databinding", "inflate:com.tqhit.battery.one.databinding.SectionChargeCurrentSessionBinding", "valAvgSpeedScreenOn:com.tqhit.battery.one.databinding.SectionChargeCurrentSessionBinding", "valTotalChargedMah:com.tqhit.battery.one.databinding.SectionChargeCurrentSessionBinding", "valSessionDuration:com.tqhit.battery.one.databinding.SectionChargeCurrentSessionBinding", "valTotalChargedPercent:com.tqhit.battery.one.databinding.SectionChargeCurrentSessionBinding", "valCurrentRate:com.tqhit.battery.one.databinding.SectionChargeCurrentSessionBinding", "valScreenOffTime:com.tqhit.battery.one.databinding.SectionChargeCurrentSessionBinding", "bind:com.tqhit.battery.one.databinding.SectionChargeCurrentSessionBinding", "btnResetSession:com.tqhit.battery.one.databinding.SectionChargeCurrentSessionBinding", "chargeSessionInfoButton:com.tqhit.battery.one.databinding.SectionChargeCurrentSessionBinding", "valScreenOnTime:com.tqhit.battery.one.databinding.SectionChargeCurrentSessionBinding", "getRoot:com.tqhit.battery.one.databinding.SectionChargeCurrentSessionBinding", "valAvgSpeedScreenOff:com.tqhit.battery.one.databinding.SectionChargeCurrentSessionBinding"], "src\\main\\java\\com\\tqhit\\battery\\one\\dialog\\permission\\BackgroundPermissionDialog.kt": ["setupListener:com.tqhit.battery.one.dialog.permission.BackgroundPermissionDialog", "BackgroundPermissionDialog:com.tqhit.battery.one.dialog.permission", "<init>:com.tqhit.battery.one.dialog.permission.BackgroundPermissionDialog.Companion", "Companion:com.tqhit.battery.one.dialog.permission.BackgroundPermissionDialog", "show:com.tqhit.battery.one.dialog.permission.BackgroundPermissionDialog", "dismiss:com.tqhit.battery.one.dialog.permission.BackgroundPermissionDialog", "binding:com.tqhit.battery.one.dialog.permission.BackgroundPermissionDialog", "initWindow:com.tqhit.battery.one.dialog.permission.BackgroundPermissionDialog", "setupUI:com.tqhit.battery.one.dialog.permission.BackgroundPermissionDialog"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\activity\\main\\Hilt_MainActivity.java": ["getDefaultViewModelProviderFactory:com.tqhit.battery.one.activity.main.Hilt_MainActivity", "onDestroy:com.tqhit.battery.one.activity.main.Hilt_MainActivity", "generatedComponent:com.tqhit.battery.one.activity.main.Hilt_MainActivity", "<init>:com.tqhit.battery.one.activity.main.Hilt_MainActivity", "createComponentManager:com.tqhit.battery.one.activity.main.Hilt_MainActivity", "onCreate:com.tqhit.battery.one.activity.main.Hilt_MainActivity", "componentManager:com.tqhit.battery.one.activity.main.Hilt_MainActivity", "Hilt_MainActivity:com.tqhit.battery.one.activity.main", "inject:com.tqhit.battery.one.activity.main.Hilt_MainActivity"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\tqhit\\battery\\one\\features\\stats\\charge\\presentation\\StatsChargeViewModel_HiltModules_BindsModule_Binds_LazyMapKey.java": ["<init>:com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel_HiltModules_BindsModule_Binds_LazyMapKey", "lazyClassKeyName:com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel_HiltModules_BindsModule_Binds_LazyMapKey", "keepFieldType:com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel_HiltModules_BindsModule_Binds_LazyMapKey", "StatsChargeViewModel_HiltModules_BindsModule_Binds_LazyMapKey:com.tqhit.battery.one.features.stats.charge.presentation"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_viewmodel_AppViewModel_HiltModules_BindsModule.java": ["<init>:hilt_aggregated_deps._com_tqhit_battery_one_viewmodel_AppViewModel_HiltModules_BindsModule", "_com_tqhit_battery_one_viewmodel_AppViewModel_HiltModules_BindsModule:hilt_aggregated_deps"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\corebattery\\service\\CoreBatteryStatsService.kt": ["appRepository:com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService", "CoreBatteryStatsService:com.tqhit.battery.one.features.stats.corebattery.service", "ACTION_START_MONITORING:com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService.Companion", "coreBatteryStatsProvider:com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService", "onStartCommand:com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService", "onBind:com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService", "onDestroy:com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService", "isReceiverRegistered:com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService", "Companion:com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService", "onCreate:com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService", "ACTION_STOP_MONITORING:com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService.Companion", "getInstance:com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService.Companion", "<init>:com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService.Companion", "simulateBatteryPercentageChanges:com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService", "<init>:com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService", "ACTION_SIMULATE_BATTERY_CHANGES:com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService.Companion"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\domain\\DischargeCalculator_Factory.java": ["INSTANCE:com.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculator_Factory.InstanceHolder", "<init>:com.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculator_Factory", "DischargeCalculator_Factory:com.tqhit.battery.one.features.stats.discharge.domain", "create:com.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculator_Factory", "<init>:com.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculator_Factory.InstanceHolder", "newInstance:com.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculator_Factory", "get:com.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculator_Factory"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_viewmodel_animation_AnimationViewModel_HiltModules_KeyModule.java": ["<init>:hilt_aggregated_deps._com_tqhit_battery_one_viewmodel_animation_AnimationViewModel_HiltModules_KeyModule", "_com_tqhit_battery_one_viewmodel_animation_AnimationViewModel_HiltModules_KeyModule:hilt_aggregated_deps"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\viewmodel\\AppViewModel_HiltModules.java": ["AppViewModel_HiltModules:com.tqhit.battery.one.viewmodel", "KeyModule:com.tqhit.battery.one.viewmodel.AppViewModel_HiltModules", "provide:com.tqhit.battery.one.viewmodel.AppViewModel_HiltModules.KeyModule", "BindsModule:com.tqhit.battery.one.viewmodel.AppViewModel_HiltModules", "binds:com.tqhit.battery.one.viewmodel.AppViewModel_HiltModules.BindsModule"], "src\\main\\java\\com\\tqhit\\battery\\one\\fragment\\main\\SettingsFragment.kt": ["binding:com.tqhit.battery.one.fragment.main.SettingsFragment", "applovinInterstitialAdManager:com.tqhit.battery.one.fragment.main.SettingsFragment", "SettingsFragment:com.tqhit.battery.one.fragment.main", "setupUI:com.tqhit.battery.one.fragment.main.SettingsFragment", "<init>:com.tqhit.battery.one.fragment.main.SettingsFragment", "preferencesHelper:com.tqhit.battery.one.fragment.main.SettingsFragment"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\tqhit\\battery\\one\\databinding\\ItemSlideLayout3Binding.java": ["button:com.tqhit.battery.one.databinding.ItemSlideLayout3Binding", "nextPage:com.tqhit.battery.one.databinding.ItemSlideLayout3Binding", "textView5:com.tqhit.battery.one.databinding.ItemSlideLayout3Binding", "bind:com.tqhit.battery.one.databinding.ItemSlideLayout3Binding", "inflate:com.tqhit.battery.one.databinding.ItemSlideLayout3Binding", "ItemSlideLayout3Binding:com.tqhit.battery.one.databinding", "getRoot:com.tqhit.battery.one.databinding.ItemSlideLayout3Binding"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\fragment\\main\\Hilt_HealthFragment.java": ["inject:com.tqhit.battery.one.fragment.main.Hilt_HealthFragment", "onAttach:com.tqhit.battery.one.fragment.main.Hilt_HealthFragment", "onGetLayoutInflater:com.tqhit.battery.one.fragment.main.Hilt_HealthFragment", "componentManager:com.tqhit.battery.one.fragment.main.Hilt_HealthFragment", "generatedComponent:com.tqhit.battery.one.fragment.main.Hilt_HealthFragment", "Hilt_HealthFragment:com.tqhit.battery.one.fragment.main", "createComponentManager:com.tqhit.battery.one.fragment.main.Hilt_HealthFragment", "getContext:com.tqhit.battery.one.fragment.main.Hilt_HealthFragment", "<init>:com.tqhit.battery.one.fragment.main.Hilt_HealthFragment", "getDefaultViewModelProviderFactory:com.tqhit.battery.one.fragment.main.Hilt_HealthFragment"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\di\\StatsDischargeProvidersModule_ProvideScreenStateReceiverFactory.java": ["create:com.tqhit.battery.one.features.stats.discharge.di.StatsDischargeProvidersModule_ProvideScreenStateReceiverFactory", "provideScreenStateReceiver:com.tqhit.battery.one.features.stats.discharge.di.StatsDischargeProvidersModule_ProvideScreenStateReceiverFactory", "StatsDischargeProvidersModule_ProvideScreenStateReceiverFactory:com.tqhit.battery.one.features.stats.discharge.di", "<init>:com.tqhit.battery.one.features.stats.discharge.di.StatsDischargeProvidersModule_ProvideScreenStateReceiverFactory", "get:com.tqhit.battery.one.features.stats.discharge.di.StatsDischargeProvidersModule_ProvideScreenStateReceiverFactory"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\BatteryApplication_MembersInjector.java": ["injectMembers:com.tqhit.battery.one.BatteryApplication_MembersInjector", "injectApplovinAppOpenAdManager:com.tqhit.battery.one.BatteryApplication_MembersInjector", "injectApplovinNativeAdManager:com.tqhit.battery.one.BatteryApplication_MembersInjector", "injectPreferencesHelper:com.tqhit.battery.one.BatteryApplication_MembersInjector", "<init>:com.tqhit.battery.one.BatteryApplication_MembersInjector", "injectApplovinInterstitialAdManager:com.tqhit.battery.one.BatteryApplication_MembersInjector", "BatteryApplication_MembersInjector:com.tqhit.battery.one", "injectCoreBatteryServiceHelper:com.tqhit.battery.one.BatteryApplication_MembersInjector", "injectApplovinBannerAdManager:com.tqhit.battery.one.BatteryApplication_MembersInjector", "injectAppRepository:com.tqhit.battery.one.BatteryApplication_MembersInjector", "injectApplovinRewardedAdManager:com.tqhit.battery.one.BatteryApplication_MembersInjector", "create:com.tqhit.battery.one.BatteryApplication_MembersInjector"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_fragment_main_animation_AnimationGridFragment_GeneratedInjector.java": ["<init>:hilt_aggregated_deps._com_tqhit_battery_one_fragment_main_animation_AnimationGridFragment_GeneratedInjector", "_com_tqhit_battery_one_fragment_main_animation_AnimationGridFragment_GeneratedInjector:hilt_aggregated_deps"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_activity_starting_StartingActivity_GeneratedInjector.java": ["<init>:hilt_aggregated_deps._com_tqhit_battery_one_activity_starting_StartingActivity_GeneratedInjector", "_com_tqhit_battery_one_activity_starting_StartingActivity_GeneratedInjector:hilt_aggregated_deps"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\presentation\\DischargeFragment_MembersInjector.java": ["<init>:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment_MembersInjector", "injectInfoButtonManager:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment_MembersInjector", "injectMembers:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment_MembersInjector", "injectTimeConverter:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment_MembersInjector", "injectAppLifecycleManager:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment_MembersInjector", "create:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment_MembersInjector", "DischargeFragment_MembersInjector:com.tqhit.battery.one.features.stats.discharge.presentation"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\ads\\core\\ApplovinBannerAdManager_Factory.java": ["<init>:com.tqhit.battery.one.ads.core.ApplovinBannerAdManager_Factory", "get:com.tqhit.battery.one.ads.core.ApplovinBannerAdManager_Factory", "newInstance:com.tqhit.battery.one.ads.core.ApplovinBannerAdManager_Factory", "create:com.tqhit.battery.one.ads.core.ApplovinBannerAdManager_Factory", "ApplovinBannerAdManager_Factory:com.tqhit.battery.one.ads.core"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\health\\repository\\HistoryBatteryRepository.kt": ["generateSampleDataForTesting:com.tqhit.battery.one.features.stats.health.repository.HistoryBatteryRepository", "getDailyWearData:com.tqhit.battery.one.features.stats.health.repository.HistoryBatteryRepository", "getTemperatureHistoryCount:com.tqhit.battery.one.features.stats.health.repository.HistoryBatteryRepository", "getHistoryTemperatureForHours:com.tqhit.battery.one.features.stats.health.repository.HistoryBatteryRepository", "HistoryBatteryRepository:com.tqhit.battery.one.features.stats.health.repository", "getBatteryHistoryCount:com.tqhit.battery.one.features.stats.health.repository.HistoryBatteryRepository", "clearHistory:com.tqhit.battery.one.features.stats.health.repository.HistoryBatteryRepository", "getHistoryBatteryForHours:com.tqhit.battery.one.features.stats.health.repository.HistoryBatteryRepository", "Companion:com.tqhit.battery.one.features.stats.health.repository.HistoryBatteryRepository", "<init>:com.tqhit.battery.one.features.stats.health.repository.HistoryBatteryRepository.Companion"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\fragment\\main\\Hilt_ChargeFragment.java": ["onAttach:com.tqhit.battery.one.fragment.main.Hilt_ChargeFragment", "inject:com.tqhit.battery.one.fragment.main.Hilt_ChargeFragment", "onGetLayoutInflater:com.tqhit.battery.one.fragment.main.Hilt_ChargeFragment", "componentManager:com.tqhit.battery.one.fragment.main.Hilt_ChargeFragment", "generatedComponent:com.tqhit.battery.one.fragment.main.Hilt_ChargeFragment", "createComponentManager:com.tqhit.battery.one.fragment.main.Hilt_ChargeFragment", "getContext:com.tqhit.battery.one.fragment.main.Hilt_ChargeFragment", "<init>:com.tqhit.battery.one.fragment.main.Hilt_ChargeFragment", "getDefaultViewModelProviderFactory:com.tqhit.battery.one.fragment.main.Hilt_ChargeFragment", "Hilt_ChargeFragment:com.tqhit.battery.one.fragment.main"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\charge\\data\\StatsChargeStatus.kt": ["logCreation:com.tqhit.battery.one.features.stats.charge.data.StatsChargeStatus.Companion", "createDefault:com.tqhit.battery.one.features.stats.charge.data.StatsChargeStatus.Companion", "StatsChargeStatus:com.tqhit.battery.one.features.stats.charge.data", "Companion:com.tqhit.battery.one.features.stats.charge.data.StatsChargeStatus", "percentage:com.tqhit.battery.one.features.stats.charge.data.StatsChargeStatus", "<init>:com.tqhit.battery.one.features.stats.charge.data.StatsChargeStatus.Companion", "isCharging:com.tqhit.battery.one.features.stats.charge.data.StatsChargeStatus", "temperatureCelsius:com.tqhit.battery.one.features.stats.charge.data.StatsChargeStatus", "currentMicroAmperes:com.tqhit.battery.one.features.stats.charge.data.StatsChargeStatus", "voltageMillivolts:com.tqhit.battery.one.features.stats.charge.data.StatsChargeStatus"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_fragment_main_DischargeFragment_GeneratedInjector.java": ["<init>:hilt_aggregated_deps._com_tqhit_battery_one_fragment_main_DischargeFragment_GeneratedInjector", "_com_tqhit_battery_one_fragment_main_DischargeFragment_GeneratedInjector:hilt_aggregated_deps"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\emoji\\domain\\use_case\\LoadCustomizationUseCase.kt": ["LoadCustomizationUseCase:com.tqhit.battery.one.features.emoji.domain.use_case", "getCurrentSelectedStyle:com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase", "EnrichedUserCustomization:com.tqhit.battery.one.features.emoji.domain.use_case", "isSelectedStyleAvailable:com.tqhit.battery.one.features.emoji.domain.use_case.EnrichedUserCustomization", "featureStatus:com.tqhit.battery.one.features.emoji.domain.use_case.EnrichedUserCustomization", "Companion:com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase", "userCustomizationFlow:com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase", "isFeatureReady:com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase", "getStatusMessage:com.tqhit.battery.one.features.emoji.domain.use_case.EnrichedUserCustomization", "getCurrentUserPreferences:com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase", "needsAttention:com.tqhit.battery.one.features.emoji.domain.use_case.EnrichedUserCustomization", "<init>:com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase.Companion", "featureStatusFlow:com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase", "validateCurrentConfiguration:com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase", "isFeatureReadyFlow:com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase", "availableStylesCount:com.tqhit.battery.one.features.emoji.domain.use_case.EnrichedUserCustomization", "userPreferencesFlow:com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase", "getCurrentCustomizationConfig:com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase", "getFeatureStatus:com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase", "getCurrentUserCustomization:com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase", "userCustomization:com.tqhit.battery.one.features.emoji.domain.use_case.EnrichedUserCustomization", "customizationConfigFlow:com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase", "getCurrentEnrichedUserCustomization:com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase", "selectedStyle:com.tqhit.battery.one.features.emoji.domain.use_case.EnrichedUserCustomization"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\apppower\\repository\\AppUsageStatsRepository.kt": ["<init>:com.tqhit.battery.one.features.stats.apppower.repository.AppUsageStatsRepository.Companion", "getAppPowerConsumption:com.tqhit.battery.one.features.stats.apppower.repository.AppUsageStatsRepository", "AppUsageStatsRepository:com.tqhit.battery.one.features.stats.apppower.repository", "Companion:com.tqhit.battery.one.features.stats.apppower.repository.AppUsageStatsRepository", "hasUsageStatsPermission:com.tqhit.battery.one.features.stats.apppower.repository.AppUsageStatsRepository"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\health\\domain\\GetHealthHistoryUseCase_Factory.java": ["newInstance:com.tqhit.battery.one.features.stats.health.domain.GetHealthHistoryUseCase_Factory", "GetHealthHistoryUseCase_Factory:com.tqhit.battery.one.features.stats.health.domain", "<init>:com.tqhit.battery.one.features.stats.health.domain.GetHealthHistoryUseCase_Factory", "create:com.tqhit.battery.one.features.stats.health.domain.GetHealthHistoryUseCase_Factory", "get:com.tqhit.battery.one.features.stats.health.domain.GetHealthHistoryUseCase_Factory"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\navigation\\NavigationState.kt": ["StateChangeReason:com.tqhit.battery.one.features.navigation", "CHARGING_STARTED:com.tqhit.battery.one.features.navigation.StateChangeReason", "isChargingState:com.tqhit.battery.one.features.navigation.NavigationState", "activeFragmentId:com.tqhit.battery.one.features.navigation.NavigationState", "NavigationStateChange:com.tqhit.battery.one.features.navigation", "<init>:com.tqhit.battery.one.features.navigation.StateChangeReason.USER_NAVIGATION", "reason:com.tqhit.battery.one.features.navigation.NavigationStateChange", "newState:com.tqhit.battery.one.features.navigation.NavigationStateChange", "NavigationState:com.tqhit.battery.one.features.navigation", "createFragment:com.tqhit.battery.one.features.navigation.NavigationState", "<init>:com.tqhit.battery.one.features.navigation.StateChangeReason.APP_RESUME", "INITIAL_SETUP:com.tqhit.battery.one.features.navigation.StateChangeReason", "isDischargingState:com.tqhit.battery.one.features.navigation.NavigationState", "getFragmentClass:com.tqhit.battery.one.features.navigation.NavigationState", "previousState:com.tqhit.battery.one.features.navigation.NavigationStateChange", "createDischargingState:com.tqhit.battery.one.features.navigation.NavigationState.Companion", "ALL_MENU_ITEMS:com.tqhit.battery.one.features.navigation.NavigationState.Companion", "USER_NAVIGATION:com.tqhit.battery.one.features.navigation.StateChangeReason", "ALWAYS_VISIBLE_ITEMS:com.tqhit.battery.one.features.navigation.NavigationState.Companion", "createDefaultState:com.tqhit.battery.one.features.navigation.NavigationState.Companion", "<init>:com.tqhit.battery.one.features.navigation.StateChangeReason.CHARGING_STARTED", "<init>:com.tqhit.battery.one.features.navigation.StateChangeReason.CHARGING_STOPPED", "isCharging:com.tqhit.battery.one.features.navigation.NavigationState", "CHARGING_STOPPED:com.tqhit.battery.one.features.navigation.StateChangeReason", "Companion:com.tqhit.battery.one.features.navigation.NavigationState", "FRAGMENT_RESTORE:com.tqhit.battery.one.features.navigation.StateChangeReason", "createChargingState:com.tqhit.battery.one.features.navigation.NavigationState.Companion", "isMenuItemVisible:com.tqhit.battery.one.features.navigation.NavigationState", "createAnimationState:com.tqhit.battery.one.features.navigation.NavigationState.Companion", "visibleMenuItems:com.tqhit.battery.one.features.navigation.NavigationState", "shouldShowTransition:com.tqhit.battery.one.features.navigation.NavigationState", "<init>:com.tqhit.battery.one.features.navigation.NavigationState.Companion", "<init>:com.tqhit.battery.one.features.navigation.StateChangeReason.INITIAL_SETUP", "<init>:com.tqhit.battery.one.features.navigation.StateChangeReason.FRAGMENT_RESTORE", "APP_RESUME:com.tqhit.battery.one.features.navigation.StateChangeReason"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\manager\\discharge\\DischargeSessionManager_Factory.java": ["DischargeSessionManager_Factory:com.tqhit.battery.one.manager.discharge", "get:com.tqhit.battery.one.manager.discharge.DischargeSessionManager_Factory", "create:com.tqhit.battery.one.manager.discharge.DischargeSessionManager_Factory", "<init>:com.tqhit.battery.one.manager.discharge.DischargeSessionManager_Factory", "newInstance:com.tqhit.battery.one.manager.discharge.DischargeSessionManager_Factory"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\cache\\DischargeRatesCache.kt": ["getAverageScreenOnRateMah:com.tqhit.battery.one.features.stats.discharge.cache.DischargeRatesCache", "saveAverageScreenOnRateMah:com.tqhit.battery.one.features.stats.discharge.cache.DischargeRatesCache", "DischargeRatesCache:com.tqhit.battery.one.features.stats.discharge.cache", "saveAverageScreenOffRateMah:com.tqhit.battery.one.features.stats.discharge.cache.DischargeRatesCache", "getAverageScreenOffRateMah:com.tqhit.battery.one.features.stats.discharge.cache.DischargeRatesCache"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\emoji\\presentation\\gallery\\BatteryGalleryState.kt": ["BatteryGalleryState:com.tqhit.battery.one.features.emoji.presentation.gallery", "hasActiveFilters:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryState", "ShowFeatureInfo:com.tqhit.battery.one.features.emoji.presentation.gallery.NavigationEvent", "style:com.tqhit.battery.one.features.emoji.presentation.gallery.NavigationEvent.NavigateToCustomization", "showCategoryFilter:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryState", "showOnlyFree:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryState", "getDisplayErrorMessage:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryState", "searchQuery:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryState", "getStyleCountForCategory:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryState", "getPremiumStyleCount:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryState", "NavigationEvent:com.tqhit.battery.one.features.emoji.presentation.gallery", "selectedCategory:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryState", "NavigateToCustomization:com.tqhit.battery.one.features.emoji.presentation.gallery.NavigationEvent", "navigationEvent:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryState", "isEmpty:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryState", "isDataStale:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryState", "lastRefreshTimestamp:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryState", "<init>:com.tqhit.battery.one.features.emoji.presentation.gallery.NavigationEvent.ShowFeatureInfo", "<init>:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryState.Companion", "allStyles:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryState", "selectedStyleId:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryState", "displayedStyles:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryState", "isEmojiBatteryEnabled:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryState", "showSearchBar:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryState", "Companion:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryState", "NavigateToSettings:com.tqhit.battery.one.features.emoji.presentation.gallery.NavigationEvent", "withFilteredStyles:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryState", "showOnlyPopular:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryState", "hasPermissions:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryState", "hasCachedData:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryState", "<init>:com.tqhit.battery.one.features.emoji.presentation.gallery.NavigationEvent.NavigateToSettings", "getFilterDescription:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryState", "isLoading:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryState", "errorMessage:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryState", "initial:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryState.Companion", "isRefreshing:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryState", "showOnlyPremium:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryState", "getFreeStyleCount:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryState", "<init>:com.tqhit.battery.one.features.emoji.presentation.gallery.NavigationEvent", "categories:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryState", "error:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryState.Companion", "getPopularStyleCount:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryState", "loading:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryState.Companion"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_viewmodel_battery_BatteryViewModel_HiltModules_KeyModule.java": ["<init>:hilt_aggregated_deps._com_tqhit_battery_one_viewmodel_battery_BatteryViewModel_HiltModules_KeyModule", "_com_tqhit_battery_one_viewmodel_battery_BatteryViewModel_HiltModules_KeyModule:hilt_aggregated_deps"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\tqhit\\battery\\one\\databinding\\FragmentEmojiBatteryBinding.java": ["loadingOverlay:com.tqhit.battery.one.databinding.FragmentEmojiBatteryBinding", "loadingProgressBar:com.tqhit.battery.one.databinding.FragmentEmojiBatteryBinding", "categoryChipGroup:com.tqhit.battery.one.databinding.FragmentEmojiBatteryBinding", "filterChipGroup:com.tqhit.battery.one.databinding.FragmentEmojiBatteryBinding", "featureDescription:com.tqhit.battery.one.databinding.FragmentEmojiBatteryBinding", "emptyStateLayout:com.tqhit.battery.one.databinding.FragmentEmojiBatteryBinding", "permissionStatusLayout:com.tqhit.battery.one.databinding.FragmentEmojiBatteryBinding", "permissionStatusText:com.tqhit.battery.one.databinding.FragmentEmojiBatteryBinding", "featureToggleSwitch:com.tqhit.battery.one.databinding.FragmentEmojiBatteryBinding", "chipPremium:com.tqhit.battery.one.databinding.FragmentEmojiBatteryBinding", "filterSection:com.tqhit.battery.one.databinding.FragmentEmojiBatteryBinding", "inflate:com.tqhit.battery.one.databinding.FragmentEmojiBatteryBinding", "chipPopular:com.tqhit.battery.one.databinding.FragmentEmojiBatteryBinding", "scrollView:com.tqhit.battery.one.databinding.FragmentEmojiBatteryBinding", "searchInputLayout:com.tqhit.battery.one.databinding.FragmentEmojiBatteryBinding", "loadingText:com.tqhit.battery.one.databinding.FragmentEmojiBatteryBinding", "permissionStatusIcon:com.tqhit.battery.one.databinding.FragmentEmojiBatteryBinding", "stylesRecyclerView:com.tqhit.battery.one.databinding.FragmentEmojiBatteryBinding", "emptyStateMessage:com.tqhit.battery.one.databinding.FragmentEmojiBatteryBinding", "bind:com.tqhit.battery.one.databinding.FragmentEmojiBatteryBinding", "categoryScrollView:com.tqhit.battery.one.databinding.FragmentEmojiBatteryBinding", "chipAll:com.tqhit.battery.one.databinding.FragmentEmojiBatteryBinding", "getRoot:com.tqhit.battery.one.databinding.FragmentEmojiBatteryBinding", "retryButton:com.tqhit.battery.one.databinding.FragmentEmojiBatteryBinding", "featureToggleCard:com.tqhit.battery.one.databinding.FragmentEmojiBatteryBinding", "requestPermissionsButton:com.tqhit.battery.one.databinding.FragmentEmojiBatteryBinding", "FragmentEmojiBatteryBinding:com.tqhit.battery.one.databinding", "featureTitle:com.tqhit.battery.one.databinding.FragmentEmojiBatteryBinding", "chipFree:com.tqhit.battery.one.databinding.FragmentEmojiBatteryBinding", "searchEditText:com.tqhit.battery.one.databinding.FragmentEmojiBatteryBinding", "emptyStateIcon:com.tqhit.battery.one.databinding.FragmentEmojiBatteryBinding", "emptyStateTitle:com.tqhit.battery.one.databinding.FragmentEmojiBatteryBinding"], "src\\main\\java\\com\\tqhit\\battery\\one\\viewmodel\\battery\\BatteryViewModel.kt": ["dischargeScreenOffAverageSpeedSession:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "batteryHealth:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "temperature:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "dischargeScreenOnMilliAmperesSession:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "screenOffTimeRemainingAt100:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "dischargeScreenOffPercentCurrentSession:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "dischargeScreenOffTimeCurrentSession:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "averageSpeedSession:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "rightNowPercentPerHourSession:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "power:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "chargingRate:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "endTimeSession:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "getCurrentChargingSession:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "setLastValidDischargeSession:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "dischargeScreenOnAverageSpeedSession:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "dischargeScreenOffMilliAmperesSession:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "dischargeEndTimeSession:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "BatteryViewModel:com.tqhit.battery.one.viewmodel.battery", "setSelectedPercent:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "setLastValidChargingSession:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "getHistoryTemperatureForHours:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "getBatteryPolarity:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "amperage:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "dischargeScreenOnTimeCurrentSession:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "endPercentSession:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "totalChargeSession:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "screenOffMilliAmperesSession:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "averageScreenOffSpeed:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "startTimeSession:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "dischargeStartPercentSession:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "startPercentSession:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "dischargeEndPercentSession:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "dischargeTotalMilliAmperesSession:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "batteryCapacity:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "averageScreenOffMilliAmperes:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "setCurrentChargingSession:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "getHistoryBatteryForHours:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "dischargeAverageSpeedSession:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "getLastValidDischargeSession:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "getSelectedPercent:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "usageStyleTimeRemainingAt100:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "selectedPercent:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "setCurrentDischargeSession:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "screenOnTimeRemainingAt100:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "clearChargingSessions:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "averageMilliAmperes:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "getBatteryCapacity:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "usageStyleTimeRemaining:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "chargingTimeRemaining:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "dischargeAverageSpeedMilliAmperesSession:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "screenOnAverageSpeedSession:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "voltage:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "dischargeStartTimeSession:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "screenOffAverageSpeedSession:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "averageSpeed:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "chargingTimeRemainingToTarget:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "dischargeScreenOnPercentCurrentSession:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "isIgnoringBatteryOptimizations:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "screenOnTimeRemaining:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "totalMilliAmperesSession:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "setBatteryCapacity:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "getDailyWearData:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "batteryPercentage:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "averageScreenOnSpeed:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "averageSpeedMilliAmperesSession:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "getCurrentDischargeSession:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "screenOnMilliAmperesSession:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "isCharging:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "clearDischargeSessions:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "dischargeRightNowPercentPerHourSession:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "getLastValidChargingSession:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "screenOffTimeRemaining:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel", "averageScreenOnMilliAmperes:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel"], "src\\main\\java\\com\\tqhit\\battery\\one\\fragment\\main\\ChargeFragment.kt": ["ChargeFragment:com.tqhit.battery.one.fragment.main", "binding:com.tqhit.battery.one.fragment.main.ChargeFragment", "vibrationService:com.tqhit.battery.one.fragment.main.ChargeFragment", "<init>:com.tqhit.battery.one.fragment.main.ChargeFragment", "applovinInterstitialAdManager:com.tqhit.battery.one.fragment.main.ChargeFragment", "setupUI:com.tqhit.battery.one.fragment.main.ChargeFragment", "setupData:com.tqhit.battery.one.fragment.main.ChargeFragment", "setupListener:com.tqhit.battery.one.fragment.main.ChargeFragment"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_features_emoji_presentation_gallery_EmojiBatteryFragment_GeneratedInjector.java": ["_com_tqhit_battery_one_features_emoji_presentation_gallery_EmojiBatteryFragment_GeneratedInjector:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_tqhit_battery_one_features_emoji_presentation_gallery_EmojiBatteryFragment_GeneratedInjector"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\emoji\\presentation\\customize\\CustomizeViewModel_Factory.java": ["CustomizeViewModel_Factory:com.tqhit.battery.one.features.emoji.presentation.customize", "create:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModel_Factory", "get:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModel_Factory", "newInstance:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModel_Factory", "<init>:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModel_Factory"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\emoji\\presentation\\gallery\\EmojiBatteryFragment.kt": ["setupUI:com.tqhit.battery.one.features.emoji.presentation.gallery.EmojiBatteryFragment", "<init>:com.tqhit.battery.one.features.emoji.presentation.gallery.EmojiBatteryFragment", "setupData:com.tqhit.battery.one.features.emoji.presentation.gallery.EmojiBatteryFragment", "EmojiBatteryFragment:com.tqhit.battery.one.features.emoji.presentation.gallery", "onViewCreated:com.tqhit.battery.one.features.emoji.presentation.gallery.EmojiBatteryFragment", "binding:com.tqhit.battery.one.features.emoji.presentation.gallery.EmojiBatteryFragment", "onResume:com.tqhit.battery.one.features.emoji.presentation.gallery.EmojiBatteryFragment", "Companion:com.tqhit.battery.one.features.emoji.presentation.gallery.EmojiBatteryFragment", "onPause:com.tqhit.battery.one.features.emoji.presentation.gallery.EmojiBatteryFragment", "<init>:com.tqhit.battery.one.features.emoji.presentation.gallery.EmojiBatteryFragment.Companion"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_activity_password_EnterPasswordActivity_GeneratedInjector.java": ["_com_tqhit_battery_one_activity_password_EnterPasswordActivity_GeneratedInjector:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_tqhit_battery_one_activity_password_EnterPasswordActivity_GeneratedInjector"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_features_stats_charge_presentation_StatsChargeViewModel_HiltModules_BindsModule.java": ["<init>:hilt_aggregated_deps._com_tqhit_battery_one_features_stats_charge_presentation_StatsChargeViewModel_HiltModules_BindsModule", "_com_tqhit_battery_one_features_stats_charge_presentation_StatsChargeViewModel_HiltModules_BindsModule:hilt_aggregated_deps"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\viewmodel\\animation\\AnimationViewModel_HiltModules.java": ["provide:com.tqhit.battery.one.viewmodel.animation.AnimationViewModel_HiltModules.KeyModule", "BindsModule:com.tqhit.battery.one.viewmodel.animation.AnimationViewModel_HiltModules", "KeyModule:com.tqhit.battery.one.viewmodel.animation.AnimationViewModel_HiltModules", "binds:com.tqhit.battery.one.viewmodel.animation.AnimationViewModel_HiltModules.BindsModule", "AnimationViewModel_HiltModules:com.tqhit.battery.one.viewmodel.animation"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\repository\\DischargeSessionRepository_Factory.java": ["<init>:com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository_Factory", "create:com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository_Factory", "DischargeSessionRepository_Factory:com.tqhit.battery.one.features.stats.discharge.repository", "newInstance:com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository_Factory", "get:com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository_Factory"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\datasource\\ScreenStateReceiver_Factory.java": ["<init>:com.tqhit.battery.one.features.stats.discharge.datasource.ScreenStateReceiver_Factory", "newInstance:com.tqhit.battery.one.features.stats.discharge.datasource.ScreenStateReceiver_Factory", "get:com.tqhit.battery.one.features.stats.discharge.datasource.ScreenStateReceiver_Factory", "create:com.tqhit.battery.one.features.stats.discharge.datasource.ScreenStateReceiver_Factory", "ScreenStateReceiver_Factory:com.tqhit.battery.one.features.stats.discharge.datasource"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\emoji\\domain\\model\\CustomizationConfig.kt": ["CENTER:com.tqhit.battery.one.features.emoji.domain.model.OverlayPosition", "<init>:com.tqhit.battery.one.features.emoji.domain.model.OverlayPosition.BOTTOM_CENTER", "isValid:com.tqhit.battery.one.features.emoji.domain.model.CustomizationConfig", "CENTER_LEFT:com.tqhit.battery.one.features.emoji.domain.model.OverlayPosition", "<init>:com.tqhit.battery.one.features.emoji.domain.model.OverlayPosition.BOTTOM_LEFT", "withFeatureEnabled:com.tqhit.battery.one.features.emoji.domain.model.CustomizationConfig", "<init>:com.tqhit.battery.one.features.emoji.domain.model.OverlayPosition.TOP_CENTER", "OverlayPosition:com.tqhit.battery.one.features.emoji.domain.model", "<init>:com.tqhit.battery.one.features.emoji.domain.model.OverlayPosition.TOP_RIGHT", "selectedEmojiImageUrl:com.tqhit.battery.one.features.emoji.domain.model.CustomizationConfig", "<init>:com.tqhit.battery.one.features.emoji.domain.model.OverlayPosition.CENTER", "TOP_CENTER:com.tqhit.battery.one.features.emoji.domain.model.OverlayPosition", "styleConfig:com.tqhit.battery.one.features.emoji.domain.model.CustomizationConfig", "TOP_LEFT:com.tqhit.battery.one.features.emoji.domain.model.OverlayPosition", "BOTTOM_LEFT:com.tqhit.battery.one.features.emoji.domain.model.OverlayPosition", "withStyleConfig:com.tqhit.battery.one.features.emoji.domain.model.CustomizationConfig", "Companion:com.tqhit.battery.one.features.emoji.domain.model.OverlayPosition", "lastModifiedTimestamp:com.tqhit.battery.one.features.emoji.domain.model.CustomizationConfig", "overlayPosition:com.tqhit.battery.one.features.emoji.domain.model.CustomizationConfig", "<init>:com.tqhit.battery.one.features.emoji.domain.model.OverlayPosition.BOTTOM_RIGHT", "<init>:com.tqhit.battery.one.features.emoji.domain.model.OverlayPosition.Companion", "TOP_RIGHT:com.tqhit.battery.one.features.emoji.domain.model.OverlayPosition", "isFeatureEnabled:com.tqhit.battery.one.features.emoji.domain.model.CustomizationConfig", "Companion:com.tqhit.battery.one.features.emoji.domain.model.CustomizationConfig", "<init>:com.tqhit.battery.one.features.emoji.domain.model.CustomizationConfig.Companion", "<init>:com.tqhit.battery.one.features.emoji.domain.model.OverlayPosition.TOP_LEFT", "BOTTOM_RIGHT:com.tqhit.battery.one.features.emoji.domain.model.OverlayPosition", "validated:com.tqhit.battery.one.features.emoji.domain.model.CustomizationConfig", "withStyle:com.tqhit.battery.one.features.emoji.domain.model.CustomizationConfig", "<init>:com.tqhit.battery.one.features.emoji.domain.model.OverlayPosition.CENTER_LEFT", "BOTTOM_CENTER:com.tqhit.battery.one.features.emoji.domain.model.OverlayPosition", "createDefault:com.tqhit.battery.one.features.emoji.domain.model.CustomizationConfig.Companion", "selectedBatteryImageUrl:com.tqhit.battery.one.features.emoji.domain.model.CustomizationConfig", "displayName:com.tqhit.battery.one.features.emoji.domain.model.OverlayPosition", "CENTER_RIGHT:com.tqhit.battery.one.features.emoji.domain.model.OverlayPosition", "<init>:com.tqhit.battery.one.features.emoji.domain.model.OverlayPosition.CENTER_RIGHT", "getDefault:com.tqhit.battery.one.features.emoji.domain.model.OverlayPosition.Companion", "CustomizationConfig:com.tqhit.battery.one.features.emoji.domain.model", "selectedStyleId:com.tqhit.battery.one.features.emoji.domain.model.CustomizationConfig", "isCustomized:com.tqhit.battery.one.features.emoji.domain.model.CustomizationConfig", "fromBatteryStyle:com.tqhit.battery.one.features.emoji.domain.model.CustomizationConfig.Companion"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_features_stats_charge_presentation_StatsChargeFragment_GeneratedInjector.java": ["<init>:hilt_aggregated_deps._com_tqhit_battery_one_features_stats_charge_presentation_StatsChargeFragment_GeneratedInjector", "_com_tqhit_battery_one_features_stats_charge_presentation_StatsChargeFragment_GeneratedInjector:hilt_aggregated_deps"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\activity\\password\\EnterPasswordActivity_MembersInjector.java": ["create:com.tqhit.battery.one.activity.password.EnterPasswordActivity_MembersInjector", "EnterPasswordActivity_MembersInjector:com.tqhit.battery.one.activity.password", "injectAppRepository:com.tqhit.battery.one.activity.password.EnterPasswordActivity_MembersInjector", "<init>:com.tqhit.battery.one.activity.password.EnterPasswordActivity_MembersInjector", "injectMembers:com.tqhit.battery.one.activity.password.EnterPasswordActivity_MembersInjector"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\service\\EnhancedDischargeTimerService_GeneratedInjector.java": ["injectEnhancedDischargeTimerService:com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerService_GeneratedInjector", "EnhancedDischargeTimerService_GeneratedInjector:com.tqhit.battery.one.features.stats.discharge.service"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\hilt_aggregated_deps\\_com_tqhit_battery_one_features_stats_discharge_di_StatsDischargeModule.java": ["<init>:hilt_aggregated_deps._com_tqhit_battery_one_features_stats_discharge_di_StatsDischargeModule", "_com_tqhit_battery_one_features_stats_discharge_di_StatsDischargeModule:hilt_aggregated_deps"], "src\\main\\java\\com\\tqhit\\battery\\one\\fragment\\main\\animation\\data\\AnimationCategory.kt": ["content:com.tqhit.battery.one.fragment.main.animation.data.AnimationCategory", "name:com.tqhit.battery.one.fragment.main.animation.data.AnimationCategory", "AnimationCategory:com.tqhit.battery.one.fragment.main.animation.data"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\activity\\splash\\Hilt_SplashActivity.java": ["componentManager:com.tqhit.battery.one.activity.splash.Hilt_SplashActivity", "onDestroy:com.tqhit.battery.one.activity.splash.Hilt_SplashActivity", "inject:com.tqhit.battery.one.activity.splash.Hilt_SplashActivity", "<init>:com.tqhit.battery.one.activity.splash.Hilt_SplashActivity", "onCreate:com.tqhit.battery.one.activity.splash.Hilt_SplashActivity", "createComponentManager:com.tqhit.battery.one.activity.splash.Hilt_SplashActivity", "generatedComponent:com.tqhit.battery.one.activity.splash.Hilt_SplashActivity", "getDefaultViewModelProviderFactory:com.tqhit.battery.one.activity.splash.Hilt_SplashActivity", "Hilt_SplashActivity:com.tqhit.battery.one.activity.splash"], "src\\main\\java\\com\\tqhit\\battery\\one\\manager\\graph\\HistoryManager.kt": ["valueToString:com.tqhit.battery.one.manager.graph.HistoryManager", "getHistory:com.tqhit.battery.one.manager.graph.HistoryManager", "HistoryManager:com.tqhit.battery.one.manager.graph", "valueToString:com.tqhit.battery.one.manager.graph.BatteryHistoryManager", "valueToString:com.tqhit.battery.one.manager.graph.TemperatureHistoryManager", "preferencesHelper:com.tqhit.battery.one.manager.graph.HistoryManager", "timestamp:com.tqhit.battery.one.manager.graph.HistoryEntry", "clearHistory:com.tqhit.battery.one.manager.graph.HistoryManager", "parseValue:com.tqhit.battery.one.manager.graph.TemperatureHistoryManager", "TemperatureHistoryManager:com.tqhit.battery.one.manager.graph", "getMinuteHistory:com.tqhit.battery.one.manager.graph.HistoryManager", "HistoryEntry:com.tqhit.battery.one.manager.graph", "parseValue:com.tqhit.battery.one.manager.graph.BatteryHistoryManager", "BatteryHistoryManager:com.tqhit.battery.one.manager.graph", "value:com.tqhit.battery.one.manager.graph.HistoryEntry", "history:com.tqhit.battery.one.manager.graph.HistoryManager", "parseValue:com.tqhit.battery.one.manager.graph.HistoryManager", "addEntry:com.tqhit.battery.one.manager.graph.HistoryManager"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\fragment\\main\\ChargeFragment_GeneratedInjector.java": ["ChargeFragment_GeneratedInjector:com.tqhit.battery.one.fragment.main", "injectChargeFragment:com.tqhit.battery.one.fragment.main.ChargeFragment_GeneratedInjector"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\navigation\\DynamicNavigationManager_Factory.java": ["get:com.tqhit.battery.one.features.navigation.DynamicNavigationManager_Factory", "newInstance:com.tqhit.battery.one.features.navigation.DynamicNavigationManager_Factory", "<init>:com.tqhit.battery.one.features.navigation.DynamicNavigationManager_Factory", "DynamicNavigationManager_Factory:com.tqhit.battery.one.features.navigation", "create:com.tqhit.battery.one.features.navigation.DynamicNavigationManager_Factory"], "src\\main\\java\\com\\tqhit\\battery\\one\\dialog\\utils\\LoadingDialog.kt": ["setupUI:com.tqhit.battery.one.dialog.utils.LoadingDialog", "initWindow:com.tqhit.battery.one.dialog.utils.LoadingDialog", "LoadingDialog:com.tqhit.battery.one.dialog.utils", "binding:com.tqhit.battery.one.dialog.utils.LoadingDialog"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\fragment\\main\\ChargeFragment_MembersInjector.java": ["<init>:com.tqhit.battery.one.fragment.main.ChargeFragment_MembersInjector", "injectApplovinInterstitialAdManager:com.tqhit.battery.one.fragment.main.ChargeFragment_MembersInjector", "ChargeFragment_MembersInjector:com.tqhit.battery.one.fragment.main", "create:com.tqhit.battery.one.fragment.main.ChargeFragment_MembersInjector", "injectVibrationService:com.tqhit.battery.one.fragment.main.ChargeFragment_MembersInjector", "injectMembers:com.tqhit.battery.one.fragment.main.ChargeFragment_MembersInjector"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\activity\\splash\\SplashActivity_GeneratedInjector.java": ["SplashActivity_GeneratedInjector:com.tqhit.battery.one.activity.splash", "injectSplashActivity:com.tqhit.battery.one.activity.splash.SplashActivity_GeneratedInjector"], "src\\main\\java\\com\\tqhit\\battery\\one\\manager\\discharge\\DischargeSessionManager.kt": ["getAverageScreenOnMilliAmperes:com.tqhit.battery.one.manager.discharge.DischargeSessionManager", "getAverageMilliAmperes:com.tqhit.battery.one.manager.discharge.DischargeSessionManager", "getAverageScreenOffSpeed:com.tqhit.battery.one.manager.discharge.DischargeSessionManager", "getAverageSpeed:com.tqhit.battery.one.manager.discharge.DischargeSessionManager", "clearSessions:com.tqhit.battery.one.manager.discharge.DischargeSessionManager", "getAverageScreenOffMilliAmperes:com.tqhit.battery.one.manager.discharge.DischargeSessionManager", "DischargeSessionManager:com.tqhit.battery.one.manager.discharge", "<init>:com.tqhit.battery.one.manager.discharge.DischargeSessionManager.Companion", "getAverageScreenOnSpeed:com.tqhit.battery.one.manager.discharge.DischargeSessionManager", "Companion:com.tqhit.battery.one.manager.discharge.DischargeSessionManager", "addSession:com.tqhit.battery.one.manager.discharge.DischargeSessionManager"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\domain\\GapEstimationCalculator.kt": ["calculateGapEstimation:com.tqhit.battery.one.features.stats.discharge.domain.GapEstimationCalculator", "GapEstimationCalculator:com.tqhit.battery.one.features.stats.discharge.domain", "Companion:com.tqhit.battery.one.features.stats.discharge.domain.GapEstimationCalculator", "<init>:com.tqhit.battery.one.features.stats.discharge.domain.GapEstimationCalculator.Companion"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_viewmodel_AppViewModel_HiltModules_KeyModule.java": ["<init>:hilt_aggregated_deps._com_tqhit_battery_one_viewmodel_AppViewModel_HiltModules_KeyModule", "_com_tqhit_battery_one_viewmodel_AppViewModel_HiltModules_KeyModule:hilt_aggregated_deps"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\hilt_aggregated_deps\\_com_tqhit_battery_one_features_stats_charge_di_StatsChargeDIModule.java": ["<init>:hilt_aggregated_deps._com_tqhit_battery_one_features_stats_charge_di_StatsChargeDIModule", "_com_tqhit_battery_one_features_stats_charge_di_StatsChargeDIModule:hilt_aggregated_deps"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\fragment\\main\\animation\\AnimationGridFragment_GeneratedInjector.java": ["AnimationGridFragment_GeneratedInjector:com.tqhit.battery.one.fragment.main.animation", "injectAnimationGridFragment:com.tqhit.battery.one.fragment.main.animation.AnimationGridFragment_GeneratedInjector"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\tqhit\\battery\\one\\databinding\\FragmentStatsChargeBinding.java": ["targetPercentageTitle:com.tqhit.battery.one.databinding.FragmentStatsChargeBinding", "tvChargingStatus:com.tqhit.battery.one.databinding.FragmentStatsChargeBinding", "fullChargeBlock:com.tqhit.battery.one.databinding.FragmentStatsChargeBinding", "btnResetSession:com.tqhit.battery.one.databinding.FragmentStatsChargeBinding", "chargeProgBarPercent:com.tqhit.battery.one.databinding.FragmentStatsChargeBinding", "targetPercentLabel:com.tqhit.battery.one.databinding.FragmentStatsChargeBinding", "tvTimeToFull:com.tqhit.battery.one.databinding.FragmentStatsChargeBinding", "percentInnerLayout:com.tqhit.battery.one.databinding.FragmentStatsChargeBinding", "tvPercentage:com.tqhit.battery.one.databinding.FragmentStatsChargeBinding", "tvVoltage:com.tqhit.battery.one.databinding.FragmentStatsChargeBinding", "tvSessionStartTime:com.tqhit.battery.one.databinding.FragmentStatsChargeBinding", "tvTimeToTarget:com.tqhit.battery.one.databinding.FragmentStatsChargeBinding", "tvSessionPercentageCharged:com.tqhit.battery.one.databinding.FragmentStatsChargeBinding", "timeEstimatesLayout:com.tqhit.battery.one.databinding.FragmentStatsChargeBinding", "tvSessionTotalChargeMah:com.tqhit.battery.one.databinding.FragmentStatsChargeBinding", "tvTargetPercentage:com.tqhit.battery.one.databinding.FragmentStatsChargeBinding", "tvSessionDuration:com.tqhit.battery.one.databinding.FragmentStatsChargeBinding", "targetPercentageRoot:com.tqhit.battery.one.databinding.FragmentStatsChargeBinding", "tvTemperature:com.tqhit.battery.one.databinding.FragmentStatsChargeBinding", "seekbarTarget:com.tqhit.battery.one.databinding.FragmentStatsChargeBinding", "bind:com.tqhit.battery.one.databinding.FragmentStatsChargeBinding", "chargingStatusBlock:com.tqhit.battery.one.databinding.FragmentStatsChargeBinding", "statsChargeScrollView:com.tqhit.battery.one.databinding.FragmentStatsChargeBinding", "targetChargeBlock:com.tqhit.battery.one.databinding.FragmentStatsChargeBinding", "percentLayout:com.tqhit.battery.one.databinding.FragmentStatsChargeBinding", "batteryAlarmBtn:com.tqhit.battery.one.databinding.FragmentStatsChargeBinding", "inflate:com.tqhit.battery.one.databinding.FragmentStatsChargeBinding", "tvPower:com.tqhit.battery.one.databinding.FragmentStatsChargeBinding", "chargeCurrentSessionRoot:com.tqhit.battery.one.databinding.FragmentStatsChargeBinding", "statsChargeMainDisplayRoot:com.tqhit.battery.one.databinding.FragmentStatsChargeBinding", "FragmentStatsChargeBinding:com.tqhit.battery.one.databinding", "getRoot:com.tqhit.battery.one.databinding.FragmentStatsChargeBinding", "tvCurrent:com.tqhit.battery.one.databinding.FragmentStatsChargeBinding", "dividerTarget:com.tqhit.battery.one.databinding.FragmentStatsChargeBinding"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\tqhit\\battery\\one\\databinding\\ActivityAnimationBinding.java": ["textDate:com.tqhit.battery.one.databinding.ActivityAnimationBinding", "inflate:com.tqhit.battery.one.databinding.ActivityAnimationBinding", "applyButton:com.tqhit.battery.one.databinding.ActivityAnimationBinding", "textTime:com.tqhit.battery.one.databinding.ActivityAnimationBinding", "ActivityAnimationBinding:com.tqhit.battery.one.databinding", "playerView:com.tqhit.battery.one.databinding.ActivityAnimationBinding", "timeRemainingValue:com.tqhit.battery.one.databinding.ActivityAnimationBinding", "iconAd:com.tqhit.battery.one.databinding.ActivityAnimationBinding", "getRoot:com.tqhit.battery.one.databinding.ActivityAnimationBinding", "applyBlock:com.tqhit.battery.one.databinding.ActivityAnimationBinding", "timeRemaining:com.tqhit.battery.one.databinding.ActivityAnimationBinding", "batteryPercent:com.tqhit.battery.one.databinding.ActivityAnimationBinding", "dateTimeContainer:com.tqhit.battery.one.databinding.ActivityAnimationBinding", "textBtn:com.tqhit.battery.one.databinding.ActivityAnimationBinding", "backButton:com.tqhit.battery.one.databinding.ActivityAnimationBinding", "bind:com.tqhit.battery.one.databinding.ActivityAnimationBinding"], "src\\main\\java\\com\\tqhit\\battery\\one\\fragment\\main\\animation\\data\\AnimationItem.kt": ["mediaOriginal:com.tqhit.battery.one.fragment.main.animation.data.AnimationItem", "AnimationItem:com.tqhit.battery.one.fragment.main.animation.data", "isPremium:com.tqhit.battery.one.fragment.main.animation.data.AnimationItem", "thumbnail:com.tqhit.battery.one.fragment.main.animation.data.AnimationItem"], "src\\main\\java\\com\\tqhit\\battery\\one\\ads\\core\\ApplovinNativeAdManager.kt": ["ApplovinNativeAdManager:com.tqhit.battery.one.ads.core"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\fragment\\main\\animation\\Hilt_AnimationGridFragment.java": ["onAttach:com.tqhit.battery.one.fragment.main.animation.Hilt_AnimationGridFragment", "onGetLayoutInflater:com.tqhit.battery.one.fragment.main.animation.Hilt_AnimationGridFragment", "componentManager:com.tqhit.battery.one.fragment.main.animation.Hilt_AnimationGridFragment", "inject:com.tqhit.battery.one.fragment.main.animation.Hilt_AnimationGridFragment", "getDefaultViewModelProviderFactory:com.tqhit.battery.one.fragment.main.animation.Hilt_AnimationGridFragment", "getContext:com.tqhit.battery.one.fragment.main.animation.Hilt_AnimationGridFragment", "createComponentManager:com.tqhit.battery.one.fragment.main.animation.Hilt_AnimationGridFragment", "generatedComponent:com.tqhit.battery.one.fragment.main.animation.Hilt_AnimationGridFragment", "<init>:com.tqhit.battery.one.fragment.main.animation.Hilt_AnimationGridFragment", "Hilt_AnimationGridFragment:com.tqhit.battery.one.fragment.main.animation"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\activity\\animation\\AnimationActivity_GeneratedInjector.java": ["AnimationActivity_GeneratedInjector:com.tqhit.battery.one.activity.animation", "injectAnimationActivity:com.tqhit.battery.one.activity.animation.AnimationActivity_GeneratedInjector"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\health\\presentation\\HealthViewModel_HiltModules.java": ["KeyModule:com.tqhit.battery.one.features.stats.health.presentation.HealthViewModel_HiltModules", "BindsModule:com.tqhit.battery.one.features.stats.health.presentation.HealthViewModel_HiltModules", "binds:com.tqhit.battery.one.features.stats.health.presentation.HealthViewModel_HiltModules.BindsModule", "HealthViewModel_HiltModules:com.tqhit.battery.one.features.stats.health.presentation", "provide:com.tqhit.battery.one.features.stats.health.presentation.HealthViewModel_HiltModules.KeyModule"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\apppower\\presentation\\AppPowerConsumptionDialogFactory.kt": ["create:com.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionDialogFactory", "AppPowerConsumptionDialogFactory:com.tqhit.battery.one.features.stats.apppower.presentation"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\fragment\\main\\HealthFragment_MembersInjector.java": ["injectMembers:com.tqhit.battery.one.fragment.main.HealthFragment_MembersInjector", "create:com.tqhit.battery.one.fragment.main.HealthFragment_MembersInjector", "injectChargingSessionManager:com.tqhit.battery.one.fragment.main.HealthFragment_MembersInjector", "HealthFragment_MembersInjector:com.tqhit.battery.one.fragment.main", "<init>:com.tqhit.battery.one.fragment.main.HealthFragment_MembersInjector", "injectHistoryBatteryRepository:com.tqhit.battery.one.fragment.main.HealthFragment_MembersInjector", "injectCoreBatteryStatsProvider:com.tqhit.battery.one.fragment.main.HealthFragment_MembersInjector", "injectApplovinInterstitialAdManager:com.tqhit.battery.one.fragment.main.HealthFragment_MembersInjector"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_features_stats_discharge_service_EnhancedDischargeTimerService_GeneratedInjector.java": ["_com_tqhit_battery_one_features_stats_discharge_service_EnhancedDischargeTimerService_GeneratedInjector:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_tqhit_battery_one_features_stats_discharge_service_EnhancedDischargeTimerService_GeneratedInjector"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\viewmodel\\battery\\BatteryViewModel_HiltModules.java": ["provide:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel_HiltModules.KeyModule", "binds:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel_HiltModules.BindsModule", "BatteryViewModel_HiltModules:com.tqhit.battery.one.viewmodel.battery", "KeyModule:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel_HiltModules", "BindsModule:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel_HiltModules"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\notifications\\UnifiedBatteryNotificationService_GeneratedInjector.java": ["UnifiedBatteryNotificationService_GeneratedInjector:com.tqhit.battery.one.features.stats.notifications", "injectUnifiedBatteryNotificationService:com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationService_GeneratedInjector"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\tqhit\\battery\\one\\databinding\\SectionChargeRemainingTimeBinding.java": ["labelTimeToTarget:com.tqhit.battery.one.databinding.SectionChargeRemainingTimeBinding", "getRoot:com.tqhit.battery.one.databinding.SectionChargeRemainingTimeBinding", "bind:com.tqhit.battery.one.databinding.SectionChargeRemainingTimeBinding", "valTimeToFull:com.tqhit.battery.one.databinding.SectionChargeRemainingTimeBinding", "chargeRemainingTimeRoot:com.tqhit.battery.one.databinding.SectionChargeRemainingTimeBinding", "SectionChargeRemainingTimeBinding:com.tqhit.battery.one.databinding", "inflate:com.tqhit.battery.one.databinding.SectionChargeRemainingTimeBinding", "valTimeToTarget:com.tqhit.battery.one.databinding.SectionChargeRemainingTimeBinding"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\emoji\\presentation\\customize\\adapter\\EmojiOptionAdapter.kt": ["<init>:com.tqhit.battery.one.features.emoji.presentation.customize.adapter.EmojiOptionAdapter.Companion", "Companion:com.tqhit.battery.one.features.emoji.presentation.customize.adapter.EmojiOptionAdapter", "onCreateViewHolder:com.tqhit.battery.one.features.emoji.presentation.customize.adapter.EmojiOptionAdapter", "EmojiOptionViewHolder:com.tqhit.battery.one.features.emoji.presentation.customize.adapter.EmojiOptionAdapter", "onBindViewHolder:com.tqhit.battery.one.features.emoji.presentation.customize.adapter.EmojiOptionAdapter", "setSelectedEmojiId:com.tqhit.battery.one.features.emoji.presentation.customize.adapter.EmojiOptionAdapter", "bind:com.tqhit.battery.one.features.emoji.presentation.customize.adapter.EmojiOptionAdapter.EmojiOptionViewHolder", "areItemsTheSame:com.tqhit.battery.one.features.emoji.presentation.customize.adapter.EmojiOptionAdapter.BatteryStyleDiffCallback", "EmojiOptionAdapter:com.tqhit.battery.one.features.emoji.presentation.customize.adapter", "areContentsTheSame:com.tqhit.battery.one.features.emoji.presentation.customize.adapter.EmojiOptionAdapter.BatteryStyleDiffCallback", "<init>:com.tqhit.battery.one.features.emoji.presentation.customize.adapter.EmojiOptionAdapter.BatteryStyleDiffCallback"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\tqhit\\battery\\one\\databinding\\ItemSlideLayout7Binding.java": ["textViewResetCharge:com.tqhit.battery.one.databinding.ItemSlideLayout7Binding", "ItemSlideLayout7Binding:com.tqhit.battery.one.databinding", "bind:com.tqhit.battery.one.databinding.ItemSlideLayout7Binding", "main:com.tqhit.battery.one.databinding.ItemSlideLayout7Binding", "capacity:com.tqhit.battery.one.databinding.ItemSlideLayout7Binding", "parameter:com.tqhit.battery.one.databinding.ItemSlideLayout7Binding", "deviceName:com.tqhit.battery.one.databinding.ItemSlideLayout7Binding", "text5:com.tqhit.battery.one.databinding.ItemSlideLayout7Binding", "inflate:com.tqhit.battery.one.databinding.ItemSlideLayout7Binding", "button:com.tqhit.battery.one.databinding.ItemSlideLayout7Binding", "manw:com.tqhit.battery.one.databinding.ItemSlideLayout7Binding", "progressbar2:com.tqhit.battery.one.databinding.ItemSlideLayout7Binding", "indentTop:com.tqhit.battery.one.databinding.ItemSlideLayout7Binding", "startMainActivity:com.tqhit.battery.one.databinding.ItemSlideLayout7Binding", "discharging:com.tqhit.battery.one.databinding.ItemSlideLayout7Binding", "textView4:com.tqhit.battery.one.databinding.ItemSlideLayout7Binding", "getRoot:com.tqhit.battery.one.databinding.ItemSlideLayout7Binding", "changeCapacity:com.tqhit.battery.one.databinding.ItemSlideLayout7Binding", "polarity:com.tqhit.battery.one.databinding.ItemSlideLayout7Binding"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\tqhit\\battery\\one\\databinding\\ItemSlideLayout6Binding.java": ["textViewResetCharge:com.tqhit.battery.one.databinding.ItemSlideLayout6Binding", "autorunView:com.tqhit.battery.one.databinding.ItemSlideLayout6Binding", "bind:com.tqhit.battery.one.databinding.ItemSlideLayout6Binding", "buttonLayout:com.tqhit.battery.one.databinding.ItemSlideLayout6Binding", "textView15:com.tqhit.battery.one.databinding.ItemSlideLayout6Binding", "dontkillmyappButton:com.tqhit.battery.one.databinding.ItemSlideLayout6Binding", "nextPage:com.tqhit.battery.one.databinding.ItemSlideLayout6Binding", "button:com.tqhit.battery.one.databinding.ItemSlideLayout6Binding", "inflate:com.tqhit.battery.one.databinding.ItemSlideLayout6Binding", "textView4:com.tqhit.battery.one.databinding.ItemSlideLayout6Binding", "workInBackgroundPermission:com.tqhit.battery.one.databinding.ItemSlideLayout6Binding", "textView16:com.tqhit.battery.one.databinding.ItemSlideLayout6Binding", "textView19:com.tqhit.battery.one.databinding.ItemSlideLayout6Binding", "textView18:com.tqhit.battery.one.databinding.ItemSlideLayout6Binding", "ItemSlideLayout6Binding:com.tqhit.battery.one.databinding", "buttonEnable:com.tqhit.battery.one.databinding.ItemSlideLayout6Binding", "getRoot:com.tqhit.battery.one.databinding.ItemSlideLayout6Binding"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\tqhit\\battery\\one\\databinding\\FragmentAnimationGridBinding.java": ["categoryRecyclerView:com.tqhit.battery.one.databinding.FragmentAnimationGridBinding", "bind:com.tqhit.battery.one.databinding.FragmentAnimationGridBinding", "animationTitle:com.tqhit.battery.one.databinding.FragmentAnimationGridBinding", "scrollView:com.tqhit.battery.one.databinding.FragmentAnimationGridBinding", "FragmentAnimationGridBinding:com.tqhit.battery.one.databinding", "animationRecyclerView:com.tqhit.battery.one.databinding.FragmentAnimationGridBinding", "animationInfo:com.tqhit.battery.one.databinding.FragmentAnimationGridBinding", "inflate:com.tqhit.battery.one.databinding.FragmentAnimationGridBinding", "getRoot:com.tqhit.battery.one.databinding.FragmentAnimationGridBinding"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\emoji\\domain\\repository\\BatteryStyleRepository.kt": ["getCurrentStyles:com.tqhit.battery.one.features.emoji.domain.repository.BatteryStyleRepository", "clearCache:com.tqhit.battery.one.features.emoji.domain.repository.BatteryStyleRepository", "getAllStyles:com.tqhit.battery.one.features.emoji.domain.repository.BatteryStyleRepository", "BatteryStyleRepository:com.tqhit.battery.one.features.emoji.domain.repository", "getStyleById:com.tqhit.battery.one.features.emoji.domain.repository.BatteryStyleRepository", "getLastFetchTimestamp:com.tqhit.battery.one.features.emoji.domain.repository.BatteryStyleRepository", "refreshStyles:com.tqhit.battery.one.features.emoji.domain.repository.BatteryStyleRepository", "isLoadingFlow:com.tqhit.battery.one.features.emoji.domain.repository.BatteryStyleRepository", "getStylesByCategory:com.tqhit.battery.one.features.emoji.domain.repository.BatteryStyleRepository", "getPremiumStyles:com.tqhit.battery.one.features.emoji.domain.repository.BatteryStyleRepository", "batteryStylesFlow:com.tqhit.battery.one.features.emoji.domain.repository.BatteryStyleRepository", "searchStyles:com.tqhit.battery.one.features.emoji.domain.repository.BatteryStyleRepository", "getFreeStyles:com.tqhit.battery.one.features.emoji.domain.repository.BatteryStyleRepository", "getPopularStyles:com.tqhit.battery.one.features.emoji.domain.repository.BatteryStyleRepository", "hasCachedData:com.tqhit.battery.one.features.emoji.domain.repository.BatteryStyleRepository"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\domain\\DischargeRateCalculator.kt": ["percentPerHour:com.tqhit.battery.one.features.stats.discharge.domain.DischargeRateCalculator.SessionRates", "DischargeRateCalculator:com.tqhit.battery.one.features.stats.discharge.domain", "calculateMahConsumed:com.tqhit.battery.one.features.stats.discharge.domain.DischargeRateCalculator", "calculateDischargeRates:com.tqhit.battery.one.features.stats.discharge.domain.DischargeRateCalculator", "SessionRates:com.tqhit.battery.one.features.stats.discharge.domain.DischargeRateCalculator", "mixedRateMahPerHour:com.tqhit.battery.one.features.stats.discharge.domain.DischargeRateCalculator.SessionRates", "screenOffRateMahPerHour:com.tqhit.battery.one.features.stats.discharge.domain.DischargeRateCalculator.SessionRates", "screenOffMahConsumed:com.tqhit.battery.one.features.stats.discharge.domain.DischargeRateCalculator.ConsumptionByState", "calculateConsumptionByState:com.tqhit.battery.one.features.stats.discharge.domain.DischargeRateCalculator", "calculateMixedRate:com.tqhit.battery.one.features.stats.discharge.domain.DischargeRateCalculator", "logMahConsumed:com.tqhit.battery.one.features.stats.discharge.domain.DischargeRateCalculator", "ConsumptionByState:com.tqhit.battery.one.features.stats.discharge.domain.DischargeRateCalculator", "screenOnRateMahPerHour:com.tqhit.battery.one.features.stats.discharge.domain.DischargeRateCalculator.SessionRates", "logRateChanges:com.tqhit.battery.one.features.stats.discharge.domain.DischargeRateCalculator", "screenOnMahConsumed:com.tqhit.battery.one.features.stats.discharge.domain.DischargeRateCalculator.ConsumptionByState"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\emoji\\presentation\\customize\\CustomizeState.kt": ["getDisplayErrorMessage:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeState", "featureStatus:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeState", "canSave:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeState", "withAvailableStyles:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeState", "enrichedCustomization:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeState", "previewBatteryLevel:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeState", "showPositionSelector:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeState", "withEditingConfig:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeState", "requiresPermissions:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeState", "withUIState:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeState", "withError:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeState", "isAnyLoading:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeState", "hasErrors:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeState", "hasUnsavedChanges:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeState", "usesPremiumFeatures:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeState", "availableStyles:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeState", "selectedEmojiStyleId:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeState", "previewUpdateTimestamp:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeState", "withCustomization:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeState", "canApplyChanges:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeState", "<init>:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeState.Companion", "isConfigModified:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeState", "Companion:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeState", "currentCustomization:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeState", "editingConfig:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeState", "showLivePreview:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeState", "getStyleDisplayTitle:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeState", "selectedBatteryStyleId:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeState", "withClearedErrors:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeState", "CustomizeState:com.tqhit.battery.one.features.emoji.presentation.customize", "isLoading:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeState", "showColorPicker:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeState", "withLoadingState:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeState", "isPreviewLoading:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeState", "getPreviewConfig:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeState", "createInitial:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeState.Companion", "hasCompleteStyleSelection:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeState", "availableEmojiStyles:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeState", "selectedStyle:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeState", "availableBatteryStyles:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeState", "validationErrors:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeState", "withEnrichedCustomization:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeState", "isSaving:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeState", "isLoadingStyles:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeState", "withStyleSelection:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeState", "errorMessage:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeState", "canEnableFeature:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeState", "getCurrentOverlayPosition:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeState"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\presentation\\DischargeViewModel.kt": ["screenOnTimeAt100PercentMs:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeUiState", "areTimeEstimationsLoading:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeUiState", "screenOffTimeAt100PercentMs:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeViewModel.TimeEstimations", "mixedUsageTimeRemainingMs:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeUiState", "screenOffTimeRemainingMs:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeViewModel.TimeEstimations", "screenOnTimeUI:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeUiState", "screenOffTimeRemainingMs:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeUiState", "batteryPercentage:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeUiState", "mixedUsageTimeRemainingMs:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeViewModel.TimeEstimations", "mixedUsageTimeAt100PercentMs:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeUiState", "<init>:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeViewModel.Companion", "isCurrentSessionLoading:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeUiState", "isCharging:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeUiState", "screenOnTimeRemainingMs:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeViewModel.TimeEstimations", "batteryCapacityMah:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeUiState", "onCleared:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeViewModel", "DischargeUiState:com.tqhit.battery.one.features.stats.discharge.presentation", "Companion:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeViewModel", "screenOffTimeAt100PercentMs:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeUiState", "DischargeViewModel:com.tqhit.battery.one.features.stats.discharge.presentation", "isLoadingInitial:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeUiState", "screenOffTimeUI:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeUiState", "screenOnTimeAt100PercentMs:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeViewModel.TimeEstimations", "mixedUsageTimeAt100PercentMs:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeViewModel.TimeEstimations", "uiState:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeViewModel", "screenOnTimeRemainingMs:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeUiState", "currentSession:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeUiState", "resetSessionData:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeViewModel"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\emoji\\presentation\\gallery\\Hilt_EmojiBatteryFragment.java": ["getContext:com.tqhit.battery.one.features.emoji.presentation.gallery.Hilt_EmojiBatteryFragment", "generatedComponent:com.tqhit.battery.one.features.emoji.presentation.gallery.Hilt_EmojiBatteryFragment", "Hilt_EmojiBatteryFragment:com.tqhit.battery.one.features.emoji.presentation.gallery", "componentManager:com.tqhit.battery.one.features.emoji.presentation.gallery.Hilt_EmojiBatteryFragment", "createComponentManager:com.tqhit.battery.one.features.emoji.presentation.gallery.Hilt_EmojiBatteryFragment", "inject:com.tqhit.battery.one.features.emoji.presentation.gallery.Hilt_EmojiBatteryFragment", "getDefaultViewModelProviderFactory:com.tqhit.battery.one.features.emoji.presentation.gallery.Hilt_EmojiBatteryFragment", "onGetLayoutInflater:com.tqhit.battery.one.features.emoji.presentation.gallery.Hilt_EmojiBatteryFragment", "<init>:com.tqhit.battery.one.features.emoji.presentation.gallery.Hilt_EmojiBatteryFragment", "onAttach:com.tqhit.battery.one.features.emoji.presentation.gallery.Hilt_EmojiBatteryFragment"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\emoji\\presentation\\gallery\\BatteryGalleryViewModel_HiltModules.java": ["BatteryGalleryViewModel_HiltModules:com.tqhit.battery.one.features.emoji.presentation.gallery", "BindsModule:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModel_HiltModules", "KeyModule:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModel_HiltModules", "provide:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModel_HiltModules.KeyModule", "binds:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModel_HiltModules.BindsModule"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\tqhit\\battery\\one\\features\\emoji\\presentation\\gallery\\BatteryGalleryViewModel_HiltModules_BindsModule_Binds_LazyMapKey.java": ["keepFieldType:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModel_HiltModules_BindsModule_Binds_LazyMapKey", "BatteryGalleryViewModel_HiltModules_BindsModule_Binds_LazyMapKey:com.tqhit.battery.one.features.emoji.presentation.gallery", "<init>:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModel_HiltModules_BindsModule_Binds_LazyMapKey", "lazyClassKeyName:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModel_HiltModules_BindsModule_Binds_LazyMapKey"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\corebattery\\service\\CoreBatteryServiceHelper_Factory.java": ["<init>:com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryServiceHelper_Factory", "CoreBatteryServiceHelper_Factory:com.tqhit.battery.one.features.stats.corebattery.service", "create:com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryServiceHelper_Factory", "get:com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryServiceHelper_Factory", "newInstance:com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryServiceHelper_Factory"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\ads\\core\\ApplovinAppOpenAdManager_Factory.java": ["<init>:com.tqhit.battery.one.ads.core.ApplovinAppOpenAdManager_Factory", "create:com.tqhit.battery.one.ads.core.ApplovinAppOpenAdManager_Factory", "ApplovinAppOpenAdManager_Factory:com.tqhit.battery.one.ads.core", "newInstance:com.tqhit.battery.one.ads.core.ApplovinAppOpenAdManager_Factory", "get:com.tqhit.battery.one.ads.core.ApplovinAppOpenAdManager_Factory"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\activity\\starting\\StartingActivity_MembersInjector.java": ["injectApplovinInterstitialAdManager:com.tqhit.battery.one.activity.starting.StartingActivity_MembersInjector", "StartingActivity_MembersInjector:com.tqhit.battery.one.activity.starting", "<init>:com.tqhit.battery.one.activity.starting.StartingActivity_MembersInjector", "create:com.tqhit.battery.one.activity.starting.StartingActivity_MembersInjector", "injectMembers:com.tqhit.battery.one.activity.starting.StartingActivity_MembersInjector"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\emoji\\presentation\\customize\\adapter\\BatteryOptionAdapter.kt": ["setSelectedBatteryId:com.tqhit.battery.one.features.emoji.presentation.customize.adapter.BatteryOptionAdapter", "<init>:com.tqhit.battery.one.features.emoji.presentation.customize.adapter.BatteryOptionAdapter.BatteryStyleDiffCallback", "onCreateViewHolder:com.tqhit.battery.one.features.emoji.presentation.customize.adapter.BatteryOptionAdapter", "bind:com.tqhit.battery.one.features.emoji.presentation.customize.adapter.BatteryOptionAdapter.BatteryOptionViewHolder", "areContentsTheSame:com.tqhit.battery.one.features.emoji.presentation.customize.adapter.BatteryOptionAdapter.BatteryStyleDiffCallback", "BatteryOptionViewHolder:com.tqhit.battery.one.features.emoji.presentation.customize.adapter.BatteryOptionAdapter", "BatteryOptionAdapter:com.tqhit.battery.one.features.emoji.presentation.customize.adapter", "Companion:com.tqhit.battery.one.features.emoji.presentation.customize.adapter.BatteryOptionAdapter", "<init>:com.tqhit.battery.one.features.emoji.presentation.customize.adapter.BatteryOptionAdapter.Companion", "onBindViewHolder:com.tqhit.battery.one.features.emoji.presentation.customize.adapter.BatteryOptionAdapter", "areItemsTheSame:com.tqhit.battery.one.features.emoji.presentation.customize.adapter.BatteryOptionAdapter.BatteryStyleDiffCallback"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\health\\repository\\DefaultHealthRepository_Factory.java": ["DefaultHealthRepository_Factory:com.tqhit.battery.one.features.stats.health.repository", "<init>:com.tqhit.battery.one.features.stats.health.repository.DefaultHealthRepository_Factory", "get:com.tqhit.battery.one.features.stats.health.repository.DefaultHealthRepository_Factory", "newInstance:com.tqhit.battery.one.features.stats.health.repository.DefaultHealthRepository_Factory", "create:com.tqhit.battery.one.features.stats.health.repository.DefaultHealthRepository_Factory"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\charge\\presentation\\StatsChargeViewModel_Factory.java": ["<init>:com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel_Factory", "StatsChargeViewModel_Factory:com.tqhit.battery.one.features.stats.charge.presentation", "get:com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel_Factory", "newInstance:com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel_Factory", "create:com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel_Factory"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\tqhit\\battery\\one\\databinding\\LayoutDischargeSectionStatusAndEstimatesBinding.java": ["LayoutDischargeSectionStatusAndEstimatesBinding:com.tqhit.battery.one.databinding", "saeTvScreenOffTime:com.tqhit.battery.one.databinding.LayoutDischargeSectionStatusAndEstimatesBinding", "saeTvScreenOnTime:com.tqhit.battery.one.databinding.LayoutDischargeSectionStatusAndEstimatesBinding", "getRoot:com.tqhit.battery.one.databinding.LayoutDischargeSectionStatusAndEstimatesBinding", "saeRlScreenOffEstimate:com.tqhit.battery.one.databinding.LayoutDischargeSectionStatusAndEstimatesBinding", "saeRlMixedUsageEstimate:com.tqhit.battery.one.databinding.LayoutDischargeSectionStatusAndEstimatesBinding", "statusAndEstimatesRoot:com.tqhit.battery.one.databinding.LayoutDischargeSectionStatusAndEstimatesBinding", "saeRlScreenOnEstimate:com.tqhit.battery.one.databinding.LayoutDischargeSectionStatusAndEstimatesBinding", "bind:com.tqhit.battery.one.databinding.LayoutDischargeSectionStatusAndEstimatesBinding", "inflate:com.tqhit.battery.one.databinding.LayoutDischargeSectionStatusAndEstimatesBinding", "saeTvFormattedStatusText:com.tqhit.battery.one.databinding.LayoutDischargeSectionStatusAndEstimatesBinding", "saeTvPercentage:com.tqhit.battery.one.databinding.LayoutDischargeSectionStatusAndEstimatesBinding", "saeTvMixedUsageTime:com.tqhit.battery.one.databinding.LayoutDischargeSectionStatusAndEstimatesBinding"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\tqhit\\battery\\one\\databinding\\DialogSelectColorThemeBinding.java": ["strelka:com.tqhit.battery.one.databinding.DialogSelectColorThemeBinding", "textView20:com.tqhit.battery.one.databinding.DialogSelectColorThemeBinding", "bind:com.tqhit.battery.one.databinding.DialogSelectColorThemeBinding", "lightBlueBtn:com.tqhit.battery.one.databinding.DialogSelectColorThemeBinding", "nightBlueBtn:com.tqhit.battery.one.databinding.DialogSelectColorThemeBinding", "getRoot:com.tqhit.battery.one.databinding.DialogSelectColorThemeBinding", "redBtn:com.tqhit.battery.one.databinding.DialogSelectColorThemeBinding", "pinkBtn:com.tqhit.battery.one.databinding.DialogSelectColorThemeBinding", "colorBtn2:com.tqhit.battery.one.databinding.DialogSelectColorThemeBinding", "table1:com.tqhit.battery.one.databinding.DialogSelectColorThemeBinding", "colorBtn5:com.tqhit.battery.one.databinding.DialogSelectColorThemeBinding", "colorBtn7:com.tqhit.battery.one.databinding.DialogSelectColorThemeBinding", "inflate:com.tqhit.battery.one.databinding.DialogSelectColorThemeBinding", "colorBtn9:com.tqhit.battery.one.databinding.DialogSelectColorThemeBinding", "lightGreenBtn:com.tqhit.battery.one.databinding.DialogSelectColorThemeBinding", "teloBtn:com.tqhit.battery.one.databinding.DialogSelectColorThemeBinding", "exitColor:com.tqhit.battery.one.databinding.DialogSelectColorThemeBinding", "orangeBtn:com.tqhit.battery.one.databinding.DialogSelectColorThemeBinding", "blueBtn:com.tqhit.battery.one.databinding.DialogSelectColorThemeBinding", "colorBtn1:com.tqhit.battery.one.databinding.DialogSelectColorThemeBinding", "colorBtn10:com.tqhit.battery.one.databinding.DialogSelectColorThemeBinding", "colorBtn3:com.tqhit.battery.one.databinding.DialogSelectColorThemeBinding", "table2:com.tqhit.battery.one.databinding.DialogSelectColorThemeBinding", "colorBtn4:com.tqhit.battery.one.databinding.DialogSelectColorThemeBinding", "colorBtn6:com.tqhit.battery.one.databinding.DialogSelectColorThemeBinding", "colorBtn8:com.tqhit.battery.one.databinding.DialogSelectColorThemeBinding", "greenBtn:com.tqhit.battery.one.databinding.DialogSelectColorThemeBinding", "DialogSelectColorThemeBinding:com.tqhit.battery.one.databinding", "goldBtn:com.tqhit.battery.one.databinding.DialogSelectColorThemeBinding"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\domain\\AppLifecycleManager_Factory.java": ["create:com.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManager_Factory", "<init>:com.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManager_Factory.InstanceHolder", "INSTANCE:com.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManager_Factory.InstanceHolder", "<init>:com.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManager_Factory", "get:com.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManager_Factory", "newInstance:com.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManager_Factory", "AppLifecycleManager_Factory:com.tqhit.battery.one.features.stats.discharge.domain"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\domain\\ScreenTimeValidationService.kt": ["PROPORTIONAL_SCALING:com.tqhit.battery.one.features.stats.discharge.domain.CorrectionStrategy", "Companion:com.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationService", "PROPORTIONAL_SCALED:com.tqhit.battery.one.features.stats.discharge.domain.CorrectionType", "<init>:com.tqhit.battery.one.features.stats.discharge.domain.CorrectionType.PROPORTIONAL_SCALED", "<init>:com.tqhit.battery.one.features.stats.discharge.domain.ValidationResult", "<init>:com.tqhit.battery.one.features.stats.discharge.domain.CorrectionType.OFF_TIME_ADJUSTED", "Corrected:com.tqhit.battery.one.features.stats.discharge.domain.ValidationResult", "ScreenTimeValidationService:com.tqhit.battery.one.features.stats.discharge.domain", "<init>:com.tqhit.battery.one.features.stats.discharge.domain.CorrectionStrategy.ADJUST_OFF_TIME", "OFF_TIME_ADJUSTED:com.tqhit.battery.one.features.stats.discharge.domain.CorrectionType", "getAppStateInfo:com.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationService", "correctionType:com.tqhit.battery.one.features.stats.discharge.domain.ValidationResult.Corrected", "Valid:com.tqhit.battery.one.features.stats.discharge.domain.ValidationResult", "CorrectionType:com.tqhit.battery.one.features.stats.discharge.domain", "<init>:com.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationService.Companion", "ValidationResult:com.tqhit.battery.one.features.stats.discharge.domain", "<init>:com.tqhit.battery.one.features.stats.discharge.domain.ValidationResult.NoValidationNeeded", "<init>:com.tqhit.battery.one.features.stats.discharge.domain.CorrectionStrategy.PROPORTIONAL_SCALING", "<init>:com.tqhit.battery.one.features.stats.discharge.domain.CorrectionStrategy.NO_CORRECTION", "screenOnTime:com.tqhit.battery.one.features.stats.discharge.domain.ValidationResult.Valid", "NO_CORRECTION:com.tqhit.battery.one.features.stats.discharge.domain.CorrectionStrategy", "correctedScreenOffTime:com.tqhit.battery.one.features.stats.discharge.domain.ValidationResult.Corrected", "ADJUST_OFF_TIME:com.tqhit.battery.one.features.stats.discharge.domain.CorrectionStrategy", "validateScreenTimes:com.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationService", "shouldTriggerUiUpdate:com.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationService", "NoValidationNeeded:com.tqhit.battery.one.features.stats.discharge.domain.ValidationResult", "screenOffTime:com.tqhit.battery.one.features.stats.discharge.domain.ValidationResult.Valid", "correctedScreenOnTime:com.tqhit.battery.one.features.stats.discharge.domain.ValidationResult.Corrected"], "src\\main\\java\\com\\tqhit\\battery\\one\\dialog\\capacity\\SetupPasswordDialog.kt": ["setupListener:com.tqhit.battery.one.dialog.capacity.SetupPasswordDialog", "SetupPasswordDialog:com.tqhit.battery.one.dialog.capacity", "binding:com.tqhit.battery.one.dialog.capacity.SetupPasswordDialog", "initWindow:com.tqhit.battery.one.dialog.capacity.SetupPasswordDialog"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_features_emoji_presentation_customize_CustomizeViewModel_HiltModules_BindsModule.java": ["_com_tqhit_battery_one_features_emoji_presentation_customize_CustomizeViewModel_HiltModules_BindsModule:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_tqhit_battery_one_features_emoji_presentation_customize_CustomizeViewModel_HiltModules_BindsModule"], "src\\main\\java\\com\\tqhit\\battery\\one\\utils\\PermissionUtils.kt": ["PermissionUtils:com.tqhit.battery.one.utils", "shouldShowRequestPermissionRationale:com.tqhit.battery.one.utils.PermissionUtils", "resetNotificationPermissionDeniedCount:com.tqhit.battery.one.utils.PermissionUtils", "isNotificationPermissionGranted:com.tqhit.battery.one.utils.PermissionUtils", "shouldShowRequestPermissionRationaleForFragment:com.tqhit.battery.one.utils.PermissionUtils", "requestNotificationPermission:com.tqhit.battery.one.utils.PermissionUtils", "shouldShowRequestPermissionRationaleForActivity:com.tqhit.battery.one.utils.PermissionUtils", "<init>:com.tqhit.battery.one.utils.PermissionUtils", "incrementNotificationPermissionDeniedCount:com.tqhit.battery.one.utils.PermissionUtils", "openAppSettings:com.tqhit.battery.one.utils.PermissionUtils"], "src\\main\\java\\com\\tqhit\\battery\\one\\dialog\\capacity\\ChangeCapacityDialog.kt": ["ChangeCapacityDialog:com.tqhit.battery.one.dialog.capacity", "setupListener:com.tqhit.battery.one.dialog.capacity.ChangeCapacityDialog", "setupData:com.tqhit.battery.one.dialog.capacity.ChangeCapacityDialog", "binding:com.tqhit.battery.one.dialog.capacity.ChangeCapacityDialog", "initWindow:com.tqhit.battery.one.dialog.capacity.ChangeCapacityDialog"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\dialog\\alarm\\SelectBatteryAlarmLowDialog_Factory.java": ["create:com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmLowDialog_Factory", "<init>:com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmLowDialog_Factory", "get:com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmLowDialog_Factory", "newInstance:com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmLowDialog_Factory", "SelectBatteryAlarmLowDialog_Factory:com.tqhit.battery.one.dialog.alarm"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\tqhit\\battery\\one\\viewmodel\\animation\\AnimationViewModel_HiltModules_KeyModule_ProvideFactory.java": ["AnimationViewModel_HiltModules_KeyModule_ProvideFactory:com.tqhit.battery.one.viewmodel.animation", "create:com.tqhit.battery.one.viewmodel.animation.AnimationViewModel_HiltModules_KeyModule_ProvideFactory", "<init>:com.tqhit.battery.one.viewmodel.animation.AnimationViewModel_HiltModules_KeyModule_ProvideFactory", "<init>:com.tqhit.battery.one.viewmodel.animation.AnimationViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder", "INSTANCE:com.tqhit.battery.one.viewmodel.animation.AnimationViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder", "get:com.tqhit.battery.one.viewmodel.animation.AnimationViewModel_HiltModules_KeyModule_ProvideFactory", "provide:com.tqhit.battery.one.viewmodel.animation.AnimationViewModel_HiltModules_KeyModule_ProvideFactory"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_fragment_main_SettingsFragment_GeneratedInjector.java": ["<init>:hilt_aggregated_deps._com_tqhit_battery_one_fragment_main_SettingsFragment_GeneratedInjector", "_com_tqhit_battery_one_fragment_main_SettingsFragment_GeneratedInjector:hilt_aggregated_deps"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\emoji\\domain\\use_case\\LoadCustomizationUseCase_Factory.java": ["get:com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase_Factory", "newInstance:com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase_Factory", "<init>:com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase_Factory", "create:com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase_Factory", "LoadCustomizationUseCase_Factory:com.tqhit.battery.one.features.emoji.domain.use_case"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\presentation\\DischargeFragment.kt": ["<init>:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment.Companion", "timeConverter:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment", "DischargeFragment:com.tqhit.battery.one.features.stats.discharge.presentation", "infoButtonManager:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment", "resetSession:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment", "onDestroyView:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment", "onCreateView:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment", "Companion:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment", "appLifecycleManager:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment", "onPause:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment", "onViewCreated:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment", "onResume:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment", "<init>:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\presentation\\DischargeViewModel_HiltModules_KeyModule_ProvideFactory.java": ["DischargeViewModel_HiltModules_KeyModule_ProvideFactory:com.tqhit.battery.one.features.stats.discharge.presentation", "<init>:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeViewModel_HiltModules_KeyModule_ProvideFactory", "get:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeViewModel_HiltModules_KeyModule_ProvideFactory", "create:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeViewModel_HiltModules_KeyModule_ProvideFactory", "provide:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeViewModel_HiltModules_KeyModule_ProvideFactory", "INSTANCE:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder", "<init>:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\emoji\\domain\\use_case\\SaveCustomizationUseCase.kt": ["saveCustomizationConfig:com.tqhit.battery.one.features.emoji.domain.use_case.SaveCustomizationUseCase", "saveUserCustomization:com.tqhit.battery.one.features.emoji.domain.use_case.SaveCustomizationUseCase", "updateStyleConfig:com.tqhit.battery.one.features.emoji.domain.use_case.SaveCustomizationUseCase", "SaveCustomizationException:com.tqhit.battery.one.features.emoji.domain.use_case", "<init>:com.tqhit.battery.one.features.emoji.domain.use_case.SaveCustomizationUseCase.Companion", "SaveCustomizationUseCase:com.tqhit.battery.one.features.emoji.domain.use_case", "Companion:com.tqhit.battery.one.features.emoji.domain.use_case.SaveCustomizationUseCase", "setFeatureEnabled:com.tqhit.battery.one.features.emoji.domain.use_case.SaveCustomizationUseCase", "saveCustomizationFromStyle:com.tqhit.battery.one.features.emoji.domain.use_case.SaveCustomizationUseCase"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\tqhit\\battery\\one\\databinding\\ItemEmojiOptionBinding.java": ["selectionIndicator:com.tqhit.battery.one.databinding.ItemEmojiOptionBinding", "loadingIndicator:com.tqhit.battery.one.databinding.ItemEmojiOptionBinding", "premiumBadge:com.tqhit.battery.one.databinding.ItemEmojiOptionBinding", "emojiImageView:com.tqhit.battery.one.databinding.ItemEmojiOptionBinding", "getRoot:com.tqhit.battery.one.databinding.ItemEmojiOptionBinding", "bind:com.tqhit.battery.one.databinding.ItemEmojiOptionBinding", "ItemEmojiOptionBinding:com.tqhit.battery.one.databinding", "inflate:com.tqhit.battery.one.databinding.ItemEmojiOptionBinding"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_service_BatteryMonitorService_GeneratedInjector.java": ["<init>:hilt_aggregated_deps._com_tqhit_battery_one_service_BatteryMonitorService_GeneratedInjector", "_com_tqhit_battery_one_service_BatteryMonitorService_GeneratedInjector:hilt_aggregated_deps"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\activity\\starting\\Hilt_StartingActivity.java": ["createComponentManager:com.tqhit.battery.one.activity.starting.Hilt_StartingActivity", "Hilt_StartingActivity:com.tqhit.battery.one.activity.starting", "onCreate:com.tqhit.battery.one.activity.starting.Hilt_StartingActivity", "generatedComponent:com.tqhit.battery.one.activity.starting.Hilt_StartingActivity", "componentManager:com.tqhit.battery.one.activity.starting.Hilt_StartingActivity", "inject:com.tqhit.battery.one.activity.starting.Hilt_StartingActivity", "onDestroy:com.tqhit.battery.one.activity.starting.Hilt_StartingActivity", "getDefaultViewModelProviderFactory:com.tqhit.battery.one.activity.starting.Hilt_StartingActivity", "<init>:com.tqhit.battery.one.activity.starting.Hilt_StartingActivity"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\activity\\password\\EnterPasswordActivity_GeneratedInjector.java": ["injectEnterPasswordActivity:com.tqhit.battery.one.activity.password.EnterPasswordActivity_GeneratedInjector", "EnterPasswordActivity_GeneratedInjector:com.tqhit.battery.one.activity.password"], "src\\main\\java\\com\\tqhit\\battery\\one\\activity\\main\\MainActivity.kt": ["statsChargeRepository:com.tqhit.battery.one.activity.main.MainActivity", "applovinBannerAdManager:com.tqhit.battery.one.activity.main.MainActivity", "coreBatteryStatsProvider:com.tqhit.battery.one.activity.main.MainActivity", "onResume:com.tqhit.battery.one.activity.main.MainActivity", "usageStatsPermissionManager:com.tqhit.battery.one.activity.main.MainActivity", "appRepository:com.tqhit.battery.one.activity.main.MainActivity", "unifiedBatteryNotificationServiceHelper:com.tqhit.battery.one.activity.main.MainActivity", "onPause:com.tqhit.battery.one.activity.main.MainActivity", "<init>:com.tqhit.battery.one.activity.main.MainActivity", "dynamicNavigationManager:com.tqhit.battery.one.activity.main.MainActivity", "remoteConfigHelper:com.tqhit.battery.one.activity.main.MainActivity", "Companion:com.tqhit.battery.one.activity.main.MainActivity", "enhancedDischargeTimerServiceHelper:com.tqhit.battery.one.activity.main.MainActivity", "onCreate:com.tqhit.battery.one.activity.main.MainActivity", "onSaveInstanceState:com.tqhit.battery.one.activity.main.MainActivity", "<init>:com.tqhit.battery.one.activity.main.MainActivity.Companion", "MainActivity:com.tqhit.battery.one.activity.main", "dischargeSessionRepository:com.tqhit.battery.one.activity.main.MainActivity", "binding:com.tqhit.battery.one.activity.main.MainActivity", "onDestroy:com.tqhit.battery.one.activity.main.MainActivity", "setupUI:com.tqhit.battery.one.activity.main.MainActivity"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\presentation\\AnimationHelper.kt": ["Companion:com.tqhit.battery.one.features.stats.discharge.presentation.AnimationHelper", "animateBatteryUpdate:com.tqhit.battery.one.features.stats.discharge.presentation.AnimationHelper", "AnimationHelper:com.tqhit.battery.one.features.stats.discharge.presentation", "<init>:com.tqhit.battery.one.features.stats.discharge.presentation.AnimationHelper.Companion", "cleanup:com.tqhit.battery.one.features.stats.discharge.presentation.AnimationHelper"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\presentation\\DischargeViewModel_Factory.java": ["<init>:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeViewModel_Factory", "newInstance:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeViewModel_Factory", "get:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeViewModel_Factory", "create:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeViewModel_Factory", "DischargeViewModel_Factory:com.tqhit.battery.one.features.stats.discharge.presentation"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\health\\cache\\DefaultHealthCache_Factory.java": ["<init>:com.tqhit.battery.one.features.stats.health.cache.DefaultHealthCache_Factory", "create:com.tqhit.battery.one.features.stats.health.cache.DefaultHealthCache_Factory", "DefaultHealthCache_Factory:com.tqhit.battery.one.features.stats.health.cache", "newInstance:com.tqhit.battery.one.features.stats.health.cache.DefaultHealthCache_Factory", "get:com.tqhit.battery.one.features.stats.health.cache.DefaultHealthCache_Factory"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\tqhit\\battery\\one\\databinding\\DialogLoadingBinding.java": ["DialogLoadingBinding:com.tqhit.battery.one.databinding", "getRoot:com.tqhit.battery.one.databinding.DialogLoadingBinding", "inflate:com.tqhit.battery.one.databinding.DialogLoadingBinding", "loadingMessage:com.tqhit.battery.one.databinding.DialogLoadingBinding", "bind:com.tqhit.battery.one.databinding.DialogLoadingBinding"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\charge\\cache\\StatsChargePrefsCache.kt": ["clearActiveSession:com.tqhit.battery.one.features.stats.charge.cache.StatsChargeCache", "StatsChargeCache:com.tqhit.battery.one.features.stats.charge.cache", "<init>:com.tqhit.battery.one.features.stats.charge.cache.PrefsStatsChargeCache.Companion", "saveActiveSession:com.tqhit.battery.one.features.stats.charge.cache.PrefsStatsChargeCache", "saveActiveSession:com.tqhit.battery.one.features.stats.charge.cache.StatsChargeCache", "getActiveSession:com.tqhit.battery.one.features.stats.charge.cache.PrefsStatsChargeCache", "getActiveSession:com.tqhit.battery.one.features.stats.charge.cache.StatsChargeCache", "PrefsStatsChargeCache:com.tqhit.battery.one.features.stats.charge.cache", "Companion:com.tqhit.battery.one.features.stats.charge.cache.PrefsStatsChargeCache", "clearActiveSession:com.tqhit.battery.one.features.stats.charge.cache.PrefsStatsChargeCache"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\hilt_aggregated_deps\\_com_tqhit_battery_one_features_stats_health_di_HealthDIModule.java": ["_com_tqhit_battery_one_features_stats_health_di_HealthDIModule:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_tqhit_battery_one_features_stats_health_di_HealthDIModule"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\tqhit\\battery\\one\\databinding\\ItemSlideLayout2Binding.java": ["confirmAndContinueButton:com.tqhit.battery.one.databinding.ItemSlideLayout2Binding", "privacyPolicyButton:com.tqhit.battery.one.databinding.ItemSlideLayout2Binding", "bind:com.tqhit.battery.one.databinding.ItemSlideLayout2Binding", "ItemSlideLayout2Binding:com.tqhit.battery.one.databinding", "getRoot:com.tqhit.battery.one.databinding.ItemSlideLayout2Binding", "inflate:com.tqhit.battery.one.databinding.ItemSlideLayout2Binding", "textView4:com.tqhit.battery.one.databinding.ItemSlideLayout2Binding", "underText:com.tqhit.battery.one.databinding.ItemSlideLayout2Binding", "nextPage:com.tqhit.battery.one.databinding.ItemSlideLayout2Binding", "button:com.tqhit.battery.one.databinding.ItemSlideLayout2Binding"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\domain\\FullSessionReEstimator.kt": ["<init>:com.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimator.Companion", "reEstimateFullSessionScreenTimes:com.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimator", "FullSessionReEstimator:com.tqhit.battery.one.features.stats.discharge.domain", "Companion:com.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimator"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\emoji\\domain\\model\\BatteryStyleCategory.kt": ["<init>:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory.SEASONAL", "SEASONAL:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory", "<init>:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory.Companion", "getAllSorted:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory.Companion", "MINIMAL:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory", "<init>:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory.MINIMAL", "<init>:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory.NATURE", "getDisplayText:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory", "emoji:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory", "Companion:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory", "<init>:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory.HEART", "BatteryStyleCategory:com.tqhit.battery.one.features.emoji.domain.model", "findByDisplayName:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory.Companion", "<init>:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory.CHARACTER", "<init>:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory.FOOD", "<init>:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory.HOT", "CUTE:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory", "<init>:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory.CUTE", "GAMING:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory", "HOT:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory", "fromString:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory.Companion", "NATURE:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory", "getDefault:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory.Companion", "<init>:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory.ANIMAL", "displayName:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory", "FOOD:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory", "HEART:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory", "ANIMAL:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory", "CHARACTER:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory", "<init>:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory.GAMING", "getMainFilterCategories:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory.Companion", "sortOrder:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory", "isFeatured:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_features_stats_discharge_presentation_DischargeViewModel_HiltModules_KeyModule.java": ["_com_tqhit_battery_one_features_stats_discharge_presentation_DischargeViewModel_HiltModules_KeyModule:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_tqhit_battery_one_features_stats_discharge_presentation_DischargeViewModel_HiltModules_KeyModule"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\activity\\overlay\\ChargingOverlayActivity_GeneratedInjector.java": ["ChargingOverlayActivity_GeneratedInjector:com.tqhit.battery.one.activity.overlay", "injectChargingOverlayActivity:com.tqhit.battery.one.activity.overlay.ChargingOverlayActivity_GeneratedInjector"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\tqhit\\battery\\one\\databinding\\ItemSlideLayout4Binding.java": ["nextPage:com.tqhit.battery.one.databinding.ItemSlideLayout4Binding", "textView4:com.tqhit.battery.one.databinding.ItemSlideLayout4Binding", "ItemSlideLayout4Binding:com.tqhit.battery.one.databinding", "bind:com.tqhit.battery.one.databinding.ItemSlideLayout4Binding", "percentLayout:com.tqhit.battery.one.databinding.ItemSlideLayout4Binding", "inflate:com.tqhit.battery.one.databinding.ItemSlideLayout4Binding", "button:com.tqhit.battery.one.databinding.ItemSlideLayout4Binding", "timeNum:com.tqhit.battery.one.databinding.ItemSlideLayout4Binding", "getRoot:com.tqhit.battery.one.databinding.ItemSlideLayout4Binding", "chargeUp:com.tqhit.battery.one.databinding.ItemSlideLayout4Binding"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\emoji\\domain\\repository\\CustomizationRepository.kt": ["saveUserPreferences:com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository", "validateAndFixConfiguration:com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository", "CustomizationRepository:com.tqhit.battery.one.features.emoji.domain.repository", "getCurrentCustomizationConfig:com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository", "MigrationException:com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepositoryException", "userPreferencesFlow:com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository", "userCustomizationFlow:com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository", "setFeatureEnabled:com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository", "recordStyleUsage:com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository", "PersistenceException:com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepositoryException", "saveUserCustomization:com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository", "ValidationException:com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepositoryException", "clearAllData:com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository", "saveCustomizationConfig:com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository", "importCustomizationData:com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository", "SerializationException:com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepositoryException", "getCurrentUserCustomization:com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository", "resetToDefaults:com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository", "CustomizationRepositoryException:com.tqhit.battery.one.features.emoji.domain.repository", "customizationConfigFlow:com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository", "updatePermissionStates:com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository", "exportCustomizationData:com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository"], "src\\main\\java\\com\\tqhit\\battery\\one\\viewmodel\\animation\\AnimationViewModel.kt": ["getTrialEndTime:com.tqhit.battery.one.viewmodel.animation.AnimationViewModel", "getTimeRemaining:com.tqhit.battery.one.viewmodel.animation.AnimationViewModel", "getEntry:com.tqhit.battery.one.viewmodel.animation.AnimationViewModel", "isExpired:com.tqhit.battery.one.viewmodel.animation.AnimationViewModel", "getApplied:com.tqhit.battery.one.viewmodel.animation.AnimationViewModel", "setApplyList:com.tqhit.battery.one.viewmodel.animation.AnimationViewModel", "setApplied:com.tqhit.battery.one.viewmodel.animation.AnimationViewModel", "applyAnimation:com.tqhit.battery.one.viewmodel.animation.AnimationViewModel", "clearAnimation:com.tqhit.battery.one.viewmodel.animation.AnimationViewModel", "AnimationViewModel:com.tqhit.battery.one.viewmodel.animation", "getApplyList:com.tqhit.battery.one.viewmodel.animation.AnimationViewModel", "setTrialEndTime:com.tqhit.battery.one.viewmodel.animation.AnimationViewModel", "isApplied:com.tqhit.battery.one.viewmodel.animation.AnimationViewModel"], "src\\main\\java\\com\\tqhit\\battery\\one\\service\\BatteryMonitorService.kt": ["<init>:com.tqhit.battery.one.service.BatteryMonitorService.Companion", "onDestroy:com.tqhit.battery.one.service.BatteryMonitorService", "onBind:com.tqhit.battery.one.service.BatteryMonitorService", "<init>:com.tqhit.battery.one.service.BatteryMonitorService", "onStartCommand:com.tqhit.battery.one.service.BatteryMonitorService", "batteryRepository:com.tqhit.battery.one.service.BatteryMonitorService", "onCreate:com.tqhit.battery.one.service.BatteryMonitorService", "BatteryMonitorService:com.tqhit.battery.one.service", "appRepository:com.tqhit.battery.one.service.BatteryMonitorService", "Companion:com.tqhit.battery.one.service.BatteryMonitorService"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_viewmodel_animation_AnimationViewModel_HiltModules_BindsModule.java": ["_com_tqhit_battery_one_viewmodel_animation_AnimationViewModel_HiltModules_BindsModule:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_tqhit_battery_one_viewmodel_animation_AnimationViewModel_HiltModules_BindsModule"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_BatteryApplication_GeneratedInjector.java": ["_com_tqhit_battery_one_BatteryApplication_GeneratedInjector:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_tqhit_battery_one_BatteryApplication_GeneratedInjector"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\tqhit\\battery\\one\\features\\stats\\health\\presentation\\HealthViewModel_HiltModules_KeyModule_ProvideFactory.java": ["INSTANCE:com.tqhit.battery.one.features.stats.health.presentation.HealthViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder", "create:com.tqhit.battery.one.features.stats.health.presentation.HealthViewModel_HiltModules_KeyModule_ProvideFactory", "<init>:com.tqhit.battery.one.features.stats.health.presentation.HealthViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder", "provide:com.tqhit.battery.one.features.stats.health.presentation.HealthViewModel_HiltModules_KeyModule_ProvideFactory", "get:com.tqhit.battery.one.features.stats.health.presentation.HealthViewModel_HiltModules_KeyModule_ProvideFactory", "HealthViewModel_HiltModules_KeyModule_ProvideFactory:com.tqhit.battery.one.features.stats.health.presentation", "<init>:com.tqhit.battery.one.features.stats.health.presentation.HealthViewModel_HiltModules_KeyModule_ProvideFactory"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\corebattery\\service\\CoreBatteryStatsService_MembersInjector.java": ["injectAppRepository:com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService_MembersInjector", "create:com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService_MembersInjector", "injectCoreBatteryStatsProvider:com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService_MembersInjector", "CoreBatteryStatsService_MembersInjector:com.tqhit.battery.one.features.stats.corebattery.service", "injectMembers:com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService_MembersInjector", "<init>:com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService_MembersInjector"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\presentation\\InfoButtonManager.kt": ["Companion:com.tqhit.battery.one.features.stats.discharge.presentation.InfoButtonManager", "initialize:com.tqhit.battery.one.features.stats.discharge.presentation.InfoButtonManager", "<init>:com.tqhit.battery.one.features.stats.discharge.presentation.InfoButtonManager.Companion", "InfoButtonManager:com.tqhit.battery.one.features.stats.discharge.presentation", "setupInfoButtonListeners:com.tqhit.battery.one.features.stats.discharge.presentation.InfoButtonManager"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\tqhit\\battery\\one\\databinding\\ActivityStartingBinding.java": ["getRoot:com.tqhit.battery.one.databinding.ActivityStartingBinding", "inflate:com.tqhit.battery.one.databinding.ActivityStartingBinding", "bind:com.tqhit.battery.one.databinding.ActivityStartingBinding", "slidePager:com.tqhit.battery.one.databinding.ActivityStartingBinding", "springDotsIndicator:com.tqhit.battery.one.databinding.ActivityStartingBinding", "ActivityStartingBinding:com.tqhit.battery.one.databinding"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\presentation\\DischargeFragment_GeneratedInjector.java": ["injectDischargeFragment:com.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment_GeneratedInjector", "DischargeFragment_GeneratedInjector:com.tqhit.battery.one.features.stats.discharge.presentation"], "src\\main\\java\\com\\tqhit\\battery\\one\\activity\\password\\EnterPasswordActivity.kt": ["<init>:com.tqhit.battery.one.activity.password.EnterPasswordActivity", "onDestroy:com.tqhit.battery.one.activity.password.EnterPasswordActivity", "onBackPressed:com.tqhit.battery.one.activity.password.EnterPasswordActivity", "Companion:com.tqhit.battery.one.activity.password.EnterPasswordActivity", "EnterPasswordActivity:com.tqhit.battery.one.activity.password", "onResume:com.tqhit.battery.one.activity.password.EnterPasswordActivity", "appRepository:com.tqhit.battery.one.activity.password.EnterPasswordActivity", "<init>:com.tqhit.battery.one.activity.password.EnterPasswordActivity.Companion", "binding:com.tqhit.battery.one.activity.password.EnterPasswordActivity", "setupData:com.tqhit.battery.one.activity.password.EnterPasswordActivity"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\BatteryApplication_GeneratedInjector.java": ["injectBatteryApplication:com.tqhit.battery.one.BatteryApplication_GeneratedInjector", "BatteryApplication_GeneratedInjector:com.tqhit.battery.one"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\emoji\\presentation\\gallery\\GridSpacingItemDecoration.kt": ["GridSpacingItemDecoration:com.tqhit.battery.one.features.emoji.presentation.gallery", "getItemOffsets:com.tqhit.battery.one.features.emoji.presentation.gallery.GridSpacingItemDecoration"], "src\\main\\java\\com\\tqhit\\battery\\one\\repository\\AppRepository.kt": ["setVibrationDischargeEnabled:com.tqhit.battery.one.repository.AppRepository", "isAnimationOverlayTimeEnabled:com.tqhit.battery.one.repository.AppRepository", "setAnimationOverlayTimeEnabled:com.tqhit.battery.one.repository.AppRepository", "isPrivacyPolicyAccepted:com.tqhit.battery.one.repository.AppRepository", "setConsentFlowUserGeography:com.tqhit.battery.one.repository.AppRepository", "isDischargeAlarmEnabled:com.tqhit.battery.one.repository.AppRepository", "getDontDisturbChargeUntilTime:com.tqhit.battery.one.repository.AppRepository", "Companion:com.tqhit.battery.one.repository.AppRepository", "getChargeAlarmPercent:com.tqhit.battery.one.repository.AppRepository", "setDischargeAlarmEnabled:com.tqhit.battery.one.repository.AppRepository", "isDontDisturbChargeEnabled:com.tqhit.battery.one.repository.AppRepository", "getPrivacyPolicyUrl:com.tqhit.battery.one.repository.AppRepository", "setDontDisturbChargeUntilTime:com.tqhit.battery.one.repository.AppRepository", "getVideoPath:com.tqhit.battery.one.repository.AppRepository", "isChargeNotificationEnabled:com.tqhit.battery.one.repository.AppRepository", "setDontDisturbDischargeUntilTime:com.tqhit.battery.one.repository.AppRepository", "setAntiThiefAlertActive:com.tqhit.battery.one.repository.AppRepository", "setAnimationOverlayEnabled:com.tqhit.battery.one.repository.AppRepository", "getDontDisturbChargeFromTime:com.tqhit.battery.one.repository.AppRepository", "getDontDisturbDischargeFromTime:com.tqhit.battery.one.repository.AppRepository", "getAntiThiefPassword:com.tqhit.battery.one.repository.AppRepository", "setShowedStartPage:com.tqhit.battery.one.repository.AppRepository", "setVibrationChargeEnabled:com.tqhit.battery.one.repository.AppRepository", "setAntiThiefSoundEnabled:com.tqhit.battery.one.repository.AppRepository", "setDontDisturbDischargeEnabled:com.tqhit.battery.one.repository.AppRepository", "isChargeAlarmEnabled:com.tqhit.battery.one.repository.AppRepository", "setDischargeAlarmPercent:com.tqhit.battery.one.repository.AppRepository", "isNotifyFullChargeEnabled:com.tqhit.battery.one.repository.AppRepository", "<init>:com.tqhit.battery.one.repository.AppRepository.Companion", "setNotifyFullChargeEnabled:com.tqhit.battery.one.repository.AppRepository", "isDontDisturbDischargeEnabled:com.tqhit.battery.one.repository.AppRepository", "getDefaultLanguage:com.tqhit.battery.one.repository.AppRepository", "isAntiThiefSoundEnabled:com.tqhit.battery.one.repository.AppRepository", "isConsentFlowUserGeography:com.tqhit.battery.one.repository.AppRepository", "setChargeNotificationEnabled:com.tqhit.battery.one.repository.AppRepository", "isDischargeNotificationEnabled:com.tqhit.battery.one.repository.AppRepository", "setDischargeNotificationEnabled:com.tqhit.battery.one.repository.AppRepository", "isAnimationOverlayEnabled:com.tqhit.battery.one.repository.AppRepository", "setLanguage:com.tqhit.battery.one.repository.AppRepository", "isAntiThiefPasswordSet:com.tqhit.battery.one.repository.AppRepository", "isVibrationChargeEnabled:com.tqhit.battery.one.repository.AppRepository", "setChargeAlarmEnabled:com.tqhit.battery.one.repository.AppRepository", "isVibrationDischargeEnabled:com.tqhit.battery.one.repository.AppRepository", "isShowedStartPage:com.tqhit.battery.one.repository.AppRepository", "acceptPrivacyPolicy:com.tqhit.battery.one.repository.AppRepository", "getLanguage:com.tqhit.battery.one.repository.AppRepository", "setVideoPath:com.tqhit.battery.one.repository.AppRepository", "setLocale:com.tqhit.battery.one.repository.AppRepository", "isAntiThiefEnabled:com.tqhit.battery.one.repository.AppRepository", "chargeAlarmPercentFlow:com.tqhit.battery.one.repository.AppRepository", "setDontDisturbDischargeFromTime:com.tqhit.battery.one.repository.AppRepository", "setAntiThiefPassword:com.tqhit.battery.one.repository.AppRepository", "getBatteryCapacity:com.tqhit.battery.one.repository.AppRepository", "getDoNotKillMyAppUrl:com.tqhit.battery.one.repository.AppRepository", "setVibrationEnabled:com.tqhit.battery.one.repository.AppRepository", "getDontDisturbDischargeUntilTime:com.tqhit.battery.one.repository.AppRepository", "setDontDisturbChargeFromTime:com.tqhit.battery.one.repository.AppRepository", "isVibrationEnabled:com.tqhit.battery.one.repository.AppRepository", "setDontDisturbChargeEnabled:com.tqhit.battery.one.repository.AppRepository", "setChargeAlarmPercent:com.tqhit.battery.one.repository.AppRepository", "getDischargeAlarmPercent:com.tqhit.battery.one.repository.AppRepository", "clearAntiThiefPassword:com.tqhit.battery.one.repository.AppRepository", "AppRepository:com.tqhit.battery.one.repository", "isAntiThiefAlertActive:com.tqhit.battery.one.repository.AppRepository", "setAntiThiefEnabled:com.tqhit.battery.one.repository.AppRepository"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\hilt_aggregated_deps\\_com_tqhit_battery_one_features_emoji_di_EmojiBatteryDIModule.java": ["_com_tqhit_battery_one_features_emoji_di_EmojiBatteryDIModule:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_tqhit_battery_one_features_emoji_di_EmojiBatteryDIModule"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_features_emoji_presentation_gallery_BatteryGalleryViewModel_HiltModules_KeyModule.java": ["_com_tqhit_battery_one_features_emoji_presentation_gallery_BatteryGalleryViewModel_HiltModules_KeyModule:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_tqhit_battery_one_features_emoji_presentation_gallery_BatteryGalleryViewModel_HiltModules_KeyModule"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\dagger\\hilt\\internal\\aggregatedroot\\codegen\\_com_tqhit_battery_one_BatteryApplication.java": ["_com_tqhit_battery_one_BatteryApplication:dagger.hilt.internal.aggregatedroot.codegen", "<init>:dagger.hilt.internal.aggregatedroot.codegen._com_tqhit_battery_one_BatteryApplication"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\tqhit\\battery\\one\\databinding\\LayoutDischargeSectionActionsBinding.java": ["actionsBtnBatteryAlarm:com.tqhit.battery.one.databinding.LayoutDischargeSectionActionsBinding", "actionsBtnResetSession:com.tqhit.battery.one.databinding.LayoutDischargeSectionActionsBinding", "LayoutDischargeSectionActionsBinding:com.tqhit.battery.one.databinding", "getRoot:com.tqhit.battery.one.databinding.LayoutDischargeSectionActionsBinding", "bind:com.tqhit.battery.one.databinding.LayoutDischargeSectionActionsBinding", "inflate:com.tqhit.battery.one.databinding.LayoutDischargeSectionActionsBinding", "actionsRoot:com.tqhit.battery.one.databinding.LayoutDischargeSectionActionsBinding"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_fragment_main_ChargeFragment_GeneratedInjector.java": ["<init>:hilt_aggregated_deps._com_tqhit_battery_one_fragment_main_ChargeFragment_GeneratedInjector", "_com_tqhit_battery_one_fragment_main_ChargeFragment_GeneratedInjector:hilt_aggregated_deps"], "src\\main\\java\\com\\tqhit\\battery\\one\\utils\\DateTimeUtils.kt": ["DateTimeUtils:com.tqhit.battery.one.utils", "getCurrentDateString:com.tqhit.battery.one.utils.DateTimeUtils", "formatMillisToTimeString:com.tqhit.battery.one.utils.DateTimeUtils", "<init>:com.tqhit.battery.one.utils.DateTimeUtils", "getCurrentTimeString:com.tqhit.battery.one.utils.DateTimeUtils"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\charge\\domain\\CalculateSimpleChargeEstimateUseCase_Factory.java": ["CalculateSimpleChargeEstimateUseCase_Factory:com.tqhit.battery.one.features.stats.charge.domain", "create:com.tqhit.battery.one.features.stats.charge.domain.CalculateSimpleChargeEstimateUseCase_Factory", "INSTANCE:com.tqhit.battery.one.features.stats.charge.domain.CalculateSimpleChargeEstimateUseCase_Factory.InstanceHolder", "<init>:com.tqhit.battery.one.features.stats.charge.domain.CalculateSimpleChargeEstimateUseCase_Factory", "<init>:com.tqhit.battery.one.features.stats.charge.domain.CalculateSimpleChargeEstimateUseCase_Factory.InstanceHolder", "get:com.tqhit.battery.one.features.stats.charge.domain.CalculateSimpleChargeEstimateUseCase_Factory", "newInstance:com.tqhit.battery.one.features.stats.charge.domain.CalculateSimpleChargeEstimateUseCase_Factory"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\apppower\\presentation\\AppPowerConsumptionDialogFactory_Factory.java": ["create:com.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionDialogFactory_Factory", "newInstance:com.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionDialogFactory_Factory", "AppPowerConsumptionDialogFactory_Factory:com.tqhit.battery.one.features.stats.apppower.presentation", "<init>:com.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionDialogFactory_Factory", "get:com.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionDialogFactory_Factory"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\tqhit\\battery\\one\\databinding\\DialogSelectLanguageBinding.java": ["ar:com.tqhit.battery.one.databinding.DialogSelectLanguageBinding", "inflate:com.tqhit.battery.one.databinding.DialogSelectLanguageBinding", "strelka:com.tqhit.battery.one.databinding.DialogSelectLanguageBinding", "textView20:com.tqhit.battery.one.databinding.DialogSelectLanguageBinding", "es:com.tqhit.battery.one.databinding.DialogSelectLanguageBinding", "hu:com.tqhit.battery.one.databinding.DialogSelectLanguageBinding", "zh:com.tqhit.battery.one.databinding.DialogSelectLanguageBinding", "fr:com.tqhit.battery.one.databinding.DialogSelectLanguageBinding", "it:com.tqhit.battery.one.databinding.DialogSelectLanguageBinding", "ua:com.tqhit.battery.one.databinding.DialogSelectLanguageBinding", "bind:com.tqhit.battery.one.databinding.DialogSelectLanguageBinding", "en:com.tqhit.battery.one.databinding.DialogSelectLanguageBinding", "ru:com.tqhit.battery.one.databinding.DialogSelectLanguageBinding", "pt:com.tqhit.battery.one.databinding.DialogSelectLanguageBinding", "de:com.tqhit.battery.one.databinding.DialogSelectLanguageBinding", "getRoot:com.tqhit.battery.one.databinding.DialogSelectLanguageBinding", "ro:com.tqhit.battery.one.databinding.DialogSelectLanguageBinding", "nl:com.tqhit.battery.one.databinding.DialogSelectLanguageBinding", "tr:com.tqhit.battery.one.databinding.DialogSelectLanguageBinding", "exit:com.tqhit.battery.one.databinding.DialogSelectLanguageBinding", "DialogSelectLanguageBinding:com.tqhit.battery.one.databinding", "pl:com.tqhit.battery.one.databinding.DialogSelectLanguageBinding"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\apppower\\presentation\\AppPowerConsumptionAdapter.kt": ["onBindViewHolder:com.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionAdapter", "AppViewHolder:com.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionAdapter", "<init>:com.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionAdapter.AppDiffCallback", "<init>:com.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionAdapter", "areItemsTheSame:com.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionAdapter.AppDiffCallback", "onCreateViewHolder:com.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionAdapter", "bind:com.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionAdapter.AppViewHolder", "AppPowerConsumptionAdapter:com.tqhit.battery.one.features.stats.apppower.presentation", "areContentsTheSame:com.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionAdapter.AppDiffCallback"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\tqhit\\battery\\one\\viewmodel\\battery\\BatteryViewModel_HiltModules_KeyModule_ProvideFactory.java": ["provide:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel_HiltModules_KeyModule_ProvideFactory", "<init>:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel_HiltModules_KeyModule_ProvideFactory", "create:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel_HiltModules_KeyModule_ProvideFactory", "BatteryViewModel_HiltModules_KeyModule_ProvideFactory:com.tqhit.battery.one.viewmodel.battery", "INSTANCE:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder", "get:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel_HiltModules_KeyModule_ProvideFactory", "<init>:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder"], "src\\main\\java\\com\\tqhit\\battery\\one\\activity\\debug\\DebugActivity.kt": ["DebugActivity:com.tqhit.battery.one.activity.debug", "<init>:com.tqhit.battery.one.activity.debug.DebugActivity", "onCreate:com.tqhit.battery.one.activity.debug.DebugActivity"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\tqhit\\battery\\one\\databinding\\NewFragmentDischargeBinding.java": ["NewFragmentDischargeBinding:com.tqhit.battery.one.databinding", "newDischargeScrollView:com.tqhit.battery.one.databinding.NewFragmentDischargeBinding", "includeCurrentSessionDetails:com.tqhit.battery.one.databinding.NewFragmentDischargeBinding", "bind:com.tqhit.battery.one.databinding.NewFragmentDischargeBinding", "dischargeChargingMessage:com.tqhit.battery.one.databinding.NewFragmentDischargeBinding", "includeLossOfCharge:com.tqhit.battery.one.databinding.NewFragmentDischargeBinding", "includeStatusAndEstimates:com.tqhit.battery.one.databinding.NewFragmentDischargeBinding", "getRoot:com.tqhit.battery.one.databinding.NewFragmentDischargeBinding", "includeActionsSection:com.tqhit.battery.one.databinding.NewFragmentDischargeBinding", "inflate:com.tqhit.battery.one.databinding.NewFragmentDischargeBinding"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\service\\ChargingOverlayService_GeneratedInjector.java": ["ChargingOverlayService_GeneratedInjector:com.tqhit.battery.one.service", "injectChargingOverlayService:com.tqhit.battery.one.service.ChargingOverlayService_GeneratedInjector"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\repository\\BatteryRepository.kt": ["BatteryRepository:com.tqhit.battery.one.features.stats.discharge.repository", "getEffectiveCapacityMah:com.tqhit.battery.one.features.stats.discharge.repository.BatteryRepository", "Companion:com.tqhit.battery.one.features.stats.discharge.repository.BatteryRepository", "<init>:com.tqhit.battery.one.features.stats.discharge.repository.BatteryRepository.Companion", "averageScreenOffDischargeRateMah:com.tqhit.battery.one.features.stats.discharge.repository.BatteryRepository", "averageScreenOnDischargeRateMah:com.tqhit.battery.one.features.stats.discharge.repository.BatteryRepository", "batteryStatusFlow:com.tqhit.battery.one.features.stats.discharge.repository.BatteryRepository"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\emoji\\presentation\\customize\\CustomizeViewModel_HiltModules.java": ["KeyModule:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModel_HiltModules", "BindsModule:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModel_HiltModules", "CustomizeViewModel_HiltModules:com.tqhit.battery.one.features.emoji.presentation.customize", "provide:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModel_HiltModules.KeyModule", "binds:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModel_HiltModules.BindsModule"], "src\\main\\java\\com\\tqhit\\battery\\one\\service\\ChargingOverlayService.kt": ["VIDEO_NAME:com.tqhit.battery.one.service.ChargingOverlayService.Companion", "<init>:com.tqhit.battery.one.service.ChargingOverlayService.Companion", "onDestroy:com.tqhit.battery.one.service.ChargingOverlayService", "onStartCommand:com.tqhit.battery.one.service.ChargingOverlayService", "onBind:com.tqhit.battery.one.service.ChargingOverlayService", "<init>:com.tqhit.battery.one.service.ChargingOverlayService", "animationRepository:com.tqhit.battery.one.service.ChargingOverlayService", "ChargingOverlayService:com.tqhit.battery.one.service", "appRepository:com.tqhit.battery.one.service.ChargingOverlayService", "Companion:com.tqhit.battery.one.service.ChargingOverlayService"], "src\\main\\java\\com\\tqhit\\battery\\one\\utils\\VideoUtils.kt": ["VideoUtils:com.tqhit.battery.one.utils", "downloadAndSaveVideo:com.tqhit.battery.one.utils.VideoUtils", "<init>:com.tqhit.battery.one.utils.VideoUtils"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\emoji\\presentation\\customize\\CustomizeFragment.kt": ["newInstance:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeFragment.Companion", "onPause:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeFragment", "<init>:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeFragment.Companion", "CustomizeFragment:com.tqhit.battery.one.features.emoji.presentation.customize", "onViewCreated:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeFragment", "onDestroyView:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeFragment", "<init>:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeFragment", "onCreateView:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeFragment", "onResume:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeFragment", "Companion:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeFragment"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\health\\domain\\CalculateBatteryHealthUseCase_Factory.java": ["<init>:com.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCase_Factory.InstanceHolder", "create:com.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCase_Factory", "get:com.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCase_Factory", "CalculateBatteryHealthUseCase_Factory:com.tqhit.battery.one.features.stats.health.domain", "INSTANCE:com.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCase_Factory.InstanceHolder", "newInstance:com.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCase_Factory", "<init>:com.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCase_Factory"], "build\\generated\\source\\buildConfig\\debug\\com\\tqhit\\battery\\one\\BuildConfig.java": ["APPLICATION_ID:com.tqhit.battery.one.BuildConfig", "ENABLE_LOGGING:com.tqhit.battery.one.BuildConfig", "DEBUG:com.tqhit.battery.one.BuildConfig", "<init>:com.tqhit.battery.one.BuildConfig", "VERSION_NAME:com.tqhit.battery.one.BuildConfig", "VERSION_CODE:com.tqhit.battery.one.BuildConfig", "BuildConfig:com.tqhit.battery.one", "BUILD_TYPE:com.tqhit.battery.one.BuildConfig"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\tqhit\\battery\\one\\databinding\\FragmentChargeBinding.java": ["remainingTable:com.tqhit.battery.one.databinding.FragmentChargeBinding", "seekBar:com.tqhit.battery.one.databinding.FragmentChargeBinding", "notChargingMessage:com.tqhit.battery.one.databinding.FragmentChargeBinding", "sdfsd:com.tqhit.battery.one.databinding.FragmentChargeBinding", "iText:com.tqhit.battery.one.databinding.FragmentChargeBinding", "cTextNiAmpere:com.tqhit.battery.one.databinding.FragmentChargeBinding", "cs21:com.tqhit.battery.one.databinding.FragmentChargeBinding", "remValBarrier:com.tqhit.battery.one.databinding.FragmentChargeBinding", "cs4:com.tqhit.battery.one.databinding.FragmentChargeBinding", "day4Percent:com.tqhit.battery.one.databinding.FragmentChargeBinding", "textRemainVar33:com.tqhit.battery.one.databinding.FragmentChargeBinding", "textSpeedChargeDaySession:com.tqhit.battery.one.databinding.FragmentChargeBinding", "btnSelector2:com.tqhit.battery.one.databinding.FragmentChargeBinding", "scrollView:com.tqhit.battery.one.databinding.FragmentChargeBinding", "textPercentChargeAll:com.tqhit.battery.one.databinding.FragmentChargeBinding", "rem1:com.tqhit.battery.one.databinding.FragmentChargeBinding", "t2:com.tqhit.battery.one.databinding.FragmentChargeBinding", "getRoot:com.tqhit.battery.one.databinding.FragmentChargeBinding", "per2:com.tqhit.battery.one.databinding.FragmentChargeBinding", "textSpeedChargeAllSession:com.tqhit.battery.one.databinding.FragmentChargeBinding", "progressBarTemp:com.tqhit.battery.one.databinding.FragmentChargeBinding", "timeNum:com.tqhit.battery.one.databinding.FragmentChargeBinding", "te77:com.tqhit.battery.one.databinding.FragmentChargeBinding", "textView6:com.tqhit.battery.one.databinding.FragmentChargeBinding", "valRemainToVar:com.tqhit.battery.one.databinding.FragmentChargeBinding", "indentDown:com.tqhit.battery.one.databinding.FragmentChargeBinding", "cs3:com.tqhit.battery.one.databinding.FragmentChargeBinding", "textRemainVar44:com.tqhit.battery.one.databinding.FragmentChargeBinding", "chargeSessionPercent:com.tqhit.battery.one.databinding.FragmentChargeBinding", "remBarrier:com.tqhit.battery.one.databinding.FragmentChargeBinding", "stText:com.tqhit.battery.one.databinding.FragmentChargeBinding", "textPercentChargeNight:com.tqhit.battery.one.databinding.FragmentChargeBinding", "textTimeDay:com.tqhit.battery.one.databinding.FragmentChargeBinding", "btnSelector1:com.tqhit.battery.one.databinding.FragmentChargeBinding", "textSpeedChargeNightSession:com.tqhit.battery.one.databinding.FragmentChargeBinding", "per1:com.tqhit.battery.one.databinding.FragmentChargeBinding", "rem2:com.tqhit.battery.one.databinding.FragmentChargeBinding", "vText:com.tqhit.battery.one.databinding.FragmentChargeBinding", "s1:com.tqhit.battery.one.databinding.FragmentChargeBinding", "resetSessionChargeButton:com.tqhit.battery.one.databinding.FragmentChargeBinding", "t3:com.tqhit.battery.one.databinding.FragmentChargeBinding", "textRemainTo100:com.tqhit.battery.one.databinding.FragmentChargeBinding", "testView:com.tqhit.battery.one.databinding.FragmentChargeBinding", "mah3:com.tqhit.battery.one.databinding.FragmentChargeBinding", "underGraphPercent:com.tqhit.battery.one.databinding.FragmentChargeBinding", "damageBarSeekwhite:com.tqhit.battery.one.databinding.FragmentChargeBinding", "saleContainer:com.tqhit.battery.one.databinding.FragmentChargeBinding", "csText:com.tqhit.battery.one.databinding.FragmentChargeBinding", "bind:com.tqhit.battery.one.databinding.FragmentChargeBinding", "nightBlock:com.tqhit.battery.one.databinding.FragmentChargeBinding", "textRemainVar55:com.tqhit.battery.one.databinding.FragmentChargeBinding", "textRemainTo100Charge:com.tqhit.battery.one.databinding.FragmentChargeBinding", "day5Percent:com.tqhit.battery.one.databinding.FragmentChargeBinding", "btn2:com.tqhit.battery.one.databinding.FragmentChargeBinding", "progressBarPower:com.tqhit.battery.one.databinding.FragmentChargeBinding", "t4:com.tqhit.battery.one.databinding.FragmentChargeBinding", "s2:com.tqhit.battery.one.databinding.FragmentChargeBinding", "valRemainTo100:com.tqhit.battery.one.databinding.FragmentChargeBinding", "t5:com.tqhit.battery.one.databinding.FragmentChargeBinding", "speedchargePercentText:com.tqhit.battery.one.databinding.FragmentChargeBinding", "day1Percent:com.tqhit.battery.one.databinding.FragmentChargeBinding", "batteryWearInfo:com.tqhit.battery.one.databinding.FragmentChargeBinding", "progressBarRemainTo1002:com.tqhit.battery.one.databinding.FragmentChargeBinding", "timeChargeSessionStart:com.tqhit.battery.one.databinding.FragmentChargeBinding", "valVoltage:com.tqhit.battery.one.databinding.FragmentChargeBinding", "imageView18:com.tqhit.battery.one.databinding.FragmentChargeBinding", "updateView:com.tqhit.battery.one.databinding.FragmentChargeBinding", "textedd:com.tqhit.battery.one.databinding.FragmentChargeBinding", "promoContainer:com.tqhit.battery.one.databinding.FragmentChargeBinding", "btn1:com.tqhit.battery.one.databinding.FragmentChargeBinding", "chargeUp:com.tqhit.battery.one.databinding.FragmentChargeBinding", "s3:com.tqhit.battery.one.databinding.FragmentChargeBinding", "t6:com.tqhit.battery.one.databinding.FragmentChargeBinding", "s4:com.tqhit.battery.one.databinding.FragmentChargeBinding", "textPercentChargeNightSession:com.tqhit.battery.one.databinding.FragmentChargeBinding", "pus:com.tqhit.battery.one.databinding.FragmentChargeBinding", "allBlock:com.tqhit.battery.one.databinding.FragmentChargeBinding", "progressBarAveragespeed:com.tqhit.battery.one.databinding.FragmentChargeBinding", "textFulltimeChargeSession:com.tqhit.battery.one.databinding.FragmentChargeBinding", "chartPercent:com.tqhit.battery.one.databinding.FragmentChargeBinding", "chargeSessionInfo:com.tqhit.battery.one.databinding.FragmentChargeBinding", "textSpeedChargeDaySession2:com.tqhit.battery.one.databinding.FragmentChargeBinding", "day6Percent:com.tqhit.battery.one.databinding.FragmentChargeBinding", "percentGraphChange:com.tqhit.battery.one.databinding.FragmentChargeBinding", "t26:com.tqhit.battery.one.databinding.FragmentChargeBinding", "progressBarRemainToVar:com.tqhit.battery.one.databinding.FragmentChargeBinding", "cTextNiPower:com.tqhit.battery.one.databinding.FragmentChargeBinding", "valTempText:com.tqhit.battery.one.databinding.FragmentChargeBinding", "damageBarPercent:com.tqhit.battery.one.databinding.FragmentChargeBinding", "remText:com.tqhit.battery.one.databinding.FragmentChargeBinding", "daView:com.tqhit.battery.one.databinding.FragmentChargeBinding", "s5:com.tqhit.battery.one.databinding.FragmentChargeBinding", "t7:com.tqhit.battery.one.databinding.FragmentChargeBinding", "progressBarCurrentMinus:com.tqhit.battery.one.databinding.FragmentChargeBinding", "textTimeDaynight:com.tqhit.battery.one.databinding.FragmentChargeBinding", "i1:com.tqhit.battery.one.databinding.FragmentChargeBinding", "varSpeedchargePercentNow:com.tqhit.battery.one.databinding.FragmentChargeBinding", "pText:com.tqhit.battery.one.databinding.FragmentChargeBinding", "day2Percent:com.tqhit.battery.one.databinding.FragmentChargeBinding", "cText:com.tqhit.battery.one.databinding.FragmentChargeBinding", "chargeProgBarPercent:com.tqhit.battery.one.databinding.FragmentChargeBinding", "graphPercent:com.tqhit.battery.one.databinding.FragmentChargeBinding", "varDamageUp4:com.tqhit.battery.one.databinding.FragmentChargeBinding", "valAverageSpeed:com.tqhit.battery.one.databinding.FragmentChargeBinding", "progressBarAveragespeedGrey:com.tqhit.battery.one.databinding.FragmentChargeBinding", "progressBarPowerMinus:com.tqhit.battery.one.databinding.FragmentChargeBinding", "textSpeedChargeAll:com.tqhit.battery.one.databinding.FragmentChargeBinding", "btn3:com.tqhit.battery.one.databinding.FragmentChargeBinding", "chargeAvgInfo:com.tqhit.battery.one.databinding.FragmentChargeBinding", "t8:com.tqhit.battery.one.databinding.FragmentChargeBinding", "textView9:com.tqhit.battery.one.databinding.FragmentChargeBinding", "s6:com.tqhit.battery.one.databinding.FragmentChargeBinding", "textPercentChargeSessionLast:com.tqhit.battery.one.databinding.FragmentChargeBinding", "textTimeNight:com.tqhit.battery.one.databinding.FragmentChargeBinding", "i2:com.tqhit.battery.one.databinding.FragmentChargeBinding", "textPercent:com.tqhit.battery.one.databinding.FragmentChargeBinding", "valCurrrentCharging:com.tqhit.battery.one.databinding.FragmentChargeBinding", "amperageTable:com.tqhit.battery.one.databinding.FragmentChargeBinding", "textPercentChargeAllSession:com.tqhit.battery.one.databinding.FragmentChargeBinding", "progressBarCurrent:com.tqhit.battery.one.databinding.FragmentChargeBinding", "te0:com.tqhit.battery.one.databinding.FragmentChargeBinding", "textViewPercent:com.tqhit.battery.one.databinding.FragmentChargeBinding", "batteryAlarmBtn:com.tqhit.battery.one.databinding.FragmentChargeBinding", "cs2:com.tqhit.battery.one.databinding.FragmentChargeBinding", "textPercentChargeDaySession:com.tqhit.battery.one.databinding.FragmentChargeBinding", "progressBarRemainToVar2:com.tqhit.battery.one.databinding.FragmentChargeBinding", "valTemp2:com.tqhit.battery.one.databinding.FragmentChargeBinding", "te99:com.tqhit.battery.one.databinding.FragmentChargeBinding", "textSpeedChargeDay:com.tqhit.battery.one.databinding.FragmentChargeBinding", "t9:com.tqhit.battery.one.databinding.FragmentChargeBinding", "i3:com.tqhit.battery.one.databinding.FragmentChargeBinding", "mah2:com.tqhit.battery.one.databinding.FragmentChargeBinding", "inflate:com.tqhit.battery.one.databinding.FragmentChargeBinding", "day3Percent:com.tqhit.battery.one.databinding.FragmentChargeBinding", "text226:com.tqhit.battery.one.databinding.FragmentChargeBinding", "FragmentChargeBinding:com.tqhit.battery.one.databinding", "cs1:com.tqhit.battery.one.databinding.FragmentChargeBinding", "progressBarRemainTo100:com.tqhit.battery.one.databinding.FragmentChargeBinding", "textRemainVar22:com.tqhit.battery.one.databinding.FragmentChargeBinding", "percentLayout:com.tqhit.battery.one.databinding.FragmentChargeBinding", "per3:com.tqhit.battery.one.databinding.FragmentChargeBinding", "t1:com.tqhit.battery.one.databinding.FragmentChargeBinding", "textSpeedChargeNight:com.tqhit.battery.one.databinding.FragmentChargeBinding", "textView7:com.tqhit.battery.one.databinding.FragmentChargeBinding", "currentSessionBlock:com.tqhit.battery.one.databinding.FragmentChargeBinding", "te88:com.tqhit.battery.one.databinding.FragmentChargeBinding", "textPercentChargeDay:com.tqhit.battery.one.databinding.FragmentChargeBinding", "progressBarVoltage:com.tqhit.battery.one.databinding.FragmentChargeBinding", "day7Percent:com.tqhit.battery.one.databinding.FragmentChargeBinding", "textRemainVar:com.tqhit.battery.one.databinding.FragmentChargeBinding", "wearRatePercent:com.tqhit.battery.one.databinding.FragmentChargeBinding", "mah1:com.tqhit.battery.one.databinding.FragmentChargeBinding", "selectorAmperage:com.tqhit.battery.one.databinding.FragmentChargeBinding", "updateViewBtn:com.tqhit.battery.one.databinding.FragmentChargeBinding", "chart1Percent:com.tqhit.battery.one.databinding.FragmentChargeBinding", "dayBlock:com.tqhit.battery.one.databinding.FragmentChargeBinding", "valPowerCharging:com.tqhit.battery.one.databinding.FragmentChargeBinding"], "src\\main\\java\\com\\tqhit\\battery\\one\\utils\\BatteryCalculatorDischarge.kt": ["calculateAveragePower:com.tqhit.battery.one.utils.BatteryCalculatorDischarge", "logDischargeMetric:com.tqhit.battery.one.utils.BatteryCalculatorDischarge", "BatteryCalculatorDischarge:com.tqhit.battery.one.utils", "<init>:com.tqhit.battery.one.utils.BatteryCalculatorDischarge", "logDischargeMetrics:com.tqhit.battery.one.utils.BatteryCalculatorDischarge", "calculateScreenOffTimeRemaining:com.tqhit.battery.one.utils.BatteryCalculatorDischarge", "updateStandbyPowerReadings:com.tqhit.battery.one.utils.BatteryCalculatorDischarge", "TAG_DISCHARGE_CALC:com.tqhit.battery.one.utils.BatteryCalculatorDischarge", "calculateUsageStyleTimeRemaining:com.tqhit.battery.one.utils.BatteryCalculatorDischarge", "calculateScreenOnTimeRemaining:com.tqhit.battery.one.utils.BatteryCalculatorDischarge", "calculatePercentPerHour:com.tqhit.battery.one.utils.BatteryCalculatorDischarge"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\charge\\presentation\\Hilt_StatsChargeFragment.java": ["Hilt_StatsChargeFragment:com.tqhit.battery.one.features.stats.charge.presentation", "onAttach:com.tqhit.battery.one.features.stats.charge.presentation.Hilt_StatsChargeFragment", "onGetLayoutInflater:com.tqhit.battery.one.features.stats.charge.presentation.Hilt_StatsChargeFragment", "<init>:com.tqhit.battery.one.features.stats.charge.presentation.Hilt_StatsChargeFragment", "getDefaultViewModelProviderFactory:com.tqhit.battery.one.features.stats.charge.presentation.Hilt_StatsChargeFragment", "getContext:com.tqhit.battery.one.features.stats.charge.presentation.Hilt_StatsChargeFragment", "createComponentManager:com.tqhit.battery.one.features.stats.charge.presentation.Hilt_StatsChargeFragment", "generatedComponent:com.tqhit.battery.one.features.stats.charge.presentation.Hilt_StatsChargeFragment", "componentManager:com.tqhit.battery.one.features.stats.charge.presentation.Hilt_StatsChargeFragment", "inject:com.tqhit.battery.one.features.stats.charge.presentation.Hilt_StatsChargeFragment"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\manager\\charge\\ChargingSessionManager_Factory.java": ["create:com.tqhit.battery.one.manager.charge.ChargingSessionManager_Factory", "<init>:com.tqhit.battery.one.manager.charge.ChargingSessionManager_Factory", "ChargingSessionManager_Factory:com.tqhit.battery.one.manager.charge", "get:com.tqhit.battery.one.manager.charge.ChargingSessionManager_Factory", "newInstance:com.tqhit.battery.one.manager.charge.ChargingSessionManager_Factory"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\tqhit\\battery\\one\\viewmodel\\battery\\BatteryViewModel_HiltModules_BindsModule_Binds_LazyMapKey.java": ["<init>:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel_HiltModules_BindsModule_Binds_LazyMapKey", "keepFieldType:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel_HiltModules_BindsModule_Binds_LazyMapKey", "lazyClassKeyName:com.tqhit.battery.one.viewmodel.battery.BatteryViewModel_HiltModules_BindsModule_Binds_LazyMapKey", "BatteryViewModel_HiltModules_BindsModule_Binds_LazyMapKey:com.tqhit.battery.one.viewmodel.battery"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\tqhit\\battery\\one\\databinding\\DialogNotificationBinding.java": ["sdg1:com.tqhit.battery.one.databinding.DialogNotificationBinding", "exit:com.tqhit.battery.one.databinding.DialogNotificationBinding", "cancelView:com.tqhit.battery.one.databinding.DialogNotificationBinding", "sdfgsdfg1:com.tqhit.battery.one.databinding.DialogNotificationBinding", "inflate:com.tqhit.battery.one.databinding.DialogNotificationBinding", "confirmChangeCapacityError:com.tqhit.battery.one.databinding.DialogNotificationBinding", "textBtn:com.tqhit.battery.one.databinding.DialogNotificationBinding", "strelka:com.tqhit.battery.one.databinding.DialogNotificationBinding", "margin:com.tqhit.battery.one.databinding.DialogNotificationBinding", "textBtnCancel:com.tqhit.battery.one.databinding.DialogNotificationBinding", "bind:com.tqhit.battery.one.databinding.DialogNotificationBinding", "cancelBtn:com.tqhit.battery.one.databinding.DialogNotificationBinding", "upText:com.tqhit.battery.one.databinding.DialogNotificationBinding", "DialogNotificationBinding:com.tqhit.battery.one.databinding", "getRoot:com.tqhit.battery.one.databinding.DialogNotificationBinding", "mainText:com.tqhit.battery.one.databinding.DialogNotificationBinding"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\domain\\ScreenTimeValidationService_Factory.java": ["create:com.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationService_Factory", "ScreenTimeValidationService_Factory:com.tqhit.battery.one.features.stats.discharge.domain", "get:com.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationService_Factory", "newInstance:com.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationService_Factory", "<init>:com.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationService_Factory"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\emoji\\domain\\use_case\\GetBatteryStylesUseCase.kt": ["getStylesByCategory:com.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCase", "searchStyles:com.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCase", "getAllStylesFlow:com.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCase", "refreshStyles:com.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCase", "getLoadingStateFlow:com.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCase", "getStyleById:com.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCase", "getPremiumStyles:com.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCase", "getCurrentStyles:com.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCase", "<init>:com.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCase.Companion", "Companion:com.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCase", "getAllStyles:com.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCase", "GetBatteryStylesUseCase:com.tqhit.battery.one.features.emoji.domain.use_case", "getFreeStyles:com.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCase", "getPopularStyles:com.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCase", "hasCachedData:com.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCase"], "src\\main\\java\\com\\tqhit\\battery\\one\\fragment\\main\\animation\\AnimationGridFragment.kt": ["<init>:com.tqhit.battery.one.fragment.main.animation.AnimationGridFragment", "setupUI:com.tqhit.battery.one.fragment.main.animation.AnimationGridFragment", "setupData:com.tqhit.battery.one.fragment.main.animation.AnimationGridFragment", "binding:com.tqhit.battery.one.fragment.main.animation.AnimationGridFragment", "applovinInterstitialAdManager:com.tqhit.battery.one.fragment.main.animation.AnimationGridFragment", "appRepository:com.tqhit.battery.one.fragment.main.animation.AnimationGridFragment", "remoteConfigHelper:com.tqhit.battery.one.fragment.main.animation.AnimationGridFragment", "AnimationGridFragment:com.tqhit.battery.one.fragment.main.animation"], "src\\main\\java\\com\\tqhit\\battery\\one\\activity\\overlay\\ChargingOverlayActivity.kt": ["appRepository:com.tqhit.battery.one.activity.overlay.ChargingOverlayActivity", "setupListener:com.tqhit.battery.one.activity.overlay.ChargingOverlayActivity", "setupData:com.tqhit.battery.one.activity.overlay.ChargingOverlayActivity", "binding:com.tqhit.battery.one.activity.overlay.ChargingOverlayActivity", "setupUI:com.tqhit.battery.one.activity.overlay.ChargingOverlayActivity", "<init>:com.tqhit.battery.one.activity.overlay.ChargingOverlayActivity", "onStart:com.tqhit.battery.one.activity.overlay.ChargingOverlayActivity", "onDestroy:com.tqhit.battery.one.activity.overlay.ChargingOverlayActivity", "onStop:com.tqhit.battery.one.activity.overlay.ChargingOverlayActivity", "onNewIntent:com.tqhit.battery.one.activity.overlay.ChargingOverlayActivity", "ChargingOverlayActivity:com.tqhit.battery.one.activity.overlay"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\tqhit\\battery\\one\\databinding\\SectionChargeOverallAverageBinding.java": ["valOverallAvgSpeedMixed:com.tqhit.battery.one.databinding.SectionChargeOverallAverageBinding", "valOverallAvgSpeedScreenOff:com.tqhit.battery.one.databinding.SectionChargeOverallAverageBinding", "inflate:com.tqhit.battery.one.databinding.SectionChargeOverallAverageBinding", "chargeOverallAverageRoot:com.tqhit.battery.one.databinding.SectionChargeOverallAverageBinding", "valOverallAvgSpeedScreenOn:com.tqhit.battery.one.databinding.SectionChargeOverallAverageBinding", "bind:com.tqhit.battery.one.databinding.SectionChargeOverallAverageBinding", "SectionChargeOverallAverageBinding:com.tqhit.battery.one.databinding", "getRoot:com.tqhit.battery.one.databinding.SectionChargeOverallAverageBinding", "chargeAvgInfoButton:com.tqhit.battery.one.databinding.SectionChargeOverallAverageBinding"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\domain\\SessionManager.kt": ["SessionManager:com.tqhit.battery.one.features.stats.discharge.domain", "createNewSession:com.tqhit.battery.one.features.stats.discharge.domain.SessionManager", "finalizeSession:com.tqhit.battery.one.features.stats.discharge.domain.SessionManager", "updateSessionScreenTime:com.tqhit.battery.one.features.stats.discharge.domain.SessionManager", "updateSession:com.tqhit.battery.one.features.stats.discharge.domain.SessionManager"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\tqhit\\battery\\one\\databinding\\DialogSelectBatteryAlarmLowBinding.java": ["switchDontDisturb:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmLowBinding", "linearLayout2:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmLowBinding", "relativ33:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmLowBinding", "p192:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmLowBinding", "inflate:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmLowBinding", "p12er1:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmLowBinding", "switchVibration:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmLowBinding", "p6:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmLowBinding", "progressbarLowAlarm:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmLowBinding", "dontDisturbUntil:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmLowBinding", "chargeL:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmLowBinding", "getRoot:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmLowBinding", "battery4:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmLowBinding", "textBuyAccess2:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmLowBinding", "DialogSelectBatteryAlarmLowBinding:com.tqhit.battery.one.databinding", "dontDisturbFrom:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmLowBinding", "relat223:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmLowBinding", "strelka:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmLowBinding", "buttonChargeAlarm:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmLowBinding", "bind:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmLowBinding", "p1221:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmLowBinding", "lowAlarmPercent:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmLowBinding", "textView20:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmLowBinding", "seekBarLowAlarm:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmLowBinding", "exit:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmLowBinding", "p11:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmLowBinding", "p12e2:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmLowBinding", "p12er:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmLowBinding", "p5:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmLowBinding", "dontDisturbFromLayout:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmLowBinding", "dontDisturbUntilLayout:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmLowBinding", "switchLowAlarm:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmLowBinding", "p112:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmLowBinding"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\charge\\di\\StatsChargeDIModule.kt": ["bindStatsChargeCache:com.tqhit.battery.one.features.stats.charge.di.StatsChargeDIModule", "StatsChargeDIModule:com.tqhit.battery.one.features.stats.charge.di", "bindStatsChargeRepository:com.tqhit.battery.one.features.stats.charge.di.StatsChargeDIModule", "<init>:com.tqhit.battery.one.features.stats.charge.di.StatsChargeDIModule"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\domain\\GapEstimationCalculator_Factory.java": ["create:com.tqhit.battery.one.features.stats.discharge.domain.GapEstimationCalculator_Factory", "newInstance:com.tqhit.battery.one.features.stats.discharge.domain.GapEstimationCalculator_Factory", "get:com.tqhit.battery.one.features.stats.discharge.domain.GapEstimationCalculator_Factory", "<init>:com.tqhit.battery.one.features.stats.discharge.domain.GapEstimationCalculator_Factory", "GapEstimationCalculator_Factory:com.tqhit.battery.one.features.stats.discharge.domain"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\activity\\main\\MainActivity_MembersInjector.java": ["create:com.tqhit.battery.one.activity.main.MainActivity_MembersInjector", "injectUsageStatsPermissionManager:com.tqhit.battery.one.activity.main.MainActivity_MembersInjector", "injectAppRepository:com.tqhit.battery.one.activity.main.MainActivity_MembersInjector", "MainActivity_MembersInjector:com.tqhit.battery.one.activity.main", "injectEnhancedDischargeTimerServiceHelper:com.tqhit.battery.one.activity.main.MainActivity_MembersInjector", "injectStatsChargeRepository:com.tqhit.battery.one.activity.main.MainActivity_MembersInjector", "injectRemoteConfigHelper:com.tqhit.battery.one.activity.main.MainActivity_MembersInjector", "injectDischargeSessionRepository:com.tqhit.battery.one.activity.main.MainActivity_MembersInjector", "injectCoreBatteryStatsProvider:com.tqhit.battery.one.activity.main.MainActivity_MembersInjector", "injectApplovinBannerAdManager:com.tqhit.battery.one.activity.main.MainActivity_MembersInjector", "injectDynamicNavigationManager:com.tqhit.battery.one.activity.main.MainActivity_MembersInjector", "injectMembers:com.tqhit.battery.one.activity.main.MainActivity_MembersInjector", "<init>:com.tqhit.battery.one.activity.main.MainActivity_MembersInjector", "injectUnifiedBatteryNotificationServiceHelper:com.tqhit.battery.one.activity.main.MainActivity_MembersInjector"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\emoji\\presentation\\gallery\\adapter\\BatteryStyleAdapter.kt": ["BatteryStyleAdapter:com.tqhit.battery.one.features.emoji.presentation.gallery.adapter", "Companion:com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapter", "areItemsTheSame:com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapter.BatteryStyleDiffCallback", "areContentsTheSame:com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapter.BatteryStyleDiffCallback", "onCreateViewHolder:com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapter", "binding:com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapter.BatteryStyleViewHolder", "onBindViewHolder:com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapter", "<init>:com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapter.BatteryStyleDiffCallback", "<init>:com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapter.Companion", "bind:com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapter.BatteryStyleViewHolder", "BatteryStyleViewHolder:com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapter", "getItem:com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapter"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\fragment\\main\\SettingsFragment_MembersInjector.java": ["injectPreferencesHelper:com.tqhit.battery.one.fragment.main.SettingsFragment_MembersInjector", "injectApplovinInterstitialAdManager:com.tqhit.battery.one.fragment.main.SettingsFragment_MembersInjector", "SettingsFragment_MembersInjector:com.tqhit.battery.one.fragment.main", "injectMembers:com.tqhit.battery.one.fragment.main.SettingsFragment_MembersInjector", "<init>:com.tqhit.battery.one.fragment.main.SettingsFragment_MembersInjector", "create:com.tqhit.battery.one.fragment.main.SettingsFragment_MembersInjector"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\ads\\core\\ApplovinRewardedAdManager_Factory.java": ["ApplovinRewardedAdManager_Factory:com.tqhit.battery.one.ads.core", "get:com.tqhit.battery.one.ads.core.ApplovinRewardedAdManager_Factory", "newInstance:com.tqhit.battery.one.ads.core.ApplovinRewardedAdManager_Factory", "<init>:com.tqhit.battery.one.ads.core.ApplovinRewardedAdManager_Factory", "create:com.tqhit.battery.one.ads.core.ApplovinRewardedAdManager_Factory"], "src\\main\\java\\com\\tqhit\\battery\\one\\dialog\\language\\SelectLanguageDialog.kt": ["binding:com.tqhit.battery.one.dialog.language.SelectLanguageDialog", "initWindow:com.tqhit.battery.one.dialog.language.SelectLanguageDialog", "SelectLanguageDialog:com.tqhit.battery.one.dialog.language", "setupListener:com.tqhit.battery.one.dialog.language.SelectLanguageDialog"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\corebattery\\di\\CoreBatteryDIModule.kt": ["<init>:com.tqhit.battery.one.features.stats.corebattery.di.CoreBatteryDIModule", "CoreBatteryDIModule:com.tqhit.battery.one.features.stats.corebattery.di", "bindCoreBatteryStatsProvider:com.tqhit.battery.one.features.stats.corebattery.di.CoreBatteryDIModule"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\tqhit\\battery\\one\\databinding\\SectionChargeMainDisplayBinding.java": ["chargeProgBarPercent:com.tqhit.battery.one.databinding.SectionChargeMainDisplayBinding", "targetChargeBlock:com.tqhit.battery.one.databinding.SectionChargeMainDisplayBinding", "textCurrentRate:com.tqhit.battery.one.databinding.SectionChargeMainDisplayBinding", "textPercent:com.tqhit.battery.one.databinding.SectionChargeMainDisplayBinding", "inflate:com.tqhit.battery.one.databinding.SectionChargeMainDisplayBinding", "textTimeToTarget:com.tqhit.battery.one.databinding.SectionChargeMainDisplayBinding", "chargeMainDisplayRoot:com.tqhit.battery.one.databinding.SectionChargeMainDisplayBinding", "percentInnerLayout:com.tqhit.battery.one.databinding.SectionChargeMainDisplayBinding", "timeEstimatesLayout:com.tqhit.battery.one.databinding.SectionChargeMainDisplayBinding", "bind:com.tqhit.battery.one.databinding.SectionChargeMainDisplayBinding", "SectionChargeMainDisplayBinding:com.tqhit.battery.one.databinding", "getRoot:com.tqhit.battery.one.databinding.SectionChargeMainDisplayBinding", "fullChargeBlock:com.tqhit.battery.one.databinding.SectionChargeMainDisplayBinding", "percentLayout:com.tqhit.battery.one.databinding.SectionChargeMainDisplayBinding", "textTimeToFull:com.tqhit.battery.one.databinding.SectionChargeMainDisplayBinding", "currentRateBlock:com.tqhit.battery.one.databinding.SectionChargeMainDisplayBinding"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\tqhit\\battery\\one\\databinding\\DialogBackgroundPermissionBinding.java": ["btnClose:com.tqhit.battery.one.databinding.DialogBackgroundPermissionBinding", "dialogTitle:com.tqhit.battery.one.databinding.DialogBackgroundPermissionBinding", "bind:com.tqhit.battery.one.databinding.DialogBackgroundPermissionBinding", "DialogBackgroundPermissionBinding:com.tqhit.battery.one.databinding", "btnAllow:com.tqhit.battery.one.databinding.DialogBackgroundPermissionBinding", "dontKillMyAppLink:com.tqhit.battery.one.databinding.DialogBackgroundPermissionBinding", "dialogMessage:com.tqhit.battery.one.databinding.DialogBackgroundPermissionBinding", "inflate:com.tqhit.battery.one.databinding.DialogBackgroundPermissionBinding", "getRoot:com.tqhit.battery.one.databinding.DialogBackgroundPermissionBinding"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\emoji\\presentation\\gallery\\BatteryGalleryEvent.kt": ["<init>:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.PurchasePremiumAccess", "show:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.ToggleCategoryFilter", "isCharging:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.BatteryStateChanged", "imageType:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.ImageLoadFailed", "FilterByCategory:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent", "<init>:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.PermissionsDenied", "<init>:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.NavigateToSettings", "<init>:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent", "<init>:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.ShowFeatureInfo", "LoadInitialData:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent", "ImageLoadFailed:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent", "<init>:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.LoadInitialData", "<init>:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.RequestPermissions", "Companion:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent", "ToggleEmojiBatteryFeature:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent", "style:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.UnlockPremiumStyle", "ToggleCategoryFilter:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent", "WatchAdToUnlock:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent", "ShowFeatureInfo:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent", "BatteryGalleryEvent:com.tqhit.battery.one.features.emoji.presentation.gallery", "showOnlyFree:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.ToggleShowOnlyFree", "ToggleShowOnlyPopular:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent", "PurchasePremiumAccess:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent", "query:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.SearchStyles", "DismissError:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent", "RetryLoading:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent", "OnResume:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent", "ClearAllFilters:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent", "NavigateToSettings:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent", "category:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.FilterByCategory", "ToggleShowOnlyFree:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent", "showOnlyPremium:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.ToggleShowOnlyPremium", "<init>:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.ClearAllFilters", "PreviewStyle:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent", "CustomizeStyle:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent", "RequestPermissions:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent", "styleId:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.PremiumUnlocked", "UnlockPremiumStyle:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent", "<init>:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.OnResume", "SearchStyles:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent", "enabled:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.ToggleEmojiBatteryFeature", "createCategoryFilterEvent:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.Companion", "<init>:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.DismissError", "PremiumUnlocked:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent", "<init>:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.Companion", "NavigateToCustomization:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent", "show:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.ToggleSearchBar", "PermissionsDenied:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent", "createSearchEvent:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.Companion", "OnPause:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent", "style:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.PreviewStyle", "styleId:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.ImageLoadFailed", "<init>:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.RetryLoading", "RefreshData:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent", "SelectStyle:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent", "style:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.CustomizeStyle", "style:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.SelectStyle", "BatteryStateChanged:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent", "style:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.NavigateToCustomization", "PermissionsGranted:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent", "ToggleShowOnlyPremium:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent", "<init>:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.PermissionsGranted", "<init>:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.OnPause", "showOnlyPopular:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.ToggleShowOnlyPopular", "batteryLevel:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.BatteryStateChanged", "ToggleSearchBar:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent", "style:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.WatchAdToUnlock", "<init>:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent.RefreshData"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\emoji\\presentation\\customize\\CustomizeViewModel.kt": ["uiState:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModel", "<init>:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModel.Companion", "CustomizeViewModel:com.tqhit.battery.one.features.emoji.presentation.customize", "Companion:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModel", "handleEvent:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModel", "onCleared:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModel"], "src\\main\\java\\com\\tqhit\\battery\\one\\repository\\BatteryRepository.kt": ["mgr:com.tqhit.battery.one.repository.BatteryRepository", "batteryPercentage:com.tqhit.battery.one.repository.BatteryRepository", "<init>:com.tqhit.battery.one.repository.BatteryRepository.Companion", "dischargeScreenOnAverageSpeedSession:com.tqhit.battery.one.repository.BatteryRepository", "endPercentSession:com.tqhit.battery.one.repository.BatteryRepository", "setLastValidDischargeSession:com.tqhit.battery.one.repository.BatteryRepository", "dischargeScreenOffPercentCurrentSession:com.tqhit.battery.one.repository.BatteryRepository", "updateSessionChargeInfo:com.tqhit.battery.one.repository.BatteryRepository", "averageSpeed:com.tqhit.battery.one.repository.BatteryRepository", "getBatteryPolarity:com.tqhit.battery.one.repository.BatteryRepository", "screenOffTimeRemainingAt100:com.tqhit.battery.one.repository.BatteryRepository", "dischargeScreenOnMilliAmperesSession:com.tqhit.battery.one.repository.BatteryRepository", "chargingRate:com.tqhit.battery.one.repository.BatteryRepository", "isCharging:com.tqhit.battery.one.repository.BatteryRepository", "usageStyleTimeRemainingAt100:com.tqhit.battery.one.repository.BatteryRepository", "averageScreenOnSpeed:com.tqhit.battery.one.repository.BatteryRepository", "CHARGING_CHECK_INTERVAL:com.tqhit.battery.one.repository.BatteryRepository.Companion", "getCurrentChargingSession:com.tqhit.battery.one.repository.BatteryRepository", "clearDischargeSessions:com.tqhit.battery.one.repository.BatteryRepository", "getCurrentDischargeSession:com.tqhit.battery.one.repository.BatteryRepository", "batteryCapacity:com.tqhit.battery.one.repository.BatteryRepository", "getLastValidChargingSession:com.tqhit.battery.one.repository.BatteryRepository", "endTimeSession:com.tqhit.battery.one.repository.BatteryRepository", "averageScreenOnMilliAmperes:com.tqhit.battery.one.repository.BatteryRepository", "dischargeRightNowPercentPerHourSession:com.tqhit.battery.one.repository.BatteryRepository", "averageSpeedMilliAmperesSession:com.tqhit.battery.one.repository.BatteryRepository", "screenOnMilliAmperesSession:com.tqhit.battery.one.repository.BatteryRepository", "temperature:com.tqhit.battery.one.repository.BatteryRepository", "batteryHealth:com.tqhit.battery.one.repository.BatteryRepository", "power:com.tqhit.battery.one.repository.BatteryRepository", "isIgnoringBatteryOptimizations:com.tqhit.battery.one.repository.BatteryRepository", "registerReceivers:com.tqhit.battery.one.repository.BatteryRepository", "dischargeScreenOnPercentCurrentSession:com.tqhit.battery.one.repository.BatteryRepository", "updateSessionDischargeInfo:com.tqhit.battery.one.repository.BatteryRepository", "dischargeScreenOffAverageSpeedSession:com.tqhit.battery.one.repository.BatteryRepository", "unregisterReceivers:com.tqhit.battery.one.repository.BatteryRepository", "updateBatteryInfo:com.tqhit.battery.one.repository.BatteryRepository", "startTimeSession:com.tqhit.battery.one.repository.BatteryRepository", "UPDATE_NOTIFICATION_INTERVAL:com.tqhit.battery.one.repository.BatteryRepository.Companion", "screenOffTimeRemaining:com.tqhit.battery.one.repository.BatteryRepository", "dischargeAverageSpeedMilliAmperesSession:com.tqhit.battery.one.repository.BatteryRepository", "screenOnTimeRemaining:com.tqhit.battery.one.repository.BatteryRepository", "clearChargingSessions:com.tqhit.battery.one.repository.BatteryRepository", "totalMilliAmperesSession:com.tqhit.battery.one.repository.BatteryRepository", "screenOnTimeRemainingAt100:com.tqhit.battery.one.repository.BatteryRepository", "BatteryRepository:com.tqhit.battery.one.repository", "getHistoryTemperatureForMinutes:com.tqhit.battery.one.repository.BatteryRepository", "chargingTimeRemaining:com.tqhit.battery.one.repository.BatteryRepository", "chargingTimeRemainingToTarget:com.tqhit.battery.one.repository.BatteryRepository", "dischargeStartTimeSession:com.tqhit.battery.one.repository.BatteryRepository", "dischargeEndTimeSession:com.tqhit.battery.one.repository.BatteryRepository", "voltage:com.tqhit.battery.one.repository.BatteryRepository", "screenOnAverageSpeedSession:com.tqhit.battery.one.repository.BatteryRepository", "setSelectedPercent:com.tqhit.battery.one.repository.BatteryRepository", "usageStyleTimeRemaining:com.tqhit.battery.one.repository.BatteryRepository", "getBatteryCapacity:com.tqhit.battery.one.repository.BatteryRepository", "setBatteryCapacity:com.tqhit.battery.one.repository.BatteryRepository", "getDailyWearData:com.tqhit.battery.one.repository.BatteryRepository", "getSelectedPercent:com.tqhit.battery.one.repository.BatteryRepository", "amperage:com.tqhit.battery.one.repository.BatteryRepository", "screenOffAverageSpeedSession:com.tqhit.battery.one.repository.BatteryRepository", "getHistoryBatteryForHours:com.tqhit.battery.one.repository.BatteryRepository", "rightNowPercentPerHourSession:com.tqhit.battery.one.repository.BatteryRepository", "dischargeScreenOnTimeCurrentSession:com.tqhit.battery.one.repository.BatteryRepository", "dischargeScreenOffMilliAmperesSession:com.tqhit.battery.one.repository.BatteryRepository", "averageMilliAmperes:com.tqhit.battery.one.repository.BatteryRepository", "getHistoryBatteryForMinutes:com.tqhit.battery.one.repository.BatteryRepository", "startPercentSession:com.tqhit.battery.one.repository.BatteryRepository", "averageScreenOffMilliAmperes:com.tqhit.battery.one.repository.BatteryRepository", "setCurrentDischargeSession:com.tqhit.battery.one.repository.BatteryRepository", "totalChargeSession:com.tqhit.battery.one.repository.BatteryRepository", "Companion:com.tqhit.battery.one.repository.BatteryRepository", "selectedPercent:com.tqhit.battery.one.repository.BatteryRepository", "setLastValidChargingSession:com.tqhit.battery.one.repository.BatteryRepository", "dischargeTotalMilliAmperesSession:com.tqhit.battery.one.repository.BatteryRepository", "dischargeEndPercentSession:com.tqhit.battery.one.repository.BatteryRepository", "dischargeStartPercentSession:com.tqhit.battery.one.repository.BatteryRepository", "dischargeScreenOffTimeCurrentSession:com.tqhit.battery.one.repository.BatteryRepository", "averageScreenOffSpeed:com.tqhit.battery.one.repository.BatteryRepository", "dischargeAverageSpeedSession:com.tqhit.battery.one.repository.BatteryRepository", "getBatteryStatus:com.tqhit.battery.one.repository.BatteryRepository", "screenOffMilliAmperesSession:com.tqhit.battery.one.repository.BatteryRepository", "getLastValidDischargeSession:com.tqhit.battery.one.repository.BatteryRepository", "averageSpeedSession:com.tqhit.battery.one.repository.BatteryRepository", "setCurrentChargingSession:com.tqhit.battery.one.repository.BatteryRepository", "getHistoryTemperatureForHours:com.tqhit.battery.one.repository.BatteryRepository"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\repository\\BatteryRepository_Factory.java": ["BatteryRepository_Factory:com.tqhit.battery.one.repository", "<init>:com.tqhit.battery.one.repository.BatteryRepository_Factory", "create:com.tqhit.battery.one.repository.BatteryRepository_Factory", "newInstance:com.tqhit.battery.one.repository.BatteryRepository_Factory", "get:com.tqhit.battery.one.repository.BatteryRepository_Factory"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\tqhit\\battery\\one\\databinding\\ItemAnimationBinding.java": ["applyButton:com.tqhit.battery.one.databinding.ItemAnimationBinding", "iconAd:com.tqhit.battery.one.databinding.ItemAnimationBinding", "shimmerLayout:com.tqhit.battery.one.databinding.ItemAnimationBinding", "textBtn:com.tqhit.battery.one.databinding.ItemAnimationBinding", "ItemAnimationBinding:com.tqhit.battery.one.databinding", "lockBtn:com.tqhit.battery.one.databinding.ItemAnimationBinding", "getRoot:com.tqhit.battery.one.databinding.ItemAnimationBinding", "inflate:com.tqhit.battery.one.databinding.ItemAnimationBinding", "applyBlock:com.tqhit.battery.one.databinding.ItemAnimationBinding", "bind:com.tqhit.battery.one.databinding.ItemAnimationBinding", "cardImage:com.tqhit.battery.one.databinding.ItemAnimationBinding"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\di\\StatsDischargeModule.kt": ["<init>:com.tqhit.battery.one.features.stats.discharge.di.StatsDischargeModule", "provideScreenStateReceiver:com.tqhit.battery.one.features.stats.discharge.di.StatsDischargeProvidersModule", "StatsDischargeModule:com.tqhit.battery.one.features.stats.discharge.di", "bindDischargeRatesCache:com.tqhit.battery.one.features.stats.discharge.di.StatsDischargeModule", "<init>:com.tqhit.battery.one.features.stats.discharge.di.StatsDischargeProvidersModule", "StatsDischargeProvidersModule:com.tqhit.battery.one.features.stats.discharge.di", "bindCurrentSessionCache:com.tqhit.battery.one.features.stats.discharge.di.StatsDischargeModule"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\fragment\\main\\Hilt_SettingsFragment.java": ["getDefaultViewModelProviderFactory:com.tqhit.battery.one.fragment.main.Hilt_SettingsFragment", "createComponentManager:com.tqhit.battery.one.fragment.main.Hilt_SettingsFragment", "getContext:com.tqhit.battery.one.fragment.main.Hilt_SettingsFragment", "Hilt_SettingsFragment:com.tqhit.battery.one.fragment.main", "<init>:com.tqhit.battery.one.fragment.main.Hilt_SettingsFragment", "componentManager:com.tqhit.battery.one.fragment.main.Hilt_SettingsFragment", "onAttach:com.tqhit.battery.one.fragment.main.Hilt_SettingsFragment", "onGetLayoutInflater:com.tqhit.battery.one.fragment.main.Hilt_SettingsFragment", "generatedComponent:com.tqhit.battery.one.fragment.main.Hilt_SettingsFragment", "inject:com.tqhit.battery.one.fragment.main.Hilt_SettingsFragment"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\notifications\\UnifiedBatteryNotificationService_MembersInjector.java": ["UnifiedBatteryNotificationService_MembersInjector:com.tqhit.battery.one.features.stats.notifications", "create:com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationService_MembersInjector", "injectAppRepository:com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationService_MembersInjector", "injectCoreBatteryStatsProvider:com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationService_MembersInjector", "injectMembers:com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationService_MembersInjector", "<init>:com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationService_MembersInjector"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\tqhit\\battery\\one\\databinding\\ItemAppPowerConsumptionBinding.java": ["ItemAppPowerConsumptionBinding:com.tqhit.battery.one.databinding", "tvAppName:com.tqhit.battery.one.databinding.ItemAppPowerConsumptionBinding", "tvPercentage:com.tqhit.battery.one.databinding.ItemAppPowerConsumptionBinding", "tvUsageTime:com.tqhit.battery.one.databinding.ItemAppPowerConsumptionBinding", "bind:com.tqhit.battery.one.databinding.ItemAppPowerConsumptionBinding", "ivAppIcon:com.tqhit.battery.one.databinding.ItemAppPowerConsumptionBinding", "getRoot:com.tqhit.battery.one.databinding.ItemAppPowerConsumptionBinding", "tvPowerConsumption:com.tqhit.battery.one.databinding.ItemAppPowerConsumptionBinding", "inflate:com.tqhit.battery.one.databinding.ItemAppPowerConsumptionBinding"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\tqhit\\battery\\one\\databinding\\DialogSelectBatteryAlarmBinding.java": ["switchDontDisturb:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmBinding", "p192:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmBinding", "inflate:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmBinding", "chargeAlarmPercent:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmBinding", "relativ33:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmBinding", "switchFull:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmBinding", "linearLayout2:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmBinding", "switchVibration:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmBinding", "linearLayout:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmBinding", "p12er1:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmBinding", "dontDisturbUntil:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmBinding", "p6:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmBinding", "bind:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmBinding", "battery4:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmBinding", "p1192:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmBinding", "strelka:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmBinding", "seekBarChargeAlarm:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmBinding", "getRoot:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmBinding", "chargeL:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmBinding", "textView20:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmBinding", "switchChargeAlarm:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmBinding", "dontDisturbFromLayout:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmBinding", "p12e2:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmBinding", "p12er:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmBinding", "dontDisturbFrom:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmBinding", "exit:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmBinding", "textBuyAccess2:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmBinding", "progressbarChargeAlarm:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmBinding", "relat223:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmBinding", "p11:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmBinding", "p112:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmBinding", "DialogSelectBatteryAlarmBinding:com.tqhit.battery.one.databinding", "dontDisturbUntilLayout:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmBinding", "buttonChargeAlarm:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmBinding", "p1221:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmBinding", "p5:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmBinding", "p7:com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmBinding"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\service\\EnhancedDischargeTimerServiceHelper_Factory.java": ["get:com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerServiceHelper_Factory", "newInstance:com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerServiceHelper_Factory", "EnhancedDischargeTimerServiceHelper_Factory:com.tqhit.battery.one.features.stats.discharge.service", "create:com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerServiceHelper_Factory", "<init>:com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerServiceHelper_Factory"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_activity_splash_SplashActivity_GeneratedInjector.java": ["_com_tqhit_battery_one_activity_splash_SplashActivity_GeneratedInjector:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_tqhit_battery_one_activity_splash_SplashActivity_GeneratedInjector"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\tqhit\\battery\\one\\fragment\\main\\Hilt_DischargeFragment.java": ["Hilt_DischargeFragment:com.tqhit.battery.one.fragment.main", "onAttach:com.tqhit.battery.one.fragment.main.Hilt_DischargeFragment", "onGetLayoutInflater:com.tqhit.battery.one.fragment.main.Hilt_DischargeFragment", "<init>:com.tqhit.battery.one.fragment.main.Hilt_DischargeFragment", "getDefaultViewModelProviderFactory:com.tqhit.battery.one.fragment.main.Hilt_DischargeFragment", "getContext:com.tqhit.battery.one.fragment.main.Hilt_DischargeFragment", "createComponentManager:com.tqhit.battery.one.fragment.main.Hilt_DischargeFragment", "generatedComponent:com.tqhit.battery.one.fragment.main.Hilt_DischargeFragment", "componentManager:com.tqhit.battery.one.fragment.main.Hilt_DischargeFragment", "inject:com.tqhit.battery.one.fragment.main.Hilt_DischargeFragment"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\tqhit\\battery\\one\\features\\emoji\\presentation\\gallery\\BatteryGalleryViewModel_HiltModules_KeyModule_ProvideFactory.java": ["BatteryGalleryViewModel_HiltModules_KeyModule_ProvideFactory:com.tqhit.battery.one.features.emoji.presentation.gallery", "INSTANCE:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder", "<init>:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModel_HiltModules_KeyModule_ProvideFactory", "provide:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModel_HiltModules_KeyModule_ProvideFactory", "create:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModel_HiltModules_KeyModule_ProvideFactory", "<init>:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder", "get:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModel_HiltModules_KeyModule_ProvideFactory"], "src\\main\\java\\com\\tqhit\\battery\\one\\utils\\BatteryLogger.kt": ["i:com.tqhit.battery.one.utils.BatteryLogger", "e:com.tqhit.battery.one.utils.BatteryLogger", "logBatteryStatus:com.tqhit.battery.one.utils.BatteryLogger", "logMetric:com.tqhit.battery.one.utils.BatteryLogger", "d:com.tqhit.battery.one.utils.BatteryLogger", "<init>:com.tqhit.battery.one.utils.BatteryLogger", "logMetrics:com.tqhit.battery.one.utils.BatteryLogger", "BatteryLogger:com.tqhit.battery.one.utils", "logTiming:com.tqhit.battery.one.utils.BatteryLogger", "w:com.tqhit.battery.one.utils.BatteryLogger", "v:com.tqhit.battery.one.utils.BatteryLogger", "LOGGING_ENABLED:com.tqhit.battery.one.utils.BatteryLogger", "isLoggingEnabled:com.tqhit.battery.one.utils.BatteryLogger"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_tqhit_battery_one_features_emoji_presentation_customize_CustomizeFragment_GeneratedInjector.java": ["_com_tqhit_battery_one_features_emoji_presentation_customize_CustomizeFragment_GeneratedInjector:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_tqhit_battery_one_features_emoji_presentation_customize_CustomizeFragment_GeneratedInjector"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\tqhit\\battery\\one\\features\\stats\\health\\presentation\\HealthViewModel_HiltModules_KeyModule_Provide_LazyMapKey.java": ["<init>:com.tqhit.battery.one.features.stats.health.presentation.HealthViewModel_HiltModules_KeyModule_Provide_LazyMapKey", "keepFieldType:com.tqhit.battery.one.features.stats.health.presentation.HealthViewModel_HiltModules_KeyModule_Provide_LazyMapKey", "HealthViewModel_HiltModules_KeyModule_Provide_LazyMapKey:com.tqhit.battery.one.features.stats.health.presentation", "lazyClassKeyName:com.tqhit.battery.one.features.stats.health.presentation.HealthViewModel_HiltModules_KeyModule_Provide_LazyMapKey"], "src\\main\\java\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\domain\\AppLifecycleManager.kt": ["<init>:com.tqhit.battery.one.features.stats.discharge.domain.AppState.BACKGROUND", "shouldTriggerUiUpdate:com.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManager", "getCurrentStateInfo:com.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManager", "onStart:com.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManager", "setDischargeFragmentActive:com.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManager", "Companion:com.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManager", "<init>:com.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManager.Companion", "<init>:com.tqhit.battery.one.features.stats.discharge.domain.AppState.FOREGROUND", "BACKGROUND:com.tqhit.battery.one.features.stats.discharge.domain.AppState", "AppLifecycleManager:com.tqhit.battery.one.features.stats.discharge.domain", "isDischargeFragmentActive:com.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManager", "appState:com.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManager", "onStop:com.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManager", "AppState:com.tqhit.battery.one.features.stats.discharge.domain", "FOREGROUND:com.tqhit.battery.one.features.stats.discharge.domain.AppState"]}