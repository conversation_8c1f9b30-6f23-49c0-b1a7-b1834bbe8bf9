package com.tqhit.battery.one;

import android.app.Activity;
import android.app.Service;
import android.content.SharedPreferences;
import android.view.View;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.SavedStateHandle;
import androidx.lifecycle.ViewModel;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.google.errorprone.annotations.CanIgnoreReturnValue;
import com.google.firebase.analytics.FirebaseAnalytics;
import com.google.firebase.remoteconfig.FirebaseRemoteConfig;
import com.google.gson.Gson;
import com.tqhit.adlib.sdk.AdLibHiltApplication_MembersInjector;
import com.tqhit.adlib.sdk.adjust.AdjustAnalyticsHelper;
import com.tqhit.adlib.sdk.ads.AdmobConsentHelper;
import com.tqhit.adlib.sdk.ads.AdmobHelper;
import com.tqhit.adlib.sdk.ads.AppOpenHelper;
import com.tqhit.adlib.sdk.ads.BannerHelper;
import com.tqhit.adlib.sdk.ads.InterstitialHelper;
import com.tqhit.adlib.sdk.ads.NativeHelper;
import com.tqhit.adlib.sdk.ads.RewardHelper;
import com.tqhit.adlib.sdk.analytics.AnalyticsTracker;
import com.tqhit.adlib.sdk.data.local.PreferencesHelper;
import com.tqhit.adlib.sdk.di.AdmobModule_ProvideAdmobConsentHelperFactory;
import com.tqhit.adlib.sdk.di.AdmobModule_ProvideAdmobHelperFactory;
import com.tqhit.adlib.sdk.di.AdmobModule_ProvideAppOpenHelperFactory;
import com.tqhit.adlib.sdk.di.AdmobModule_ProvideBannerHelperFactory;
import com.tqhit.adlib.sdk.di.AdmobModule_ProvideInterstitialHelperFactory;
import com.tqhit.adlib.sdk.di.AdmobModule_ProvideNativeHelperFactory;
import com.tqhit.adlib.sdk.di.AdmobModule_ProvideRewardHelperFactory;
import com.tqhit.adlib.sdk.di.AnalyticsModule_ProvideAdjustAnalyticsHelperFactory;
import com.tqhit.adlib.sdk.di.AnalyticsModule_ProvideTrackingManagerFactory;
import com.tqhit.adlib.sdk.di.FirebaseModule_ProvideFirebaseAnalyticsFactory;
import com.tqhit.adlib.sdk.di.FirebaseModule_ProvideFirebaseAnalyticsHelperFactory;
import com.tqhit.adlib.sdk.di.FirebaseModule_ProvideFirebaseRemoteConfigFactory;
import com.tqhit.adlib.sdk.di.FirebaseModule_ProvideFirebaseRemoteConfigHelperFactory;
import com.tqhit.adlib.sdk.di.SharedPreferencesModule_ProvideGsonFactory;
import com.tqhit.adlib.sdk.di.SharedPreferencesModule_ProvidePreferencesHelperFactory;
import com.tqhit.adlib.sdk.di.SharedPreferencesModule_ProvideSharedPreferencesFactory;
import com.tqhit.adlib.sdk.firebase.FirebaseAnalyticsHelper;
import com.tqhit.adlib.sdk.firebase.FirebaseRemoteConfigHelper;
import com.tqhit.adlib.sdk.ui.main.MainActivity;
import com.tqhit.adlib.sdk.ui.main.MainActivity_MembersInjector;
import com.tqhit.battery.one.activity.animation.AnimationActivity;
import com.tqhit.battery.one.activity.animation.AnimationActivity_MembersInjector;
import com.tqhit.battery.one.activity.overlay.ChargingOverlayActivity;
import com.tqhit.battery.one.activity.overlay.ChargingOverlayActivity_MembersInjector;
import com.tqhit.battery.one.activity.password.EnterPasswordActivity;
import com.tqhit.battery.one.activity.password.EnterPasswordActivity_MembersInjector;
import com.tqhit.battery.one.activity.splash.SplashActivity;
import com.tqhit.battery.one.activity.starting.StartingActivity;
import com.tqhit.battery.one.activity.starting.StartingActivity_MembersInjector;
import com.tqhit.battery.one.ads.core.ApplovinAppOpenAdManager;
import com.tqhit.battery.one.ads.core.ApplovinBannerAdManager;
import com.tqhit.battery.one.ads.core.ApplovinInterstitialAdManager;
import com.tqhit.battery.one.ads.core.ApplovinNativeAdManager;
import com.tqhit.battery.one.ads.core.ApplovinRewardedAdManager;
import com.tqhit.battery.one.features.emoji.data.datastore.CustomizationDataStore;
import com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl;
import com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImpl;
import com.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCase;
import com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase;
import com.tqhit.battery.one.features.emoji.domain.use_case.ResetCustomizationUseCase;
import com.tqhit.battery.one.features.emoji.domain.use_case.SaveCustomizationUseCase;
import com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeFragment;
import com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModel;
import com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModel_HiltModules;
import com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModel_HiltModules_BindsModule_Binds_LazyMapKey;
import com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModel_HiltModules_KeyModule_Provide_LazyMapKey;
import com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModel;
import com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModel_HiltModules;
import com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModel_HiltModules_BindsModule_Binds_LazyMapKey;
import com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModel_HiltModules_KeyModule_Provide_LazyMapKey;
import com.tqhit.battery.one.features.emoji.presentation.gallery.EmojiBatteryFragment;
import com.tqhit.battery.one.features.navigation.DynamicNavigationManager;
import com.tqhit.battery.one.features.stats.apppower.permission.UsageStatsPermissionManager;
import com.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionDialogFactory;
import com.tqhit.battery.one.features.stats.apppower.repository.AppUsageStatsRepository;
import com.tqhit.battery.one.features.stats.charge.cache.PrefsStatsChargeCache;
import com.tqhit.battery.one.features.stats.charge.domain.CalculateSimpleChargeEstimateUseCase;
import com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment;
import com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment_MembersInjector;
import com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel;
import com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel_HiltModules;
import com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel_HiltModules_BindsModule_Binds_LazyMapKey;
import com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel_HiltModules_KeyModule_Provide_LazyMapKey;
import com.tqhit.battery.one.features.stats.charge.repository.DefaultStatsChargeRepository;
import com.tqhit.battery.one.features.stats.corebattery.domain.DefaultCoreBatteryStatsProvider;
import com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryServiceHelper;
import com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService;
import com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService_MembersInjector;
import com.tqhit.battery.one.features.stats.discharge.cache.PrefsCurrentSessionCache;
import com.tqhit.battery.one.features.stats.discharge.cache.PrefsDischargeRatesCache;
import com.tqhit.battery.one.features.stats.discharge.datasource.ScreenStateReceiver;
import com.tqhit.battery.one.features.stats.discharge.di.StatsDischargeProvidersModule_ProvideScreenStateReceiverFactory;
import com.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManager;
import com.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculator;
import com.tqhit.battery.one.features.stats.discharge.domain.DischargeRateCalculator;
import com.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimator;
import com.tqhit.battery.one.features.stats.discharge.domain.GapEstimationCalculator;
import com.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeCalculator;
import com.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationService;
import com.tqhit.battery.one.features.stats.discharge.domain.SessionManager;
import com.tqhit.battery.one.features.stats.discharge.domain.SessionMetricsCalculator;
import com.tqhit.battery.one.features.stats.discharge.domain.TimeConverter;
import com.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment;
import com.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment_MembersInjector;
import com.tqhit.battery.one.features.stats.discharge.presentation.DischargeViewModel;
import com.tqhit.battery.one.features.stats.discharge.presentation.DischargeViewModel_HiltModules;
import com.tqhit.battery.one.features.stats.discharge.presentation.DischargeViewModel_HiltModules_BindsModule_Binds_LazyMapKey;
import com.tqhit.battery.one.features.stats.discharge.presentation.DischargeViewModel_HiltModules_KeyModule_Provide_LazyMapKey;
import com.tqhit.battery.one.features.stats.discharge.presentation.InfoButtonManager;
import com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository;
import com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerService;
import com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerServiceHelper;
import com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerService_MembersInjector;
import com.tqhit.battery.one.features.stats.health.cache.DefaultHealthCache;
import com.tqhit.battery.one.features.stats.health.presentation.HealthViewModel;
import com.tqhit.battery.one.features.stats.health.presentation.HealthViewModel_HiltModules;
import com.tqhit.battery.one.features.stats.health.presentation.HealthViewModel_HiltModules_BindsModule_Binds_LazyMapKey;
import com.tqhit.battery.one.features.stats.health.presentation.HealthViewModel_HiltModules_KeyModule_Provide_LazyMapKey;
import com.tqhit.battery.one.features.stats.health.repository.DefaultHealthRepository;
import com.tqhit.battery.one.features.stats.health.repository.HistoryBatteryRepository;
import com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationService;
import com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationServiceHelper;
import com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationService_MembersInjector;
import com.tqhit.battery.one.fragment.main.ChargeFragment;
import com.tqhit.battery.one.fragment.main.ChargeFragment_MembersInjector;
import com.tqhit.battery.one.fragment.main.HealthFragment;
import com.tqhit.battery.one.fragment.main.HealthFragment_MembersInjector;
import com.tqhit.battery.one.fragment.main.SettingsFragment;
import com.tqhit.battery.one.fragment.main.SettingsFragment_MembersInjector;
import com.tqhit.battery.one.fragment.main.animation.AnimationGridFragment;
import com.tqhit.battery.one.fragment.main.animation.AnimationGridFragment_MembersInjector;
import com.tqhit.battery.one.manager.charge.ChargingSessionManager;
import com.tqhit.battery.one.manager.discharge.DischargeSessionManager;
import com.tqhit.battery.one.manager.graph.BatteryHistoryManager;
import com.tqhit.battery.one.manager.graph.TemperatureHistoryManager;
import com.tqhit.battery.one.repository.AnimationRepository;
import com.tqhit.battery.one.repository.AppRepository;
import com.tqhit.battery.one.repository.BatteryRepository;
import com.tqhit.battery.one.service.BatteryMonitorService;
import com.tqhit.battery.one.service.BatteryMonitorService_MembersInjector;
import com.tqhit.battery.one.service.ChargingOverlayService;
import com.tqhit.battery.one.service.ChargingOverlayService_MembersInjector;
import com.tqhit.battery.one.service.VibrationService;
import com.tqhit.battery.one.viewmodel.AppViewModel;
import com.tqhit.battery.one.viewmodel.AppViewModel_HiltModules;
import com.tqhit.battery.one.viewmodel.AppViewModel_HiltModules_BindsModule_Binds_LazyMapKey;
import com.tqhit.battery.one.viewmodel.AppViewModel_HiltModules_KeyModule_Provide_LazyMapKey;
import com.tqhit.battery.one.viewmodel.animation.AnimationViewModel;
import com.tqhit.battery.one.viewmodel.animation.AnimationViewModel_HiltModules;
import com.tqhit.battery.one.viewmodel.animation.AnimationViewModel_HiltModules_BindsModule_Binds_LazyMapKey;
import com.tqhit.battery.one.viewmodel.animation.AnimationViewModel_HiltModules_KeyModule_Provide_LazyMapKey;
import com.tqhit.battery.one.viewmodel.battery.BatteryViewModel;
import com.tqhit.battery.one.viewmodel.battery.BatteryViewModel_HiltModules;
import com.tqhit.battery.one.viewmodel.battery.BatteryViewModel_HiltModules_BindsModule_Binds_LazyMapKey;
import com.tqhit.battery.one.viewmodel.battery.BatteryViewModel_HiltModules_KeyModule_Provide_LazyMapKey;
import dagger.hilt.android.ActivityRetainedLifecycle;
import dagger.hilt.android.ViewModelLifecycle;
import dagger.hilt.android.internal.builders.ActivityComponentBuilder;
import dagger.hilt.android.internal.builders.ActivityRetainedComponentBuilder;
import dagger.hilt.android.internal.builders.FragmentComponentBuilder;
import dagger.hilt.android.internal.builders.ServiceComponentBuilder;
import dagger.hilt.android.internal.builders.ViewComponentBuilder;
import dagger.hilt.android.internal.builders.ViewModelComponentBuilder;
import dagger.hilt.android.internal.builders.ViewWithFragmentComponentBuilder;
import dagger.hilt.android.internal.lifecycle.DefaultViewModelFactories;
import dagger.hilt.android.internal.lifecycle.DefaultViewModelFactories_InternalFactoryFactory_Factory;
import dagger.hilt.android.internal.managers.ActivityRetainedComponentManager_LifecycleModule_ProvideActivityRetainedLifecycleFactory;
import dagger.hilt.android.internal.managers.SavedStateHandleHolder;
import dagger.hilt.android.internal.modules.ApplicationContextModule;
import dagger.hilt.android.internal.modules.ApplicationContextModule_ProvideContextFactory;
import dagger.internal.DaggerGenerated;
import dagger.internal.DoubleCheck;
import dagger.internal.LazyClassKeyMap;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import java.util.Map;
import java.util.Set;
import javax.annotation.processing.Generated;

@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class DaggerBatteryApplication_HiltComponents_SingletonC {
  private DaggerBatteryApplication_HiltComponents_SingletonC() {
  }

  public static Builder builder() {
    return new Builder();
  }

  public static final class Builder {
    private ApplicationContextModule applicationContextModule;

    private Builder() {
    }

    public Builder applicationContextModule(ApplicationContextModule applicationContextModule) {
      this.applicationContextModule = Preconditions.checkNotNull(applicationContextModule);
      return this;
    }

    public BatteryApplication_HiltComponents.SingletonC build() {
      Preconditions.checkBuilderRequirement(applicationContextModule, ApplicationContextModule.class);
      return new SingletonCImpl(applicationContextModule);
    }
  }

  private static final class ActivityRetainedCBuilder implements BatteryApplication_HiltComponents.ActivityRetainedC.Builder {
    private final SingletonCImpl singletonCImpl;

    private SavedStateHandleHolder savedStateHandleHolder;

    private ActivityRetainedCBuilder(SingletonCImpl singletonCImpl) {
      this.singletonCImpl = singletonCImpl;
    }

    @Override
    public ActivityRetainedCBuilder savedStateHandleHolder(
        SavedStateHandleHolder savedStateHandleHolder) {
      this.savedStateHandleHolder = Preconditions.checkNotNull(savedStateHandleHolder);
      return this;
    }

    @Override
    public BatteryApplication_HiltComponents.ActivityRetainedC build() {
      Preconditions.checkBuilderRequirement(savedStateHandleHolder, SavedStateHandleHolder.class);
      return new ActivityRetainedCImpl(singletonCImpl, savedStateHandleHolder);
    }
  }

  private static final class ActivityCBuilder implements BatteryApplication_HiltComponents.ActivityC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private Activity activity;

    private ActivityCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
    }

    @Override
    public ActivityCBuilder activity(Activity activity) {
      this.activity = Preconditions.checkNotNull(activity);
      return this;
    }

    @Override
    public BatteryApplication_HiltComponents.ActivityC build() {
      Preconditions.checkBuilderRequirement(activity, Activity.class);
      return new ActivityCImpl(singletonCImpl, activityRetainedCImpl, activity);
    }
  }

  private static final class FragmentCBuilder implements BatteryApplication_HiltComponents.FragmentC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private Fragment fragment;

    private FragmentCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
    }

    @Override
    public FragmentCBuilder fragment(Fragment fragment) {
      this.fragment = Preconditions.checkNotNull(fragment);
      return this;
    }

    @Override
    public BatteryApplication_HiltComponents.FragmentC build() {
      Preconditions.checkBuilderRequirement(fragment, Fragment.class);
      return new FragmentCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, fragment);
    }
  }

  private static final class ViewWithFragmentCBuilder implements BatteryApplication_HiltComponents.ViewWithFragmentC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl;

    private View view;

    private ViewWithFragmentCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl,
        FragmentCImpl fragmentCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
      this.fragmentCImpl = fragmentCImpl;
    }

    @Override
    public ViewWithFragmentCBuilder view(View view) {
      this.view = Preconditions.checkNotNull(view);
      return this;
    }

    @Override
    public BatteryApplication_HiltComponents.ViewWithFragmentC build() {
      Preconditions.checkBuilderRequirement(view, View.class);
      return new ViewWithFragmentCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, fragmentCImpl, view);
    }
  }

  private static final class ViewCBuilder implements BatteryApplication_HiltComponents.ViewC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private View view;

    private ViewCBuilder(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
        ActivityCImpl activityCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
    }

    @Override
    public ViewCBuilder view(View view) {
      this.view = Preconditions.checkNotNull(view);
      return this;
    }

    @Override
    public BatteryApplication_HiltComponents.ViewC build() {
      Preconditions.checkBuilderRequirement(view, View.class);
      return new ViewCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, view);
    }
  }

  private static final class ViewModelCBuilder implements BatteryApplication_HiltComponents.ViewModelC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private SavedStateHandle savedStateHandle;

    private ViewModelLifecycle viewModelLifecycle;

    private ViewModelCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
    }

    @Override
    public ViewModelCBuilder savedStateHandle(SavedStateHandle handle) {
      this.savedStateHandle = Preconditions.checkNotNull(handle);
      return this;
    }

    @Override
    public ViewModelCBuilder viewModelLifecycle(ViewModelLifecycle viewModelLifecycle) {
      this.viewModelLifecycle = Preconditions.checkNotNull(viewModelLifecycle);
      return this;
    }

    @Override
    public BatteryApplication_HiltComponents.ViewModelC build() {
      Preconditions.checkBuilderRequirement(savedStateHandle, SavedStateHandle.class);
      Preconditions.checkBuilderRequirement(viewModelLifecycle, ViewModelLifecycle.class);
      return new ViewModelCImpl(singletonCImpl, activityRetainedCImpl, savedStateHandle, viewModelLifecycle);
    }
  }

  private static final class ServiceCBuilder implements BatteryApplication_HiltComponents.ServiceC.Builder {
    private final SingletonCImpl singletonCImpl;

    private Service service;

    private ServiceCBuilder(SingletonCImpl singletonCImpl) {
      this.singletonCImpl = singletonCImpl;
    }

    @Override
    public ServiceCBuilder service(Service service) {
      this.service = Preconditions.checkNotNull(service);
      return this;
    }

    @Override
    public BatteryApplication_HiltComponents.ServiceC build() {
      Preconditions.checkBuilderRequirement(service, Service.class);
      return new ServiceCImpl(singletonCImpl, service);
    }
  }

  private static final class ViewWithFragmentCImpl extends BatteryApplication_HiltComponents.ViewWithFragmentC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl;

    private final ViewWithFragmentCImpl viewWithFragmentCImpl = this;

    ViewWithFragmentCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl,
        FragmentCImpl fragmentCImpl, View viewParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
      this.fragmentCImpl = fragmentCImpl;


    }
  }

  private static final class FragmentCImpl extends BatteryApplication_HiltComponents.FragmentC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl = this;

    FragmentCImpl(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
        ActivityCImpl activityCImpl, Fragment fragmentParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;


    }

    InfoButtonManager infoButtonManager() {
      return new InfoButtonManager(activityCImpl.activity, singletonCImpl.appPowerConsumptionDialogFactoryProvider.get(), singletonCImpl.usageStatsPermissionManagerProvider.get());
    }

    @Override
    public void injectCustomizeFragment(CustomizeFragment arg0) {
    }

    @Override
    public void injectEmojiBatteryFragment(EmojiBatteryFragment arg0) {
    }

    @Override
    public void injectStatsChargeFragment(StatsChargeFragment arg0) {
      injectStatsChargeFragment2(arg0);
    }

    @Override
    public void injectDischargeFragment(DischargeFragment arg0) {
      injectDischargeFragment2(arg0);
    }

    @Override
    public void injectDischargeFragment(
        com.tqhit.battery.one.fragment.main.DischargeFragment arg0) {
      injectDischargeFragment3(arg0);
    }

    @Override
    public void injectChargeFragment(ChargeFragment arg0) {
      injectChargeFragment2(arg0);
    }

    @Override
    public void injectHealthFragment(HealthFragment arg0) {
      injectHealthFragment2(arg0);
    }

    @Override
    public void injectSettingsFragment(SettingsFragment arg0) {
      injectSettingsFragment2(arg0);
    }

    @Override
    public void injectAnimationGridFragment(AnimationGridFragment arg0) {
      injectAnimationGridFragment2(arg0);
    }

    @Override
    public DefaultViewModelFactories.InternalFactoryFactory getHiltInternalFactoryFactory() {
      return activityCImpl.getHiltInternalFactoryFactory();
    }

    @Override
    public ViewWithFragmentComponentBuilder viewWithFragmentComponentBuilder() {
      return new ViewWithFragmentCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl, fragmentCImpl);
    }

    @CanIgnoreReturnValue
    private StatsChargeFragment injectStatsChargeFragment2(StatsChargeFragment instance) {
      StatsChargeFragment_MembersInjector.injectVibrationService(instance, singletonCImpl.vibrationServiceProvider.get());
      return instance;
    }

    @CanIgnoreReturnValue
    private DischargeFragment injectDischargeFragment2(DischargeFragment instance2) {
      DischargeFragment_MembersInjector.injectInfoButtonManager(instance2, infoButtonManager());
      DischargeFragment_MembersInjector.injectTimeConverter(instance2, singletonCImpl.timeConverterProvider.get());
      DischargeFragment_MembersInjector.injectAppLifecycleManager(instance2, singletonCImpl.appLifecycleManagerProvider.get());
      return instance2;
    }

    @CanIgnoreReturnValue
    private com.tqhit.battery.one.fragment.main.DischargeFragment injectDischargeFragment3(
        com.tqhit.battery.one.fragment.main.DischargeFragment instance3) {
      com.tqhit.battery.one.fragment.main.DischargeFragment_MembersInjector.injectApplovinInterstitialAdManager(instance3, singletonCImpl.applovinInterstitialAdManagerProvider.get());
      com.tqhit.battery.one.fragment.main.DischargeFragment_MembersInjector.injectVibrationService(instance3, singletonCImpl.vibrationServiceProvider.get());
      return instance3;
    }

    @CanIgnoreReturnValue
    private ChargeFragment injectChargeFragment2(ChargeFragment instance4) {
      ChargeFragment_MembersInjector.injectVibrationService(instance4, singletonCImpl.vibrationServiceProvider.get());
      ChargeFragment_MembersInjector.injectApplovinInterstitialAdManager(instance4, singletonCImpl.applovinInterstitialAdManagerProvider.get());
      return instance4;
    }

    @CanIgnoreReturnValue
    private HealthFragment injectHealthFragment2(HealthFragment instance5) {
      HealthFragment_MembersInjector.injectApplovinInterstitialAdManager(instance5, singletonCImpl.applovinInterstitialAdManagerProvider.get());
      HealthFragment_MembersInjector.injectChargingSessionManager(instance5, singletonCImpl.chargingSessionManagerProvider.get());
      HealthFragment_MembersInjector.injectHistoryBatteryRepository(instance5, singletonCImpl.historyBatteryRepositoryProvider.get());
      HealthFragment_MembersInjector.injectCoreBatteryStatsProvider(instance5, singletonCImpl.defaultCoreBatteryStatsProvider.get());
      return instance5;
    }

    @CanIgnoreReturnValue
    private SettingsFragment injectSettingsFragment2(SettingsFragment instance6) {
      SettingsFragment_MembersInjector.injectApplovinInterstitialAdManager(instance6, singletonCImpl.applovinInterstitialAdManagerProvider.get());
      SettingsFragment_MembersInjector.injectPreferencesHelper(instance6, singletonCImpl.providePreferencesHelperProvider.get());
      return instance6;
    }

    @CanIgnoreReturnValue
    private AnimationGridFragment injectAnimationGridFragment2(AnimationGridFragment instance7) {
      AnimationGridFragment_MembersInjector.injectApplovinInterstitialAdManager(instance7, singletonCImpl.applovinInterstitialAdManagerProvider.get());
      AnimationGridFragment_MembersInjector.injectRemoteConfigHelper(instance7, singletonCImpl.provideFirebaseRemoteConfigHelperProvider.get());
      AnimationGridFragment_MembersInjector.injectAppRepository(instance7, singletonCImpl.appRepositoryProvider.get());
      return instance7;
    }
  }

  private static final class ViewCImpl extends BatteryApplication_HiltComponents.ViewC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final ViewCImpl viewCImpl = this;

    ViewCImpl(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
        ActivityCImpl activityCImpl, View viewParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;


    }
  }

  private static final class ActivityCImpl extends BatteryApplication_HiltComponents.ActivityC {
    private final Activity activity;

    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl = this;

    ActivityCImpl(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
        Activity activityParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activity = activityParam;

    }

    @Override
    public void injectMainActivity(MainActivity arg0) {
      injectMainActivity2(arg0);
    }

    @Override
    public void injectMainActivity(com.tqhit.battery.one.activity.main.MainActivity arg0) {
      injectMainActivity3(arg0);
    }

    @Override
    public void injectAnimationActivity(AnimationActivity arg0) {
      injectAnimationActivity2(arg0);
    }

    @Override
    public void injectChargingOverlayActivity(ChargingOverlayActivity arg0) {
      injectChargingOverlayActivity2(arg0);
    }

    @Override
    public void injectEnterPasswordActivity(EnterPasswordActivity arg0) {
      injectEnterPasswordActivity2(arg0);
    }

    @Override
    public void injectSplashActivity(SplashActivity arg0) {
    }

    @Override
    public void injectStartingActivity(StartingActivity arg0) {
      injectStartingActivity2(arg0);
    }

    @Override
    public DefaultViewModelFactories.InternalFactoryFactory getHiltInternalFactoryFactory() {
      return DefaultViewModelFactories_InternalFactoryFactory_Factory.newInstance(getViewModelKeys(), new ViewModelCBuilder(singletonCImpl, activityRetainedCImpl));
    }

    @Override
    public Map<Class<?>, Boolean> getViewModelKeys() {
      return LazyClassKeyMap.<Boolean>of(ImmutableMap.<String, Boolean>builderWithExpectedSize(8).put(AnimationViewModel_HiltModules_KeyModule_Provide_LazyMapKey.lazyClassKeyName, AnimationViewModel_HiltModules.KeyModule.provide()).put(AppViewModel_HiltModules_KeyModule_Provide_LazyMapKey.lazyClassKeyName, AppViewModel_HiltModules.KeyModule.provide()).put(BatteryGalleryViewModel_HiltModules_KeyModule_Provide_LazyMapKey.lazyClassKeyName, BatteryGalleryViewModel_HiltModules.KeyModule.provide()).put(BatteryViewModel_HiltModules_KeyModule_Provide_LazyMapKey.lazyClassKeyName, BatteryViewModel_HiltModules.KeyModule.provide()).put(CustomizeViewModel_HiltModules_KeyModule_Provide_LazyMapKey.lazyClassKeyName, CustomizeViewModel_HiltModules.KeyModule.provide()).put(DischargeViewModel_HiltModules_KeyModule_Provide_LazyMapKey.lazyClassKeyName, DischargeViewModel_HiltModules.KeyModule.provide()).put(HealthViewModel_HiltModules_KeyModule_Provide_LazyMapKey.lazyClassKeyName, HealthViewModel_HiltModules.KeyModule.provide()).put(StatsChargeViewModel_HiltModules_KeyModule_Provide_LazyMapKey.lazyClassKeyName, StatsChargeViewModel_HiltModules.KeyModule.provide()).build());
    }

    @Override
    public ViewModelComponentBuilder getViewModelComponentBuilder() {
      return new ViewModelCBuilder(singletonCImpl, activityRetainedCImpl);
    }

    @Override
    public FragmentComponentBuilder fragmentComponentBuilder() {
      return new FragmentCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl);
    }

    @Override
    public ViewComponentBuilder viewComponentBuilder() {
      return new ViewCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl);
    }

    @CanIgnoreReturnValue
    private MainActivity injectMainActivity2(MainActivity instance) {
      MainActivity_MembersInjector.injectAdmobConsentHelper(instance, singletonCImpl.provideAdmobConsentHelperProvider.get());
      return instance;
    }

    @CanIgnoreReturnValue
    private com.tqhit.battery.one.activity.main.MainActivity injectMainActivity3(
        com.tqhit.battery.one.activity.main.MainActivity instance2) {
      com.tqhit.battery.one.activity.main.MainActivity_MembersInjector.injectStatsChargeRepository(instance2, singletonCImpl.defaultStatsChargeRepositoryProvider.get());
      com.tqhit.battery.one.activity.main.MainActivity_MembersInjector.injectAppRepository(instance2, singletonCImpl.appRepositoryProvider.get());
      com.tqhit.battery.one.activity.main.MainActivity_MembersInjector.injectApplovinBannerAdManager(instance2, singletonCImpl.applovinBannerAdManagerProvider.get());
      com.tqhit.battery.one.activity.main.MainActivity_MembersInjector.injectRemoteConfigHelper(instance2, singletonCImpl.provideFirebaseRemoteConfigHelperProvider.get());
      com.tqhit.battery.one.activity.main.MainActivity_MembersInjector.injectEnhancedDischargeTimerServiceHelper(instance2, singletonCImpl.enhancedDischargeTimerServiceHelperProvider.get());
      com.tqhit.battery.one.activity.main.MainActivity_MembersInjector.injectDischargeSessionRepository(instance2, singletonCImpl.dischargeSessionRepositoryProvider.get());
      com.tqhit.battery.one.activity.main.MainActivity_MembersInjector.injectUnifiedBatteryNotificationServiceHelper(instance2, singletonCImpl.unifiedBatteryNotificationServiceHelperProvider.get());
      com.tqhit.battery.one.activity.main.MainActivity_MembersInjector.injectUsageStatsPermissionManager(instance2, singletonCImpl.usageStatsPermissionManagerProvider.get());
      com.tqhit.battery.one.activity.main.MainActivity_MembersInjector.injectDynamicNavigationManager(instance2, singletonCImpl.dynamicNavigationManagerProvider.get());
      com.tqhit.battery.one.activity.main.MainActivity_MembersInjector.injectCoreBatteryStatsProvider(instance2, singletonCImpl.defaultCoreBatteryStatsProvider.get());
      return instance2;
    }

    @CanIgnoreReturnValue
    private AnimationActivity injectAnimationActivity2(AnimationActivity instance3) {
      AnimationActivity_MembersInjector.injectRemoteConfigHelper(instance3, singletonCImpl.provideFirebaseRemoteConfigHelperProvider.get());
      AnimationActivity_MembersInjector.injectApplovinRewardedAdManager(instance3, singletonCImpl.applovinRewardedAdManagerProvider.get());
      AnimationActivity_MembersInjector.injectAppRepository(instance3, singletonCImpl.appRepositoryProvider.get());
      return instance3;
    }

    @CanIgnoreReturnValue
    private ChargingOverlayActivity injectChargingOverlayActivity2(
        ChargingOverlayActivity instance4) {
      ChargingOverlayActivity_MembersInjector.injectAppRepository(instance4, singletonCImpl.appRepositoryProvider.get());
      return instance4;
    }

    @CanIgnoreReturnValue
    private EnterPasswordActivity injectEnterPasswordActivity2(EnterPasswordActivity instance5) {
      EnterPasswordActivity_MembersInjector.injectAppRepository(instance5, singletonCImpl.appRepositoryProvider.get());
      return instance5;
    }

    @CanIgnoreReturnValue
    private StartingActivity injectStartingActivity2(StartingActivity instance6) {
      StartingActivity_MembersInjector.injectApplovinInterstitialAdManager(instance6, singletonCImpl.applovinInterstitialAdManagerProvider.get());
      return instance6;
    }
  }

  private static final class ViewModelCImpl extends BatteryApplication_HiltComponents.ViewModelC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ViewModelCImpl viewModelCImpl = this;

    Provider<AnimationViewModel> animationViewModelProvider;

    Provider<AppViewModel> appViewModelProvider;

    Provider<BatteryGalleryViewModel> batteryGalleryViewModelProvider;

    Provider<BatteryViewModel> batteryViewModelProvider;

    Provider<CustomizeViewModel> customizeViewModelProvider;

    Provider<DischargeViewModel> dischargeViewModelProvider;

    Provider<HealthViewModel> healthViewModelProvider;

    Provider<StatsChargeViewModel> statsChargeViewModelProvider;

    ViewModelCImpl(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
        SavedStateHandle savedStateHandleParam, ViewModelLifecycle viewModelLifecycleParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;

      initialize(savedStateHandleParam, viewModelLifecycleParam);

    }

    @SuppressWarnings("unchecked")
    private void initialize(final SavedStateHandle savedStateHandleParam,
        final ViewModelLifecycle viewModelLifecycleParam) {
      this.animationViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 0);
      this.appViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 1);
      this.batteryGalleryViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 2);
      this.batteryViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 3);
      this.customizeViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 4);
      this.dischargeViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 5);
      this.healthViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 6);
      this.statsChargeViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 7);
    }

    @Override
    public Map<Class<?>, javax.inject.Provider<ViewModel>> getHiltViewModelMap() {
      return LazyClassKeyMap.<javax.inject.Provider<ViewModel>>of(ImmutableMap.<String, javax.inject.Provider<ViewModel>>builderWithExpectedSize(8).put(AnimationViewModel_HiltModules_BindsModule_Binds_LazyMapKey.lazyClassKeyName, ((Provider) (animationViewModelProvider))).put(AppViewModel_HiltModules_BindsModule_Binds_LazyMapKey.lazyClassKeyName, ((Provider) (appViewModelProvider))).put(BatteryGalleryViewModel_HiltModules_BindsModule_Binds_LazyMapKey.lazyClassKeyName, ((Provider) (batteryGalleryViewModelProvider))).put(BatteryViewModel_HiltModules_BindsModule_Binds_LazyMapKey.lazyClassKeyName, ((Provider) (batteryViewModelProvider))).put(CustomizeViewModel_HiltModules_BindsModule_Binds_LazyMapKey.lazyClassKeyName, ((Provider) (customizeViewModelProvider))).put(DischargeViewModel_HiltModules_BindsModule_Binds_LazyMapKey.lazyClassKeyName, ((Provider) (dischargeViewModelProvider))).put(HealthViewModel_HiltModules_BindsModule_Binds_LazyMapKey.lazyClassKeyName, ((Provider) (healthViewModelProvider))).put(StatsChargeViewModel_HiltModules_BindsModule_Binds_LazyMapKey.lazyClassKeyName, ((Provider) (statsChargeViewModelProvider))).build());
    }

    @Override
    public Map<Class<?>, Object> getHiltViewModelAssistedMap() {
      return ImmutableMap.<Class<?>, Object>of();
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final ActivityRetainedCImpl activityRetainedCImpl;

      private final ViewModelCImpl viewModelCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
          ViewModelCImpl viewModelCImpl, int id) {
        this.singletonCImpl = singletonCImpl;
        this.activityRetainedCImpl = activityRetainedCImpl;
        this.viewModelCImpl = viewModelCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // com.tqhit.battery.one.viewmodel.animation.AnimationViewModel
          return (T) new AnimationViewModel(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.animationRepositoryProvider.get());

          case 1: // com.tqhit.battery.one.viewmodel.AppViewModel
          return (T) new AppViewModel(singletonCImpl.appRepositoryProvider.get());

          case 2: // com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModel
          return (T) new BatteryGalleryViewModel(singletonCImpl.getBatteryStylesUseCaseProvider.get(), singletonCImpl.defaultCoreBatteryStatsProvider.get());

          case 3: // com.tqhit.battery.one.viewmodel.battery.BatteryViewModel
          return (T) new BatteryViewModel(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.batteryRepositoryProvider.get());

          case 4: // com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModel
          return (T) new CustomizeViewModel(singletonCImpl.loadCustomizationUseCaseProvider.get(), singletonCImpl.saveCustomizationUseCaseProvider.get(), singletonCImpl.resetCustomizationUseCaseProvider.get(), singletonCImpl.defaultCoreBatteryStatsProvider.get());

          case 5: // com.tqhit.battery.one.features.stats.discharge.presentation.DischargeViewModel
          return (T) new DischargeViewModel(singletonCImpl.batteryRepositoryProvider2.get(), singletonCImpl.dischargeCalculatorProvider.get(), singletonCImpl.dischargeSessionRepositoryProvider.get(), singletonCImpl.timeConverterProvider.get(), singletonCImpl.enhancedDischargeTimerServiceHelperProvider.get());

          case 6: // com.tqhit.battery.one.features.stats.health.presentation.HealthViewModel
          return (T) new HealthViewModel(singletonCImpl.defaultHealthRepositoryProvider.get());

          case 7: // com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel
          return (T) new StatsChargeViewModel(singletonCImpl.defaultStatsChargeRepositoryProvider.get(), new CalculateSimpleChargeEstimateUseCase(), singletonCImpl.appRepositoryProvider.get());

          default: throw new AssertionError(id);
        }
      }
    }
  }

  private static final class ActivityRetainedCImpl extends BatteryApplication_HiltComponents.ActivityRetainedC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl = this;

    Provider<ActivityRetainedLifecycle> provideActivityRetainedLifecycleProvider;

    ActivityRetainedCImpl(SingletonCImpl singletonCImpl,
        SavedStateHandleHolder savedStateHandleHolderParam) {
      this.singletonCImpl = singletonCImpl;

      initialize(savedStateHandleHolderParam);

    }

    @SuppressWarnings("unchecked")
    private void initialize(final SavedStateHandleHolder savedStateHandleHolderParam) {
      this.provideActivityRetainedLifecycleProvider = DoubleCheck.provider(new SwitchingProvider<ActivityRetainedLifecycle>(singletonCImpl, activityRetainedCImpl, 0));
    }

    @Override
    public ActivityComponentBuilder activityComponentBuilder() {
      return new ActivityCBuilder(singletonCImpl, activityRetainedCImpl);
    }

    @Override
    public ActivityRetainedLifecycle getActivityRetainedLifecycle() {
      return provideActivityRetainedLifecycleProvider.get();
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final ActivityRetainedCImpl activityRetainedCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
          int id) {
        this.singletonCImpl = singletonCImpl;
        this.activityRetainedCImpl = activityRetainedCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // dagger.hilt.android.ActivityRetainedLifecycle
          return (T) ActivityRetainedComponentManager_LifecycleModule_ProvideActivityRetainedLifecycleFactory.provideActivityRetainedLifecycle();

          default: throw new AssertionError(id);
        }
      }
    }
  }

  private static final class ServiceCImpl extends BatteryApplication_HiltComponents.ServiceC {
    private final SingletonCImpl singletonCImpl;

    private final ServiceCImpl serviceCImpl = this;

    ServiceCImpl(SingletonCImpl singletonCImpl, Service serviceParam) {
      this.singletonCImpl = singletonCImpl;


    }

    @Override
    public void injectCoreBatteryStatsService(CoreBatteryStatsService arg0) {
      injectCoreBatteryStatsService2(arg0);
    }

    @Override
    public void injectEnhancedDischargeTimerService(EnhancedDischargeTimerService arg0) {
      injectEnhancedDischargeTimerService2(arg0);
    }

    @Override
    public void injectUnifiedBatteryNotificationService(UnifiedBatteryNotificationService arg0) {
      injectUnifiedBatteryNotificationService2(arg0);
    }

    @Override
    public void injectBatteryMonitorService(BatteryMonitorService arg0) {
      injectBatteryMonitorService2(arg0);
    }

    @Override
    public void injectChargingOverlayService(ChargingOverlayService arg0) {
      injectChargingOverlayService2(arg0);
    }

    @CanIgnoreReturnValue
    private CoreBatteryStatsService injectCoreBatteryStatsService2(
        CoreBatteryStatsService instance) {
      CoreBatteryStatsService_MembersInjector.injectCoreBatteryStatsProvider(instance, singletonCImpl.defaultCoreBatteryStatsProvider.get());
      CoreBatteryStatsService_MembersInjector.injectAppRepository(instance, singletonCImpl.appRepositoryProvider.get());
      return instance;
    }

    @CanIgnoreReturnValue
    private EnhancedDischargeTimerService injectEnhancedDischargeTimerService2(
        EnhancedDischargeTimerService instance2) {
      EnhancedDischargeTimerService_MembersInjector.injectDischargeSessionRepository(instance2, singletonCImpl.dischargeSessionRepositoryProvider.get());
      EnhancedDischargeTimerService_MembersInjector.injectScreenTimeValidationService(instance2, singletonCImpl.screenTimeValidationServiceProvider.get());
      EnhancedDischargeTimerService_MembersInjector.injectAppLifecycleManager(instance2, singletonCImpl.appLifecycleManagerProvider.get());
      return instance2;
    }

    @CanIgnoreReturnValue
    private UnifiedBatteryNotificationService injectUnifiedBatteryNotificationService2(
        UnifiedBatteryNotificationService instance3) {
      UnifiedBatteryNotificationService_MembersInjector.injectCoreBatteryStatsProvider(instance3, singletonCImpl.defaultCoreBatteryStatsProvider.get());
      UnifiedBatteryNotificationService_MembersInjector.injectAppRepository(instance3, singletonCImpl.appRepositoryProvider.get());
      return instance3;
    }

    @CanIgnoreReturnValue
    private BatteryMonitorService injectBatteryMonitorService2(BatteryMonitorService instance4) {
      BatteryMonitorService_MembersInjector.injectBatteryRepository(instance4, singletonCImpl.batteryRepositoryProvider.get());
      BatteryMonitorService_MembersInjector.injectAppRepository(instance4, singletonCImpl.appRepositoryProvider.get());
      return instance4;
    }

    @CanIgnoreReturnValue
    private ChargingOverlayService injectChargingOverlayService2(ChargingOverlayService instance5) {
      ChargingOverlayService_MembersInjector.injectAppRepository(instance5, singletonCImpl.appRepositoryProvider.get());
      ChargingOverlayService_MembersInjector.injectAnimationRepository(instance5, singletonCImpl.animationRepositoryProvider.get());
      return instance5;
    }
  }

  private static final class SingletonCImpl extends BatteryApplication_HiltComponents.SingletonC {
    private final ApplicationContextModule applicationContextModule;

    private final SingletonCImpl singletonCImpl = this;

    Provider<AdmobConsentHelper> provideAdmobConsentHelperProvider;

    Provider<FirebaseAnalytics> provideFirebaseAnalyticsProvider;

    Provider<FirebaseAnalyticsHelper> provideFirebaseAnalyticsHelperProvider;

    Provider<AdjustAnalyticsHelper> provideAdjustAnalyticsHelperProvider;

    Provider<AnalyticsTracker> provideTrackingManagerProvider;

    Provider<BannerHelper> provideBannerHelperProvider;

    Provider<InterstitialHelper> provideInterstitialHelperProvider;

    Provider<RewardHelper> provideRewardHelperProvider;

    Provider<NativeHelper> provideNativeHelperProvider;

    Provider<FirebaseRemoteConfig> provideFirebaseRemoteConfigProvider;

    Provider<FirebaseRemoteConfigHelper> provideFirebaseRemoteConfigHelperProvider;

    Provider<AppOpenHelper> provideAppOpenHelperProvider;

    Provider<AdmobHelper> provideAdmobHelperProvider;

    Provider<SharedPreferences> provideSharedPreferencesProvider;

    Provider<Gson> provideGsonProvider;

    Provider<PreferencesHelper> providePreferencesHelperProvider;

    Provider<AppRepository> appRepositoryProvider;

    Provider<ApplovinNativeAdManager> applovinNativeAdManagerProvider;

    Provider<ApplovinRewardedAdManager> applovinRewardedAdManagerProvider;

    Provider<ApplovinInterstitialAdManager> applovinInterstitialAdManagerProvider;

    Provider<ApplovinBannerAdManager> applovinBannerAdManagerProvider;

    Provider<ApplovinAppOpenAdManager> applovinAppOpenAdManagerProvider;

    Provider<CoreBatteryServiceHelper> coreBatteryServiceHelperProvider;

    Provider<DefaultCoreBatteryStatsProvider> defaultCoreBatteryStatsProvider;

    Provider<PrefsStatsChargeCache> prefsStatsChargeCacheProvider;

    Provider<DefaultStatsChargeRepository> defaultStatsChargeRepositoryProvider;

    Provider<EnhancedDischargeTimerServiceHelper> enhancedDischargeTimerServiceHelperProvider;

    Provider<PrefsCurrentSessionCache> prefsCurrentSessionCacheProvider;

    Provider<TimeConverter> timeConverterProvider;

    Provider<DischargeRateCalculator> dischargeRateCalculatorProvider;

    Provider<SessionMetricsCalculator> sessionMetricsCalculatorProvider;

    Provider<ScreenTimeCalculator> screenTimeCalculatorProvider;

    Provider<DischargeCalculator> dischargeCalculatorProvider;

    Provider<SessionManager> sessionManagerProvider;

    Provider<PrefsDischargeRatesCache> prefsDischargeRatesCacheProvider;

    Provider<GapEstimationCalculator> gapEstimationCalculatorProvider;

    Provider<FullSessionReEstimator> fullSessionReEstimatorProvider;

    Provider<ScreenStateReceiver> provideScreenStateReceiverProvider;

    Provider<DischargeSessionRepository> dischargeSessionRepositoryProvider;

    Provider<UnifiedBatteryNotificationServiceHelper> unifiedBatteryNotificationServiceHelperProvider;

    Provider<AppUsageStatsRepository> appUsageStatsRepositoryProvider;

    Provider<UsageStatsPermissionManager> usageStatsPermissionManagerProvider;

    Provider<DynamicNavigationManager> dynamicNavigationManagerProvider;

    Provider<VibrationService> vibrationServiceProvider;

    Provider<AppPowerConsumptionDialogFactory> appPowerConsumptionDialogFactoryProvider;

    Provider<AppLifecycleManager> appLifecycleManagerProvider;

    Provider<ChargingSessionManager> chargingSessionManagerProvider;

    Provider<HistoryBatteryRepository> historyBatteryRepositoryProvider;

    Provider<AnimationRepository> animationRepositoryProvider;

    Provider<BatteryStyleRepositoryImpl> batteryStyleRepositoryImplProvider;

    Provider<GetBatteryStylesUseCase> getBatteryStylesUseCaseProvider;

    Provider<DischargeSessionManager> dischargeSessionManagerProvider;

    Provider<BatteryHistoryManager> batteryHistoryManagerProvider;

    Provider<TemperatureHistoryManager> temperatureHistoryManagerProvider;

    Provider<BatteryRepository> batteryRepositoryProvider;

    Provider<CustomizationDataStore> customizationDataStoreProvider;

    Provider<CustomizationRepositoryImpl> customizationRepositoryImplProvider;

    Provider<LoadCustomizationUseCase> loadCustomizationUseCaseProvider;

    Provider<SaveCustomizationUseCase> saveCustomizationUseCaseProvider;

    Provider<ResetCustomizationUseCase> resetCustomizationUseCaseProvider;

    Provider<com.tqhit.battery.one.features.stats.discharge.repository.BatteryRepository> batteryRepositoryProvider2;

    Provider<DefaultHealthCache> defaultHealthCacheProvider;

    Provider<DefaultHealthRepository> defaultHealthRepositoryProvider;

    Provider<ScreenTimeValidationService> screenTimeValidationServiceProvider;

    SingletonCImpl(ApplicationContextModule applicationContextModuleParam) {
      this.applicationContextModule = applicationContextModuleParam;
      initialize(applicationContextModuleParam);
      initialize2(applicationContextModuleParam);
      initialize3(applicationContextModuleParam);

    }

    @SuppressWarnings("unchecked")
    private void initialize(final ApplicationContextModule applicationContextModuleParam) {
      this.provideAdmobConsentHelperProvider = DoubleCheck.provider(new SwitchingProvider<AdmobConsentHelper>(singletonCImpl, 2));
      this.provideFirebaseAnalyticsProvider = DoubleCheck.provider(new SwitchingProvider<FirebaseAnalytics>(singletonCImpl, 5));
      this.provideFirebaseAnalyticsHelperProvider = DoubleCheck.provider(new SwitchingProvider<FirebaseAnalyticsHelper>(singletonCImpl, 4));
      this.provideAdjustAnalyticsHelperProvider = DoubleCheck.provider(new SwitchingProvider<AdjustAnalyticsHelper>(singletonCImpl, 6));
      this.provideTrackingManagerProvider = DoubleCheck.provider(new SwitchingProvider<AnalyticsTracker>(singletonCImpl, 3));
      this.provideBannerHelperProvider = DoubleCheck.provider(new SwitchingProvider<BannerHelper>(singletonCImpl, 1));
      this.provideInterstitialHelperProvider = DoubleCheck.provider(new SwitchingProvider<InterstitialHelper>(singletonCImpl, 7));
      this.provideRewardHelperProvider = DoubleCheck.provider(new SwitchingProvider<RewardHelper>(singletonCImpl, 8));
      this.provideNativeHelperProvider = DoubleCheck.provider(new SwitchingProvider<NativeHelper>(singletonCImpl, 9));
      this.provideFirebaseRemoteConfigProvider = DoubleCheck.provider(new SwitchingProvider<FirebaseRemoteConfig>(singletonCImpl, 12));
      this.provideFirebaseRemoteConfigHelperProvider = DoubleCheck.provider(new SwitchingProvider<FirebaseRemoteConfigHelper>(singletonCImpl, 11));
      this.provideAppOpenHelperProvider = DoubleCheck.provider(new SwitchingProvider<AppOpenHelper>(singletonCImpl, 10));
      this.provideAdmobHelperProvider = DoubleCheck.provider(new SwitchingProvider<AdmobHelper>(singletonCImpl, 0));
      this.provideSharedPreferencesProvider = DoubleCheck.provider(new SwitchingProvider<SharedPreferences>(singletonCImpl, 14));
      this.provideGsonProvider = DoubleCheck.provider(new SwitchingProvider<Gson>(singletonCImpl, 15));
      this.providePreferencesHelperProvider = DoubleCheck.provider(new SwitchingProvider<PreferencesHelper>(singletonCImpl, 13));
      this.appRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<AppRepository>(singletonCImpl, 16));
      this.applovinNativeAdManagerProvider = DoubleCheck.provider(new SwitchingProvider<ApplovinNativeAdManager>(singletonCImpl, 17));
      this.applovinRewardedAdManagerProvider = DoubleCheck.provider(new SwitchingProvider<ApplovinRewardedAdManager>(singletonCImpl, 18));
      this.applovinInterstitialAdManagerProvider = DoubleCheck.provider(new SwitchingProvider<ApplovinInterstitialAdManager>(singletonCImpl, 19));
      this.applovinBannerAdManagerProvider = DoubleCheck.provider(new SwitchingProvider<ApplovinBannerAdManager>(singletonCImpl, 20));
      this.applovinAppOpenAdManagerProvider = DoubleCheck.provider(new SwitchingProvider<ApplovinAppOpenAdManager>(singletonCImpl, 21));
      this.coreBatteryServiceHelperProvider = DoubleCheck.provider(new SwitchingProvider<CoreBatteryServiceHelper>(singletonCImpl, 22));
      this.defaultCoreBatteryStatsProvider = DoubleCheck.provider(new SwitchingProvider<DefaultCoreBatteryStatsProvider>(singletonCImpl, 24));
      this.prefsStatsChargeCacheProvider = DoubleCheck.provider(new SwitchingProvider<PrefsStatsChargeCache>(singletonCImpl, 25));
    }

    @SuppressWarnings("unchecked")
    private void initialize2(final ApplicationContextModule applicationContextModuleParam) {
      this.defaultStatsChargeRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<DefaultStatsChargeRepository>(singletonCImpl, 23));
      this.enhancedDischargeTimerServiceHelperProvider = DoubleCheck.provider(new SwitchingProvider<EnhancedDischargeTimerServiceHelper>(singletonCImpl, 26));
      this.prefsCurrentSessionCacheProvider = DoubleCheck.provider(new SwitchingProvider<PrefsCurrentSessionCache>(singletonCImpl, 28));
      this.timeConverterProvider = DoubleCheck.provider(new SwitchingProvider<TimeConverter>(singletonCImpl, 31));
      this.dischargeRateCalculatorProvider = DoubleCheck.provider(new SwitchingProvider<DischargeRateCalculator>(singletonCImpl, 32));
      this.sessionMetricsCalculatorProvider = DoubleCheck.provider(new SwitchingProvider<SessionMetricsCalculator>(singletonCImpl, 30));
      this.screenTimeCalculatorProvider = DoubleCheck.provider(new SwitchingProvider<ScreenTimeCalculator>(singletonCImpl, 33));
      this.dischargeCalculatorProvider = DoubleCheck.provider(new SwitchingProvider<DischargeCalculator>(singletonCImpl, 34));
      this.sessionManagerProvider = DoubleCheck.provider(new SwitchingProvider<SessionManager>(singletonCImpl, 29));
      this.prefsDischargeRatesCacheProvider = DoubleCheck.provider(new SwitchingProvider<PrefsDischargeRatesCache>(singletonCImpl, 36));
      this.gapEstimationCalculatorProvider = DoubleCheck.provider(new SwitchingProvider<GapEstimationCalculator>(singletonCImpl, 35));
      this.fullSessionReEstimatorProvider = DoubleCheck.provider(new SwitchingProvider<FullSessionReEstimator>(singletonCImpl, 37));
      this.provideScreenStateReceiverProvider = DoubleCheck.provider(new SwitchingProvider<ScreenStateReceiver>(singletonCImpl, 38));
      this.dischargeSessionRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<DischargeSessionRepository>(singletonCImpl, 27));
      this.unifiedBatteryNotificationServiceHelperProvider = DoubleCheck.provider(new SwitchingProvider<UnifiedBatteryNotificationServiceHelper>(singletonCImpl, 39));
      this.appUsageStatsRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<AppUsageStatsRepository>(singletonCImpl, 41));
      this.usageStatsPermissionManagerProvider = DoubleCheck.provider(new SwitchingProvider<UsageStatsPermissionManager>(singletonCImpl, 40));
      this.dynamicNavigationManagerProvider = DoubleCheck.provider(new SwitchingProvider<DynamicNavigationManager>(singletonCImpl, 42));
      this.vibrationServiceProvider = DoubleCheck.provider(new SwitchingProvider<VibrationService>(singletonCImpl, 43));
      this.appPowerConsumptionDialogFactoryProvider = DoubleCheck.provider(new SwitchingProvider<AppPowerConsumptionDialogFactory>(singletonCImpl, 44));
      this.appLifecycleManagerProvider = DoubleCheck.provider(new SwitchingProvider<AppLifecycleManager>(singletonCImpl, 45));
      this.chargingSessionManagerProvider = DoubleCheck.provider(new SwitchingProvider<ChargingSessionManager>(singletonCImpl, 46));
      this.historyBatteryRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<HistoryBatteryRepository>(singletonCImpl, 47));
      this.animationRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<AnimationRepository>(singletonCImpl, 48));
      this.batteryStyleRepositoryImplProvider = DoubleCheck.provider(new SwitchingProvider<BatteryStyleRepositoryImpl>(singletonCImpl, 50));
    }

    @SuppressWarnings("unchecked")
    private void initialize3(final ApplicationContextModule applicationContextModuleParam) {
      this.getBatteryStylesUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<GetBatteryStylesUseCase>(singletonCImpl, 49));
      this.dischargeSessionManagerProvider = DoubleCheck.provider(new SwitchingProvider<DischargeSessionManager>(singletonCImpl, 52));
      this.batteryHistoryManagerProvider = DoubleCheck.provider(new SwitchingProvider<BatteryHistoryManager>(singletonCImpl, 53));
      this.temperatureHistoryManagerProvider = DoubleCheck.provider(new SwitchingProvider<TemperatureHistoryManager>(singletonCImpl, 54));
      this.batteryRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<BatteryRepository>(singletonCImpl, 51));
      this.customizationDataStoreProvider = DoubleCheck.provider(new SwitchingProvider<CustomizationDataStore>(singletonCImpl, 57));
      this.customizationRepositoryImplProvider = DoubleCheck.provider(new SwitchingProvider<CustomizationRepositoryImpl>(singletonCImpl, 56));
      this.loadCustomizationUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<LoadCustomizationUseCase>(singletonCImpl, 55));
      this.saveCustomizationUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<SaveCustomizationUseCase>(singletonCImpl, 58));
      this.resetCustomizationUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<ResetCustomizationUseCase>(singletonCImpl, 59));
      this.batteryRepositoryProvider2 = DoubleCheck.provider(new SwitchingProvider<com.tqhit.battery.one.features.stats.discharge.repository.BatteryRepository>(singletonCImpl, 60));
      this.defaultHealthCacheProvider = DoubleCheck.provider(new SwitchingProvider<DefaultHealthCache>(singletonCImpl, 62));
      this.defaultHealthRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<DefaultHealthRepository>(singletonCImpl, 61));
      this.screenTimeValidationServiceProvider = DoubleCheck.provider(new SwitchingProvider<ScreenTimeValidationService>(singletonCImpl, 63));
    }

    @Override
    public void injectBatteryApplication(BatteryApplication arg0) {
      injectBatteryApplication2(arg0);
    }

    @Override
    public Set<Boolean> getDisableFragmentGetContextFix() {
      return ImmutableSet.<Boolean>of();
    }

    @Override
    public ActivityRetainedComponentBuilder retainedComponentBuilder() {
      return new ActivityRetainedCBuilder(singletonCImpl);
    }

    @Override
    public ServiceComponentBuilder serviceComponentBuilder() {
      return new ServiceCBuilder(singletonCImpl);
    }

    @CanIgnoreReturnValue
    private BatteryApplication injectBatteryApplication2(BatteryApplication instance) {
      AdLibHiltApplication_MembersInjector.injectAdmobHelper(instance, provideAdmobHelperProvider.get());
      AdLibHiltApplication_MembersInjector.injectAppOpenHelper(instance, provideAppOpenHelperProvider.get());
      AdLibHiltApplication_MembersInjector.injectAnalyticsHelper(instance, provideAdjustAnalyticsHelperProvider.get());
      AdLibHiltApplication_MembersInjector.injectAnalyticsTracker(instance, provideTrackingManagerProvider.get());
      AdLibHiltApplication_MembersInjector.injectRemoteConfigHelper(instance, provideFirebaseRemoteConfigHelperProvider.get());
      BatteryApplication_MembersInjector.injectPreferencesHelper(instance, providePreferencesHelperProvider.get());
      BatteryApplication_MembersInjector.injectAppRepository(instance, appRepositoryProvider.get());
      BatteryApplication_MembersInjector.injectApplovinNativeAdManager(instance, applovinNativeAdManagerProvider.get());
      BatteryApplication_MembersInjector.injectApplovinRewardedAdManager(instance, applovinRewardedAdManagerProvider.get());
      BatteryApplication_MembersInjector.injectApplovinInterstitialAdManager(instance, applovinInterstitialAdManagerProvider.get());
      BatteryApplication_MembersInjector.injectApplovinBannerAdManager(instance, applovinBannerAdManagerProvider.get());
      BatteryApplication_MembersInjector.injectApplovinAppOpenAdManager(instance, applovinAppOpenAdManagerProvider.get());
      BatteryApplication_MembersInjector.injectCoreBatteryServiceHelper(instance, coreBatteryServiceHelperProvider.get());
      return instance;
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl, int id) {
        this.singletonCImpl = singletonCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // com.tqhit.adlib.sdk.ads.AdmobHelper
          return (T) AdmobModule_ProvideAdmobHelperFactory.provideAdmobHelper(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.provideBannerHelperProvider.get(), singletonCImpl.provideInterstitialHelperProvider.get(), singletonCImpl.provideRewardHelperProvider.get(), singletonCImpl.provideNativeHelperProvider.get(), singletonCImpl.provideAppOpenHelperProvider.get());

          case 1: // com.tqhit.adlib.sdk.ads.BannerHelper
          return (T) AdmobModule_ProvideBannerHelperFactory.provideBannerHelper(singletonCImpl.provideAdmobConsentHelperProvider.get(), singletonCImpl.provideTrackingManagerProvider.get());

          case 2: // com.tqhit.adlib.sdk.ads.AdmobConsentHelper
          return (T) AdmobModule_ProvideAdmobConsentHelperFactory.provideAdmobConsentHelper(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 3: // com.tqhit.adlib.sdk.analytics.AnalyticsTracker
          return (T) AnalyticsModule_ProvideTrackingManagerFactory.provideTrackingManager(singletonCImpl.provideFirebaseAnalyticsHelperProvider.get(), singletonCImpl.provideAdjustAnalyticsHelperProvider.get());

          case 4: // com.tqhit.adlib.sdk.firebase.FirebaseAnalyticsHelper
          return (T) FirebaseModule_ProvideFirebaseAnalyticsHelperFactory.provideFirebaseAnalyticsHelper(singletonCImpl.provideFirebaseAnalyticsProvider.get());

          case 5: // com.google.firebase.analytics.FirebaseAnalytics
          return (T) FirebaseModule_ProvideFirebaseAnalyticsFactory.provideFirebaseAnalytics(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 6: // com.tqhit.adlib.sdk.adjust.AdjustAnalyticsHelper
          return (T) AnalyticsModule_ProvideAdjustAnalyticsHelperFactory.provideAdjustAnalyticsHelper(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 7: // com.tqhit.adlib.sdk.ads.InterstitialHelper
          return (T) AdmobModule_ProvideInterstitialHelperFactory.provideInterstitialHelper(singletonCImpl.provideAdmobConsentHelperProvider.get(), singletonCImpl.provideTrackingManagerProvider.get());

          case 8: // com.tqhit.adlib.sdk.ads.RewardHelper
          return (T) AdmobModule_ProvideRewardHelperFactory.provideRewardHelper(singletonCImpl.provideAdmobConsentHelperProvider.get(), singletonCImpl.provideTrackingManagerProvider.get());

          case 9: // com.tqhit.adlib.sdk.ads.NativeHelper
          return (T) AdmobModule_ProvideNativeHelperFactory.provideNativeHelper(singletonCImpl.provideAdmobConsentHelperProvider.get(), singletonCImpl.provideTrackingManagerProvider.get());

          case 10: // com.tqhit.adlib.sdk.ads.AppOpenHelper
          return (T) AdmobModule_ProvideAppOpenHelperFactory.provideAppOpenHelper(singletonCImpl.provideAdmobConsentHelperProvider.get(), singletonCImpl.provideTrackingManagerProvider.get(), singletonCImpl.provideFirebaseRemoteConfigHelperProvider.get());

          case 11: // com.tqhit.adlib.sdk.firebase.FirebaseRemoteConfigHelper
          return (T) FirebaseModule_ProvideFirebaseRemoteConfigHelperFactory.provideFirebaseRemoteConfigHelper(singletonCImpl.provideFirebaseRemoteConfigProvider.get());

          case 12: // com.google.firebase.remoteconfig.FirebaseRemoteConfig
          return (T) FirebaseModule_ProvideFirebaseRemoteConfigFactory.provideFirebaseRemoteConfig();

          case 13: // com.tqhit.adlib.sdk.data.local.PreferencesHelper
          return (T) SharedPreferencesModule_ProvidePreferencesHelperFactory.providePreferencesHelper(singletonCImpl.provideSharedPreferencesProvider.get(), singletonCImpl.provideGsonProvider.get());

          case 14: // android.content.SharedPreferences
          return (T) SharedPreferencesModule_ProvideSharedPreferencesFactory.provideSharedPreferences(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 15: // com.google.gson.Gson
          return (T) SharedPreferencesModule_ProvideGsonFactory.provideGson();

          case 16: // com.tqhit.battery.one.repository.AppRepository
          return (T) new AppRepository(singletonCImpl.providePreferencesHelperProvider.get(), ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 17: // com.tqhit.battery.one.ads.core.ApplovinNativeAdManager
          return (T) new ApplovinNativeAdManager(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 18: // com.tqhit.battery.one.ads.core.ApplovinRewardedAdManager
          return (T) new ApplovinRewardedAdManager(singletonCImpl.provideTrackingManagerProvider.get());

          case 19: // com.tqhit.battery.one.ads.core.ApplovinInterstitialAdManager
          return (T) new ApplovinInterstitialAdManager(singletonCImpl.provideFirebaseRemoteConfigHelperProvider.get(), singletonCImpl.applovinRewardedAdManagerProvider.get(), singletonCImpl.provideTrackingManagerProvider.get());

          case 20: // com.tqhit.battery.one.ads.core.ApplovinBannerAdManager
          return (T) new ApplovinBannerAdManager(singletonCImpl.provideTrackingManagerProvider.get());

          case 21: // com.tqhit.battery.one.ads.core.ApplovinAppOpenAdManager
          return (T) new ApplovinAppOpenAdManager(singletonCImpl.provideFirebaseRemoteConfigHelperProvider.get(), singletonCImpl.provideTrackingManagerProvider.get());

          case 22: // com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryServiceHelper
          return (T) new CoreBatteryServiceHelper(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 23: // com.tqhit.battery.one.features.stats.charge.repository.DefaultStatsChargeRepository
          return (T) new DefaultStatsChargeRepository(singletonCImpl.defaultCoreBatteryStatsProvider.get(), singletonCImpl.prefsStatsChargeCacheProvider.get(), singletonCImpl.appRepositoryProvider.get());

          case 24: // com.tqhit.battery.one.features.stats.corebattery.domain.DefaultCoreBatteryStatsProvider
          return (T) new DefaultCoreBatteryStatsProvider();

          case 25: // com.tqhit.battery.one.features.stats.charge.cache.PrefsStatsChargeCache
          return (T) new PrefsStatsChargeCache(singletonCImpl.providePreferencesHelperProvider.get(), singletonCImpl.provideGsonProvider.get());

          case 26: // com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerServiceHelper
          return (T) new EnhancedDischargeTimerServiceHelper(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 27: // com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository
          return (T) new DischargeSessionRepository(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.prefsCurrentSessionCacheProvider.get(), singletonCImpl.sessionManagerProvider.get(), singletonCImpl.gapEstimationCalculatorProvider.get(), singletonCImpl.fullSessionReEstimatorProvider.get(), singletonCImpl.sessionMetricsCalculatorProvider.get(), singletonCImpl.provideScreenStateReceiverProvider.get());

          case 28: // com.tqhit.battery.one.features.stats.discharge.cache.PrefsCurrentSessionCache
          return (T) new PrefsCurrentSessionCache(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.provideGsonProvider.get());

          case 29: // com.tqhit.battery.one.features.stats.discharge.domain.SessionManager
          return (T) new SessionManager(singletonCImpl.sessionMetricsCalculatorProvider.get(), singletonCImpl.screenTimeCalculatorProvider.get(), singletonCImpl.dischargeRateCalculatorProvider.get(), singletonCImpl.dischargeCalculatorProvider.get());

          case 30: // com.tqhit.battery.one.features.stats.discharge.domain.SessionMetricsCalculator
          return (T) new SessionMetricsCalculator(singletonCImpl.timeConverterProvider.get(), singletonCImpl.dischargeRateCalculatorProvider.get());

          case 31: // com.tqhit.battery.one.features.stats.discharge.domain.TimeConverter
          return (T) new TimeConverter();

          case 32: // com.tqhit.battery.one.features.stats.discharge.domain.DischargeRateCalculator
          return (T) new DischargeRateCalculator(singletonCImpl.timeConverterProvider.get());

          case 33: // com.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeCalculator
          return (T) new ScreenTimeCalculator(singletonCImpl.timeConverterProvider.get());

          case 34: // com.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculator
          return (T) new DischargeCalculator();

          case 35: // com.tqhit.battery.one.features.stats.discharge.domain.GapEstimationCalculator
          return (T) new GapEstimationCalculator(singletonCImpl.sessionMetricsCalculatorProvider.get(), singletonCImpl.screenTimeCalculatorProvider.get(), singletonCImpl.prefsDischargeRatesCacheProvider.get(), singletonCImpl.timeConverterProvider.get(), singletonCImpl.dischargeRateCalculatorProvider.get());

          case 36: // com.tqhit.battery.one.features.stats.discharge.cache.PrefsDischargeRatesCache
          return (T) new PrefsDischargeRatesCache(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 37: // com.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimator
          return (T) new FullSessionReEstimator(singletonCImpl.screenTimeCalculatorProvider.get(), singletonCImpl.prefsDischargeRatesCacheProvider.get(), singletonCImpl.timeConverterProvider.get(), singletonCImpl.dischargeRateCalculatorProvider.get());

          case 38: // com.tqhit.battery.one.features.stats.discharge.datasource.ScreenStateReceiver
          return (T) StatsDischargeProvidersModule_ProvideScreenStateReceiverFactory.provideScreenStateReceiver(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 39: // com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationServiceHelper
          return (T) new UnifiedBatteryNotificationServiceHelper(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 40: // com.tqhit.battery.one.features.stats.apppower.permission.UsageStatsPermissionManager
          return (T) new UsageStatsPermissionManager(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.appUsageStatsRepositoryProvider.get());

          case 41: // com.tqhit.battery.one.features.stats.apppower.repository.AppUsageStatsRepository
          return (T) new AppUsageStatsRepository(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 42: // com.tqhit.battery.one.features.navigation.DynamicNavigationManager
          return (T) new DynamicNavigationManager(singletonCImpl.defaultCoreBatteryStatsProvider.get());

          case 43: // com.tqhit.battery.one.service.VibrationService
          return (T) new VibrationService(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 44: // com.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionDialogFactory
          return (T) new AppPowerConsumptionDialogFactory(singletonCImpl.appUsageStatsRepositoryProvider.get(), singletonCImpl.usageStatsPermissionManagerProvider.get());

          case 45: // com.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManager
          return (T) new AppLifecycleManager();

          case 46: // com.tqhit.battery.one.manager.charge.ChargingSessionManager
          return (T) new ChargingSessionManager(singletonCImpl.providePreferencesHelperProvider.get());

          case 47: // com.tqhit.battery.one.features.stats.health.repository.HistoryBatteryRepository
          return (T) new HistoryBatteryRepository(singletonCImpl.defaultCoreBatteryStatsProvider.get(), singletonCImpl.providePreferencesHelperProvider.get());

          case 48: // com.tqhit.battery.one.repository.AnimationRepository
          return (T) new AnimationRepository(singletonCImpl.providePreferencesHelperProvider.get());

          case 49: // com.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCase
          return (T) new GetBatteryStylesUseCase(singletonCImpl.batteryStyleRepositoryImplProvider.get());

          case 50: // com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl
          return (T) new BatteryStyleRepositoryImpl(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.provideFirebaseRemoteConfigHelperProvider.get(), singletonCImpl.provideGsonProvider.get());

          case 51: // com.tqhit.battery.one.repository.BatteryRepository
          return (T) new BatteryRepository(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.providePreferencesHelperProvider.get(), singletonCImpl.chargingSessionManagerProvider.get(), singletonCImpl.dischargeSessionManagerProvider.get(), singletonCImpl.batteryHistoryManagerProvider.get(), singletonCImpl.temperatureHistoryManagerProvider.get());

          case 52: // com.tqhit.battery.one.manager.discharge.DischargeSessionManager
          return (T) new DischargeSessionManager(singletonCImpl.providePreferencesHelperProvider.get());

          case 53: // com.tqhit.battery.one.manager.graph.BatteryHistoryManager
          return (T) new BatteryHistoryManager(singletonCImpl.providePreferencesHelperProvider.get());

          case 54: // com.tqhit.battery.one.manager.graph.TemperatureHistoryManager
          return (T) new TemperatureHistoryManager(singletonCImpl.providePreferencesHelperProvider.get());

          case 55: // com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase
          return (T) new LoadCustomizationUseCase(singletonCImpl.customizationRepositoryImplProvider.get(), singletonCImpl.batteryStyleRepositoryImplProvider.get());

          case 56: // com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImpl
          return (T) new CustomizationRepositoryImpl(singletonCImpl.customizationDataStoreProvider.get(), singletonCImpl.provideGsonProvider.get());

          case 57: // com.tqhit.battery.one.features.emoji.data.datastore.CustomizationDataStore
          return (T) new CustomizationDataStore(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.provideGsonProvider.get());

          case 58: // com.tqhit.battery.one.features.emoji.domain.use_case.SaveCustomizationUseCase
          return (T) new SaveCustomizationUseCase(singletonCImpl.customizationRepositoryImplProvider.get(), singletonCImpl.batteryStyleRepositoryImplProvider.get());

          case 59: // com.tqhit.battery.one.features.emoji.domain.use_case.ResetCustomizationUseCase
          return (T) new ResetCustomizationUseCase(singletonCImpl.customizationRepositoryImplProvider.get());

          case 60: // com.tqhit.battery.one.features.stats.discharge.repository.BatteryRepository
          return (T) new com.tqhit.battery.one.features.stats.discharge.repository.BatteryRepository(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.defaultCoreBatteryStatsProvider.get(), singletonCImpl.appRepositoryProvider.get(), singletonCImpl.prefsDischargeRatesCacheProvider.get(), singletonCImpl.dischargeSessionRepositoryProvider.get());

          case 61: // com.tqhit.battery.one.features.stats.health.repository.DefaultHealthRepository
          return (T) new DefaultHealthRepository(singletonCImpl.defaultCoreBatteryStatsProvider.get(), singletonCImpl.chargingSessionManagerProvider.get(), singletonCImpl.appRepositoryProvider.get(), singletonCImpl.defaultHealthCacheProvider.get(), singletonCImpl.historyBatteryRepositoryProvider.get());

          case 62: // com.tqhit.battery.one.features.stats.health.cache.DefaultHealthCache
          return (T) new DefaultHealthCache(singletonCImpl.providePreferencesHelperProvider.get());

          case 63: // com.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationService
          return (T) new ScreenTimeValidationService(singletonCImpl.appLifecycleManagerProvider.get());

          default: throw new AssertionError(id);
        }
      }
    }
  }
}
