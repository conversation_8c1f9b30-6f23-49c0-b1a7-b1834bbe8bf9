<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        android:orientation="vertical"
        android:visibility="visible"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true">
        <LinearLayout
            android:gravity="center"
            android:layout_gravity="center"
            android:orientation="vertical"
            android:background="@drawable/ic_capacity"
            android:layout_width="105dp"
            android:layout_height="108dp"/>
        <TextView
            android:textSize="22sp"
            android:textColor="?attr/black"
            android:gravity="center"
            android:id="@+id/textView4"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:text="@string/real_capacity"
            android:layout_marginStart="14dp"
            android:layout_marginEnd="14dp"/>
        <TextView
            android:textSize="14sp"
            android:textColor="?attr/black"
            android:gravity="center"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="7dp"
            android:layout_marginBottom="10dp"
            android:text="@string/real_capacity_text"
            android:layout_marginStart="9dp"
            android:layout_marginEnd="7dp"/>
    </LinearLayout>
    <LinearLayout
        android:id="@+id/charge_up"
        android:visibility="invisible"
        android:clipChildren="false"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:baselineAligned="false">
        <RelativeLayout
            android:id="@+id/percent_layout"
            android:background="@drawable/white_block"
            android:padding="8dp"
            android:layout_width="0dp"
            android:layout_height="235dp"
            android:layout_marginTop="3dp"
            android:layout_marginBottom="14dp"
            android:layout_weight="1"
            android:rotation="0"
            android:layout_marginStart="9dp"
            android:layout_marginEnd="14dp"
            android:elevation="1dp"/>
        <LinearLayout
            android:orientation="vertical"
            android:id="@+id/time_num"
            android:background="@drawable/white_block"
            android:padding="8dp"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginTop="3dp"
            android:layout_marginBottom="14dp"
            android:layout_marginEnd="9dp"
            android:elevation="1dp">
            <LinearLayout
                android:orientation="horizontal"
                android:visibility="invisible"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_weight="0"
                android:paddingStart="7dp"
                android:paddingEnd="7dp">
                <LinearLayout
                    android:padding="2dp"
                    android:layout_width="32dp"
                    android:layout_height="wrap_content"
                    android:contentDescription="@string/zero"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:gravity="center"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:text="@string/for_time_up_charge"/>
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>
    <RelativeLayout
        android:layout_gravity="center"
        android:id="@+id/button"
        android:background="@drawable/white_block_round"
        android:visibility="visible"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:layout_marginTop="20dp"
        android:layout_marginBottom="50dp"
        android:layout_alignParentBottom="true"
        android:layout_marginEnd="30dp"
        android:layout_alignParentEnd="true"
        android:stateListAnimator="@null"
        android:outlineProvider="background">
        <LinearLayout
            android:orientation="vertical"
            android:background="@drawable/white_block_round"
            android:visibility="visible"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_centerInParent="true"/>
        <Button
            android:id="@+id/next_page"
            android:background="@drawable/button_static"
            android:visibility="visible"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="8dp"
            android:layout_centerInParent="true"
            style="@style/Widget.AppCompat.Button.Borderless"/>
        <LinearLayout
            android:orientation="vertical"
            android:background="@drawable/ic_strelka"
            android:visibility="visible"
            android:layout_width="26dp"
            android:layout_height="20dp"
            android:layout_centerInParent="true"
            android:scaleX="-1"/>
    </RelativeLayout>
</RelativeLayout>
