package com.tqhit.battery.one.fragment.main.animation

import android.content.res.Resources
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment
import com.tqhit.battery.one.databinding.FragmentAnimationGridBinding
import com.tqhit.battery.one.fragment.main.animation.adapter.CategoryAdapter
import com.tqhit.battery.one.fragment.main.animation.adapter.AnimationAdapter
import com.tqhit.battery.one.fragment.main.animation.adapter.GridSpacingItemDecoration
import com.tqhit.battery.one.fragment.main.animation.data.AnimationCategory
import com.tqhit.battery.one.fragment.main.animation.data.AnimationItem
import org.json.JSONArray
import android.view.animation.AnimationUtils
import androidx.activity.viewModels
import androidx.fragment.app.viewModels
import com.tqhit.adlib.sdk.firebase.FirebaseRemoteConfigHelper
import com.tqhit.battery.one.R
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject
import com.google.gson.Gson
import com.tqhit.battery.one.ads.core.ApplovinInterstitialAdManager
import com.tqhit.battery.one.dialog.utils.NotificationDialog
import com.tqhit.battery.one.repository.AnimationRepository
import com.tqhit.battery.one.repository.AppRepository
import com.tqhit.battery.one.viewmodel.animation.AnimationViewModel

@AndroidEntryPoint
class AnimationGridFragment : AdLibBaseFragment<FragmentAnimationGridBinding>() {
    override val binding by lazy {
        FragmentAnimationGridBinding.inflate(layoutInflater)
    }
    @Inject lateinit var applovinInterstitialAdManager: ApplovinInterstitialAdManager
    @Inject lateinit var remoteConfigHelper: FirebaseRemoteConfigHelper
    @Inject lateinit var appRepository: AppRepository
    private val animationViewModel: AnimationViewModel by viewModels()
    private lateinit var animationAdapter: AnimationAdapter
    private lateinit var categoryAdapter: CategoryAdapter
    private var categories: List<AnimationCategory> = emptyList()
    private var selectedCategoryIndex: Int = 0

    override fun setupData() {
        super.setupData()
        val parsed = parseAnimationData()
        categories = parsed
        // Setup category RecyclerView
        categoryAdapter = CategoryAdapter(categories, selectedCategoryIndex) { index ->
            if (selectedCategoryIndex != index) {
                selectedCategoryIndex = index
                categoryAdapter.setSelectedIndex(index)
                animationAdapter.updateItems(categories[index].content)
                // Trigger layout animation when switching category
                binding.animationRecyclerView.layoutAnimation =
                    AnimationUtils.loadLayoutAnimation(requireContext(), R.anim.layout_animation_fall_down)
                binding.animationRecyclerView.scheduleLayoutAnimation()
            }
        }
        binding.categoryRecyclerView.apply {
            layoutManager =
                LinearLayoutManager(requireContext(), LinearLayoutManager.HORIZONTAL, false)
            adapter = categoryAdapter
        }
        // Setup animation RecyclerView
        binding.animationRecyclerView.layoutManager = GridLayoutManager(requireContext(), 2)
        val spacing = (16 * Resources.getSystem().displayMetrics.density).toInt() // 16dp to px
        binding.animationRecyclerView.addItemDecoration(
            GridSpacingItemDecoration(2, spacing)
        )
        animationAdapter =
            AnimationAdapter(requireActivity(), categories.getOrNull(selectedCategoryIndex)?.content ?: emptyList(), animationViewModel, appRepository)
        binding.animationRecyclerView.adapter = animationAdapter
    }

    private fun parseAnimationData(): List<AnimationCategory> {
        val jsonString = remoteConfigHelper.getString("animation_json")
        return Gson().fromJson(jsonString, Array<AnimationCategory>::class.java).toList()
    }

    override fun setupUI() {
        super.setupUI()
        // Info dialog for animation feature
        binding.animationInfo.setOnClickListener {
            applovinInterstitialAdManager.showInterstitialAd(
                "default_iv",
                requireActivity(),
            ) {
                NotificationDialog(
                    requireContext(),
                    getString(R.string.animation),
                    getString(R.string.animation_info_desc)
                ).show()
            }
        }
    }
}