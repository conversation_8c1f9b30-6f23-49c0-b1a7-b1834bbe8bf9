<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="72dp"
    android:layout_height="72dp"
    android:layout_margin="@dimen/spacing_small"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?attr/selectableItemBackground"
    app:cardCornerRadius="@dimen/card_corner_radius"
    app:cardElevation="@dimen/card_elevation_small"
    app:strokeColor="?attr/colorOutline"
    app:strokeWidth="1dp">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="@dimen/spacing_small">

        <!-- Battery Container Image -->
        <ImageView
            android:id="@+id/batteryImageView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerInside"
            android:contentDescription="@string/battery_container_image"
            tools:src="@drawable/ic_battery_placeholder" />

        <!-- Selection Indicator -->
        <ImageView
            android:id="@+id/selectionIndicator"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_gravity="top|end"
            android:layout_margin="2dp"
            android:src="@drawable/ic_check_circle"
            android:tint="?attr/colorPrimary"
            android:visibility="gone"
            android:contentDescription="@string/selected_indicator" />

        <!-- Premium Badge -->
        <ImageView
            android:id="@+id/premiumBadge"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_gravity="top|start"
            android:layout_margin="2dp"
            android:src="@drawable/ic_premium_star"
            android:tint="?attr/colorSecondary"
            android:visibility="gone"
            android:contentDescription="@string/premium_content" />

        <!-- Loading Indicator -->
        <ProgressBar
            android:id="@+id/loadingIndicator"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_gravity="center"
            android:visibility="gone" />

    </FrameLayout>

</com.google.android.material.card.MaterialCardView>
