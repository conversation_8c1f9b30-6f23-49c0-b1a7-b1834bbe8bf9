package com.tqhit.battery.one.dialog.permission

import android.content.Context
import android.graphics.Color
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.ViewGroup
import android.view.WindowManager
import androidx.core.graphics.drawable.toDrawable
import com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog
import com.tqhit.battery.one.databinding.DialogBackgroundPermissionBinding
import com.tqhit.battery.one.utils.BackgroundPermissionManager
import dagger.hilt.android.qualifiers.ActivityContext

/**
 * Background permission dialog that follows the established dialog architecture pattern.
 * Based on the content and design of Onboarding Slide Layout 6.
 */
class BackgroundPermissionDialog(
    @ActivityContext private val context: Context,
    private val onPermissionGranted: () -> Unit = {},
    private val onPermissionDenied: () -> Unit = {},
    private val onDialogClosed: () -> Unit = {}
) : AdLibBaseDialog<DialogBackgroundPermissionBinding>(context) {
    
    override val binding by lazy { DialogBackgroundPermissionBinding.inflate(layoutInflater) }

    // Auto-close monitoring
    private val handler = Handler(Looper.getMainLooper())
    private var permissionMonitorRunnable: Runnable? = null
    private var isMonitoringPermission = false

    companion object {
        private const val TAG = "BackgroundPermissionDialog"
        private const val PERMISSION_CHECK_INTERVAL_MS = 1000L // Check every second
    }
    
    override fun initWindow() {
        Log.d(TAG, "Initializing background permission dialog window")
        window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        window?.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
        val layoutParams = WindowManager.LayoutParams()
        layoutParams.copyFrom(window?.attributes)
        window?.attributes = layoutParams
        
        setCanceledOnTouchOutside(false)
        setCancelable(false) // Prevent dismissal by back button
    }
    
    override fun setupUI() {
        super.setupUI()
        Log.d(TAG, "Setting up background permission dialog UI")
        
        // UI is already set up via the layout file with proper strings
        // The layout includes:
        // - Icon (ic_health_1)
        // - Title (background_permission_dialog_title)
        // - Message (background_permission_dialog_message)
        // - Don't Kill My App link
        // - Close and Allow buttons
    }
    
    override fun setupListener() {
        super.setupListener()
        Log.d(TAG, "Setting up background permission dialog listeners")
        
        // Close button - dismiss dialog without requesting permission
        binding.btnClose.setOnClickListener {
            Log.d(TAG, "Close button clicked")

            // Record dismissal for rate limiting
            BackgroundPermissionManager.recordDialogDismissal(context)

            onPermissionDenied()
            onDialogClosed()
            dismiss()
        }
        
        // Allow button - request battery optimization permission
        binding.btnAllow.setOnClickListener {
            Log.d(TAG, "Allow button clicked")
            requestBackgroundPermission()

            // Start monitoring for permission grant to auto-close dialog
            startPermissionMonitoring()
        }
        
        // Don't Kill My App link - open website
        binding.dontKillMyAppLink.setOnClickListener {
            Log.d(TAG, "Don't Kill My App link clicked")
            BackgroundPermissionManager.openDontKillMyAppWebsite(context)
        }
    }
    
    /**
     * Requests background permission using BackgroundPermissionManager
     */
    private fun requestBackgroundPermission() {
        Log.d(TAG, "Requesting background permission")
        
        BackgroundPermissionManager.requestBatteryOptimizationPermission(
            context = context,
            onPermissionGranted = {
                Log.d(TAG, "Background permission granted")
                onPermissionGranted()
                onDialogClosed()
                dismiss()
            },
            onPermissionDenied = {
                Log.d(TAG, "Background permission denied")
                onPermissionDenied()
                // Don't dismiss dialog immediately - let user try again
            }
        )
    }
    
    /**
     * Shows the dialog with proper logging
     */
    override fun show() {
        Log.d(TAG, "Showing background permission dialog")
        super.show()
    }
    
    /**
     * Starts monitoring permission status for auto-close functionality
     */
    private fun startPermissionMonitoring() {
        if (isMonitoringPermission) {
            Log.d(TAG, "Permission monitoring already active")
            return
        }

        Log.d(TAG, "Starting permission monitoring for auto-close")
        isMonitoringPermission = true

        permissionMonitorRunnable = object : Runnable {
            override fun run() {
                if (!isMonitoringPermission || !isShowing) {
                    Log.d(TAG, "Stopping permission monitoring - dialog not showing or monitoring disabled")
                    return
                }

                // Check if permission has been granted
                if (BackgroundPermissionManager.isIgnoringBatteryOptimizations(context)) {
                    Log.d(TAG, "Permission granted detected - auto-closing dialog")

                    // Stop monitoring
                    stopPermissionMonitoring()

                    // Trigger callbacks and close dialog
                    onPermissionGranted()
                    onDialogClosed()
                    dismiss()
                    return
                }

                // Schedule next check
                handler.postDelayed(this, PERMISSION_CHECK_INTERVAL_MS)
            }
        }

        // Start monitoring
        handler.post(permissionMonitorRunnable!!)
    }

    /**
     * Stops permission monitoring
     */
    private fun stopPermissionMonitoring() {
        Log.d(TAG, "Stopping permission monitoring")
        isMonitoringPermission = false
        permissionMonitorRunnable?.let { runnable ->
            handler.removeCallbacks(runnable)
        }
        permissionMonitorRunnable = null
    }

    /**
     * Dismisses the dialog with proper logging and cleanup
     */
    override fun dismiss() {
        Log.d(TAG, "Dismissing background permission dialog")

        // Stop permission monitoring when dialog is dismissed
        stopPermissionMonitoring()

        super.dismiss()
    }
}
