package com.tqhit.battery.one.repository

import android.app.Activity
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.BatteryManager
import android.os.Build
import com.tqhit.adlib.sdk.data.local.PreferencesHelper
import com.tqhit.battery.one.manager.graph.BatteryHistoryManager
import com.tqhit.battery.one.utils.BatteryUtils
import com.tqhit.battery.one.utils.BatteryCalculatorDischarge
import com.tqhit.battery.one.manager.charge.ChargeSession
import com.tqhit.battery.one.manager.charge.ChargingSessionManager
import com.tqhit.battery.one.manager.discharge.DischargeSession
import com.tqhit.battery.one.manager.discharge.DischargeSessionManager
import com.tqhit.battery.one.manager.graph.HistoryEntry
import com.tqhit.battery.one.manager.graph.TemperatureHistoryManager
import dagger.hilt.android.qualifiers.ApplicationContext
import java.io.BufferedReader
import java.io.InputStreamReader
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.math.abs
import kotlin.math.max
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import android.util.Log

@Singleton
class BatteryRepository @Inject constructor(
    @ApplicationContext private val context: Context,
    private val preferencesHelper: PreferencesHelper,
    private val chargingSessionManager: ChargingSessionManager,
    private val dischargeSessionManager: DischargeSessionManager,
    private val batteryHistoryManager: BatteryHistoryManager,
    private val temperatureHistoryManager: TemperatureHistoryManager
) {
    companion object {
        private const val KEY_BATTERY_CAPACITY = "battery_capacity"
        private const val KEY_SELECTED_PERCENT = "selected_percent"
        private const val KEY_CURRENT_CHARGING_SESSION = "current_charging_session"
        private const val KEY_LAST_VALID_CHARGING_SESSION = "last_valid_charging_session"
        private const val KEY_CURRENT_DISCHARGE_SESSION = "current_discharge_session"
        private const val KEY_LAST_VALID_DISCHARGE_SESSION = "last_valid_discharge_session"
        const val CHARGING_CHECK_INTERVAL = 1000L // 1 second
        const val UPDATE_NOTIFICATION_INTERVAL = 5000L // 5 seconds
        private const val DEFAULT_SELECTED_PERCENT = 80
        private const val MAX_POWER_READINGS = 100 // Keep last 100 readings for average
        private const val MAX_STANDBY_READINGS = 50 // Keep last 50 readings for average
    }

    val mgr = context.getSystemService(Activity.BATTERY_SERVICE) as BatteryManager

    private val _batteryPercentage = MutableStateFlow(0)
    val batteryPercentage: StateFlow<Int> = _batteryPercentage.asStateFlow()

    private val _batteryHealth = MutableStateFlow(100)
    val batteryHealth: StateFlow<Int> = _batteryHealth.asStateFlow()

    private val _totalChargeSession = MutableStateFlow(0)
    val totalChargeSession: StateFlow<Int> = _totalChargeSession.asStateFlow()

    private val _voltage = MutableStateFlow(0.0)
    val voltage: StateFlow<Double> = _voltage.asStateFlow()

    private val _power = MutableStateFlow(0.0)
    val power: StateFlow<Double> = _power.asStateFlow()

    private var _averageDischargePower = 0.0

    private val dischargePowerReadings = mutableListOf<Double>()

    private val _amperage = MutableStateFlow(0.0)
    val amperage: StateFlow<Double> = _amperage.asStateFlow()

    private val _temperature = MutableStateFlow(0.0)
    val temperature: StateFlow<Double> = _temperature.asStateFlow()

    private val _chargingRate = MutableStateFlow(0.0)
    val chargingRate: StateFlow<Double> = _chargingRate.asStateFlow()

    private val _isCharging = MutableStateFlow(false)
    val isCharging: StateFlow<Boolean> = _isCharging.asStateFlow()

    private val _chargingTimeRemaining = MutableStateFlow(0L)
    val chargingTimeRemaining: StateFlow<Long> = _chargingTimeRemaining.asStateFlow()

    private val _chargingTimeRemainingToTarget = MutableStateFlow(0L)
    val chargingTimeRemainingToTarget: StateFlow<Long> = _chargingTimeRemainingToTarget.asStateFlow()

    private val _screenOnTimeRemaining = MutableStateFlow(0L)
    val screenOnTimeRemaining: StateFlow<Long> = _screenOnTimeRemaining.asStateFlow()

    private val _usageStyleTimeRemaining = MutableStateFlow(0L)
    val usageStyleTimeRemaining: StateFlow<Long> = _usageStyleTimeRemaining.asStateFlow()

    private val _screenOffTimeRemaining = MutableStateFlow(0L)
    val screenOffTimeRemaining: StateFlow<Long> = _screenOffTimeRemaining.asStateFlow()

    private val _screenOnTimeRemainingAt100 = MutableStateFlow(0L)
    val screenOnTimeRemainingAt100: StateFlow<Long> = _screenOnTimeRemainingAt100.asStateFlow()

    private val _usageStyleTimeRemainingAt100 = MutableStateFlow(0L)
    val usageStyleTimeRemainingAt100: StateFlow<Long> = _usageStyleTimeRemainingAt100.asStateFlow()

    private val _screenOffTimeRemainingAt100 = MutableStateFlow(0L)
    val screenOffTimeRemainingAt100: StateFlow<Long> = _screenOffTimeRemainingAt100.asStateFlow()

    // Charge session variables
    private var _currentSession: ChargeSession? = null
    private var _lastValidSession: ChargeSession? = null

    private var _startTimeSession = MutableStateFlow(0L)
    val startTimeSession: StateFlow<Long> = _startTimeSession.asStateFlow()
    private var _endTimeSession = MutableStateFlow(0L)
    val endTimeSession: StateFlow<Long> = _endTimeSession.asStateFlow()
    private var _averageSpeedSession = MutableStateFlow(0.0)
    val averageSpeedSession: StateFlow<Double> = _averageSpeedSession.asStateFlow()
    private var _averageSpeedMilliAmperesSession = MutableStateFlow(0)
    val averageSpeedMilliAmperesSession: StateFlow<Int> = _averageSpeedMilliAmperesSession.asStateFlow()
    private var _startPercentSession = MutableStateFlow(0)
    val startPercentSession: StateFlow<Int> = _startPercentSession.asStateFlow()
    private var _endPercentSession = MutableStateFlow(0)
    val endPercentSession: StateFlow<Int> = _endPercentSession.asStateFlow()
    private var _totalMilliAmperesSession = MutableStateFlow(0)
    val totalMilliAmperesSession: StateFlow<Int> = _totalMilliAmperesSession.asStateFlow()
    private var _screenOffAverageSpeedSession = MutableStateFlow(0.0)
    val screenOffAverageSpeedSession: StateFlow<Double> = _screenOffAverageSpeedSession.asStateFlow()
    private var _screenOffMilliAmperesSession = MutableStateFlow(0)
    val screenOffMilliAmperesSession: StateFlow<Int> = _screenOffMilliAmperesSession.asStateFlow()
    private var _screenOnAverageSpeedSession = MutableStateFlow(0.0)
    val screenOnAverageSpeedSession: StateFlow<Double> = _screenOnAverageSpeedSession.asStateFlow()
    private var _screenOnMilliAmperesSession = MutableStateFlow(0)
    val screenOnMilliAmperesSession: StateFlow<Int> = _screenOnMilliAmperesSession.asStateFlow()
    private val _rightNowPercentPerHourSession = MutableStateFlow(0.0)
    val rightNowPercentPerHourSession: StateFlow<Double> = _rightNowPercentPerHourSession
    private var _screenOnTimeChargingCurrentSession = 0L
    private var _screenOnPercentChargingCurrentSession: Int = 0
    private var _screenOffTimeChargingCurrentSession = 0L
    private var _screenOffPercentChargingCurrentSession: Int = 0

    private var _lastScreenStateChangeTime = System.currentTimeMillis()

    private val _averageScreenOnSpeed = MutableStateFlow(0.0)
    val averageScreenOnSpeed: StateFlow<Double> = _averageScreenOnSpeed.asStateFlow()

    private val _averageScreenOffSpeed = MutableStateFlow(0.0)
    val averageScreenOffSpeed: StateFlow<Double> = _averageScreenOffSpeed.asStateFlow()

    private val _averageSpeed = MutableStateFlow(0.0)
    val averageSpeed: StateFlow<Double> = _averageSpeed.asStateFlow()

    private val _averageScreenOnMilliAmperes = MutableStateFlow(0)
    val averageScreenOnMilliAmperes: StateFlow<Int> = _averageScreenOnMilliAmperes.asStateFlow()

    private val _averageScreenOffMilliAmperes = MutableStateFlow(0)
    val averageScreenOffMilliAmperes: StateFlow<Int> = _averageScreenOffMilliAmperes.asStateFlow()

    private val _averageMilliAmperes = MutableStateFlow(0)
    val averageMilliAmperes: StateFlow<Int> = _averageMilliAmperes.asStateFlow()

    // Discharge session variables
    private var _currentDischargeSession: DischargeSession? = null
    private var _lastValidDischargeSession: DischargeSession? = null

    private var _dischargeStartTimeSession = MutableStateFlow(0L)
    val dischargeStartTimeSession: StateFlow<Long> = _dischargeStartTimeSession.asStateFlow()
    private var _dischargeEndTimeSession = MutableStateFlow(0L)
    val dischargeEndTimeSession: StateFlow<Long> = _dischargeEndTimeSession.asStateFlow()
    private var _dischargeAverageSpeedSession = MutableStateFlow(0.0)
    val dischargeAverageSpeedSession: StateFlow<Double> = _dischargeAverageSpeedSession.asStateFlow()
    private var _dischargeAverageSpeedMilliAmperesSession = MutableStateFlow(0)
    val dischargeAverageSpeedMilliAmperesSession: StateFlow<Int> = _dischargeAverageSpeedMilliAmperesSession.asStateFlow()
    private var _dischargeStartPercentSession = MutableStateFlow(0)
    val dischargeStartPercentSession: StateFlow<Int> = _dischargeStartPercentSession.asStateFlow()
    private var _dischargeEndPercentSession = MutableStateFlow(0)
    val dischargeEndPercentSession: StateFlow<Int> = _dischargeEndPercentSession.asStateFlow()
    private var _dischargeTotalMilliAmperesSession = MutableStateFlow(0)
    val dischargeTotalMilliAmperesSession: StateFlow<Int> = _dischargeTotalMilliAmperesSession.asStateFlow()
    private var _dischargeScreenOffAverageSpeedSession = MutableStateFlow(0.0)
    val dischargeScreenOffAverageSpeedSession: StateFlow<Double> = _dischargeScreenOffAverageSpeedSession.asStateFlow()
    private var _dischargeScreenOffMilliAmperesSession = MutableStateFlow(0)
    val dischargeScreenOffMilliAmperesSession: StateFlow<Int> = _dischargeScreenOffMilliAmperesSession.asStateFlow()
    private var _dischargeScreenOnAverageSpeedSession = MutableStateFlow(0.0)
    val dischargeScreenOnAverageSpeedSession: StateFlow<Double> = _dischargeScreenOnAverageSpeedSession.asStateFlow()
    private var _dischargeScreenOnMilliAmperesSession = MutableStateFlow(0)
    val dischargeScreenOnMilliAmperesSession: StateFlow<Int> = _dischargeScreenOnMilliAmperesSession.asStateFlow()
    private var _dischargeRightNowPercentPerHourSession = MutableStateFlow(0.0)
    val dischargeRightNowPercentPerHourSession: StateFlow<Double> = _dischargeRightNowPercentPerHourSession.asStateFlow()

    private var _dischargeScreenOnTimeCurrentSession = 0L
    val dischargeScreenOnTimeCurrentSession: Long
        get() = _dischargeScreenOnTimeCurrentSession

    private var _dischargeScreenOnPercentCurrentSession: Int = 0
    val dischargeScreenOnPercentCurrentSession: Int
        get() = _dischargeScreenOnPercentCurrentSession

    private var _dischargeScreenOffTimeCurrentSession = 0L
    val dischargeScreenOffTimeCurrentSession: Long
        get() = _dischargeScreenOffTimeCurrentSession

    private var _dischargeScreenOffPercentCurrentSession: Int = 0
    val dischargeScreenOffPercentCurrentSession: Int
        get() = _dischargeScreenOffPercentCurrentSession

    private val standbyPowerReadings = mutableListOf<Double>()

    private var isScreenOn = true
    private val screenStateReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            when (intent?.action) {
                Intent.ACTION_SCREEN_ON -> isScreenOn = true
                Intent.ACTION_SCREEN_OFF -> isScreenOn = false
            }
        }
    }

    private val _batteryCapacity = MutableStateFlow(
        preferencesHelper.getInt(
            KEY_BATTERY_CAPACITY,
            BatteryUtils.getBatteryCapacity(context)
        )
    )
    val batteryCapacity: StateFlow<Int> = _batteryCapacity.asStateFlow()

    private val _selectedPercent = MutableStateFlow(preferencesHelper.getInt(KEY_SELECTED_PERCENT, DEFAULT_SELECTED_PERCENT))
    val selectedPercent: StateFlow<Int> = _selectedPercent.asStateFlow()

    private val batteryReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            if (intent?.action == Intent.ACTION_BATTERY_CHANGED) {
                // We'll handle battery updates in the interval loop instead
                // This receiver is kept for system battery change events
            }
        }
    }

    private var _lastProcessorIdleTime = 0L
    private var _isProcessorIdle = false

    private val batteryHistory = mutableListOf<Pair<Long, Int>>() // timestamp, percent
    private val temperatureHistory = mutableListOf<Pair<Long, Double>>() // timestamp, temp

    // Add tracked discharge currents for screen on/off states
    private var _trackedScreenOnCurrentMa = 0.0
    private var _trackedScreenOffCurrentMa = 0.0
    private val _lastScreenStateTime = MutableStateFlow(System.currentTimeMillis())

    fun registerReceivers() {
        // Register for battery updates
        context.registerReceiver(batteryReceiver, IntentFilter(Intent.ACTION_BATTERY_CHANGED))

        // Register for screen state updates
        val screenFilter = IntentFilter().apply {
            addAction(Intent.ACTION_SCREEN_ON)
            addAction(Intent.ACTION_SCREEN_OFF)
        }
        context.registerReceiver(screenStateReceiver, screenFilter)
    }

    fun unregisterReceivers() {
        try {
            context.unregisterReceiver(batteryReceiver)
            context.unregisterReceiver(screenStateReceiver)
        } catch (e: Exception) {
            // Receiver not registered
        }
    }

    fun updateBatteryInfo(intent: Intent) {
        val currentTime = System.currentTimeMillis()
        
        // First, determine charging status early as many calculations depend on it
        val status = intent.getIntExtra(BatteryManager.EXTRA_STATUS, -1)
        val isCharging = status == BatteryManager.BATTERY_STATUS_CHARGING || 
                        status == BatteryManager.BATTERY_STATUS_FULL
        _isCharging.value = isCharging
        
        // Now process other battery information
        val level = intent.getIntExtra(BatteryManager.EXTRA_LEVEL, -1)
        val scale = intent.getIntExtra(BatteryManager.EXTRA_SCALE, -1)
        val batteryPct = level * 100 / scale.toFloat()
        _batteryPercentage.value = batteryPct.toInt()

        // Calculate battery health based on total charge sessions
        val totalSessions = calculateTotalChargeSessions()
        _totalChargeSession.value = totalSessions

        // Convert charge sessions to health percentage (100% - wear impact)
        // Assuming 500 sessions is considered end of life (20% health)
        val healthPercentage = (100 - (totalSessions / 500.0 * 80.0)).toInt().coerceIn(0, 100)
        _batteryHealth.value = healthPercentage

        // Update voltage (in volts)
        val milliVoltage = intent.getIntExtra(BatteryManager.EXTRA_VOLTAGE, 0).toDouble()
        val voltage = milliVoltage.div(1000.0f)
        _voltage.value = voltage

        // Update temperature (in Celsius)
        val temperature = intent.getIntExtra(BatteryManager.EXTRA_TEMPERATURE, 0).toDouble().div(10.0f)
        _temperature.value = temperature

        // Update amperage and track screen on/off current
        val _microAmps = mgr.getLongProperty(BatteryManager.BATTERY_PROPERTY_CURRENT_NOW)
        val microAmps = if(_microAmps < 1000){
            _microAmps.toDouble()*1000
        } else {
            _microAmps.toDouble()
        }
        val milliAmps = microAmps.div(1_000.0f)
        val amps = milliAmps.div(1_000.0f)
        _amperage.value = milliAmps.toDouble()
        Log.d("BatteryRepo", "milliAmps: $milliAmps, amps: $amps, microAmps: $_microAmps")
        
        // Track discharge current by screen state (only when discharging)
        if (!isCharging && abs(milliAmps) > 0) {
            // Track the current based on screen state
            if (isScreenOn) {
                // Update screen on current tracking
                _trackedScreenOnCurrentMa = if (_trackedScreenOnCurrentMa <= 0) {
                    abs(milliAmps)
                } else {
                    // Weighted average with 80% old value, 20% new value for stability
                    (_trackedScreenOnCurrentMa * 0.8) + (abs(milliAmps) * 0.2)
                }
                Log.d("BatteryRepo", "Updated screen ON current: $_trackedScreenOnCurrentMa mA")
            } else {
                // Update screen off current tracking
                _trackedScreenOffCurrentMa = if (_trackedScreenOffCurrentMa <= 0) {
                    abs(milliAmps)
                } else {
                    // Weighted average with 80% old value, 20% new value for stability
                    (_trackedScreenOffCurrentMa * 0.8) + (abs(milliAmps) * 0.2)
                }
                Log.d("BatteryRepo", "Updated screen OFF current: $_trackedScreenOffCurrentMa mA")
            }
        }
        
        // Update last screen state time
        _lastScreenStateTime.value = currentTime

        // Calculate power (in watts)
        // Negative power means discharging, positive means charging
        val watts = amps * voltage
        _power.value = watts

        // Update average power consumption only when discharging
        if (!isCharging) { // Only track discharging power
            dischargePowerReadings.add(-watts) // Convert to positive for easier calculation
            if (dischargePowerReadings.size > MAX_POWER_READINGS) {
                dischargePowerReadings.removeAt(0)
            }
        }
        // Always update the average, even when charging (using existing readings)
        _averageDischargePower = BatteryCalculatorDischarge.calculateAveragePower(dischargePowerReadings)

        // Update standby power readings when screen is off and not charging
        val currentStandbyPower = if (!isCharging && !isScreenOn) {
            // Convert microamps to watts
            val microAmpsToWatts = microAmps.toDouble() / 1_000_000.0
            abs(microAmpsToWatts * voltage)
        } else {
            // Default to a reasonable value if current reading is not available
            0.15
        }

        // Use the utility method to update standby power readings
        BatteryCalculatorDischarge.updateStandbyPowerReadings(
            isCharging = _isCharging.value,
            isScreenOn = isScreenOn,
            currentPower = currentStandbyPower,
            standbyPowerReadings = standbyPowerReadings
        )

        // Calculate charging rate (in percentage per hour)
        val chargingRate = 100 * milliAmps.toDouble() / max(batteryCapacity.value, 1)
        _chargingRate.value = chargingRate

        // Calculate charging time remaining
        _chargingTimeRemaining.value = calculateChargingTimeRemaining(batteryPct.toInt(), amps, batteryCapacity.value)

        // Calculate charging time remaining to target percentage
        _chargingTimeRemainingToTarget.value = calculateChargingTimeRemaining(
            batteryPct.toInt(),
            amps,
            batteryCapacity.value,
            selectedPercent.value
        )

        // Calculate screen on time remaining
        val remainingCapacity = (batteryCapacity.value * batteryPct.toInt() / 100.0) // Remaining capacity in mAh
        
        // Get screen on current - use tracked value or fallback to 4:1 ratio
        val screenOnCurrentMa = when {
            _trackedScreenOnCurrentMa > 0 -> _trackedScreenOnCurrentMa
            _trackedScreenOffCurrentMa > 0 -> _trackedScreenOffCurrentMa * 4.0
            abs(_amperage.value) > 0 -> abs(_amperage.value)
            else -> 200.0 // Default fallback
        }
        
        Log.d("BatteryRepo", "Using screen ON current: $screenOnCurrentMa mA")
        
        _screenOnTimeRemaining.value = BatteryCalculatorDischarge.calculateScreenOnTimeRemaining(
            remainingCapacity, 
            screenOnCurrentMa,
            isCharging = _isCharging.value
        )

        // Calculate 100% battery estimates
        val fullCapacity = batteryCapacity.value.toDouble() // Full capacity in mAh

        // Calculate screen on time at 100%
        _screenOnTimeRemainingAt100.value = BatteryCalculatorDischarge.calculateScreenOnTimeRemaining(
            fullCapacity,
            screenOnCurrentMa,
            isCharging = _isCharging.value
        )

        // Calculate usage style based time remaining
        _usageStyleTimeRemaining.value = BatteryCalculatorDischarge.calculateUsageStyleTimeRemaining(
            remainingCapacity, 
            screenOnCurrentMa,
            isCharging = _isCharging.value
        )

        // Calculate usage style time at 100%
        _usageStyleTimeRemainingAt100.value = BatteryCalculatorDischarge.calculateUsageStyleTimeRemaining(
            fullCapacity, 
            screenOnCurrentMa,
            isCharging = _isCharging.value
        )

        // Get screen off current - use tracked value or fallback to 1:4 ratio of screen on
        val screenOffCurrentMa = when {
            _trackedScreenOffCurrentMa > 0 -> _trackedScreenOffCurrentMa
            _trackedScreenOnCurrentMa > 0 -> _trackedScreenOnCurrentMa / 4.0
            abs(_amperage.value) > 0 -> abs(_amperage.value) / 4.0
            else -> 50.0 // Default fallback
        }
        
        Log.d("BatteryRepo", "Using screen OFF current: $screenOffCurrentMa mA")
        
        _screenOffTimeRemaining.value = BatteryCalculatorDischarge.calculateScreenOffTimeRemaining(
            remainingCapacity, 
            screenOffCurrentMa,
            isCharging = _isCharging.value
        )

        // Calculate screen off time at 100%
        _screenOffTimeRemainingAt100.value = BatteryCalculatorDischarge.calculateScreenOffTimeRemaining(
            fullCapacity, 
            screenOffCurrentMa,
            isCharging = _isCharging.value
        )

        // Update both session types
        updateSessionChargeInfo(intent)
        updateSessionDischargeInfo(intent)

        // Add to history for graphing
        addHistory(batteryPct.toInt(), temperature)
    }

    fun updateSessionChargeInfo(intent: Intent) {
        if (_isCharging.value) {
            if (_currentSession == null) {
                _currentSession = getCurrentChargingSession()
                _currentSession = _currentSession ?: ChargeSession(
                    startTime = System.currentTimeMillis(),
                    startPercent = batteryPercentage.value,
                )
                _startTimeSession.value = _currentSession?.startTime ?: 0L
                _startPercentSession.value = _currentSession?.startPercent ?: 0
                _lastScreenStateChangeTime = System.currentTimeMillis()
                // Reset values for new session
                _endTimeSession.value = _currentSession?.endTime ?: 0L
                _averageSpeedSession.value = _currentSession?.averageSpeed ?: 0.0
                _averageSpeedMilliAmperesSession.value = _currentSession?.averageSpeedMilliAmperes ?: 0
                _startPercentSession.value = _currentSession?.startPercent ?: 0
                _endPercentSession.value = _currentSession?.endPercent ?: 0
                _totalMilliAmperesSession.value = _currentSession?.totalMilliAmperes ?: 0
                _screenOffAverageSpeedSession.value = _currentSession?.screenOffPercent ?: 0.0
                _screenOffMilliAmperesSession.value = _currentSession?.screenOffMilliAmperes ?: 0
                _screenOnAverageSpeedSession.value = _currentSession?.screenOnPercent ?: 0.0
                _screenOnMilliAmperesSession.value = _currentSession?.screenOnMilliAmperes ?: 0
                _screenOnTimeChargingCurrentSession = System.currentTimeMillis() - _startTimeSession.value
                _screenOffTimeChargingCurrentSession = System.currentTimeMillis() - _startTimeSession.value
                _screenOnPercentChargingCurrentSession = 0
                _screenOffPercentChargingCurrentSession = 0
            } else {
                val currentTime = System.currentTimeMillis()
                _endTimeSession.value = currentTime

                // update screen on/off time based on current state
                if (_endPercentSession.value != 0) {
                    if (isScreenOn) {
                        _screenOnTimeChargingCurrentSession += currentTime - _lastScreenStateChangeTime
                        _screenOnPercentChargingCurrentSession += (batteryPercentage.value - _endPercentSession.value)
                    } else {
                        _screenOffTimeChargingCurrentSession += currentTime - _lastScreenStateChangeTime
                        _screenOffPercentChargingCurrentSession += (batteryPercentage.value - _endPercentSession.value)
                    }
                    _lastScreenStateChangeTime = currentTime
                }

                if (_endPercentSession.value != batteryPercentage.value) {
                    _endPercentSession.value = batteryPercentage.value
                    _averageSpeedSession.value = (_endPercentSession.value - _startPercentSession.value).toDouble() /
                            ((currentTime - _startTimeSession.value) / 1000.0 / 3600.0)
                    _averageSpeedMilliAmperesSession.value = (averageSpeedSession.value * batteryCapacity.value / 100).toInt()
                    _totalMilliAmperesSession.value = (_endPercentSession.value - _startPercentSession.value) *
                            batteryCapacity.value / 100

                    if (_screenOffPercentChargingCurrentSession > 0) {
                        _screenOffAverageSpeedSession.value = (_screenOffPercentChargingCurrentSession.toDouble() /
                                (_screenOffTimeChargingCurrentSession / 1000.0 / 3600.0))
                        _screenOffMilliAmperesSession.value = (_screenOffAverageSpeedSession.value * batteryCapacity.value / 100).toInt()
                    }
                    if (_screenOnPercentChargingCurrentSession > 0) {
                        _screenOnAverageSpeedSession.value = (_screenOnPercentChargingCurrentSession.toDouble() /
                                (_screenOnTimeChargingCurrentSession / 1000.0 / 3600.0))
                        _screenOnMilliAmperesSession.value = (_screenOnAverageSpeedSession.value * batteryCapacity.value / 100).toInt()
                    }
                }

                _currentSession?.let { session ->
                    session.startTime = _startTimeSession.value
                    session.endTime = _endTimeSession.value
                    session.averageSpeed = _averageSpeedSession.value
                    session.averageSpeedMilliAmperes = _averageSpeedMilliAmperesSession.value
                    session.startPercent = _startPercentSession.value
                    session.endPercent = _endPercentSession.value
                    session.totalMilliAmperes = _totalMilliAmperesSession.value
                    session.screenOffPercent = _screenOffAverageSpeedSession.value
                    session.screenOffMilliAmperes = _screenOffMilliAmperesSession.value
                    session.screenOnPercent = _screenOnAverageSpeedSession.value
                    session.screenOnMilliAmperes = _screenOnMilliAmperesSession.value
                }
            }

            // Calculate right now percent per hour using existing amperage value
            _rightNowPercentPerHourSession.value = BatteryCalculatorDischarge.calculatePercentPerHour(
                _amperage.value,
                batteryCapacity.value,
                isCharging = true
            )
            setCurrentChargingSession()
        } else {
            _lastValidSession = getLastValidChargingSession()
            _currentSession?.let { session ->
                session.endTime = System.currentTimeMillis()
                if (session.endTime - session.startTime > 1_000 * 60 || _lastValidSession == null) {
                    _lastValidSession = session.copy()
                    setLastValidChargingSession()
                    // Add the completed session to the session manager and update averages
                    chargingSessionManager.addSession(session)
                    updateAverageSpeeds()
                }
                _currentSession = null
                setCurrentChargingSession()

                _startTimeSession.value = _lastValidSession?.startTime ?: 0L
                _endTimeSession.value = _lastValidSession?.endTime ?: 0L
                _averageSpeedSession.value = _lastValidSession?.averageSpeed ?: 0.0
                _averageSpeedMilliAmperesSession.value = _lastValidSession?.averageSpeedMilliAmperes ?: 0
                _startPercentSession.value = _lastValidSession?.startPercent ?: 0
                _endPercentSession.value = _lastValidSession?.endPercent ?: 0
                _totalMilliAmperesSession.value = _lastValidSession?.totalMilliAmperes ?: 0
                _screenOffAverageSpeedSession.value = _lastValidSession?.screenOffPercent ?: 0.0
                _screenOffMilliAmperesSession.value = _lastValidSession?.screenOffMilliAmperes ?: 0
                _screenOnAverageSpeedSession.value = _lastValidSession?.screenOnPercent ?: 0.0
                _screenOnMilliAmperesSession.value = _lastValidSession?.screenOnMilliAmperes ?: 0
                _rightNowPercentPerHourSession.value = 0.0
            }
        }
    }

    fun updateSessionDischargeInfo(intent: Intent) {
        if (_isCharging.value) {
            _lastValidDischargeSession = getLastValidDischargeSession()
            _currentDischargeSession?.let { session ->
                session.endTime = System.currentTimeMillis()
                if (session.endTime - session.startTime > 1_000 * 60 || _lastValidDischargeSession == null) {
                    _lastValidDischargeSession = session.copy()
                    setLastValidDischargeSession()
                    // Add the completed session to the session manager and update averages
                    dischargeSessionManager.addSession(session)
                    updateAverageDischargeSpeeds()
                }
                _currentDischargeSession = null
                setCurrentDischargeSession()

                _dischargeStartTimeSession.value = _lastValidDischargeSession?.startTime ?: 0L
                _dischargeEndTimeSession.value = _lastValidDischargeSession?.endTime ?: 0L
                _dischargeAverageSpeedSession.value = _lastValidDischargeSession?.averageSpeed ?: 0.0
                _dischargeAverageSpeedMilliAmperesSession.value = _lastValidDischargeSession?.averageSpeedMilliAmperes ?: 0
                _dischargeStartPercentSession.value = _lastValidDischargeSession?.startPercent ?: 0
                _dischargeEndPercentSession.value = _lastValidDischargeSession?.endPercent ?: 0
                _dischargeTotalMilliAmperesSession.value = _lastValidDischargeSession?.totalMilliAmperes ?: 0
                _dischargeScreenOffAverageSpeedSession.value = _lastValidDischargeSession?.screenOffPercent ?: 0.0
                _dischargeScreenOffMilliAmperesSession.value = _lastValidDischargeSession?.screenOffMilliAmperes ?: 0
                _dischargeScreenOnAverageSpeedSession.value = _lastValidDischargeSession?.screenOnPercent ?: 0.0
                _dischargeScreenOnMilliAmperesSession.value = _lastValidDischargeSession?.screenOnMilliAmperes ?: 0
                _dischargeScreenOnTimeCurrentSession = System.currentTimeMillis() - _dischargeStartTimeSession.value
                _dischargeScreenOffTimeCurrentSession = System.currentTimeMillis() - _dischargeStartTimeSession.value
                _dischargeScreenOnPercentCurrentSession = 0
                _dischargeScreenOffPercentCurrentSession = 0
                _dischargeRightNowPercentPerHourSession.value = 0.0
            }
        } else {
            if (_currentDischargeSession == null) {
                _currentDischargeSession = getCurrentDischargeSession()
                _currentDischargeSession = _currentDischargeSession ?: DischargeSession(
                    startTime = System.currentTimeMillis(),
                    startPercent = batteryPercentage.value,
                )
                _dischargeStartTimeSession.value = _currentDischargeSession?.startTime ?: 0L
                _dischargeStartPercentSession.value = _currentDischargeSession?.startPercent ?: 0
                _lastScreenStateChangeTime = System.currentTimeMillis()
                // Reset values for new session
                _dischargeEndTimeSession.value = _currentDischargeSession?.endTime ?: 0L
                _dischargeAverageSpeedSession.value = _currentDischargeSession?.averageSpeed ?: 0.0
                _dischargeAverageSpeedMilliAmperesSession.value = _currentDischargeSession?.averageSpeedMilliAmperes ?: 0
                _dischargeStartPercentSession.value = _currentDischargeSession?.startPercent ?: 0
                _dischargeEndPercentSession.value = _currentDischargeSession?.endPercent ?: 0
                _dischargeTotalMilliAmperesSession.value = _currentDischargeSession?.totalMilliAmperes ?: 0
                _dischargeScreenOffAverageSpeedSession.value = _currentDischargeSession?.screenOffPercent ?: 0.0
                _dischargeScreenOffMilliAmperesSession.value = _currentDischargeSession?.screenOffMilliAmperes ?: 0
                _dischargeScreenOnAverageSpeedSession.value = _currentDischargeSession?.screenOnPercent ?: 0.0
                _dischargeScreenOnMilliAmperesSession.value = _currentDischargeSession?.screenOnMilliAmperes ?: 0
                _dischargeScreenOnTimeCurrentSession = System.currentTimeMillis() - _dischargeStartTimeSession.value
                _dischargeScreenOffTimeCurrentSession = System.currentTimeMillis() - _dischargeStartTimeSession.value
                _dischargeScreenOnPercentCurrentSession = 0
                _dischargeScreenOffPercentCurrentSession = 0
                _dischargeRightNowPercentPerHourSession.value = 0.0
            } else {
                val currentTime = System.currentTimeMillis()
                _dischargeEndTimeSession.value = currentTime

                // update screen on/off time based on current state
                if (_dischargeEndPercentSession.value != 0) {
                    if (isScreenOn) {
                        _dischargeScreenOnTimeCurrentSession += currentTime - _lastScreenStateChangeTime
                        _dischargeScreenOnPercentCurrentSession += (_dischargeEndPercentSession.value - batteryPercentage.value)
                    } else {
                        _dischargeScreenOffTimeCurrentSession += currentTime - _lastScreenStateChangeTime
                        _dischargeScreenOffPercentCurrentSession += (_dischargeEndPercentSession.value - batteryPercentage.value)
                    }
                    _lastScreenStateChangeTime = currentTime
                }

                if (_dischargeEndPercentSession.value != batteryPercentage.value) {
                    _dischargeEndPercentSession.value = batteryPercentage.value
                    _dischargeAverageSpeedSession.value = (_dischargeStartPercentSession.value - _dischargeEndPercentSession.value).toDouble() /
                            ((currentTime - _dischargeStartTimeSession.value) / 1000.0 / 3600.0)
                    _dischargeAverageSpeedMilliAmperesSession.value = (dischargeAverageSpeedSession.value * batteryCapacity.value / 100).toInt()
                    _dischargeTotalMilliAmperesSession.value = (_dischargeStartPercentSession.value - _dischargeEndPercentSession.value) * batteryCapacity.value / 100

                    if (_dischargeScreenOffPercentCurrentSession > 0) {
                        _dischargeScreenOffAverageSpeedSession.value = (_dischargeScreenOffPercentCurrentSession.toDouble() /
                                (_dischargeScreenOffTimeCurrentSession / 1000.0 / 3600.0))
                        _dischargeScreenOffMilliAmperesSession.value = (_dischargeScreenOffAverageSpeedSession.value * batteryCapacity.value / 100).toInt()
                    }
                    if (_dischargeScreenOnPercentCurrentSession > 0) {
                        _dischargeScreenOnAverageSpeedSession.value = (_dischargeScreenOnPercentCurrentSession.toDouble() /
                                (_dischargeScreenOnTimeCurrentSession / 1000.0 / 3600.0))
                        _dischargeScreenOnMilliAmperesSession.value = (_dischargeScreenOnAverageSpeedSession.value * batteryCapacity.value / 100).toInt()
                    }
                }

                _currentDischargeSession?.let { session ->
                    session.startTime = _dischargeStartTimeSession.value
                    session.endTime = _dischargeEndTimeSession.value
                    session.averageSpeed = _dischargeAverageSpeedSession.value
                    session.averageSpeedMilliAmperes = _dischargeAverageSpeedMilliAmperesSession.value
                    session.startPercent = _dischargeStartPercentSession.value
                    session.endPercent = _dischargeEndPercentSession.value
                    session.totalMilliAmperes = _dischargeTotalMilliAmperesSession.value
                    session.screenOffPercent = _dischargeScreenOffAverageSpeedSession.value
                    session.screenOffMilliAmperes = _dischargeScreenOffMilliAmperesSession.value
                    session.screenOnPercent = _dischargeScreenOnAverageSpeedSession.value
                    session.screenOnMilliAmperes = _dischargeScreenOnMilliAmperesSession.value
                }
            }

            // Calculate right now percent per hour using existing amperage value
            _dischargeRightNowPercentPerHourSession.value = BatteryCalculatorDischarge.calculatePercentPerHour(
                _amperage.value,
                batteryCapacity.value,
                isCharging = false
            )
            setCurrentDischargeSession()
        }
    }

    private fun calculateChargingTimeRemaining(
        currentPercentage: Int,
        currentNow: Double,
        batteryCapacity: Int,
        targetPercentage: Int = 100
    ): Long {
        // If not charging or current is invalid, return 0
        if (_isCharging.value == false || currentNow <= 0 || currentPercentage >= 100) {
            return 0
        }

        // Calculate remaining capacity to charge (in Ah)
        val remainingCapacity = (batteryCapacity * (targetPercentage - currentPercentage) / 100.0f) / 1_000.0f

        // Calculate time in hours
        val timeInHours = remainingCapacity / currentNow

        // Convert to milliseconds
        return (timeInHours * 3600 * 1000).toLong()
    }

    fun getBatteryCapacity(): Int = batteryCapacity.value

    fun setBatteryCapacity(capacity: Int) {
        preferencesHelper.saveInt(KEY_BATTERY_CAPACITY, capacity)
        _batteryCapacity.value = capacity
    }

    fun isIgnoringBatteryOptimizations(): Boolean = BatteryUtils.isIgnoringBatteryOptimizations(context)

    fun getBatteryPolarity(): String = BatteryUtils.getBatteryPolarity(context)

    fun getSelectedPercent(): Int = _selectedPercent.value

    fun setSelectedPercent(percent: Int) {
        preferencesHelper.saveInt(KEY_SELECTED_PERCENT, percent)
        _selectedPercent.value = percent
    }

    fun getCurrentChargingSession(): ChargeSession? {
        return _currentSession ?: preferencesHelper.getString(KEY_CURRENT_CHARGING_SESSION)?.let {
            ChargeSession.fromString(it)
        }
    }

    fun setCurrentChargingSession() {
        preferencesHelper.saveString(KEY_CURRENT_CHARGING_SESSION, _currentSession?.toString() ?: "")
    }

    fun getLastValidChargingSession(): ChargeSession? {
        return _lastValidSession ?: preferencesHelper.getString(KEY_LAST_VALID_CHARGING_SESSION).let {
            ChargeSession.fromString(it)
        }
    }

    fun setLastValidChargingSession() {
        _lastValidSession?.let {
            preferencesHelper.saveString(KEY_LAST_VALID_CHARGING_SESSION, it.toString())
        }
    }

    fun clearChargingSessions() {
        chargingSessionManager.clearSessions()
        updateAverageSpeeds()
    }

    fun getCurrentDischargeSession(): DischargeSession? {
        return _currentDischargeSession ?: preferencesHelper.getString(KEY_CURRENT_DISCHARGE_SESSION)?.let {
            DischargeSession.fromString(it)
        }
    }

    fun setCurrentDischargeSession() {
        preferencesHelper.saveString(KEY_CURRENT_DISCHARGE_SESSION, _currentDischargeSession?.toString() ?: "")
    }

    fun getLastValidDischargeSession(): DischargeSession? {
        return _lastValidDischargeSession ?: preferencesHelper.getString(KEY_LAST_VALID_DISCHARGE_SESSION).let {
            DischargeSession.fromString(it)
        }
    }

    fun setLastValidDischargeSession() {
        _lastValidDischargeSession?.let {
            preferencesHelper.saveString(KEY_LAST_VALID_DISCHARGE_SESSION, it.toString())
        }
    }

    fun clearDischargeSessions() {
        dischargeSessionManager.clearSessions()
        updateAverageDischargeSpeeds()
    }

    private fun updateAverageSpeeds() {
        _averageScreenOnSpeed.value = chargingSessionManager.getAverageScreenOnSpeed()
        _averageScreenOffSpeed.value = chargingSessionManager.getAverageScreenOffSpeed()
        _averageSpeed.value = chargingSessionManager.getAverageSpeed()
        _averageScreenOnMilliAmperes.value = chargingSessionManager.getAverageScreenOnMilliAmperes()
        _averageScreenOffMilliAmperes.value = chargingSessionManager.getAverageScreenOffMilliAmperes()
        _averageMilliAmperes.value = chargingSessionManager.getAverageMilliAmperes()
    }

    private fun updateAverageDischargeSpeeds() {
        _averageScreenOnSpeed.value = dischargeSessionManager.getAverageScreenOnSpeed()
        _averageScreenOffSpeed.value = dischargeSessionManager.getAverageScreenOffSpeed()
        _averageSpeed.value = dischargeSessionManager.getAverageSpeed()
        _averageScreenOnMilliAmperes.value = dischargeSessionManager.getAverageScreenOnMilliAmperes()
        _averageScreenOffMilliAmperes.value = dischargeSessionManager.getAverageScreenOffMilliAmperes()
        _averageMilliAmperes.value = dischargeSessionManager.getAverageMilliAmperes()
    }

    private fun isProcessorIdle(): Boolean {
        // Get CPU usage information
        val cpuUsage = getCpuUsage()
        // Consider processor idle if CPU usage is below 5%
        return cpuUsage < 5.0
    }

    private fun getCpuUsage(): Double {
        try {
            val process = Runtime.getRuntime().exec("top -n 1")
            val reader = BufferedReader(InputStreamReader(process.inputStream))
            var line: String?
            var totalCpuUsage = 0.0
            var count = 0

            while (reader.readLine().also { line = it } != null) {
                if (line?.contains("CPU usage:") == true) {
                    val parts = line?.split(" ")?.filter { it.isNotEmpty() }
                    parts?.forEach { part ->
                        if (part.endsWith("%")) {
                            totalCpuUsage += part.removeSuffix("%").toDoubleOrNull() ?: 0.0
                            count++
                        }
                    }
                }
            }

            reader.close()
            process.destroy()

            return if (count > 0) totalCpuUsage / count else 0.0
        } catch (e: Exception) {
            return 0.0
        }
    }

    private fun calculateTotalChargeSessions(): Int {
        var totalSessions = 0
        val sessions = chargingSessionManager.getAllSessions()
        totalSessions = sessions.size
        return totalSessions
    }

    private fun pruneHistory() {
        val cutoff = System.currentTimeMillis() - 24 * 60 * 60 * 1000
        batteryHistory.removeAll { it.first < cutoff }
        temperatureHistory.removeAll { it.first < cutoff }
    }

    private fun addHistory(percent: Int, temp: Double) {
        val now = System.currentTimeMillis()
        // Only add history entry if a minute has passed since the last entry
        val lastBatteryEntry = batteryHistoryManager.getMinuteHistory(1).lastOrNull()
        val lastTempEntry = temperatureHistoryManager.getMinuteHistory(1).lastOrNull()
        
        if (lastBatteryEntry == null || now - lastBatteryEntry.timestamp >= 60000) {
            batteryHistoryManager.addEntry(now, percent)
        }
        
        if (lastTempEntry == null || now - lastTempEntry.timestamp >= 60000) {
            temperatureHistoryManager.addEntry(now, temp)
        }
    }

    fun getHistoryBatteryForHours(hours: Int): List<HistoryEntry<Int>> {
        return batteryHistoryManager.getHistory(hours)
    }

    fun getHistoryTemperatureForHours(hours: Int): List<HistoryEntry<Double>> {
        return temperatureHistoryManager.getHistory(hours)
    }

    fun getHistoryBatteryForMinutes(minutes: Int): List<HistoryEntry<Int>> {
        return batteryHistoryManager.getMinuteHistory(minutes)
    }

    fun getHistoryTemperatureForMinutes(minutes: Int): List<HistoryEntry<Double>> {
        return temperatureHistoryManager.getMinuteHistory(minutes)
    }

    fun getDailyWearData(days: Int): List<Double> {
        val calendar = Calendar.getInstance()
        val currentTime = calendar.timeInMillis
        val wearData = mutableListOf<Double>()

        // Get all charge sessions
        val sessions = chargingSessionManager.getAllSessions()

        // Calculate wear for each day
        for (i in 0 until days) {
            calendar.timeInMillis = currentTime - (i * 24 * 60 * 60 * 1000)
            calendar.set(Calendar.HOUR_OF_DAY, 0)
            calendar.set(Calendar.MINUTE, 0)
            calendar.set(Calendar.SECOND, 0)
            calendar.set(Calendar.MILLISECOND, 0)
            val dayStart = calendar.timeInMillis
            calendar.add(Calendar.DAY_OF_MONTH, 1)
            val dayEnd = calendar.timeInMillis

            // Filter sessions for this day
            val daySessions = sessions.filter { it.startTime in dayStart until dayEnd }

            // Calculate total wear for the day
            val dayWear = daySessions.sumOf { session ->
                BatteryUtils.calculateWearCycles(session.startPercent, session.endPercent)
            }

            wearData.add(0, dayWear) // Add to start of list to maintain chronological order
        }

        return wearData
    }

    fun getBatteryStatus(): Intent? {
        return context.registerReceiver(null, IntentFilter(Intent.ACTION_BATTERY_CHANGED))
    }
} 