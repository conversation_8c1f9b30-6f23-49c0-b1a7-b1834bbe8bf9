<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_emoji_battery" modulePackage="com.tqhit.battery.one" filePath="app\src\main\res\layout\fragment_emoji_battery.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/fragment_emoji_battery_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="315" endOffset="53"/></Target><Target id="@+id/scrollView" view="androidx.core.widget.NestedScrollView"><Expressions/><location startLine="10" startOffset="4" endLine="226" endOffset="43"/></Target><Target id="@+id/featureToggleCard" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="23" startOffset="12" endLine="118" endOffset="63"/></Target><Target id="@+id/featureTitle" view="TextView"><Expressions/><location startLine="51" startOffset="28" endLine="57" endOffset="74"/></Target><Target id="@+id/featureDescription" view="TextView"><Expressions/><location startLine="59" startOffset="28" endLine="66" endOffset="81"/></Target><Target id="@+id/featureToggleSwitch" view="com.google.android.material.switchmaterial.SwitchMaterial"><Expressions/><location startLine="70" startOffset="24" endLine="74" endOffset="63"/></Target><Target id="@+id/permissionStatusLayout" view="LinearLayout"><Expressions/><location startLine="79" startOffset="20" endLine="114" endOffset="34"/></Target><Target id="@+id/permissionStatusIcon" view="ImageView"><Expressions/><location startLine="88" startOffset="24" endLine="94" endOffset="57"/></Target><Target id="@+id/permissionStatusText" view="TextView"><Expressions/><location startLine="96" startOffset="24" endLine="104" endOffset="66"/></Target><Target id="@+id/requestPermissionsButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="106" startOffset="24" endLine="112" endOffset="53"/></Target><Target id="@+id/filterSection" view="LinearLayout"><Expressions/><location startLine="121" startOffset="12" endLine="189" endOffset="26"/></Target><Target id="@+id/filterChipGroup" view="com.google.android.material.chip.ChipGroup"><Expressions/><location startLine="129" startOffset="16" endLine="166" endOffset="60"/></Target><Target id="@+id/chipAll" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="137" startOffset="20" endLine="143" endOffset="59"/></Target><Target id="@+id/chipPopular" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="145" startOffset="20" endLine="150" endOffset="56"/></Target><Target id="@+id/chipFree" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="152" startOffset="20" endLine="157" endOffset="53"/></Target><Target id="@+id/chipPremium" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="159" startOffset="20" endLine="164" endOffset="56"/></Target><Target id="@+id/searchInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="169" startOffset="16" endLine="187" endOffset="71"/></Target><Target id="@+id/searchEditText" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="179" startOffset="20" endLine="185" endOffset="46"/></Target><Target id="@+id/categoryScrollView" view="HorizontalScrollView"><Expressions/><location startLine="192" startOffset="12" endLine="211" endOffset="34"/></Target><Target id="@+id/categoryChipGroup" view="com.google.android.material.chip.ChipGroup"><Expressions/><location startLine="199" startOffset="16" endLine="209" endOffset="60"/></Target><Target id="@+id/stylesRecyclerView" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="214" startOffset="12" endLine="222" endOffset="37"/></Target><Target id="@+id/loadingOverlay" view="FrameLayout"><Expressions/><location startLine="229" startOffset="4" endLine="261" endOffset="17"/></Target><Target id="@+id/loadingProgressBar" view="com.google.android.material.progressindicator.CircularProgressIndicator"><Expressions/><location startLine="244" startOffset="12" endLine="248" endOffset="46"/></Target><Target id="@+id/loadingText" view="TextView"><Expressions/><location startLine="250" startOffset="12" endLine="257" endOffset="58"/></Target><Target id="@+id/emptyStateLayout" view="LinearLayout"><Expressions/><location startLine="264" startOffset="4" endLine="309" endOffset="18"/></Target><Target id="@+id/emptyStateIcon" view="ImageView"><Expressions/><location startLine="275" startOffset="8" endLine="280" endOffset="52"/></Target><Target id="@+id/emptyStateTitle" view="TextView"><Expressions/><location startLine="282" startOffset="8" endLine="289" endOffset="54"/></Target><Target id="@+id/emptyStateMessage" view="TextView"><Expressions/><location startLine="291" startOffset="8" endLine="299" endOffset="61"/></Target><Target id="@+id/retryButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="301" startOffset="8" endLine="307" endOffset="42"/></Target></Targets></Layout>