package com.tqhit.battery.one.features.stats.discharge.domain

import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class DischargeCalculator @Inject constructor() {
    companion object {
        const val DEFAULT_AVG_SCREEN_ON_CURRENT_MA = 250.0 // mA
        const val DEFAULT_AVG_SCREEN_OFF_CURRENT_MA = 50.0  // mA
        const val MIXED_USAGE_SCREEN_ON_WEIGHT = 0.4
        const val MIXED_USAGE_SCREEN_OFF_WEIGHT = 0.6
        const val DEFAULT_EFFECTIVE_CAPACITY_MAH = 3000.0 // Default capacity to use if not available
    }

    /**
     * Estimates remaining time in milliseconds based on current capacity and discharge rate
     */
    fun estimateTimeRemainingMillis(currentCapacityMah: Double, averageDischargeRateMah: Double): Long {
        if (averageDischargeRateMah <= 0.001 || currentCapacityMah <= 0) return 0L // Avoid division by zero or tiny rates
        val hours = currentCapacityMah / averageDischargeRateMah
        return (hours * 3600.0 * 1000.0).toLong()
    }
    
    /**
     * Calculates the mixed discharge rate based on screen on and screen off rates
     */
    fun calculateMixedDischargeRate(screenOnRateMah: Double, screenOffRateMah: Double): Double {
        return (screenOnRateMah * MIXED_USAGE_SCREEN_ON_WEIGHT) + 
               (screenOffRateMah * MIXED_USAGE_SCREEN_OFF_WEIGHT)
    }
    
    /**
     * Calculates current capacity in mAh based on battery percentage and total capacity
     */
    fun calculateCurrentCapacityMah(batteryPercentage: Int, totalCapacityMah: Double): Double {
        return (batteryPercentage / 100.0) * totalCapacityMah
    }
    
    /**
     * Determines if an update should be skipped based on time and percentage change
     */
    fun shouldSkipUpdate(timeSinceLastUpdateMs: Long, currentPercentage: Int, lastPercentage: Int): Boolean {
        return timeSinceLastUpdateMs < 1000 && currentPercentage == lastPercentage
    }
}
