package com.tqhit.battery.one.features.stats.discharge.cache

import android.content.Context
import com.google.gson.Gson
import com.tqhit.battery.one.features.stats.discharge.data.DischargeSessionData
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class PrefsCurrentSessionCache @Inject constructor(
    @ApplicationContext private val context: Context,
    private val gson: Gson
) : CurrentSessionCache {
    
    private val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    
    companion object {
        private const val PREFS_NAME = "discharge_session_cache"
        private const val KEY_CURRENT_SESSION = "current_session"
    }
    
    override suspend fun getCurrentSession(): DischargeSessionData? = withContext(Dispatchers.IO) {
        val json = prefs.getString(KEY_CURRENT_SESSION, null)
        json?.let { gson.fromJson(it, DischargeSessionData::class.java) }
    }
    
    override suspend fun saveCurrentSession(session: DischargeSessionData) = withContext(Dispatchers.IO) {
        val json = gson.toJson(session)
        prefs.edit().putString(KEY_CURRENT_SESSION, json).apply()
    }
    
    override suspend fun clearCurrentSession() = withContext(Dispatchers.IO) {
        prefs.edit().remove(KEY_CURRENT_SESSION).apply()
    }
}
