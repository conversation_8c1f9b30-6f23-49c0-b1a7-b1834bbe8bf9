#!/bin/bash

# Dynamic Navigation Testing Script
# This script tests the dynamic fragment switching functionality using ADB commands

PACKAGE_NAME="com.fc.p.tj.charginganimation.batterycharging.chargeeffect"
ACTIVITY_NAME="com.tqhit.battery.one.activity.main.MainActivity"
LOG_TAG="DynamicNavigationTest"

echo "=== Dynamic Navigation Testing Script ==="
echo "Package: $PACKAGE_NAME"
echo "Activity: $ACTIVITY_NAME"
echo ""

# Function to check if device is connected
check_device() {
    if ! adb devices | grep -q "device$"; then
        echo "❌ No device connected. Please connect a device or start an emulator."
        exit 1
    fi
    echo "✅ Device connected"
}

# Function to install the app
install_app() {
    echo "📱 Installing app..."
    if adb install -r app/build/outputs/apk/debug/app-debug.apk 2>/dev/null; then
        echo "✅ App installed successfully"
    else
        echo "⚠️  App installation failed or app already installed"
    fi
}

# Function to start the app
start_app() {
    echo "🚀 Starting app..."
    adb shell am start -n "$PACKAGE_NAME/$ACTIVITY_NAME"
    sleep 3
    echo "✅ App started"
}

# Function to monitor logs
start_log_monitoring() {
    echo "📋 Starting log monitoring..."
    echo "Monitoring logs for: CoreBatteryStatsService, DynamicNavigationManager, MainActivity"
    echo "Press Ctrl+C to stop monitoring"
    echo ""
    
    adb logcat -c  # Clear existing logs
    adb logcat | grep -E "(CoreBatteryStatsService|DynamicNavigationManager|MainActivity|$LOG_TAG)" &
    LOG_PID=$!
}

# Function to stop log monitoring
stop_log_monitoring() {
    if [ ! -z "$LOG_PID" ]; then
        kill $LOG_PID 2>/dev/null
        echo "📋 Log monitoring stopped"
    fi
}

# Function to simulate charging
simulate_charging() {
    echo "🔌 Simulating charging state..."
    adb shell dumpsys battery set ac 1
    adb shell dumpsys battery set status 2  # BATTERY_STATUS_CHARGING
    echo "✅ Charging simulation enabled"
    sleep 2
}

# Function to simulate discharging
simulate_discharging() {
    echo "🔋 Simulating discharging state..."
    adb shell dumpsys battery set ac 0
    adb shell dumpsys battery set status 3  # BATTERY_STATUS_NOT_CHARGING
    echo "✅ Discharging simulation enabled"
    sleep 2
}

# Function to reset battery simulation
reset_battery_simulation() {
    echo "🔄 Resetting battery simulation..."
    adb shell dumpsys battery reset
    echo "✅ Battery simulation reset"
    sleep 2
}

# Function to get current battery status
get_battery_status() {
    echo "🔋 Current battery status:"
    adb shell dumpsys battery | grep -E "(AC powered|status|level)"
}

# Function to test charging state switching
test_charging_state_switching() {
    echo ""
    echo "=== Testing Charging State Switching ==="
    
    # Test 1: Start with discharging
    echo "Test 1: Starting with discharging state"
    simulate_discharging
    start_app
    sleep 3
    echo "✅ App should show discharge fragment"
    
    # Test 2: Switch to charging
    echo ""
    echo "Test 2: Switching to charging state"
    simulate_charging
    sleep 3
    echo "✅ App should switch to charge fragment"
    
    # Test 3: Switch back to discharging
    echo ""
    echo "Test 3: Switching back to discharging state"
    simulate_discharging
    sleep 3
    echo "✅ App should switch back to discharge fragment"
    
    # Test 4: Reset to normal
    echo ""
    echo "Test 4: Resetting to normal battery state"
    reset_battery_simulation
    sleep 3
    echo "✅ App should show fragment based on actual battery state"
}

# Function to test app resume behavior
test_app_resume() {
    echo ""
    echo "=== Testing App Resume Behavior ==="
    
    # Set charging state
    simulate_charging
    start_app
    sleep 2
    
    # Send app to background
    echo "📱 Sending app to background..."
    adb shell am start -a android.intent.action.MAIN -c android.intent.category.HOME
    sleep 2
    
    # Change charging state while in background
    echo "🔄 Changing charging state while app is in background..."
    simulate_discharging
    sleep 2
    
    # Bring app back to foreground
    echo "📱 Bringing app back to foreground..."
    adb shell am start -n "$PACKAGE_NAME/$ACTIVITY_NAME"
    sleep 3
    
    echo "✅ App should show correct fragment based on new charging state"
}

# Function to run unit tests
run_unit_tests() {
    echo ""
    echo "=== Running Unit Tests ==="
    echo "📋 Running DynamicNavigationManager tests..."
    
    adb shell am instrument -w -e class com.tqhit.battery.one.features.navigation.DynamicNavigationManagerTest \
        "$PACKAGE_NAME.test/androidx.test.runner.AndroidJUnitRunner"
    
    echo "📋 Running NavigationState tests..."
    adb shell am instrument -w -e class com.tqhit.battery.one.features.navigation.NavigationStateTest \
        "$PACKAGE_NAME.test/androidx.test.runner.AndroidJUnitRunner"
}

# Function to run integration tests
run_integration_tests() {
    echo ""
    echo "=== Running Integration Tests ==="
    echo "📋 Running dynamic navigation integration tests..."
    
    adb shell am instrument -w -e class com.tqhit.battery.one.features.navigation.DynamicNavigationIntegrationTest \
        "$PACKAGE_NAME.test/androidx.test.runner.AndroidJUnitRunner"
}

# Function to cleanup
cleanup() {
    echo ""
    echo "🧹 Cleaning up..."
    stop_log_monitoring
    reset_battery_simulation
    echo "✅ Cleanup completed"
}

# Trap to ensure cleanup on script exit
trap cleanup EXIT

# Main execution
main() {
    echo "Starting dynamic navigation tests..."
    echo ""
    
    check_device
    install_app
    
    # Start log monitoring in background
    start_log_monitoring
    
    # Get initial battery status
    get_battery_status
    
    # Run tests
    test_charging_state_switching
    test_app_resume
    
    # Ask user if they want to run automated tests
    echo ""
    read -p "Do you want to run unit tests? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        run_unit_tests
    fi
    
    echo ""
    read -p "Do you want to run integration tests? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        run_integration_tests
    fi
    
    echo ""
    echo "=== Test Summary ==="
    echo "✅ Dynamic navigation tests completed"
    echo "📋 Check the logs above for detailed results"
    echo "🔋 Battery simulation has been reset"
    echo ""
    echo "Manual testing suggestions:"
    echo "1. Try plugging/unplugging the device charger"
    echo "2. Navigate between different fragments"
    echo "3. Send app to background and resume"
    echo "4. Check that only appropriate navigation items are visible"
}

# Run main function
main "$@"
