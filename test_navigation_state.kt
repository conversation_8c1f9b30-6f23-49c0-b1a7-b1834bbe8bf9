// Simple test to verify NavigationState compilation and basic functionality
import com.tqhit.battery.one.features.navigation.NavigationState
import com.tqhit.battery.one.features.navigation.StateChangeReason
import com.tqhit.battery.one.R

fun main() {
    println("Testing NavigationState compilation and basic functionality...")
    
    // Test createDefaultState
    val defaultState = NavigationState.createDefaultState()
    println("✓ createDefaultState() works: activeFragmentId=${defaultState.activeFragmentId}, isCharging=${defaultState.isCharging}")
    
    // Test createChargingState
    val chargingState = NavigationState.createChargingState()
    println("✓ createChargingState() works: activeFragmentId=${chargingState.activeFragmentId}, isCharging=${chargingState.isCharging}")
    
    // Test createDischargingState
    val dischargingState = NavigationState.createDischargingState()
    println("✓ createDischargingState() works: activeFragmentId=${dischargingState.activeFragmentId}, isCharging=${dischargingState.isCharging}")
    
    // Test createAnimationState
    val animationState = NavigationState.createAnimationState(isCharging = true)
    println("✓ createAnimationState() works: activeFragmentId=${animationState.activeFragmentId}, isCharging=${animationState.isCharging}")
    
    // Test state methods
    println("✓ isChargingState(): ${chargingState.isChargingState()}")
    println("✓ isDischargingState(): ${dischargingState.isDischargingState()}")
    println("✓ isMenuItemVisible(): ${chargingState.isMenuItemVisible(R.id.chargeFragment)}")
    
    // Test StateChangeReason enum
    val reason = StateChangeReason.CHARGING_STARTED
    println("✓ StateChangeReason enum works: $reason")
    
    println("\n🎉 All NavigationState tests passed! Compilation error is fixed.")
}
