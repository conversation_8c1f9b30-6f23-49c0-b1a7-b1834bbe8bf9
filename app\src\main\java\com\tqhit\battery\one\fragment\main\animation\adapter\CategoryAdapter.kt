package com.tqhit.battery.one.fragment.main.animation.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.tqhit.battery.one.R
import com.tqhit.battery.one.fragment.main.animation.data.AnimationCategory

class CategoryAdapter(
    private val categories: List<AnimationCategory>,
    private var selectedIndex: Int,
    private val onCategoryClick: (Int) -> Unit
) : RecyclerView.Adapter<CategoryAdapter.CategoryViewHolder>() {
    fun setSelectedIndex(index: Int) {
        selectedIndex = index
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): CategoryViewHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.item_category, parent, false)
        return CategoryViewHolder(view)
    }
    override fun onBindViewHolder(holder: CategoryViewHolder, position: Int) {
        holder.bind(categories[position].name, position == selectedIndex)
        holder.itemView.setOnClickListener { onCategoryClick(position) }
    }
    override fun getItemCount(): Int = categories.size
    class CategoryViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        fun bind(name: String, selected: Boolean) {
            val background = itemView.findViewById<ConstraintLayout>(R.id.categoryBlock)
            val tv = itemView.findViewById<TextView>(R.id.categoryName)
            tv.text = name
            background.background =
                ContextCompat.getDrawable(
                    itemView.context,
                    if (selected) R.drawable.grey_block_selected_line_up
                    else R.drawable.grey_block_line_up
                )
        }
    }
}