package com.tqhit.battery.one.service

import android.content.Context
import android.os.Build
import android.os.VibrationEffect
import android.os.Vibrator
import android.os.VibratorManager
import android.provider.Settings
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class VibrationService @Inject constructor(
    @ApplicationContext private val context: Context
) {
    companion object {
        // Duration constants
        const val VIBRATION_DURATION_SHORT = 50L
        const val VIBRATION_DURATION_MEDIUM = 100L
        const val VIBRATION_DURATION_LONG = 200L
        
        // Amplitude constants
        const val AMPLITUDE_LIGHT = VibrationEffect.DEFAULT_AMPLITUDE / 2
        const val AMPLITUDE_MEDIUM = VibrationEffect.DEFAULT_AMPLITUDE
        const val AMPLITUDE_HEAVY = VibrationEffect.DEFAULT_AMPLITUDE * 2

        // Common vibration patterns
        val PATTERN_CLICK = longArrayOf(0, VIBRATION_DURATION_SHORT)
        val PATTERN_DOUBLE_CLICK = longArrayOf(0, VIBRATION_DURATION_SHORT, 100, VIBRATION_DURATION_SHORT)
        val PATTERN_TICK = longArrayOf(0, VIBRATION_DURATION_SHORT)
        val PATTERN_ERROR = longArrayOf(0, VIBRATION_DURATION_MEDIUM, 100, VIBRATION_DURATION_MEDIUM)
        val PATTERN_SUCCESS = longArrayOf(0, VIBRATION_DURATION_SHORT, 100, VIBRATION_DURATION_MEDIUM)
        val PATTERN_WARNING = longArrayOf(0, VIBRATION_DURATION_MEDIUM, 100, VIBRATION_DURATION_SHORT, 100, VIBRATION_DURATION_MEDIUM)
    }

    private val vibrator by lazy {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            val vibratorManager = context.getSystemService(Context.VIBRATOR_MANAGER_SERVICE) as VibratorManager
            vibratorManager.defaultVibrator
        } else {
            @Suppress("DEPRECATION")
            context.getSystemService(Context.VIBRATOR_SERVICE) as Vibrator
        }
    }

    /**
     * Check if vibration is enabled in system settings
     */
    fun isVibrationEnabled(): Boolean {
        return try {
            Settings.System.getInt(context.contentResolver, Settings.System.VIBRATE_WHEN_RINGING) == 1
        } catch (e: Settings.SettingNotFoundException) {
            // If setting not found, assume vibration is enabled
            true
        }
    }

    /**
     * Check if device has vibrator capability
     */
    fun hasVibrator(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            val vibratorManager = context.getSystemService(Context.VIBRATOR_MANAGER_SERVICE) as VibratorManager
            vibratorManager.defaultVibrator.hasVibrator()
        } else {
            @Suppress("DEPRECATION")
            (context.getSystemService(Context.VIBRATOR_SERVICE) as Vibrator).hasVibrator()
        }
    }

    /**
     * Check if vibration is available (both device capability and system settings)
     */
    fun isVibrationAvailable(): Boolean {
        return hasVibrator() && isVibrationEnabled()
    }

    /**
     * Simple vibration with custom duration and amplitude
     */
    fun vibrate(duration: Long = VIBRATION_DURATION_SHORT, amplitude: Int = AMPLITUDE_MEDIUM) {
        if (!isVibrationAvailable()) return
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            vibrator.vibrate(VibrationEffect.createOneShot(duration, amplitude))
        } else {
            @Suppress("DEPRECATION")
            vibrator.vibrate(duration)
        }
    }

    /**
     * Vibrate with a custom pattern
     */
    fun vibratePattern(pattern: LongArray, repeat: Int = -1) {
        if (!isVibrationAvailable()) return
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            vibrator.vibrate(VibrationEffect.createWaveform(pattern, repeat))
        } else {
            @Suppress("DEPRECATION")
            vibrator.vibrate(pattern, repeat)
        }
    }

    /**
     * Vibrate with predefined patterns
     */
    fun vibrateClick() = vibratePattern(PATTERN_CLICK)
    fun vibrateDoubleClick() = vibratePattern(PATTERN_DOUBLE_CLICK)
    fun vibrateTick() = vibratePattern(PATTERN_TICK)
    fun vibrateError() = vibratePattern(PATTERN_ERROR)
    fun vibrateSuccess() = vibratePattern(PATTERN_SUCCESS)
    fun vibrateWarning() = vibratePattern(PATTERN_WARNING)

    /**
     * Create a custom vibration effect with varying amplitudes
     */
    fun vibrateCustomEffect(timings: LongArray, amplitudes: IntArray, repeat: Int = -1) {
        if (!isVibrationAvailable()) return
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            vibrator.vibrate(VibrationEffect.createWaveform(timings, amplitudes, repeat))
        } else {
            @Suppress("DEPRECATION")
            vibrator.vibrate(timings, repeat)
        }
    }

    /**
     * Cancel any ongoing vibration
     */
    fun cancel() {
        vibrator.cancel()
    }
} 