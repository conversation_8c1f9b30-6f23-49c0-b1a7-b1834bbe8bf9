## Codebase Summary: Battery Charging Animation 3D
Bundle id: com.tqhit.battery.one

### 1. Overall Purpose

The application is a comprehensive battery utility designed to provide users with detailed, real-time statistics and historical data about their device's battery. The core of the application has been refactored into a unified, modular **"stats" architecture** (`features/stats`) centered around a single, reliable service (`CoreBatteryStatsService`) for all battery monitoring. This modern approach ensures data consistency across features like charging/discharging analysis, battery health monitoring, and system notifications.

Beyond statistics, the app offers aesthetic enhancements like customizable charging animations, security features like anti-theft protection, and configurable battery alarms. The goal is to empower users with a deep understanding and greater control over their device's battery performance and longevity through a clean, efficient, and feature-rich interface.

### 2. Key Features

*   **Unified Battery Stats Module (`features/stats`):** This is the new core of the application, providing a cohesive set of features based on a single source of battery data.
    *   **Core Monitoring (`features/stats/corebattery`):**
        *   A single `CoreBatteryStatsService` runs in the foreground, acting as the sole monitor for `ACTION_BATTERY_CHANGED`. It extracts raw battery data (percentage, charging state, current, voltage, temperature) into a `CoreBatteryStatus` object.
        *   The `CoreBatteryStatsProvider` serves this data as a reactive `StateFlow`, ensuring all modules have access to consistent, real-time information.
    *   **Dynamic Navigation (`features/navigation`):**
        *   A `DynamicNavigationManager` intelligently switches the main view between the `StatsChargeFragment` and `DischargeFragment` based on the device's real-time charging state from `CoreBatteryStatsProvider`, providing a seamless user experience.
    *   **Charging Stats (`features/stats/charge`):**
        *   `StatsChargeFragment` and `StatsChargeViewModel` display real-time charging metrics.
        *   `StatsChargeRepository` manages charging sessions and calculates estimates for "Time to Full" and "Time to Target" using `CalculateSimpleChargeEstimateUseCase`.
    *   **Discharging Stats (`features/stats/discharge`):**
        *   `DischargeFragment` and `DischargeViewModel` provide detailed discharge analysis.
        *   The module tracks screen-on vs. screen-off time and consumption (`ScreenStateTimeTracker`, `DischargeRateCalculator`).
        *   It offers robust time estimations based on learned discharge rates and handles app restarts gracefully with a `GapEstimationCalculator` to fill in missing data.
        *   **App Power Consumption (`features/stats/apppower`):** A dialog launched from the discharge screen estimates power usage per application for the current session, requiring `PACKAGE_USAGE_STATS` permission.
    *   **Battery Health (`features/stats/health`):**
        *   `HealthFragment` and `HealthViewModel` display a comprehensive battery health overview.
        *   Health percentage is calculated based on total charge cycles (`CalculateBatteryHealthUseCase`).
        *   Historical charts visualize battery percentage and temperature over time, using data collected by `HistoryBatteryRepository` from the `CoreBatteryStatsService`.
    *   **Unified Notifications (`features/stats/notifications`):**
        *   A single `UnifiedBatteryNotificationService` handles all battery-related alerts (charge target, full charge, charge started/stopped, anti-theft).
        *   It consumes data from `CoreBatteryStatsProvider` and uses an adaptive update frequency to optimize battery usage.

*   **Charging Animations:**
    *   Allows users to select and apply full-screen video animations that play during charging (`AnimationActivity`, `ChargingOverlayService`).
    *   Animations are sourced from Firebase Remote Config (`animation_json`) and managed by `AnimationRepository`.
    *   Includes a trial system for premium animations.

*   **Customization & Personalization:**
    *   **Themes:** Full theme engine (`ThemeManager`) supporting Light, Dark, Amoled, Grey, and inverted variants.
    *   **Colors:** Accent color selection for UI elements.
    *   **Language:** Dynamic language switching within the app.

*   **Anti-Theft Protection:**
    *   Handled by `UnifiedBatteryNotificationService`, it triggers an alarm if the charger is disconnected while the feature is active.
    *   Requires a user-set password to disable the alarm.

*   **Onboarding & Permissions:**
    *   `StartingActivity` guides new users through privacy policy, notification permissions, and initial setup.
    *   Includes a dedicated `BackgroundPermissionDialog` with rate-limiting to prompt for battery optimization permissions.

*   **Planned Features:**
    *   **Emoji Battery (`features/emoji`):** Project structure and DI module are in place for a future feature allowing users to customize their status bar battery icon with emojis.

### 3. Project Structure

The project has been refactored to a modular architecture, with a central `stats` feature module. Legacy components have been deprecated.

```
├── java/com/tqhit/battery/one/
│   ├── activity/
│   ├── ads/core/
│   ├── dialog/
│   ├── features/
│   │   ├── emoji/                 # (Planned) Emoji Battery feature
│   │   ├── navigation/            # Dynamic navigation manager
│   │   └── stats/                 # NEW: Unified stats module
│   │       ├── apppower/          # App power consumption
│   │       ├── charge/            # Charging stats
│   │       ├── corebattery/       # Single source of truth for battery data
│   │       ├── discharge/         # Discharging stats
│   │       ├── health/            # Battery health feature
│   │       └── notifications/     # Unified notification service
│   ├── fragment/main/
│   ├── manager/                   # Legacy session managers
│   ├── repository/                # General/Legacy repositories
│   ├── service/                   # General/Legacy services
│   ├── utils/
│   ├── viewmodel/                 # Legacy/General ViewModels
│   └── BatteryApplication.kt
├── res/
│   ├── layout/                    # Modular section layouts for stats screens
...
```

### 4. File Breakdown and Summaries

#### 4.1. Core Application & Setup

*   **`BatteryApplication.kt`**: Initializes the application, ThemeManager, and language settings. Starts the `CoreBatteryServiceHelper`.
*   **`AndroidManifest.xml`**: Declares components, permissions (including `PACKAGE_USAGE_STATS`). The launcher is `SplashActivity`. `CoreBatteryStatsService`, `UnifiedBatteryNotificationService`, and `EnhancedDischargeTimerService` are the primary active services.

#### 4.2. Activities

*   **`MainActivity.kt`**: Main UI host. **Crucially, it now uses `DynamicNavigationManager`** to automatically switch between `StatsChargeFragment` and `DischargeFragment` based on the charging state, providing a seamless user experience. It also starts the `UnifiedBatteryNotificationService`.
*   **`SplashActivity.kt`**: Checks if onboarding (`StartingActivity`) is needed.
*   **`StartingActivity.kt`**: Guides new users through initial setup and permissions.

#### 4.3. Fragments

*   **`StatsChargeFragment.kt`**: The **primary fragment for charging information**, using `StatsChargeViewModel` to display real-time data from the new unified architecture.
*   **`DischargeFragment.kt`**: The **primary fragment for discharge information**, using `DischargeViewModel` to display detailed stats, including mAh-based consumption and learned-rate estimates.
*   **`HealthFragment.kt`**: Displays battery health, historical charts, and daily wear. **It now uses `HealthViewModel`** and gets its data from the new `HealthRepository` and `HistoryBatteryRepository`.
*   **`SettingsFragment.kt`**: UI for all user-configurable settings.
*   **`AnimationGridFragment.kt`**: Displays a grid of available charging animations.

#### 4.4. ViewModels

*   **`StatsChargeViewModel.kt`**: Manages UI state for `StatsChargeFragment`.
*   **`DischargeViewModel.kt`**: Manages UI state for `DischargeFragment`.
*   **`HealthViewModel.kt`**: Manages UI state for `HealthFragment`.
*   **`AppViewModel.kt`**: Manages app-wide settings.
*   **`AnimationViewModel.kt`**: Manages animation states.
*   `BatteryViewModel.kt` (legacy): Still used by some older components but is being phased out.

#### 4.5. Repositories & Data Sources

*   **`CoreBatteryStatsProvider.kt`**: The **single source of truth** for battery data, providing a `StateFlow<CoreBatteryStatus>`.
*   **`StatsChargeRepository.kt`**: Consumes `CoreBatteryStatus` and manages charging sessions (`StatsChargeSession`).
*   **`DischargeSessionRepository.kt`**: Consumes `CoreBatteryStatus` and manages the complex state of a discharge session, including screen on/off time and gap estimation.
*   **`HealthRepository.kt`**: Calculates `HealthStatus` based on charge cycle history and provides chart data.
*   **`HistoryBatteryRepository.kt`**: Collects and persists historical battery and temperature data from `CoreBatteryStatsProvider` for use in charts.
*   **`AppPowerConsumptionRepository.kt`**: Retrieves and processes app usage stats to estimate power consumption.

#### 4.6. Services

*   **`CoreBatteryStatsService.kt`**: The **only service that listens for system battery broadcasts**. It runs in the foreground and provides consistent `CoreBatteryStatus` data to the entire app via `CoreBatteryStatsProvider`.
*   **`UnifiedBatteryNotificationService.kt`**: The **single service for handling all user-facing notifications** related to battery state (full, target, charging/not charging, anti-theft). It consumes data from the unified provider.
*   **`EnhancedDischargeTimerService.kt`**: A background service that ensures accurate screen on/off time tracking for the discharge module, even when the app is not in the foreground.
*   `BatteryMonitorService.kt` (legacy): Calls to start this service have been removed from `MainActivity`.

#### 4.7. Ad Management (`ads/core/`)

*   Managers for AppLovin ad formats are present, though the core ad-showing logic appears to be currently commented out. Remote Config is used to control ad behavior.