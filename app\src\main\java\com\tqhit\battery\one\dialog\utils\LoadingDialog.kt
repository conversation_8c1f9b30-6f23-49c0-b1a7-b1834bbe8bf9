package com.tqhit.battery.one.dialog.utils

import android.content.Context
import android.graphics.Color
import android.view.ViewGroup
import android.view.WindowManager
import androidx.core.graphics.drawable.toDrawable
import com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog
import com.tqhit.battery.one.databinding.DialogLoadingBinding

class LoadingDialog(
    context: Context,
    private val message: String = ""
) : AdLibBaseDialog<DialogLoadingBinding>(context) {
    override val binding by lazy { DialogLoadingBinding.inflate(layoutInflater) }

    override fun initWindow() {
        super.initWindow()
        setCanceledOnTouchOutside(false)
        setCancelable(false)
    }

    override fun setupUI() {
        super.setupUI()
        binding.loadingMessage.text = message
    }
} 