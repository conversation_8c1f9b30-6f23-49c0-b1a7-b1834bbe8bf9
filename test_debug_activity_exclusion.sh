#!/bin/bash

# Test script to verify DebugActivity exclusion from both debug and release builds
# This script verifies that DebugActivity is properly excluded from both build variants

echo "=========================================="
echo "DebugActivity Exclusion Verification Test"
echo "=========================================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Package name
PACKAGE_NAME="com.fc.p.tj.charginganimation.batterycharging.chargeeffect"
DEBUG_ACTIVITY="com.tqhit.battery.one.activity.debug.DebugActivity"

# Function to check if APK exists
check_apk_exists() {
    local apk_path=$1
    local build_type=$2
    
    if [ -f "$apk_path" ]; then
        echo -e "${GREEN}✅ $build_type APK found: $apk_path${NC}"
        return 0
    else
        echo -e "${RED}❌ $build_type APK not found: $apk_path${NC}"
        return 1
    fi
}

# Function to check if DebugActivity is in manifest
check_debug_activity_in_manifest() {
    local apk_path=$1
    local build_type=$2
    
    echo "🔍 Checking $build_type APK manifest for DebugActivity..."
    
    # Extract and check manifest
    if command -v aapt &> /dev/null; then
        local manifest_content=$(aapt dump xmltree "$apk_path" AndroidManifest.xml 2>/dev/null | grep -i "debugactivity" || true)
        
        if [ -z "$manifest_content" ]; then
            echo -e "${GREEN}✅ DebugActivity NOT found in $build_type manifest (CORRECT)${NC}"
            return 0
        else
            echo -e "${RED}❌ DebugActivity found in $build_type manifest (INCORRECT)${NC}"
            echo "   Found: $manifest_content"
            return 1
        fi
    else
        echo -e "${YELLOW}⚠️  aapt not found, skipping manifest check for $build_type${NC}"
        return 0
    fi
}

# Function to test ADB launch (should fail)
test_adb_launch() {
    local build_type=$1
    
    echo "🚀 Testing ADB launch of DebugActivity in $build_type build..."
    
    if command -v adb &> /dev/null; then
        # Try to start the DebugActivity (should fail)
        local result=$(adb shell am start -n "$PACKAGE_NAME/$DEBUG_ACTIVITY" 2>&1)
        
        if echo "$result" | grep -q "Error\|not found\|does not exist"; then
            echo -e "${GREEN}✅ DebugActivity launch failed as expected in $build_type (CORRECT)${NC}"
            return 0
        else
            echo -e "${RED}❌ DebugActivity launched successfully in $build_type (INCORRECT)${NC}"
            echo "   Result: $result"
            return 1
        fi
    else
        echo -e "${YELLOW}⚠️  ADB not found, skipping launch test for $build_type${NC}"
        return 0
    fi
}

# Main test execution
echo "Step 1: Building both APK variants..."
echo "======================================"

# Build both variants
if ./gradlew clean assembleDebug assembleRelease; then
    echo -e "${GREEN}✅ Build completed successfully${NC}"
else
    echo -e "${RED}❌ Build failed${NC}"
    exit 1
fi

echo ""
echo "Step 2: Checking APK files..."
echo "============================="

# Check if APKs exist
DEBUG_APK="app/build/outputs/apk/debug/app-debug.apk"
RELEASE_APK="app/build/outputs/apk/release/app-release.apk"

debug_apk_exists=false
release_apk_exists=false

if check_apk_exists "$DEBUG_APK" "Debug"; then
    debug_apk_exists=true
fi

if check_apk_exists "$RELEASE_APK" "Release"; then
    release_apk_exists=true
fi

echo ""
echo "Step 3: Manifest verification..."
echo "================================"

debug_manifest_ok=false
release_manifest_ok=false

if [ "$debug_apk_exists" = true ]; then
    if check_debug_activity_in_manifest "$DEBUG_APK" "Debug"; then
        debug_manifest_ok=true
    fi
fi

if [ "$release_apk_exists" = true ]; then
    if check_debug_activity_in_manifest "$RELEASE_APK" "Release"; then
        release_manifest_ok=true
    fi
fi

echo ""
echo "Step 4: ADB launch testing (optional)..."
echo "========================================"

# Check if device is connected
if command -v adb &> /dev/null && adb devices | grep -q "device$"; then
    echo "📱 Device detected, testing ADB launches..."
    
    if [ "$debug_apk_exists" = true ]; then
        echo "Installing debug APK..."
        adb install -r "$DEBUG_APK" > /dev/null 2>&1
        test_adb_launch "Debug"
    fi
    
    if [ "$release_apk_exists" = true ]; then
        echo "Installing release APK..."
        adb install -r "$RELEASE_APK" > /dev/null 2>&1
        test_adb_launch "Release"
    fi
else
    echo -e "${YELLOW}⚠️  No ADB device connected, skipping launch tests${NC}"
fi

echo ""
echo "=========================================="
echo "Test Summary"
echo "=========================================="

# Summary
all_tests_passed=true

if [ "$debug_apk_exists" = true ] && [ "$debug_manifest_ok" = true ]; then
    echo -e "${GREEN}✅ Debug build: DebugActivity properly excluded${NC}"
else
    echo -e "${RED}❌ Debug build: Issues detected${NC}"
    all_tests_passed=false
fi

if [ "$release_apk_exists" = true ] && [ "$release_manifest_ok" = true ]; then
    echo -e "${GREEN}✅ Release build: DebugActivity properly excluded${NC}"
else
    echo -e "${RED}❌ Release build: Issues detected${NC}"
    all_tests_passed=false
fi

echo ""
if [ "$all_tests_passed" = true ]; then
    echo -e "${GREEN}🎉 ALL TESTS PASSED: DebugActivity is properly excluded from both build variants${NC}"
    exit 0
else
    echo -e "${RED}❌ SOME TESTS FAILED: Please review the issues above${NC}"
    exit 1
fi
