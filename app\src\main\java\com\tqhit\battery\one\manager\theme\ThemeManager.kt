package com.tqhit.battery.one.manager.theme

import android.app.Activity
import android.content.Context
import android.content.SharedPreferences
import android.content.res.Configuration
import android.graphics.Color
import android.os.Build
import android.view.Window
import androidx.appcompat.app.AppCompatDelegate
import androidx.core.view.WindowCompat
import com.tqhit.battery.one.R
import androidx.core.content.edit

object ThemeManager {
    private lateinit var sharedPreferences: SharedPreferences
    private const val KEY_SELECTED_THEME = "selected_theme"
    private const val KEY_SELECTED_COLOR = "selected_color"
    private const val DEFAULT_THEME = "AutoTheme"
    private const val DEFAULT_COLOR = "green"
    
    fun initialize(context: Context) {
        sharedPreferences = context.getSharedPreferences("theme_preferences", Context.MODE_PRIVATE)
    }
    
    fun applyTheme(activity: Activity) {
        val themeName = getSelectedTheme()
        val colorName = getSelectedColor()
        
        // Get base theme resource ID
        val themeResId = getThemeResourceId(activity, themeName)
        
        // Apply base theme
        activity.setTheme(themeResId)
        
        // Apply color style
        val colorStyleResId = getColorStyleResourceId(colorName)
        activity.theme.applyStyle(colorStyleResId, true)
        
        // Apply system bars based on theme
        applySystemBars(activity.window, themeName)
    }

    private fun isSystemInDarkMode(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            val mode = context.resources.configuration.uiMode and 
                Configuration.UI_MODE_NIGHT_MASK
            mode == Configuration.UI_MODE_NIGHT_YES
        } else {
            AppCompatDelegate.getDefaultNightMode() == AppCompatDelegate.MODE_NIGHT_YES
        }
    }
    
    fun getSelectedTheme(): String {
        return sharedPreferences.getString(KEY_SELECTED_THEME, DEFAULT_THEME) ?: DEFAULT_THEME
    }

    fun saveTheme(themeName: String) {
        sharedPreferences.edit() { putString(KEY_SELECTED_THEME, themeName) }
    }

    fun getSelectedColor(): String {
        return sharedPreferences.getString(KEY_SELECTED_COLOR, DEFAULT_COLOR) ?: DEFAULT_COLOR
    }

    fun saveColor(colorName: String) {
        sharedPreferences.edit() { putString(KEY_SELECTED_COLOR, colorName) }
    }

    private fun getColorStyleResourceId(colorName: String): Int {
        return when (colorName) {
            "blue" -> R.style.Blue
            "green" -> R.style.Green
            "orange" -> R.style.Orange
            "light_blue" -> R.style.Light_blue
            "red" -> R.style.Red
            "pink" -> R.style.Pink
            "light_green" -> R.style.Light_green
            "telo" -> R.style.Telo
            "gold" -> R.style.Gold
            "night_blue" -> R.style.Night_blue
            "color1" -> R.style.color_1
            "color2" -> R.style.color_2
            "color3" -> R.style.color_3
            "color4" -> R.style.color_4
            "color5" -> R.style.color_5
            "color6" -> R.style.color_6
            "color7" -> R.style.color_7
            "color8" -> R.style.color_8
            "color9" -> R.style.color_9
            "color10" -> R.style.color_10
            else -> R.style.Blue
        }
    }
    
    fun getThemeResourceId(context: Context, themeName: String): Int {
        val name = if (themeName == "AutoTheme") {
            if (isSystemInDarkMode(context)) "BlackTheme" else "LightTheme"
        } else {
            themeName
        }
        return when (name) {
            "LightTheme" -> R.style.LightTheme
            "BlackTheme" -> R.style.BlackTheme
            "AmoledTheme" -> R.style.AmoledTheme
            "GreyTheme" -> R.style.GreyTheme
            "LightThemeInverted" -> R.style.LightThemeInverted
            "BlackThemeInverted" -> R.style.BlackThemeInverted
            "AmoledThemeInverted" -> R.style.AmoledThemeInverted
            "GreyThemeInverted" -> R.style.GreyThemeInverted
            else -> R.style.LightTheme
        }
    }
    
    private fun applySystemBars(window: Window, themeName: String) {
        when (themeName) {
            "LightTheme", "LightThemeInverted" -> setLightSystemBars(window)
            "BlackTheme", "BlackThemeInverted", "AmoledTheme", "AmoledThemeInverted" -> setDarkSystemBars(window)
            "GreyTheme", "GreyThemeInverted" -> setGreySystemBars(window)
            else -> {
                if (isSystemInDarkMode(window.context)) {
                    setDarkSystemBars(window)
                } else {
                    setLightSystemBars(window)
                }
            }
        }
    }
    
    private fun setLightSystemBars(window: Window) {
        window.statusBarColor = Color.WHITE
        window.navigationBarColor = Color.WHITE
        WindowCompat.getInsetsController(window, window.decorView).apply {
            isAppearanceLightStatusBars = true
            isAppearanceLightNavigationBars = true
        }
    }
    
    private fun setDarkSystemBars(window: Window) {
        window.statusBarColor = Color.BLACK
        window.navigationBarColor = Color.BLACK
        WindowCompat.getInsetsController(window, window.decorView).apply {
            isAppearanceLightStatusBars = false
            isAppearanceLightNavigationBars = false
        }
    }

    private fun setGreySystemBars(window: Window) {
        window.statusBarColor = Color.parseColor("#bdbdbd")
        window.navigationBarColor = Color.parseColor("#bdbdbd")
        WindowCompat.getInsetsController(window, window.decorView).apply {
            isAppearanceLightStatusBars = true
            isAppearanceLightNavigationBars = true
        }
    }
} 