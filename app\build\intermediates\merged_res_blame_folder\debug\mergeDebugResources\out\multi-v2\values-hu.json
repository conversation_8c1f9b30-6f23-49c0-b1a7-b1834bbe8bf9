{"logs": [{"outputFile": "com.tqhit.battery.one.app-mergeDebugResources-99:/values-hu/values-hu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5b80dcbf636dc26335bd1b8e4f16f918\\transformed\\material-1.12.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,263,344,420,497,587,667,766,886,969,1032,1096,1195,1270,1329,1439,1501,1570,1628,1700,1761,1816,1919,1976,2036,2091,2172,2292,2375,2453,2549,2635,2723,2858,2941,3021,3161,3255,3337,3390,3441,3507,3583,3665,3736,3820,3897,3972,4051,4128,4233,4329,4406,4498,4595,4669,4754,4851,4903,4986,5053,5141,5228,5290,5354,5417,5483,5581,5687,5781,5888,5945,6000,6085,6170,6247", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,80,75,76,89,79,98,119,82,62,63,98,74,58,109,61,68,57,71,60,54,102,56,59,54,80,119,82,77,95,85,87,134,82,79,139,93,81,52,50,65,75,81,70,83,76,74,78,76,104,95,76,91,96,73,84,96,51,82,66,87,86,61,63,62,65,97,105,93,106,56,54,84,84,76,72", "endOffsets": "258,339,415,492,582,662,761,881,964,1027,1091,1190,1265,1324,1434,1496,1565,1623,1695,1756,1811,1914,1971,2031,2086,2167,2287,2370,2448,2544,2630,2718,2853,2936,3016,3156,3250,3332,3385,3436,3502,3578,3660,3731,3815,3892,3967,4046,4123,4228,4324,4401,4493,4590,4664,4749,4846,4898,4981,5048,5136,5223,5285,5349,5412,5478,5576,5682,5776,5883,5940,5995,6080,6165,6242,6315"}, "to": {"startLines": "19,106,107,108,109,110,126,127,141,209,211,263,287,295,367,368,369,370,371,372,373,374,375,376,377,378,379,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,463,497,498,509", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "787,8664,8745,8821,8898,8988,10318,10417,11480,19933,20083,23995,25985,26561,34151,34261,34323,34392,34450,34522,34583,34638,34741,34798,34858,34913,34994,35247,35330,35408,35504,35590,35678,35813,35896,35976,36116,36210,36292,36345,36396,36462,36538,36620,36691,36775,36852,36927,37006,37083,37188,37284,37361,37453,37550,37624,37709,37806,37858,37941,38008,38096,38183,38245,38309,38372,38438,38536,38642,38736,38843,38900,42241,44650,44735,45671", "endLines": "22,106,107,108,109,110,126,127,141,209,211,263,287,295,367,368,369,370,371,372,373,374,375,376,377,378,379,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,463,497,498,509", "endColumns": "12,80,75,76,89,79,98,119,82,62,63,98,74,58,109,61,68,57,71,60,54,102,56,59,54,80,119,82,77,95,85,87,134,82,79,139,93,81,52,50,65,75,81,70,83,76,74,78,76,104,95,76,91,96,73,84,96,51,82,66,87,86,61,63,62,65,97,105,93,106,56,54,84,84,76,72", "endOffsets": "945,8740,8816,8893,8983,9063,10412,10532,11558,19991,20142,24089,26055,26615,34256,34318,34387,34445,34517,34578,34633,34736,34793,34853,34908,34989,35109,35325,35403,35499,35585,35673,35808,35891,35971,36111,36205,36287,36340,36391,36457,36533,36615,36686,36770,36847,36922,37001,37078,37183,37279,37356,37448,37545,37619,37704,37801,37853,37936,38003,38091,38178,38240,38304,38367,38433,38531,38637,38731,38838,38895,38950,42321,44730,44807,45739"}}, {"source": "D:\\Duc\\AndroidStudioProjects\\TJ_BatteryOne\\app\\src\\main\\res\\values-hu\\strings.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,237,18,19,20,21,22,23,24,25,26,27,28,29,265,30,31,32,33,34,35,36,241,251,254,256,37,2,264,244,262,38,39,40,41,42,43,44,45,115,116,114,113,117,46,47,48,49,50,51,52,53,54,55,56,239,57,58,243,59,60,61,62,63,64,65,66,67,68,249,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,119,120,259,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,260,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,242,168,169,170,171,172,173,174,175,176,177,178,247,246,245,253,257,179,180,181,182,183,184,255,185,186,187,240,188,261,189,190,191,192,193,194,195,196,248,197,198,258,199,200,201,202,203,252,204,205,206,207,208,209,210,211,212,213,214,215,216,263,217,218,219,220,221,222,223,224,225,226,250,227,228,229,230,231,232,233,234,235,236", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "127,196,244,292,341,389,440,486,535,589,656,705,753,803,851,20801,899,985,1040,1088,1135,1189,1267,1338,1419,1499,1573,1635,23277,1697,1739,1906,1962,2047,2090,2152,21060,21879,22282,22411,2245,57,23229,21259,23108,2289,2335,2381,2465,2522,2596,2985,3379,11443,11513,10892,10809,11578,3420,3495,3570,3785,4047,4122,4181,4222,4282,4362,4410,20949,4479,4551,21183,4593,4676,4719,4793,4942,5009,5094,5167,5240,5327,21758,5393,5449,5505,5567,5628,5677,5750,5837,5887,6177,6235,6298,6839,7395,7467,7516,7664,7733,8005,8055,8505,8570,8704,8849,8993,9059,9103,9150,9221,9310,9386,9436,9540,9644,9698,9763,9829,10066,10282,10449,10606,10689,11665,11738,22919,11791,11831,11919,11986,12044,12251,12301,12381,12445,12507,12660,12721,12871,13094,13135,13195,13233,13271,13307,13343,13424,13468,13527,13577,13644,22979,13714,13783,13843,13887,13930,14007,14402,14464,14542,14706,14787,14860,14897,14932,14968,15006,15058,15126,15193,15248,15329,15479,21108,15515,15575,15627,15723,15796,15873,15988,16104,16176,16214,16294,21580,21385,21303,22181,22683,16536,16576,16626,16677,16743,16827,22333,16878,16964,17013,21015,17061,23041,17189,17275,17363,17419,17490,17688,17761,17825,21686,17892,17953,22728,18012,18089,18187,18269,18319,22106,18399,18481,18525,18583,18628,18749,19102,19178,19224,19283,19357,19418,19471,23171,19511,19580,19787,19872,19933,20002,20056,20135,20207,20241,21823,20299,20364,20412,20449,20502,20555,20619,20681,20719,20755", "endColumns": "67,46,46,47,46,49,44,47,52,65,47,46,48,46,46,87,84,53,46,45,52,76,69,79,78,72,60,60,70,40,165,54,83,41,60,91,46,225,49,270,42,68,46,42,61,44,44,82,55,72,387,392,39,68,63,549,81,83,73,73,213,260,73,57,39,58,78,46,67,64,70,40,74,81,41,72,147,65,83,71,71,85,64,63,54,54,60,59,47,71,85,48,288,56,61,539,554,70,47,146,67,270,48,448,63,132,143,142,64,42,45,69,87,74,48,102,102,52,63,64,235,214,165,155,81,73,71,51,58,38,86,65,56,205,48,78,62,60,151,59,148,221,39,58,36,36,34,34,79,42,57,48,65,68,60,67,58,42,41,75,393,60,76,162,79,71,35,33,34,36,50,66,65,53,79,148,34,73,58,50,94,71,75,113,114,70,36,78,240,104,193,80,99,43,38,48,49,64,82,49,76,84,47,46,43,126,65,84,86,54,69,196,71,62,65,70,59,57,189,75,96,80,48,78,73,80,42,56,43,119,351,74,44,57,72,59,51,38,56,67,205,83,59,67,52,77,70,32,56,54,63,46,35,51,51,62,60,36,34,44", "endOffsets": "190,238,286,335,383,434,480,529,583,650,699,747,797,845,893,20884,979,1034,1082,1129,1183,1261,1332,1413,1493,1567,1629,1691,23343,1733,1900,1956,2041,2084,2146,2239,21102,22100,22327,22677,2283,121,23271,21297,23165,2329,2375,2459,2516,2590,2979,3373,3414,11507,11572,11437,10886,11657,3489,3564,3779,4041,4116,4175,4216,4276,4356,4404,4473,21009,4545,4587,21253,4670,4713,4787,4936,5003,5088,5161,5234,5321,5387,21817,5443,5499,5561,5622,5671,5744,5831,5881,6171,6229,6292,6833,7389,7461,7510,7658,7727,7999,8049,8499,8564,8698,8843,8987,9053,9097,9144,9215,9304,9380,9430,9534,9638,9692,9757,9823,10060,10276,10443,10600,10683,10758,11732,11785,22973,11825,11913,11980,12038,12245,12295,12375,12439,12501,12654,12715,12865,13088,13129,13189,13227,13265,13301,13337,13418,13462,13521,13571,13638,13708,23035,13777,13837,13881,13924,14001,14396,14458,14536,14700,14781,14854,14891,14926,14962,15000,15052,15120,15187,15242,15323,15473,15509,21177,15569,15621,15717,15790,15867,15982,16098,16170,16208,16288,16530,21680,21574,21379,22276,22722,16570,16620,16671,16737,16821,16872,22405,16958,17007,17055,21054,17183,23102,17269,17357,17413,17484,17682,17755,17819,17886,21752,17947,18006,22913,18083,18181,18263,18313,18393,22175,18475,18519,18577,18622,18743,19096,19172,19218,19277,19351,19412,19465,19505,23223,19574,19781,19866,19927,19996,20050,20129,20201,20235,20293,21873,20358,20406,20443,20496,20549,20613,20675,20713,20749,20795"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,41,42,45,46,57,58,60,61,62,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,111,112,113,114,115,123,124,125,128,129,130,131,132,133,134,135,136,137,138,139,140,162,163,164,165,167,168,169,170,171,172,173,174,175,176,177,178,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,204,205,206,207,208,210,262,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,288,289,291,293,294,296,297,298,299,300,301,302,303,304,305,363,364,365,366,380,381,427,434,435,436,437,438,440,441,442,443,444,445,446,447,456,457,458,459,460,461,462,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,481,482,483,484,485,486,494,495,499,501,502,503,504,505,506,507,508,510,511,512,513,514,515,516,520,521,522,525,527,528,529,530,531,534,535,536,537,538,539,540,541,542,543,545,546,547,548,549,550,551,552", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "950,1018,1065,1112,1160,1207,1257,1302,1350,1403,1469,1517,1564,1613,1660,1707,1995,2080,2372,2419,3403,3456,3643,3713,3793,4173,4246,4307,4368,4439,4480,4646,4701,4785,4827,4888,4980,5027,5253,5303,5574,5617,5686,5733,5776,5838,5883,5928,6011,6067,6140,6528,6921,6961,7030,7094,7644,7726,7810,7884,7958,8172,8433,8507,8565,8605,9068,9147,9194,9262,9327,10120,10161,10236,10537,10579,10652,10800,10866,10950,11022,11094,11180,11245,11309,11364,11419,14174,14234,14282,14354,14536,14585,14874,14931,14993,15533,16088,16159,16207,16354,16422,16693,16938,17387,17451,17584,17728,17871,17936,17979,18025,18095,18183,18258,18307,18410,18513,18566,18630,18695,18931,19146,19312,19468,19637,19711,19783,19835,19894,19996,23929,24414,24471,24677,24726,24805,24868,24929,25081,25141,25290,25512,25552,25611,25648,25685,25720,25755,25835,25878,25936,26060,26126,26277,26434,26502,26620,26663,26705,26781,27175,27236,27313,27476,27556,27628,33994,34028,34063,34100,35114,35181,38955,39484,39564,39713,39748,39822,39968,40019,40114,40186,40262,40376,40491,40562,41397,41476,41717,41822,42016,42097,42197,42326,42365,42414,42464,42529,42612,42662,42739,42824,42872,42919,42963,43090,43156,43241,43499,43554,43624,43821,43893,43956,44435,44506,44812,44944,45134,45210,45307,45388,45437,45516,45590,45744,45787,45844,45888,46008,46360,46435,46722,46780,46853,47058,47231,47270,47327,47395,47601,47871,47931,47999,48052,48130,48201,48234,48291,48346,48410,48517,48553,48605,48657,48720,48781,48818,48853", "endColumns": "67,46,46,47,46,49,44,47,52,65,47,46,48,46,46,87,84,53,46,45,52,76,69,79,78,72,60,60,70,40,165,54,83,41,60,91,46,225,49,270,42,68,46,42,61,44,44,82,55,72,387,392,39,68,63,549,81,83,73,73,213,260,73,57,39,58,78,46,67,64,70,40,74,81,41,72,147,65,83,71,71,85,64,63,54,54,60,59,47,71,85,48,288,56,61,539,554,70,47,146,67,270,48,448,63,132,143,142,64,42,45,69,87,74,48,102,102,52,63,64,235,214,165,155,81,73,71,51,58,38,86,65,56,205,48,78,62,60,151,59,148,221,39,58,36,36,34,34,79,42,57,48,65,68,60,67,58,42,41,75,393,60,76,162,79,71,35,33,34,36,50,66,65,53,79,148,34,73,58,50,94,71,75,113,114,70,36,78,240,104,193,80,99,43,38,48,49,64,82,49,76,84,47,46,43,126,65,84,86,54,69,196,71,62,65,70,59,57,189,75,96,80,48,78,73,80,42,56,43,119,351,74,44,57,72,59,51,38,56,67,205,83,59,67,52,77,70,32,56,54,63,46,35,51,51,62,60,36,34,44", "endOffsets": "1013,1060,1107,1155,1202,1252,1297,1345,1398,1464,1512,1559,1608,1655,1702,1790,2075,2129,2414,2460,3451,3528,3708,3788,3867,4241,4302,4363,4434,4475,4641,4696,4780,4822,4883,4975,5022,5248,5298,5569,5612,5681,5728,5771,5833,5878,5923,6006,6062,6135,6523,6916,6956,7025,7089,7639,7721,7805,7879,7953,8167,8428,8502,8560,8600,8659,9142,9189,9257,9322,9393,10156,10231,10313,10574,10647,10795,10861,10945,11017,11089,11175,11240,11304,11359,11414,11475,14229,14277,14349,14435,14580,14869,14926,14988,15528,16083,16154,16202,16349,16417,16688,16737,17382,17446,17579,17723,17866,17931,17974,18020,18090,18178,18253,18302,18405,18508,18561,18625,18690,18926,19141,19307,19463,19545,19706,19778,19830,19889,19928,20078,23990,24466,24672,24721,24800,24863,24924,25076,25136,25285,25507,25547,25606,25643,25680,25715,25750,25830,25873,25931,25980,26121,26190,26333,26497,26556,26658,26700,26776,27170,27231,27308,27471,27551,27623,27659,34023,34058,34095,34146,35176,35242,39004,39559,39708,39743,39817,39876,40014,40109,40181,40257,40371,40486,40557,40594,41471,41712,41817,42011,42092,42192,42236,42360,42409,42459,42524,42607,42657,42734,42819,42867,42914,42958,43085,43151,43236,43323,43549,43619,43816,43888,43951,44017,44501,44561,44865,45129,45205,45302,45383,45432,45511,45585,45666,45782,45839,45883,46003,46355,46430,46475,46775,46848,46908,47105,47265,47322,47390,47596,47680,47926,47994,48047,48125,48196,48229,48286,48341,48405,48452,48548,48600,48652,48715,48776,48813,48848,48893"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\179e6486bd57a16ea175623aa423e7ed\\transformed\\jetified-material3-release\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,176,292,400,516,611,708,822,962,1085,1232,1317,1417,1515,1617,1739,1876,1981,2121,2259,2385,2581,2704,2826,2948,3074,3173,3268,3387,3524,3626,3737,3841,3986,4133,4240,4347,4431,4529,4623,4731,4819,4906,5007,5088,5171,5270,5376,5471,5574,5660,5769,5867,5973,6094,6175,6287", "endColumns": "120,115,107,115,94,96,113,139,122,146,84,99,97,101,121,136,104,139,137,125,195,122,121,121,125,98,94,118,136,101,110,103,144,146,106,106,83,97,93,107,87,86,100,80,82,98,105,94,102,85,108,97,105,120,80,111,97", "endOffsets": "171,287,395,511,606,703,817,957,1080,1227,1312,1412,1510,1612,1734,1871,1976,2116,2254,2380,2576,2699,2821,2943,3069,3168,3263,3382,3519,3621,3732,3836,3981,4128,4235,4342,4426,4524,4618,4726,4814,4901,5002,5083,5166,5265,5371,5466,5569,5655,5764,5862,5968,6089,6170,6282,6380"}, "to": {"startLines": "306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "27664,27785,27901,28009,28125,28220,28317,28431,28571,28694,28841,28926,29026,29124,29226,29348,29485,29590,29730,29868,29994,30190,30313,30435,30557,30683,30782,30877,30996,31133,31235,31346,31450,31595,31742,31849,31956,32040,32138,32232,32340,32428,32515,32616,32697,32780,32879,32985,33080,33183,33269,33378,33476,33582,33703,33784,33896", "endColumns": "120,115,107,115,94,96,113,139,122,146,84,99,97,101,121,136,104,139,137,125,195,122,121,121,125,98,94,118,136,101,110,103,144,146,106,106,83,97,93,107,87,86,100,80,82,98,105,94,102,85,108,97,105,120,80,111,97", "endOffsets": "27780,27896,28004,28120,28215,28312,28426,28566,28689,28836,28921,29021,29119,29221,29343,29480,29585,29725,29863,29989,30185,30308,30430,30552,30678,30777,30872,30991,31128,31230,31341,31445,31590,31737,31844,31951,32035,32133,32227,32335,32423,32510,32611,32692,32775,32874,32980,33075,33178,33264,33373,33471,33577,33698,33779,33891,33989"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5c5c72c6ff4a7863322da50648a25e99\\transformed\\browser-1.8.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,151,252,367", "endColumns": "95,100,114,103", "endOffsets": "146,247,362,466"}, "to": {"startLines": "166,264,265,266", "startColumns": "4,4,4,4", "startOffsets": "14440,24094,24195,24310", "endColumns": "95,100,114,103", "endOffsets": "14531,24190,24305,24409"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\237e0b5db534c615c4317f1b214e3e7f\\transformed\\jetified-play-services-ads-24.2.0\\res\\values-hu\\values.xml", "from": {"startLines": "4,5,6,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,241,288,591,653,778,894,1035,1085,1136,1255,1357,1396,1487,1524,1562,1615,1701,1742", "endColumns": "41,46,52,61,124,115,140,49,50,118,101,38,90,36,37,52,85,40,55", "endOffsets": "240,287,340,652,777,893,1034,1084,1135,1254,1356,1395,1486,1523,1561,1614,1700,1741,1797"}, "to": {"startLines": "428,429,430,448,449,450,451,452,453,454,455,487,488,489,490,491,492,493,544", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "39009,39055,39106,40599,40665,40794,40914,41059,41113,41168,41291,44022,44065,44160,44201,44243,44300,44390,48457", "endColumns": "45,50,56,65,128,119,144,53,54,122,105,42,94,40,41,56,89,44,59", "endOffsets": "39050,39101,39158,40660,40789,40909,41054,41108,41163,41286,41392,44060,44155,44196,44238,44295,44385,44430,48512"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\237635df39b25799c092d66a208ce67d\\transformed\\jetified-foundation-release\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,144", "endColumns": "88,96", "endOffsets": "139,236"}, "to": {"startLines": "532,533", "startColumns": "4,4", "startOffsets": "47685,47774", "endColumns": "88,96", "endOffsets": "47769,47866"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f908cdc45776521b403beeef1508641c\\transformed\\core-1.16.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,356,457,560,667,777", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "147,249,351,452,555,662,772,873"}, "to": {"startLines": "116,117,118,119,120,121,122,519", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "9398,9495,9597,9699,9800,9903,10010,46621", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "9490,9592,9694,9795,9898,10005,10115,46717"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d56ddc8f70c1b6c4f2dfff25a6818549\\transformed\\jetified-play-services-base-18.5.0\\res\\values-hu\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,480,614,719,883,1017,1135,1241,1407,1511,1692,1825,1993,2161,2228,2292", "endColumns": "106,179,133,104,163,133,117,105,165,103,180,132,167,167,66,63,83", "endOffsets": "299,479,613,718,882,1016,1134,1240,1406,1510,1691,1824,1992,2160,2227,2291,2375"}, "to": {"startLines": "144,145,146,147,148,149,150,151,153,154,155,156,157,158,159,160,161", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "11746,11857,12041,12179,12288,12456,12594,12716,13003,13173,13281,13466,13603,13775,13947,14018,14086", "endColumns": "110,183,137,108,167,137,121,109,169,107,184,136,171,171,70,67,87", "endOffsets": "11852,12036,12174,12283,12451,12589,12711,12821,13168,13276,13461,13598,13770,13942,14013,14081,14169"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9e0a763189144907fb0197c2b097244b\\transformed\\jetified-ui-release\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,288,385,484,571,653,749,838,925,1008,1096,1245,1316,1386,1465,1531", "endColumns": "94,87,96,98,86,81,95,88,86,82,87,73,70,69,78,65,120", "endOffsets": "195,283,380,479,566,648,744,833,920,1003,1091,1165,1311,1381,1460,1526,1647"}, "to": {"startLines": "142,143,179,180,203,290,292,433,439,479,480,500,517,518,523,524,526", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "11563,11658,16742,16839,19550,26195,26338,39395,39881,43328,43411,44870,46480,46551,46913,46992,47110", "endColumns": "94,87,96,98,86,81,95,88,86,82,87,73,70,69,78,65,120", "endOffsets": "11653,11741,16834,16933,19632,26272,26429,39479,39963,43406,43494,44939,46546,46616,46987,47053,47226"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\42f95d9fa807b14415e836fc15872a54\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-hu\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "172", "endOffsets": "367"}, "to": {"startLines": "152", "startColumns": "4", "startOffsets": "12826", "endColumns": "176", "endOffsets": "12998"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\93d3043f0a8b9466a00a736e170a6ddc\\transformed\\appcompat-1.7.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,6,7,10,11,12,13,14,15,16,17,18,19,22,26,27,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,504,619,894,985,1078,1173,1267,1367,1460,1555,1650,1741,2025,2455,2574,2859", "endColumns": "107,91,114,122,90,92,94,93,99,92,94,94,90,90,109,118,181,83", "endOffsets": "208,300,614,737,980,1073,1168,1262,1362,1455,1550,1645,1736,1827,2130,2569,2751,2938"}, "to": {"startLines": "39,40,43,44,47,48,49,50,51,52,53,54,55,56,59,63,64,496", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1795,1903,2134,2249,2465,2556,2649,2744,2838,2938,3031,3126,3221,3312,3533,3872,3991,44566", "endColumns": "107,91,114,122,90,92,94,93,99,92,94,94,90,90,109,118,181,83", "endOffsets": "1898,1990,2244,2367,2551,2644,2739,2833,2933,3026,3121,3216,3307,3398,3638,3986,4168,44645"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b572512e02266e069f95737c22215ab9\\transformed\\jetified-media3-exoplayer-1.6.1\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,192,266,338,416,489,583,673", "endColumns": "74,61,73,71,77,72,93,89,80", "endOffsets": "125,187,261,333,411,484,578,668,749"}, "to": {"startLines": "236,237,238,239,240,241,242,243,244", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "22150,22225,22287,22361,22433,22511,22584,22678,22768", "endColumns": "74,61,73,71,77,72,93,89,80", "endOffsets": "22220,22282,22356,22428,22506,22579,22673,22763,22844"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5444be4bc77930bd89cfbb9f2224d8e4\\transformed\\navigation-ui-2.8.9\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,119", "endOffsets": "162,282"}, "to": {"startLines": "431,432", "startColumns": "4,4", "startOffsets": "39163,39275", "endColumns": "111,119", "endOffsets": "39270,39390"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8bd7feaae90e869538df51f29dd16595\\transformed\\jetified-media3-ui-1.6.1\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,490,692,780,867,945,1031,1134,1208,1276,1373,1474,1547,1615,1680,1748,1861,1972,2082,2156,2238,2312,2385,2475,2564,2632,2695,2748,2806,2854,2915,2979,3046,3110,3178,3243,3302,3367,3420,3485,3567,3649,3706", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,87,86,77,85,102,73,67,96,100,72,67,64,67,112,110,109,73,81,73,72,89,88,67,62,52,57,47,60,63,66,63,67,64,58,64,52,64,81,81,56,68", "endOffsets": "280,485,687,775,862,940,1026,1129,1203,1271,1368,1469,1542,1610,1675,1743,1856,1967,2077,2151,2233,2307,2380,2470,2559,2627,2690,2743,2801,2849,2910,2974,3041,3105,3173,3238,3297,3362,3415,3480,3562,3644,3701,3770"}, "to": {"startLines": "2,11,15,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,585,20147,20235,20322,20400,20486,20589,20663,20731,20828,20929,21002,21070,21135,21203,21316,21427,21537,21611,21693,21767,21840,21930,22019,22087,22849,22902,22960,23008,23069,23133,23200,23264,23332,23397,23456,23521,23574,23639,23721,23803,23860", "endLines": "10,14,18,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261", "endColumns": "17,12,12,87,86,77,85,102,73,67,96,100,72,67,64,67,112,110,109,73,81,73,72,89,88,67,62,52,57,47,60,63,66,63,67,64,58,64,52,64,81,81,56,68", "endOffsets": "375,580,782,20230,20317,20395,20481,20584,20658,20726,20823,20924,20997,21065,21130,21198,21311,21422,21532,21606,21688,21762,21835,21925,22014,22082,22145,22897,22955,23003,23064,23128,23195,23259,23327,23392,23451,23516,23569,23634,23716,23798,23855,23924"}}]}]}