package com.tqhit.battery.one.features.emoji.presentation.gallery

import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory

/**
 * UI state for the Battery Gallery screen following MVI pattern.
 * 
 * This state class follows the established stats module architecture pattern:
 * - Immutable data class for predictable state management
 * - Comprehensive state representation for all UI scenarios
 * - Clear separation of loading, error, and success states
 * - Support for filtering and categorization
 * - Integration with CoreBatteryStatsService for feature toggle state
 * 
 * The state is designed to handle all possible UI scenarios including:
 * - Initial loading from repository
 * - Category filtering and selection
 * - Search functionality
 * - Error handling for network/parsing failures
 * - Empty states for better UX
 * - Premium/free filtering
 */
data class BatteryGalleryState(
    // Data states
    val allStyles: List<BatteryStyle> = emptyList(),
    val displayedStyles: List<BatteryStyle> = emptyList(),
    val categories: List<BatteryStyleCategory> = BatteryStyleCategory.values().toList(),
    
    // UI states
    val isLoading: Boolean = false,
    val isRefreshing: Boolean = false,
    val errorMessage: String? = null,
    
    // Filter states
    val selectedCategory: BatteryStyleCategory? = null,
    val searchQuery: String = "",
    val showOnlyFree: Boolean = false,
    val showOnlyPremium: Boolean = false,
    val showOnlyPopular: Boolean = false,
    
    // Feature states
    val isEmojiBatteryEnabled: Boolean = false,
    val hasPermissions: Boolean = false,
    
    // UI interaction states
    val selectedStyleId: String? = null,
    val showCategoryFilter: Boolean = false,
    val showSearchBar: Boolean = false,

    // Navigation states
    val navigationEvent: NavigationEvent? = null,
    
    // Data freshness
    val lastRefreshTimestamp: Long = 0L,
    val hasCachedData: Boolean = false
) {
    
    /**
     * Checks if the gallery is in an empty state.
     * This helps determine what empty state message to show.
     * 
     * @return True if no styles are displayed and not loading
     */
    fun isEmpty(): Boolean {
        return displayedStyles.isEmpty() && !isLoading
    }
    
    /**
     * Checks if any filters are currently active.
     * Useful for showing filter indicators in the UI.
     * 
     * @return True if any filter is active
     */
    fun hasActiveFilters(): Boolean {
        return selectedCategory != null ||
               searchQuery.isNotBlank() ||
               showOnlyFree ||
               showOnlyPremium ||
               showOnlyPopular
    }
    
    /**
     * Gets the count of styles for a specific category.
     * Useful for showing category counts in filter UI.
     * 
     * @param category The category to count
     * @return Number of styles in the category
     */
    fun getStyleCountForCategory(category: BatteryStyleCategory): Int {
        return allStyles.count { it.category == category }
    }
    
    /**
     * Gets the count of premium styles.
     * 
     * @return Number of premium styles
     */
    fun getPremiumStyleCount(): Int {
        return allStyles.count { it.isPremium }
    }
    
    /**
     * Gets the count of free styles.
     * 
     * @return Number of free styles
     */
    fun getFreeStyleCount(): Int {
        return allStyles.count { !it.isPremium }
    }
    
    /**
     * Gets the count of popular styles.
     * 
     * @return Number of popular styles
     */
    fun getPopularStyleCount(): Int {
        return allStyles.count { it.isPopular }
    }
    
    /**
     * Checks if data is stale and needs refresh.
     * Uses 1 hour as the staleness threshold.
     * 
     * @return True if data should be refreshed
     */
    fun isDataStale(): Boolean {
        if (lastRefreshTimestamp == 0L) return true
        
        val oneHourInMillis = 60 * 60 * 1000L
        return (System.currentTimeMillis() - lastRefreshTimestamp) > oneHourInMillis
    }
    
    /**
     * Gets a user-friendly error message for display.
     * 
     * @return Formatted error message or null if no error
     */
    fun getDisplayErrorMessage(): String? {
        return errorMessage?.let { error ->
            when {
                error.contains("network", ignoreCase = true) -> 
                    "Network error. Please check your connection and try again."
                error.contains("timeout", ignoreCase = true) -> 
                    "Request timed out. Please try again."
                error.contains("parse", ignoreCase = true) -> 
                    "Data format error. Please try refreshing."
                else -> "Something went wrong. Please try again."
            }
        }
    }
    
    /**
     * Gets the current filter description for UI display.
     * 
     * @return Human-readable description of active filters
     */
    fun getFilterDescription(): String {
        val filters = mutableListOf<String>()
        
        selectedCategory?.let { filters.add(it.displayName) }
        if (showOnlyFree) filters.add("Free")
        if (showOnlyPremium) filters.add("Premium")
        if (showOnlyPopular) filters.add("Popular")
        if (searchQuery.isNotBlank()) filters.add("Search: \"$searchQuery\"")
        
        return when {
            filters.isEmpty() -> "All Styles"
            filters.size == 1 -> filters.first()
            else -> filters.joinToString(" • ")
        }
    }
    
    /**
     * Creates a copy with updated displayed styles based on current filters.
     * This is a helper method for the ViewModel to apply filters consistently.
     * 
     * @return Updated state with filtered displayed styles
     */
    fun withFilteredStyles(): BatteryGalleryState {
        val filtered = allStyles.filter { style ->
            // Category filter
            val categoryMatch = selectedCategory?.let { style.category == it } ?: true
            
            // Premium/Free filter
            val premiumMatch = when {
                showOnlyPremium -> style.isPremium
                showOnlyFree -> !style.isPremium
                else -> true
            }
            
            // Popular filter
            val popularMatch = if (showOnlyPopular) style.isPopular else true
            
            // Search filter
            val searchMatch = if (searchQuery.isNotBlank()) {
                style.matchesSearch(searchQuery)
            } else true
            
            categoryMatch && premiumMatch && popularMatch && searchMatch
        }
        
        return copy(displayedStyles = filtered)
    }
    
    companion object {
        /**
         * Creates the initial state for the gallery.
         * 
         * @return Initial BatteryGalleryState
         */
        fun initial(): BatteryGalleryState {
            return BatteryGalleryState(
                isLoading = true,
                categories = BatteryStyleCategory.values().toList()
            )
        }
        
        /**
         * Creates a loading state.
         * 
         * @param isRefreshing Whether this is a refresh operation
         * @return Loading BatteryGalleryState
         */
        fun loading(isRefreshing: Boolean = false): BatteryGalleryState {
            return BatteryGalleryState(
                isLoading = !isRefreshing,
                isRefreshing = isRefreshing
            )
        }
        
        /**
         * Creates an error state.
         * 
         * @param message Error message
         * @param previousState Previous state to preserve data
         * @return Error BatteryGalleryState
         */
        fun error(message: String, previousState: BatteryGalleryState? = null): BatteryGalleryState {
            return (previousState ?: BatteryGalleryState()).copy(
                isLoading = false,
                isRefreshing = false,
                errorMessage = message
            )
        }
    }
}

/**
 * Navigation events for the Battery Gallery screen.
 * Used to trigger navigation actions from the ViewModel to the Fragment.
 */
sealed class NavigationEvent {
    /**
     * Navigate to customization screen with selected style.
     */
    data class NavigateToCustomization(val style: BatteryStyle) : NavigationEvent()

    /**
     * Navigate to settings screen.
     */
    object NavigateToSettings : NavigationEvent()

    /**
     * Show feature information dialog.
     */
    object ShowFeatureInfo : NavigationEvent()
}
