package com.tqhit.battery.one.manager.graph

import com.tqhit.adlib.sdk.data.local.PreferencesHelper
import javax.inject.Inject
import javax.inject.Singleton

data class HistoryEntry<T>(
    val timestamp: Long,
    val value: T
)

abstract class HistoryManager<T>(
    protected val preferencesHelper: PreferencesHelper,
    private val key: String,
    private val maxItems: Int = 60 * 24 // Store 24 hours of minute-based entries
) {
    protected val history = mutableListOf<HistoryEntry<T>>()

    init {
        loadHistory()
    }

    protected abstract fun parseValue(value: String): T?
    protected abstract fun valueToString(value: T): String

    private fun loadHistory() {
        val historyJson = preferencesHelper.getString(key)
        if (!historyJson.isNullOrEmpty()) {
            history.clear()
            history.addAll(
                historyJson.split("|").mapNotNull { entry ->
                    val parts = entry.split(",")
                    if (parts.size == 2) {
                        parts[0].toLongOrNull()?.let { timestamp ->
                            parseValue(parts[1])?.let { value ->
                                HistoryEntry(timestamp, value)
                            }
                        }
                    } else null
                }
            )
        }
    }

    private fun saveHistory() {
        val historyJson = history.joinToString("|") { "${it.timestamp},${valueToString(it.value)}" }
        preferencesHelper.saveString(key, historyJson)
    }

    fun addEntry(timestamp: Long, value: T) {
        // Round timestamp to nearest minute
        val minuteTimestamp = (timestamp / 60000) * 60000
        
        // Remove any existing entry for this minute
        history.removeAll { it.timestamp == minuteTimestamp }
        
        // Add new entry
        history.add(HistoryEntry(minuteTimestamp, value))
        
        // Keep only the last maxItems entries
        if (history.size > maxItems) {
            history.removeAll { it.timestamp < history[history.size - maxItems].timestamp }
        }
        
        saveHistory()
    }

    fun getHistory(hours: Int): List<HistoryEntry<T>> {
        val cutoff = System.currentTimeMillis() - hours * 60 * 60 * 1000
        return history.filter { it.timestamp >= cutoff }
    }

    fun getMinuteHistory(minutes: Int): List<HistoryEntry<T>> {
        val cutoff = System.currentTimeMillis() - minutes * 60 * 1000
        return history.filter { it.timestamp >= cutoff }
    }

    fun clearHistory() {
        history.clear()
        saveHistory()
    }
}

@Singleton
class BatteryHistoryManager @Inject constructor(
    preferencesHelper: PreferencesHelper
) : HistoryManager<Int>(
    preferencesHelper,
    "battery_history"
) {
    override fun parseValue(value: String): Int? = value.toIntOrNull()
    override fun valueToString(value: Int): String = value.toString()
}

@Singleton
class TemperatureHistoryManager @Inject constructor(
    preferencesHelper: PreferencesHelper
) : HistoryManager<Double>(
    preferencesHelper,
    "temperature_history"
) {
    override fun parseValue(value: String): Double? = value.toDoubleOrNull()
    override fun valueToString(value: Double): String = value.toString()
}
