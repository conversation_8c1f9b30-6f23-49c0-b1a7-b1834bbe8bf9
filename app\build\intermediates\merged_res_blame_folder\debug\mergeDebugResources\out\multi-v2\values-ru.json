{"logs": [{"outputFile": "com.tqhit.battery.one.app-mergeDebugResources-99:/values-ru/values-ru.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8bd7feaae90e869538df51f29dd16595\\transformed\\jetified-media3-ui-1.6.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,280,619,949,1032,1115,1198,1288,1388,1459,1532,1631,1732,1805,1877,1942,2020,2132,2243,2360,2437,2532,2604,2677,2765,2853,2922,2987,3040,3102,3150,3211,3278,3346,3412,3494,3552,3609,3675,3727,3788,3873,3958,4021", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63", "endColumns": "17,12,12,82,82,82,89,99,70,72,98,100,72,71,64,77,111,110,116,76,94,71,72,87,87,68,64,52,61,47,60,66,67,65,81,57,56,65,51,60,84,84,62,68", "endOffsets": "275,614,944,1027,1110,1193,1283,1383,1454,1527,1626,1727,1800,1872,1937,2015,2127,2238,2355,2432,2527,2599,2672,2760,2848,2917,2982,3035,3097,3145,3206,3273,3341,3407,3489,3547,3604,3670,3722,3783,3868,3953,4016,4085"}, "to": {"startLines": "2,11,17,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,375,714,20704,20787,20870,20953,21043,21143,21214,21287,21386,21487,21560,21632,21697,21775,21887,21998,22115,22192,22287,22359,22432,22520,22608,22677,23409,23462,23524,23572,23633,23700,23768,23834,23916,23974,24031,24097,24149,24210,24295,24380,24443", "endLines": "10,16,22,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267", "endColumns": "17,12,12,82,82,82,89,99,70,72,98,100,72,71,64,77,111,110,116,76,94,71,72,87,87,68,64,52,61,47,60,66,67,65,81,57,56,65,51,60,84,84,62,68", "endOffsets": "370,709,1039,20782,20865,20948,21038,21138,21209,21282,21381,21482,21555,21627,21692,21770,21882,21993,22110,22187,22282,22354,22427,22515,22603,22672,22737,23457,23519,23567,23628,23695,23763,23829,23911,23969,24026,24092,24144,24205,24290,24375,24438,24507"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\42f95d9fa807b14415e836fc15872a54\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-ru\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "152", "endOffsets": "347"}, "to": {"startLines": "158", "startColumns": "4", "startOffsets": "13399", "endColumns": "156", "endOffsets": "13551"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\237635df39b25799c092d66a208ce67d\\transformed\\jetified-foundation-release\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,90", "endOffsets": "138,229"}, "to": {"startLines": "538,539", "startColumns": "4,4", "startOffsets": "48190,48278", "endColumns": "87,90", "endOffsets": "48273,48364"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f908cdc45776521b403beeef1508641c\\transformed\\core-1.16.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,457,562,665,782", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "148,250,351,452,557,660,777,878"}, "to": {"startLines": "122,123,124,125,126,127,128,525", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "10024,10122,10224,10325,10426,10531,10634,47100", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "10117,10219,10320,10421,10526,10629,10746,47196"}}, {"source": "D:\\Duc\\AndroidStudioProjects\\TJ_BatteryOne\\app\\src\\main\\res\\values-ru\\strings.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,237,18,19,20,21,22,23,24,25,26,27,28,29,264,30,31,32,33,34,35,36,240,250,253,255,37,2,263,243,261,38,39,40,41,42,43,44,45,115,116,114,113,117,46,47,48,49,50,51,52,53,54,55,56,238,57,58,242,59,60,61,62,63,64,65,66,67,68,248,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,119,120,258,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,259,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,241,168,169,170,171,172,173,174,175,176,177,178,246,245,244,252,256,179,180,181,182,183,184,254,185,186,187,239,188,260,189,190,191,192,193,194,195,196,247,197,198,257,199,200,201,202,203,251,204,205,206,207,208,209,210,211,212,213,214,215,216,262,217,218,219,220,221,222,223,224,225,226,249,227,228,229,230,231,232,233,234,235,236", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "123,193,247,300,353,404,459,511,561,620,692,745,795,847,898,20926,951,1030,1087,1137,1185,1237,1315,1384,1464,1542,1613,1673,23396,1728,1767,1934,1991,2093,2136,2204,21117,21940,22355,22486,2289,57,23349,21316,23222,2333,2382,2421,2509,2569,2644,3053,3465,11672,11739,11020,10937,11804,3512,3585,3662,3865,4146,4221,4279,4321,4388,4465,4515,21003,4584,4652,21242,4695,4774,4816,4890,5046,5117,5207,5288,5350,5437,21818,5499,5555,5611,5676,5747,5796,5867,5960,6014,6334,6388,6448,6919,7401,7469,7520,7664,7726,7997,8050,8477,8540,8671,8825,8970,9034,9082,9128,9202,9295,9382,9433,9556,9654,9712,9777,9844,10119,10385,10565,10728,10813,11891,11961,23024,12016,12058,12150,12215,12273,12495,12544,12617,12687,12744,12882,12941,13071,13291,13331,13396,13434,13472,13508,13544,13613,13659,13718,13770,13834,23083,13904,13971,14031,14074,14117,14200,14566,14620,14694,14866,14937,15008,15045,15080,15116,15154,15208,15271,15343,15398,15479,15626,21165,15662,15720,15774,15861,15933,16008,16117,16237,16308,16348,16427,21620,21449,21361,22260,22798,16737,16777,16827,16878,16941,17028,22403,17077,17163,17213,21072,17257,23148,17388,17478,17570,17629,17700,17878,17954,18009,21733,18069,18124,22843,18183,18263,18360,18440,18488,22188,18558,18638,18680,18736,18786,18904,19227,19302,19349,19407,19481,19539,19592,23288,19632,19700,19916,20009,20075,20144,20199,20272,20346,20380,21883,20438,20495,20543,20581,20635,20692,20754,20809,20845,20881", "endColumns": "68,52,51,51,49,53,50,48,57,70,51,48,50,49,51,75,77,55,48,46,50,76,67,78,76,69,58,53,64,37,165,55,100,41,66,83,46,246,46,310,42,64,45,43,64,47,37,86,58,73,407,410,45,65,63,650,81,83,71,75,201,279,73,56,40,65,75,48,67,67,66,41,72,77,40,72,154,69,88,79,60,85,60,63,54,54,63,69,47,69,91,52,318,52,58,469,480,66,49,142,60,269,51,425,61,129,152,143,62,46,44,72,91,85,49,121,96,56,63,65,273,264,178,161,83,77,68,53,57,40,90,63,56,220,47,71,68,55,136,57,128,218,38,63,36,36,34,34,67,44,57,50,62,68,63,65,58,41,41,81,364,52,72,170,69,69,35,33,34,36,52,61,70,53,79,145,34,75,56,52,85,70,73,107,118,69,38,77,308,111,169,86,93,43,38,48,49,61,85,47,81,84,48,42,43,129,72,88,90,57,69,176,74,53,58,83,53,57,179,78,95,78,46,68,70,78,40,54,48,116,321,73,45,56,72,56,51,38,59,66,214,91,64,67,53,71,72,32,56,55,55,46,36,52,55,60,53,34,34,43", "endOffsets": "187,241,294,347,398,453,505,555,614,686,739,789,841,892,945,20997,1024,1081,1131,1179,1231,1309,1378,1458,1536,1607,1667,1722,23456,1761,1928,1985,2087,2130,2198,2283,21159,22182,22397,22792,2327,117,23390,21355,23282,2376,2415,2503,2563,2638,3047,3459,3506,11733,11798,11666,11014,11883,3579,3656,3859,4140,4215,4273,4315,4382,4459,4509,4578,21066,4646,4689,21310,4768,4810,4884,5040,5111,5201,5282,5344,5431,5493,21877,5549,5605,5670,5741,5790,5861,5954,6008,6328,6382,6442,6913,7395,7463,7514,7658,7720,7991,8044,8471,8534,8665,8819,8964,9028,9076,9122,9196,9289,9376,9427,9550,9648,9706,9771,9838,10113,10379,10559,10722,10807,10886,11955,12010,23077,12052,12144,12209,12267,12489,12538,12611,12681,12738,12876,12935,13065,13285,13325,13390,13428,13466,13502,13538,13607,13653,13712,13764,13828,13898,23142,13965,14025,14068,14111,14194,14560,14614,14688,14860,14931,15002,15039,15074,15110,15148,15202,15265,15337,15392,15473,15620,15656,21236,15714,15768,15855,15927,16002,16111,16231,16302,16342,16421,16731,21727,21614,21443,22349,22837,16771,16821,16872,16935,17022,17071,22480,17157,17207,17251,21111,17382,23216,17472,17564,17623,17694,17872,17948,18003,18063,21812,18118,18177,23018,18257,18354,18434,18482,18552,22254,18632,18674,18730,18780,18898,19221,19296,19343,19401,19475,19533,19586,19626,23343,19694,19910,20003,20069,20138,20193,20266,20340,20374,20432,21934,20489,20537,20575,20629,20686,20748,20803,20839,20875,20920"}, "to": {"startLines": "29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,47,48,51,52,63,64,66,67,68,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,117,118,119,120,121,129,130,131,134,135,136,137,138,139,140,141,142,143,144,145,146,168,169,170,171,173,174,175,176,177,178,179,180,181,182,183,184,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,210,211,212,213,214,216,268,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,294,295,297,299,300,302,303,304,305,306,307,308,309,310,311,369,370,371,372,386,387,433,440,441,442,443,444,446,447,448,449,450,451,452,453,462,463,464,465,466,467,468,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,487,488,489,490,491,492,500,501,505,507,508,509,510,511,512,513,514,516,517,518,519,520,521,522,526,527,528,531,533,534,535,536,537,540,541,542,543,544,545,546,547,548,549,551,552,553,554,555,556,557,558", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1325,1394,1447,1499,1551,1601,1655,1706,1755,1813,1884,1936,1985,2036,2086,2138,2431,2509,2791,2840,3825,3876,4060,4128,4207,4564,4634,4693,4747,4812,4850,5016,5072,5173,5215,5282,5366,5413,5660,5707,6018,6061,6126,6172,6216,6281,6329,6367,6454,6513,6587,6995,7406,7452,7518,7582,8233,8315,8399,8471,8547,8749,9029,9103,9160,9201,9696,9772,9821,9889,9957,10751,10793,10866,11178,11219,11292,11447,11517,11606,11686,11747,11833,11894,11958,12013,12068,14673,14743,14791,14861,15061,15114,15433,15486,15545,16015,16496,16563,16613,16756,16817,17087,17339,17765,17827,17957,18110,18254,18317,18364,18409,18482,18574,18660,18710,18832,18929,18986,19050,19116,19390,19655,19834,19996,20172,20250,20319,20373,20431,20547,24512,24997,25054,25275,25323,25395,25464,25520,25657,25715,25844,26063,26102,26166,26203,26240,26275,26310,26378,26423,26481,26609,26672,26823,26977,27043,27165,27207,27249,27331,27696,27749,27822,27993,28063,28133,34427,34461,34496,34533,35563,35625,39376,39912,39992,40138,40173,40249,40388,40441,40527,40598,40672,40780,40899,40969,41878,41956,42265,42377,42547,42634,42728,42852,42891,42940,42990,43052,43138,43186,43268,43353,43402,43445,43489,43619,43692,43781,44043,44101,44171,44348,44423,44477,44943,45027,45333,45463,45643,45722,45818,45897,45944,46013,46084,46242,46283,46338,46387,46504,46826,46900,47201,47258,47331,47542,47717,47756,47816,47883,48098,48369,48434,48502,48556,48628,48701,48734,48791,48847,48903,49010,49047,49100,49156,49217,49271,49306,49341", "endColumns": "68,52,51,51,49,53,50,48,57,70,51,48,50,49,51,75,77,55,48,46,50,76,67,78,76,69,58,53,64,37,165,55,100,41,66,83,46,246,46,310,42,64,45,43,64,47,37,86,58,73,407,410,45,65,63,650,81,83,71,75,201,279,73,56,40,65,75,48,67,67,66,41,72,77,40,72,154,69,88,79,60,85,60,63,54,54,63,69,47,69,91,52,318,52,58,469,480,66,49,142,60,269,51,425,61,129,152,143,62,46,44,72,91,85,49,121,96,56,63,65,273,264,178,161,83,77,68,53,57,40,90,63,56,220,47,71,68,55,136,57,128,218,38,63,36,36,34,34,67,44,57,50,62,68,63,65,58,41,41,81,364,52,72,170,69,69,35,33,34,36,52,61,70,53,79,145,34,75,56,52,85,70,73,107,118,69,38,77,308,111,169,86,93,43,38,48,49,61,85,47,81,84,48,42,43,129,72,88,90,57,69,176,74,53,58,83,53,57,179,78,95,78,46,68,70,78,40,54,48,116,321,73,45,56,72,56,51,38,59,66,214,91,64,67,53,71,72,32,56,55,55,46,36,52,55,60,53,34,34,43", "endOffsets": "1389,1442,1494,1546,1596,1650,1701,1750,1808,1879,1931,1980,2031,2081,2133,2209,2504,2560,2835,2882,3871,3948,4123,4202,4279,4629,4688,4742,4807,4845,5011,5067,5168,5210,5277,5361,5408,5655,5702,6013,6056,6121,6167,6211,6276,6324,6362,6449,6508,6582,6990,7401,7447,7513,7577,8228,8310,8394,8466,8542,8744,9024,9098,9155,9196,9262,9767,9816,9884,9952,10019,10788,10861,10939,11214,11287,11442,11512,11601,11681,11742,11828,11889,11953,12008,12063,12127,14738,14786,14856,14948,15109,15428,15481,15540,16010,16491,16558,16608,16751,16812,17082,17134,17760,17822,17952,18105,18249,18312,18359,18404,18477,18569,18655,18705,18827,18924,18981,19045,19111,19385,19650,19829,19991,20075,20245,20314,20368,20426,20467,20633,24571,25049,25270,25318,25390,25459,25515,25652,25710,25839,26058,26097,26161,26198,26235,26270,26305,26373,26418,26476,26527,26667,26736,26882,27038,27097,27202,27244,27326,27691,27744,27817,27988,28058,28128,28164,34456,34491,34528,34581,35620,35691,39425,39987,40133,40168,40244,40301,40436,40522,40593,40667,40775,40894,40964,41003,41951,42260,42372,42542,42629,42723,42767,42886,42935,42985,43047,43133,43181,43263,43348,43397,43440,43484,43614,43687,43776,43867,44096,44166,44343,44418,44472,44531,45022,45076,45386,45638,45717,45813,45892,45939,46008,46079,46158,46278,46333,46382,46499,46821,46895,46941,47253,47326,47383,47589,47751,47811,47878,48093,48185,48429,48497,48551,48623,48696,48729,48786,48842,48898,48945,49042,49095,49151,49212,49266,49301,49336,49380"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\93d3043f0a8b9466a00a736e170a6ddc\\transformed\\appcompat-1.7.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,6,7,10,11,12,13,14,15,16,17,18,19,22,26,27,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,507,612,888,980,1074,1169,1262,1357,1451,1547,1642,1734,2021,2442,2556,2822", "endColumns": "114,101,104,120,91,93,94,92,94,93,95,94,91,91,106,113,165,81", "endOffsets": "215,317,607,728,975,1069,1164,1257,1352,1446,1542,1637,1729,1821,2123,2551,2717,2899"}, "to": {"startLines": "45,46,49,50,53,54,55,56,57,58,59,60,61,62,65,69,70,502", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2214,2329,2565,2670,2887,2979,3073,3168,3261,3356,3450,3546,3641,3733,3953,4284,4398,45081", "endColumns": "114,101,104,120,91,93,94,92,94,93,95,94,91,91,106,113,165,81", "endOffsets": "2324,2426,2665,2786,2974,3068,3163,3256,3351,3445,3541,3636,3728,3820,4055,4393,4559,45158"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\179e6486bd57a16ea175623aa423e7ed\\transformed\\jetified-material3-release\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,289,407,525,624,721,835,976,1093,1233,1317,1415,1508,1606,1721,1844,1947,2076,2204,2330,2510,2634,2757,2884,3004,3098,3198,3319,3452,3550,3664,3771,3903,4041,4151,4251,4336,4431,4527,4650,4744,4831,4939,5019,5103,5201,5302,5396,5491,5579,5686,5784,5883,6030,6110,6216", "endColumns": "117,115,117,117,98,96,113,140,116,139,83,97,92,97,114,122,102,128,127,125,179,123,122,126,119,93,99,120,132,97,113,106,131,137,109,99,84,94,95,122,93,86,107,79,83,97,100,93,94,87,106,97,98,146,79,105,96", "endOffsets": "168,284,402,520,619,716,830,971,1088,1228,1312,1410,1503,1601,1716,1839,1942,2071,2199,2325,2505,2629,2752,2879,2999,3093,3193,3314,3447,3545,3659,3766,3898,4036,4146,4246,4331,4426,4522,4645,4739,4826,4934,5014,5098,5196,5297,5391,5486,5574,5681,5779,5878,6025,6105,6211,6308"}, "to": {"startLines": "312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "28169,28287,28403,28521,28639,28738,28835,28949,29090,29207,29347,29431,29529,29622,29720,29835,29958,30061,30190,30318,30444,30624,30748,30871,30998,31118,31212,31312,31433,31566,31664,31778,31885,32017,32155,32265,32365,32450,32545,32641,32764,32858,32945,33053,33133,33217,33315,33416,33510,33605,33693,33800,33898,33997,34144,34224,34330", "endColumns": "117,115,117,117,98,96,113,140,116,139,83,97,92,97,114,122,102,128,127,125,179,123,122,126,119,93,99,120,132,97,113,106,131,137,109,99,84,94,95,122,93,86,107,79,83,97,100,93,94,87,106,97,98,146,79,105,96", "endOffsets": "28282,28398,28516,28634,28733,28830,28944,29085,29202,29342,29426,29524,29617,29715,29830,29953,30056,30185,30313,30439,30619,30743,30866,30993,31113,31207,31307,31428,31561,31659,31773,31880,32012,32150,32260,32360,32445,32540,32636,32759,32853,32940,33048,33128,33212,33310,33411,33505,33600,33688,33795,33893,33992,34139,34219,34325,34422"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d56ddc8f70c1b6c4f2dfff25a6818549\\transformed\\jetified-play-services-base-18.5.0\\res\\values-ru\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,458,580,686,824,949,1060,1160,1337,1440,1599,1721,1884,2038,2103,2159", "endColumns": "102,161,121,105,137,124,110,99,176,102,158,121,162,153,64,55,81", "endOffsets": "295,457,579,685,823,948,1059,1159,1336,1439,1598,1720,1883,2037,2102,2158,2240"}, "to": {"startLines": "150,151,152,153,154,155,156,157,159,160,161,162,163,164,165,166,167", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12400,12507,12673,12799,12909,13051,13180,13295,13556,13737,13844,14007,14133,14300,14458,14527,14587", "endColumns": "106,165,125,109,141,128,114,103,180,106,162,125,166,157,68,59,85", "endOffsets": "12502,12668,12794,12904,13046,13175,13290,13394,13732,13839,14002,14128,14295,14453,14522,14582,14668"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9e0a763189144907fb0197c2b097244b\\transformed\\jetified-ui-release\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,281,379,481,573,655,745,833,915,999,1086,1234,1312,1388,1472,1542", "endColumns": "92,82,97,101,91,81,89,87,81,83,86,71,77,75,83,69,122", "endOffsets": "193,276,374,476,568,650,740,828,910,994,1081,1153,1307,1383,1467,1537,1660"}, "to": {"startLines": "148,149,185,186,209,296,298,439,445,485,486,506,523,524,529,530,532", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12224,12317,17139,17237,20080,26741,26887,39824,40306,43872,43956,45391,46946,47024,47388,47472,47594", "endColumns": "92,82,97,101,91,81,89,87,81,83,86,71,77,75,83,69,122", "endOffsets": "12312,12395,17232,17334,20167,26818,26972,39907,40383,43951,44038,45458,47019,47095,47467,47537,47712"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\237e0b5db534c615c4317f1b214e3e7f\\transformed\\jetified-play-services-ads-24.2.0\\res\\values-ru\\values.xml", "from": {"startLines": "4,5,6,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,241,291,588,652,814,952,1084,1134,1194,1338,1426,1475,1556,1593,1630,1677,1758,1805", "endColumns": "41,49,62,63,161,137,131,49,59,143,87,48,80,36,36,46,80,46,55", "endOffsets": "240,290,353,651,813,951,1083,1133,1193,1337,1425,1474,1555,1592,1629,1676,1757,1804,1860"}, "to": {"startLines": "434,435,436,454,455,456,457,458,459,460,461,493,494,495,496,497,498,499,550", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "39430,39476,39530,41008,41076,41242,41384,41520,41574,41638,41786,44536,44589,44674,44715,44756,44807,44892,48950", "endColumns": "45,53,66,67,165,141,135,53,63,147,91,52,84,40,40,50,84,50,59", "endOffsets": "39471,39525,39592,41071,41237,41379,41515,41569,41633,41781,41873,44584,44669,44710,44751,44802,44887,44938,49005"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5444be4bc77930bd89cfbb9f2224d8e4\\transformed\\navigation-ui-2.8.9\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,163", "endColumns": "107,118", "endOffsets": "158,277"}, "to": {"startLines": "437,438", "startColumns": "4,4", "startOffsets": "39597,39705", "endColumns": "107,118", "endOffsets": "39700,39819"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5b80dcbf636dc26335bd1b8e4f16f918\\transformed\\material-1.12.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,381,459,537,621,719,810,907,1044,1136,1211,1277,1376,1453,1516,1634,1695,1760,1817,1887,1948,2002,2118,2175,2237,2291,2365,2493,2581,2668,2771,2863,2949,3086,3170,3255,3389,3480,3556,3610,3661,3727,3799,3877,3948,4030,4110,4186,4263,4340,4447,4536,4609,4699,4794,4868,4949,5042,5097,5178,5244,5330,5415,5477,5541,5604,5676,5774,5873,5968,6060,6118,6173,6253,6347,6423", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "endColumns": "12,77,77,83,97,90,96,136,91,74,65,98,76,62,117,60,64,56,69,60,53,115,56,61,53,73,127,87,86,102,91,85,136,83,84,133,90,75,53,50,65,71,77,70,81,79,75,76,76,106,88,72,89,94,73,80,92,54,80,65,85,84,61,63,62,71,97,98,94,91,57,54,79,93,75,78", "endOffsets": "376,454,532,616,714,805,902,1039,1131,1206,1272,1371,1448,1511,1629,1690,1755,1812,1882,1943,1997,2113,2170,2232,2286,2360,2488,2576,2663,2766,2858,2944,3081,3165,3250,3384,3475,3551,3605,3656,3722,3794,3872,3943,4025,4105,4181,4258,4335,4442,4531,4604,4694,4789,4863,4944,5037,5092,5173,5239,5325,5410,5472,5536,5599,5671,5769,5868,5963,6055,6113,6168,6248,6342,6418,6497"}, "to": {"startLines": "23,112,113,114,115,116,132,133,147,215,217,269,293,301,373,374,375,376,377,378,379,380,381,382,383,384,385,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,469,503,504,515", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1044,9267,9345,9423,9507,9605,10944,11041,12132,20472,20638,24576,26532,27102,34586,34704,34765,34830,34887,34957,35018,35072,35188,35245,35307,35361,35435,35696,35784,35871,35974,36066,36152,36289,36373,36458,36592,36683,36759,36813,36864,36930,37002,37080,37151,37233,37313,37389,37466,37543,37650,37739,37812,37902,37997,38071,38152,38245,38300,38381,38447,38533,38618,38680,38744,38807,38879,38977,39076,39171,39263,39321,42772,45163,45257,46163", "endLines": "28,112,113,114,115,116,132,133,147,215,217,269,293,301,373,374,375,376,377,378,379,380,381,382,383,384,385,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,469,503,504,515", "endColumns": "12,77,77,83,97,90,96,136,91,74,65,98,76,62,117,60,64,56,69,60,53,115,56,61,53,73,127,87,86,102,91,85,136,83,84,133,90,75,53,50,65,71,77,70,81,79,75,76,76,106,88,72,89,94,73,80,92,54,80,65,85,84,61,63,62,71,97,98,94,91,57,54,79,93,75,78", "endOffsets": "1320,9340,9418,9502,9600,9691,11036,11173,12219,20542,20699,24670,26604,27160,34699,34760,34825,34882,34952,35013,35067,35183,35240,35302,35356,35430,35558,35779,35866,35969,36061,36147,36284,36368,36453,36587,36678,36754,36808,36859,36925,36997,37075,37146,37228,37308,37384,37461,37538,37645,37734,37807,37897,37992,38066,38147,38240,38295,38376,38442,38528,38613,38675,38739,38802,38874,38972,39071,39166,39258,39316,39371,42847,45252,45328,46237"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5c5c72c6ff4a7863322da50648a25e99\\transformed\\browser-1.8.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,163,268,380", "endColumns": "107,104,111,104", "endOffsets": "158,263,375,480"}, "to": {"startLines": "172,270,271,272", "startColumns": "4,4,4,4", "startOffsets": "14953,24675,24780,24892", "endColumns": "107,104,111,104", "endOffsets": "15056,24775,24887,24992"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b572512e02266e069f95737c22215ab9\\transformed\\jetified-media3-exoplayer-1.6.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,188,253,319,397,471,559,645", "endColumns": "71,60,64,65,77,73,87,85,76", "endOffsets": "122,183,248,314,392,466,554,640,717"}, "to": {"startLines": "242,243,244,245,246,247,248,249,250", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "22742,22814,22875,22940,23006,23084,23158,23246,23332", "endColumns": "71,60,64,65,77,73,87,85,76", "endOffsets": "22809,22870,22935,23001,23079,23153,23241,23327,23404"}}]}]}