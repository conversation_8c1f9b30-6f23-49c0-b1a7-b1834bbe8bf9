<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - Package com.tqhit.battery.one.features.emoji.presentation.customize</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>Package com.tqhit.battery.one.features.emoji.presentation.customize</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; com.tqhit.battery.one.features.emoji.presentation.customize</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">20</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">10</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.246s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox failures" id="successRate">
<div class="percent">50%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div id="tabs">
<ul class="tabLinks">
<li>
<a href="#tab0">Failed tests</a>
</li>
<li>
<a href="#tab1">Classes</a>
</li>
</ul>
<div id="tab0" class="tab">
<h2>Failed tests</h2>
<ul class="linkList">
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModelTest.html">CustomizeViewModelTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModelTest.html#handleEvent ChangeEmojiSize should clamp values">handleEvent ChangeEmojiSize should clamp values</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModelTest.html">CustomizeViewModelTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModelTest.html#handleEvent ChangePercentageColor should update color">handleEvent ChangePercentageColor should update color</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModelTest.html">CustomizeViewModelTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModelTest.html#handleEvent ChangePercentageFontSize should clamp values">handleEvent ChangePercentageFontSize should clamp values</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModelTest.html">CustomizeViewModelTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModelTest.html#handleEvent DismissError should clear errors">handleEvent DismissError should clear errors</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModelTest.html">CustomizeViewModelTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModelTest.html#handleEvent SelectBatteryContainer should update selection">handleEvent SelectBatteryContainer should update selection</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModelTest.html">CustomizeViewModelTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModelTest.html#handleEvent SelectEmojiCharacter should update selection">handleEvent SelectEmojiCharacter should update selection</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModelTest.html">CustomizeViewModelTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModelTest.html#handleEvent ToggleEmojiVisibility should update style config">handleEvent ToggleEmojiVisibility should update style config</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModelTest.html">CustomizeViewModelTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModelTest.html#handleEvent TogglePercentageVisibility should update style config">handleEvent TogglePercentageVisibility should update style config</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModelTest.html">CustomizeViewModelTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModelTest.html#initial state should be loading">initial state should be loading</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModelTest.html">CustomizeViewModelTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModelTest.html#save configuration failure should show error">save configuration failure should show error</a>
</li>
</ul>
</div>
<div id="tab1" class="tab">
<h2>Classes</h2>
<table>
<thead>
<tr>
<th>Class</th>
<th>Tests</th>
<th>Failures</th>
<th>Ignored</th>
<th>Duration</th>
<th>Success rate</th>
</tr>
</thead>
<tr>
<td class="failures">
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModelTest.html">CustomizeViewModelTest</a>
</td>
<td>20</td>
<td>10</td>
<td>0</td>
<td>0.246s</td>
<td class="failures">50%</td>
</tr>
</table>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="http://www.gradle.org">Gradle 8.11.1</a> at Jun 20, 2025, 4:39:53 PM</p>
</div>
</div>
</body>
</html>
