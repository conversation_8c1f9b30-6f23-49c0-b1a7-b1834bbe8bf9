<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - Package com.tqhit.battery.one.features.emoji.data.repository</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>Package com.tqhit.battery.one.features.emoji.data.repository</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; com.tqhit.battery.one.features.emoji.data.repository</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">36</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">27</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">1.781s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox failures" id="successRate">
<div class="percent">25%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div id="tabs">
<ul class="tabLinks">
<li>
<a href="#tab0">Failed tests</a>
</li>
<li>
<a href="#tab1">Classes</a>
</li>
</ul>
<div id="tab0" class="tab">
<h2>Failed tests</h2>
<ul class="linkList">
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest.html">BatteryStyleRepositoryImplTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest.html#test getAllStyles returns empty list when both remote and local fail">test getAllStyles returns empty list when both remote and local fail</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest.html">BatteryStyleRepositoryImplTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest.html#test getPopularStyles filters correctly">test getPopularStyles filters correctly</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest.html">BatteryStyleRepositoryImplTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest.html#test getPremiumStyles filters correctly">test getPremiumStyles filters correctly</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest.html">BatteryStyleRepositoryImplTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest.html#test getStylesByCategory filters correctly">test getStylesByCategory filters correctly</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest.html">BatteryStyleRepositoryImplTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest.html#test searchStyles filters by name correctly">test searchStyles filters by name correctly</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest.html">CustomizationRepositoryImplTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest.html#clearAllData should clear DataStore">clearAllData should clear DataStore</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest.html">CustomizationRepositoryImplTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest.html#customizationConfigFlow should expose DataStore flow">customizationConfigFlow should expose DataStore flow</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest.html">CustomizationRepositoryImplTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest.html#exportCustomizationData should serialize current data">exportCustomizationData should serialize current data</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest.html">CustomizationRepositoryImplTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest.html#getCurrentCustomizationConfig should return current config">getCurrentCustomizationConfig should return current config</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest.html">CustomizationRepositoryImplTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest.html#getCurrentCustomizationConfig should return default on error">getCurrentCustomizationConfig should return default on error</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest.html">CustomizationRepositoryImplTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest.html#getCurrentUserCustomization should return current customization">getCurrentUserCustomization should return current customization</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest.html">CustomizationRepositoryImplTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest.html#importCustomizationData should fail for invalid data format">importCustomizationData should fail for invalid data format</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest.html">CustomizationRepositoryImplTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest.html#importCustomizationData should fail for invalid version">importCustomizationData should fail for invalid version</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest.html">CustomizationRepositoryImplTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest.html#importCustomizationData should validate and import data">importCustomizationData should validate and import data</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest.html">CustomizationRepositoryImplTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest.html#recordStyleUsage should update usage history">recordStyleUsage should update usage history</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest.html">CustomizationRepositoryImplTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest.html#resetToDefaults should reset everything when not preserving preferences">resetToDefaults should reset everything when not preserving preferences</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest.html">CustomizationRepositoryImplTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest.html#resetToDefaults should reset with preserved preferences">resetToDefaults should reset with preserved preferences</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest.html">CustomizationRepositoryImplTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest.html#saveCustomizationConfig should handle DataStore errors">saveCustomizationConfig should handle DataStore errors</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest.html">CustomizationRepositoryImplTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest.html#saveCustomizationConfig should validate and save config">saveCustomizationConfig should validate and save config</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest.html">CustomizationRepositoryImplTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest.html#saveUserCustomization should validate and save customization">saveUserCustomization should validate and save customization</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest.html">CustomizationRepositoryImplTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest.html#saveUserPreferences should save preferences">saveUserPreferences should save preferences</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest.html">CustomizationRepositoryImplTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest.html#setFeatureEnabled should update feature state">setFeatureEnabled should update feature state</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest.html">CustomizationRepositoryImplTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest.html#updatePermissionStates should update permissions">updatePermissionStates should update permissions</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest.html">CustomizationRepositoryImplTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest.html#userCustomizationFlow should expose DataStore flow">userCustomizationFlow should expose DataStore flow</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest.html">CustomizationRepositoryImplTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest.html#userPreferencesFlow should expose DataStore flow">userPreferencesFlow should expose DataStore flow</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest.html">CustomizationRepositoryImplTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest.html#validateAndFixConfiguration should detect and fix issues">validateAndFixConfiguration should detect and fix issues</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest.html">CustomizationRepositoryImplTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest.html#validateAndFixConfiguration should pass for valid configuration">validateAndFixConfiguration should pass for valid configuration</a>
</li>
</ul>
</div>
<div id="tab1" class="tab">
<h2>Classes</h2>
<table>
<thead>
<tr>
<th>Class</th>
<th>Tests</th>
<th>Failures</th>
<th>Ignored</th>
<th>Duration</th>
<th>Success rate</th>
</tr>
</thead>
<tr>
<td class="failures">
<a href="../classes/com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest.html">BatteryStyleRepositoryImplTest</a>
</td>
<td>14</td>
<td>5</td>
<td>0</td>
<td>1.713s</td>
<td class="failures">64%</td>
</tr>
<tr>
<td class="failures">
<a href="../classes/com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest.html">CustomizationRepositoryImplTest</a>
</td>
<td>22</td>
<td>22</td>
<td>0</td>
<td>0.068s</td>
<td class="failures">0%</td>
</tr>
</table>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="http://www.gradle.org">Gradle 8.11.1</a> at Jun 20, 2025, 4:39:53 PM</p>
</div>
</div>
</body>
</html>
