{"logs": [{"outputFile": "com.tqhit.battery.one.app-mergeDebugResources-99:/values-de/values-de.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\237e0b5db534c615c4317f1b214e3e7f\\transformed\\jetified-play-services-ads-24.2.0\\res\\values-de\\values.xml", "from": {"startLines": "4,5,6,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,240,294,576,640,782,901,1019,1069,1127,1259,1348,1390,1488,1525,1561,1613,1702,1741", "endColumns": "40,53,55,63,141,118,117,49,57,131,88,41,97,36,35,51,88,38,55", "endOffsets": "239,293,349,639,781,900,1018,1068,1126,1258,1347,1389,1487,1524,1560,1612,1701,1740,1796"}, "to": {"startLines": "428,429,430,448,449,450,451,452,453,454,455,487,488,489,490,491,492,493,544", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "40757,40802,40860,42388,42456,42602,42725,42847,42901,42963,43099,45875,45921,46023,46064,46104,46160,46253,50451", "endColumns": "44,57,59,67,145,122,121,53,61,135,92,45,101,40,39,55,92,42,59", "endOffsets": "40797,40855,40915,42451,42597,42720,42842,42896,42958,43094,43187,45916,46018,46059,46099,46155,46248,46291,50506"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5b80dcbf636dc26335bd1b8e4f16f918\\transformed\\material-1.12.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,374,463,547,637,719,820,942,1023,1085,1151,1245,1315,1374,1482,1548,1617,1675,1747,1811,1865,1993,2053,2115,2169,2247,2384,2476,2554,2648,2734,2818,2963,3047,3133,3266,3356,3435,3492,3543,3609,3683,3765,3836,3911,3985,4063,4135,4209,4319,4411,4493,4582,4671,4745,4823,4909,4964,5043,5110,5190,5274,5336,5400,5463,5532,5639,5746,5845,5951,6012,6067,6149,6232,6309", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,90,88,83,89,81,100,121,80,61,65,93,69,58,107,65,68,57,71,63,53,127,59,61,53,77,136,91,77,93,85,83,144,83,85,132,89,78,56,50,65,73,81,70,74,73,77,71,73,109,91,81,88,88,73,77,85,54,78,66,79,83,61,63,62,68,106,106,98,105,60,54,81,82,76,75", "endOffsets": "278,369,458,542,632,714,815,937,1018,1080,1146,1240,1310,1369,1477,1543,1612,1670,1742,1806,1860,1988,2048,2110,2164,2242,2379,2471,2549,2643,2729,2813,2958,3042,3128,3261,3351,3430,3487,3538,3604,3678,3760,3831,3906,3980,4058,4130,4204,4314,4406,4488,4577,4666,4740,4818,4904,4959,5038,5105,5185,5269,5331,5395,5458,5527,5634,5741,5840,5946,6007,6062,6144,6227,6304,6380"}, "to": {"startLines": "19,106,107,108,109,110,126,127,141,209,211,263,287,295,367,368,369,370,371,372,373,374,375,376,377,378,379,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,463,497,498,509", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "787,9361,9452,9541,9625,9715,11050,11151,12256,21417,21588,25556,27553,28124,35875,35983,36049,36118,36176,36248,36312,36366,36494,36554,36616,36670,36748,37017,37109,37187,37281,37367,37451,37596,37680,37766,37899,37989,38068,38125,38176,38242,38316,38398,38469,38544,38618,38696,38768,38842,38952,39044,39126,39215,39304,39378,39456,39542,39597,39676,39743,39823,39907,39969,40033,40096,40165,40272,40379,40478,40584,40645,44093,46513,46596,47570", "endLines": "22,106,107,108,109,110,126,127,141,209,211,263,287,295,367,368,369,370,371,372,373,374,375,376,377,378,379,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,463,497,498,509", "endColumns": "12,90,88,83,89,81,100,121,80,61,65,93,69,58,107,65,68,57,71,63,53,127,59,61,53,77,136,91,77,93,85,83,144,83,85,132,89,78,56,50,65,73,81,70,74,73,77,71,73,109,91,81,88,88,73,77,85,54,78,66,79,83,61,63,62,68,106,106,98,105,60,54,81,82,76,75", "endOffsets": "965,9447,9536,9620,9710,9792,11146,11268,12332,21474,21649,25645,27618,28178,35978,36044,36113,36171,36243,36307,36361,36489,36549,36611,36665,36743,36880,37104,37182,37276,37362,37446,37591,37675,37761,37894,37984,38063,38120,38171,38237,38311,38393,38464,38539,38613,38691,38763,38837,38947,39039,39121,39210,39299,39373,39451,39537,39592,39671,39738,39818,39902,39964,40028,40091,40160,40267,40374,40473,40579,40640,40695,44170,46591,46668,47641"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5c5c72c6ff4a7863322da50648a25e99\\transformed\\browser-1.8.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,159,260,371", "endColumns": "103,100,110,99", "endOffsets": "154,255,366,466"}, "to": {"startLines": "166,264,265,266", "startColumns": "4,4,4,4", "startOffsets": "15161,25650,25751,25862", "endColumns": "103,100,110,99", "endOffsets": "15260,25746,25857,25957"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f908cdc45776521b403beeef1508641c\\transformed\\core-1.16.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,455,563,668,786", "endColumns": "97,101,99,99,107,104,117,100", "endOffsets": "148,250,350,450,558,663,781,882"}, "to": {"startLines": "116,117,118,119,120,121,122,519", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "10122,10220,10322,10422,10522,10630,10735,48591", "endColumns": "97,101,99,99,107,104,117,100", "endOffsets": "10215,10317,10417,10517,10625,10730,10848,48687"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5444be4bc77930bd89cfbb9f2224d8e4\\transformed\\navigation-ui-2.8.9\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,159", "endColumns": "103,118", "endOffsets": "154,273"}, "to": {"startLines": "431,432", "startColumns": "4,4", "startOffsets": "40920,41024", "endColumns": "103,118", "endOffsets": "41019,41138"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8bd7feaae90e869538df51f29dd16595\\transformed\\jetified-media3-ui-1.6.1\\res\\values-de\\values-de.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,311,501,692,779,867,942,1032,1118,1197,1262,1366,1470,1539,1609,1681,1750,1877,2005,2138,2211,2295,2371,2448,2535,2623,2689,2754,2807,2867,2915,2976,3048,3118,3183,3254,3319,3377,3443,3495,3557,3633,3709,3765", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,86,87,74,89,85,78,64,103,103,68,69,71,68,126,127,132,72,83,75,76,86,87,65,64,52,59,47,60,71,69,64,70,64,57,65,51,61,75,75,55,67", "endOffsets": "306,496,687,774,862,937,1027,1113,1192,1257,1361,1465,1534,1604,1676,1745,1872,2000,2133,2206,2290,2366,2443,2530,2618,2684,2749,2802,2862,2910,2971,3043,3113,3178,3249,3314,3372,3438,3490,3552,3628,3704,3760,3828"}, "to": {"startLines": "2,11,15,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,406,596,21654,21741,21829,21904,21994,22080,22159,22224,22328,22432,22501,22571,22643,22712,22839,22967,23100,23173,23257,23333,23410,23497,23585,23651,24411,24464,24524,24572,24633,24705,24775,24840,24911,24976,25034,25100,25152,25214,25290,25366,25422", "endLines": "10,14,18,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261", "endColumns": "17,12,12,86,87,74,89,85,78,64,103,103,68,69,71,68,126,127,132,72,83,75,76,86,87,65,64,52,59,47,60,71,69,64,70,64,57,65,51,61,75,75,55,67", "endOffsets": "401,591,782,21736,21824,21899,21989,22075,22154,22219,22323,22427,22496,22566,22638,22707,22834,22962,23095,23168,23252,23328,23405,23492,23580,23646,23711,24459,24519,24567,24628,24700,24770,24835,24906,24971,25029,25095,25147,25209,25285,25361,25417,25485"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d56ddc8f70c1b6c4f2dfff25a6818549\\transformed\\jetified-play-services-base-18.5.0\\res\\values-de\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,298,458,582,690,864,991,1108,1223,1399,1507,1672,1799,1957,2129,2196,2255", "endColumns": "104,159,123,107,173,126,116,114,175,107,164,126,157,171,66,58,75", "endOffsets": "297,457,581,689,863,990,1107,1222,1398,1506,1671,1798,1956,2128,2195,2254,2330"}, "to": {"startLines": "144,145,146,147,148,149,150,151,153,154,155,156,157,158,159,160,161", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12521,12630,12794,12922,13034,13212,13343,13464,13728,13908,14020,14189,14320,14482,14658,14729,14792", "endColumns": "108,163,127,111,177,130,120,118,179,111,168,130,161,175,70,62,79", "endOffsets": "12625,12789,12917,13029,13207,13338,13459,13578,13903,14015,14184,14315,14477,14653,14724,14787,14867"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9e0a763189144907fb0197c2b097244b\\transformed\\jetified-ui-release\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,289,387,487,574,659,751,840,928,1009,1093,1243,1315,1385,1464,1530", "endColumns": "95,87,97,99,86,84,91,88,87,80,83,74,71,69,78,65,119", "endOffsets": "196,284,382,482,569,654,746,835,923,1004,1088,1163,1310,1380,1459,1525,1645"}, "to": {"startLines": "142,143,179,180,203,290,292,433,439,479,480,500,517,518,523,524,526", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12337,12433,17872,17970,21022,27761,27912,41143,41646,45184,45265,46733,48449,48521,48889,48968,49085", "endColumns": "95,87,97,99,86,84,91,88,87,80,83,74,71,69,78,65,119", "endOffsets": "12428,12516,17965,18065,21104,27841,27999,41227,41729,45260,45344,46803,48516,48586,48963,49029,49200"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b572512e02266e069f95737c22215ab9\\transformed\\jetified-media3-exoplayer-1.6.1\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,196,270,342,419,486,583,674", "endColumns": "73,66,73,71,76,66,96,90,75", "endOffsets": "124,191,265,337,414,481,578,669,745"}, "to": {"startLines": "236,237,238,239,240,241,242,243,244", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "23716,23790,23857,23931,24003,24080,24147,24244,24335", "endColumns": "73,66,73,71,76,66,96,90,75", "endOffsets": "23785,23852,23926,23998,24075,24142,24239,24330,24406"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\42f95d9fa807b14415e836fc15872a54\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-de\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "140", "endOffsets": "335"}, "to": {"startLines": "152", "startColumns": "4", "startOffsets": "13583", "endColumns": "144", "endOffsets": "13723"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\93d3043f0a8b9466a00a736e170a6ddc\\transformed\\appcompat-1.7.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,6,7,10,11,12,13,14,15,16,17,18,19,22,26,27,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,506,612,880,972,1066,1162,1263,1370,1470,1574,1672,1770,2060,2470,2574,2832", "endColumns": "104,97,105,114,91,93,95,100,106,99,103,97,97,96,101,103,155,81", "endOffsets": "205,303,607,722,967,1061,1157,1258,1365,1465,1569,1667,1765,1862,2157,2569,2725,2909"}, "to": {"startLines": "39,40,43,44,47,48,49,50,51,52,53,54,55,56,59,63,64,496", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1864,1969,2205,2311,2521,2613,2707,2803,2904,3011,3111,3215,3313,3411,3637,3960,4064,46431", "endColumns": "104,97,105,114,91,93,95,100,106,99,103,97,97,96,101,103,155,81", "endOffsets": "1964,2062,2306,2421,2608,2702,2798,2899,3006,3106,3210,3308,3406,3503,3734,4059,4215,46508"}}, {"source": "D:\\Duc\\AndroidStudioProjects\\TJ_BatteryOne\\app\\src\\main\\res\\values-de\\strings.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,237,18,19,20,21,22,23,24,25,26,27,28,29,264,30,31,32,33,34,35,36,240,250,253,255,37,2,263,243,261,38,39,40,41,42,43,44,45,115,116,114,113,117,46,47,48,49,50,51,52,53,54,55,56,238,57,58,242,59,60,61,62,63,64,65,66,67,68,248,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,119,120,258,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,259,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,241,168,169,170,171,172,173,174,175,176,177,178,246,245,244,252,256,179,180,181,182,183,184,254,185,186,187,239,188,260,189,190,191,192,193,194,195,196,247,197,198,257,199,200,201,202,203,251,204,205,206,207,208,209,210,211,212,213,214,215,216,262,217,218,219,220,221,222,223,224,225,226,249,227,228,229,230,231,232,233,234,235,236", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "121,187,242,293,346,396,450,502,552,611,677,730,781,832,883,22527,936,1019,1076,1125,1173,1226,1304,1374,1454,1528,1601,1668,25142,1723,1763,1938,1995,2085,2128,2191,22738,23576,24035,24160,2279,57,25094,22943,24963,2322,2372,2411,2501,2573,2661,3176,3694,12774,12840,12066,11980,12907,3735,3809,3880,4107,4429,4506,4555,4597,4659,4728,4781,22622,4851,4918,22864,4964,5039,5081,5154,5337,5407,5500,5577,5643,5730,23455,5801,5856,5911,5973,6034,6082,6159,6266,6316,6721,6779,6841,7491,8164,8236,8285,8439,8507,8834,8885,9439,9500,9633,9795,9955,10017,10060,10108,10184,10283,10369,10420,10550,10645,10702,10768,10835,11140,11403,11614,11778,11859,12994,13071,24766,13127,13169,13279,13346,13404,13634,13686,13765,13833,13895,14042,14101,14247,14492,14531,14590,14628,14666,14702,14738,14812,14859,14912,14957,15024,24828,15097,15162,15219,15265,15305,15380,15846,15907,15991,16177,16244,16328,16365,16400,16441,16484,16548,16609,16682,16740,16825,16981,22787,17018,17082,17141,17236,17307,17384,17494,17622,17705,17744,17820,23262,23075,22987,23942,24507,18126,18166,18221,18272,18334,18425,24091,18476,18558,18607,22693,18651,24895,18780,18873,18968,19027,19097,19304,19377,19435,23373,19500,19555,24554,19616,19703,19812,19898,19950,23867,20022,20099,20144,20203,20250,20375,20787,20863,20909,20963,21048,21109,21161,25032,21202,21270,21497,21579,21646,21723,21780,21867,21930,21969,23517,22021,22078,22124,22166,22220,22279,22351,22410,22446,22482", "endColumns": "64,53,49,51,48,52,50,48,57,64,51,49,49,49,51,93,81,55,47,46,51,76,68,78,72,71,65,53,69,38,173,55,88,41,61,86,47,289,54,345,41,62,46,42,67,48,37,88,70,86,513,516,39,64,65,706,84,83,72,69,225,320,75,47,40,60,67,51,68,69,65,44,77,73,40,71,181,68,91,75,64,85,69,60,53,53,60,59,46,75,105,48,403,56,60,648,671,70,47,152,66,325,49,552,59,131,160,158,60,41,46,74,97,84,49,128,93,55,64,65,303,261,209,162,79,74,75,54,60,40,108,65,56,228,50,77,66,60,145,57,144,243,37,57,36,36,34,34,72,45,51,43,65,71,65,63,55,44,38,73,464,59,82,184,65,82,35,33,39,41,62,59,71,56,83,154,35,75,62,57,93,69,75,108,126,81,37,74,304,109,185,86,91,45,38,53,49,60,89,49,67,80,47,42,43,127,66,91,93,57,68,205,71,56,63,80,53,59,210,85,107,84,50,70,73,75,43,57,45,123,410,74,44,52,83,59,50,39,60,66,225,80,65,75,55,85,61,37,50,57,55,44,40,52,57,70,57,34,34,43", "endOffsets": "181,236,287,340,390,444,496,546,605,671,724,775,826,877,930,22616,1013,1070,1119,1167,1220,1298,1368,1448,1522,1595,1662,1717,25207,1757,1932,1989,2079,2122,2185,2273,22781,23861,24085,24501,2316,115,25136,22981,25026,2366,2405,2495,2567,2655,3170,3688,3729,12834,12901,12768,12060,12986,3803,3874,4101,4423,4500,4549,4591,4653,4722,4775,4845,22687,4912,4958,22937,5033,5075,5148,5331,5401,5494,5571,5637,5724,5795,23511,5850,5905,5967,6028,6076,6153,6260,6310,6715,6773,6835,7485,8158,8230,8279,8433,8501,8828,8879,9433,9494,9627,9789,9949,10011,10054,10102,10178,10277,10363,10414,10544,10639,10696,10762,10829,11134,11397,11608,11772,11853,11929,13065,13121,24822,13163,13273,13340,13398,13628,13680,13759,13827,13889,14036,14095,14241,14486,14525,14584,14622,14660,14696,14732,14806,14853,14906,14951,15018,15091,24889,15156,15213,15259,15299,15374,15840,15901,15985,16171,16238,16322,16359,16394,16435,16478,16542,16603,16676,16734,16819,16975,17012,22858,17076,17135,17230,17301,17378,17488,17616,17699,17738,17814,18120,23367,23256,23069,24029,24548,18160,18215,18266,18328,18419,18470,24154,18552,18601,18645,22732,18774,24957,18867,18962,19021,19091,19298,19371,19429,19494,23449,19549,19610,24760,19697,19806,19892,19944,20016,23936,20093,20138,20197,20244,20369,20781,20857,20903,20957,21042,21103,21155,21196,25088,21264,21491,21573,21640,21717,21774,21861,21924,21963,22015,23570,22072,22118,22160,22214,22273,22345,22404,22440,22476,22521"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,41,42,45,46,57,58,60,61,62,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,111,112,113,114,115,123,124,125,128,129,130,131,132,133,134,135,136,137,138,139,140,162,163,164,165,167,168,169,170,171,172,173,174,175,176,177,178,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,204,205,206,207,208,210,262,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,288,289,291,293,294,296,297,298,299,300,301,302,303,304,305,363,364,365,366,380,381,427,434,435,436,437,438,440,441,442,443,444,445,446,447,456,457,458,459,460,461,462,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,481,482,483,484,485,486,494,495,499,501,502,503,504,505,506,507,508,510,511,512,513,514,515,516,520,521,522,525,527,528,529,530,531,534,535,536,537,538,539,540,541,542,543,545,546,547,548,549,550,551,552", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "970,1035,1089,1139,1191,1240,1293,1344,1393,1451,1516,1568,1618,1668,1718,1770,2067,2149,2426,2474,3508,3560,3739,3808,3887,4220,4292,4358,4412,4482,4521,4695,4751,4840,4882,4944,5031,5079,5369,5424,5770,5812,5875,5922,5965,6033,6082,6120,6209,6280,6367,6881,7398,7438,7503,7569,8276,8361,8445,8518,8588,8814,9135,9211,9259,9300,9797,9865,9917,9986,10056,10853,10898,10976,11273,11314,11386,11568,11637,11729,11805,11870,11956,12026,12087,12141,12195,14872,14932,14979,15055,15265,15314,15718,15775,15836,16485,17157,17228,17276,17429,17496,17822,18070,18623,18683,18815,18976,19135,19196,19238,19285,19360,19458,19543,19593,19722,19816,19872,19937,20003,20307,20569,20779,20942,21109,21184,21260,21315,21376,21479,25490,25962,26019,26248,26299,26377,26444,26505,26651,26709,26854,27098,27136,27194,27231,27268,27303,27338,27411,27457,27509,27623,27689,27846,28004,28068,28183,28228,28267,28341,28806,28866,28949,29134,29200,29283,35696,35730,35770,35812,36885,36945,40700,41232,41316,41471,41507,41583,41734,41792,41886,41956,42032,42141,42268,42350,43192,43267,43572,43682,43868,43955,44047,44175,44214,44268,44318,44379,44469,44519,44587,44668,44716,44759,44803,44931,44998,45090,45349,45407,45476,45682,45754,45811,46296,46377,46673,46808,47019,47105,47213,47298,47349,47420,47494,47646,47690,47748,47794,47918,48329,48404,48692,48745,48829,49034,49205,49245,49306,49373,49599,49857,49923,49999,50055,50141,50203,50241,50292,50350,50406,50511,50552,50605,50663,50734,50792,50827,50862", "endColumns": "64,53,49,51,48,52,50,48,57,64,51,49,49,49,51,93,81,55,47,46,51,76,68,78,72,71,65,53,69,38,173,55,88,41,61,86,47,289,54,345,41,62,46,42,67,48,37,88,70,86,513,516,39,64,65,706,84,83,72,69,225,320,75,47,40,60,67,51,68,69,65,44,77,73,40,71,181,68,91,75,64,85,69,60,53,53,60,59,46,75,105,48,403,56,60,648,671,70,47,152,66,325,49,552,59,131,160,158,60,41,46,74,97,84,49,128,93,55,64,65,303,261,209,162,79,74,75,54,60,40,108,65,56,228,50,77,66,60,145,57,144,243,37,57,36,36,34,34,72,45,51,43,65,71,65,63,55,44,38,73,464,59,82,184,65,82,35,33,39,41,62,59,71,56,83,154,35,75,62,57,93,69,75,108,126,81,37,74,304,109,185,86,91,45,38,53,49,60,89,49,67,80,47,42,43,127,66,91,93,57,68,205,71,56,63,80,53,59,210,85,107,84,50,70,73,75,43,57,45,123,410,74,44,52,83,59,50,39,60,66,225,80,65,75,55,85,61,37,50,57,55,44,40,52,57,70,57,34,34,43", "endOffsets": "1030,1084,1134,1186,1235,1288,1339,1388,1446,1511,1563,1613,1663,1713,1765,1859,2144,2200,2469,2516,3555,3632,3803,3882,3955,4287,4353,4407,4477,4516,4690,4746,4835,4877,4939,5026,5074,5364,5419,5765,5807,5870,5917,5960,6028,6077,6115,6204,6275,6362,6876,7393,7433,7498,7564,8271,8356,8440,8513,8583,8809,9130,9206,9254,9295,9356,9860,9912,9981,10051,10117,10893,10971,11045,11309,11381,11563,11632,11724,11800,11865,11951,12021,12082,12136,12190,12251,14927,14974,15050,15156,15309,15713,15770,15831,16480,17152,17223,17271,17424,17491,17817,17867,18618,18678,18810,18971,19130,19191,19233,19280,19355,19453,19538,19588,19717,19811,19867,19932,19998,20302,20564,20774,20937,21017,21179,21255,21310,21371,21412,21583,25551,26014,26243,26294,26372,26439,26500,26646,26704,26849,27093,27131,27189,27226,27263,27298,27333,27406,27452,27504,27548,27684,27756,27907,28063,28119,28223,28262,28336,28801,28861,28944,29129,29195,29278,29314,35725,35765,35807,35870,36940,37012,40752,41311,41466,41502,41578,41641,41787,41881,41951,42027,42136,42263,42345,42383,43262,43567,43677,43863,43950,44042,44088,44209,44263,44313,44374,44464,44514,44582,44663,44711,44754,44798,44926,44993,45085,45179,45402,45471,45677,45749,45806,45870,46372,46426,46728,47014,47100,47208,47293,47344,47415,47489,47565,47685,47743,47789,47913,48324,48399,48444,48740,48824,48884,49080,49240,49301,49368,49594,49675,49918,49994,50050,50136,50198,50236,50287,50345,50401,50446,50547,50600,50658,50729,50787,50822,50857,50901"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\179e6486bd57a16ea175623aa423e7ed\\transformed\\jetified-material3-release\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,186,315,424,553,663,758,870,1014,1132,1288,1373,1478,1573,1675,1793,1919,2029,2165,2302,2437,2616,2744,2867,2995,3120,3216,3314,3434,3563,3663,3768,3870,4011,4159,4265,4367,4447,4543,4638,4758,4844,4933,5034,5114,5200,5300,5406,5501,5602,5690,5799,5900,6004,6142,6231,6336", "endColumns": "130,128,108,128,109,94,111,143,117,155,84,104,94,101,117,125,109,135,136,134,178,127,122,127,124,95,97,119,128,99,104,101,140,147,105,101,79,95,94,119,85,88,100,79,85,99,105,94,100,87,108,100,103,137,88,104,95", "endOffsets": "181,310,419,548,658,753,865,1009,1127,1283,1368,1473,1568,1670,1788,1914,2024,2160,2297,2432,2611,2739,2862,2990,3115,3211,3309,3429,3558,3658,3763,3865,4006,4154,4260,4362,4442,4538,4633,4753,4839,4928,5029,5109,5195,5295,5401,5496,5597,5685,5794,5895,5999,6137,6226,6331,6427"}, "to": {"startLines": "306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "29319,29450,29579,29688,29817,29927,30022,30134,30278,30396,30552,30637,30742,30837,30939,31057,31183,31293,31429,31566,31701,31880,32008,32131,32259,32384,32480,32578,32698,32827,32927,33032,33134,33275,33423,33529,33631,33711,33807,33902,34022,34108,34197,34298,34378,34464,34564,34670,34765,34866,34954,35063,35164,35268,35406,35495,35600", "endColumns": "130,128,108,128,109,94,111,143,117,155,84,104,94,101,117,125,109,135,136,134,178,127,122,127,124,95,97,119,128,99,104,101,140,147,105,101,79,95,94,119,85,88,100,79,85,99,105,94,100,87,108,100,103,137,88,104,95", "endOffsets": "29445,29574,29683,29812,29922,30017,30129,30273,30391,30547,30632,30737,30832,30934,31052,31178,31288,31424,31561,31696,31875,32003,32126,32254,32379,32475,32573,32693,32822,32922,33027,33129,33270,33418,33524,33626,33706,33802,33897,34017,34103,34192,34293,34373,34459,34559,34665,34760,34861,34949,35058,35159,35263,35401,35490,35595,35691"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\237635df39b25799c092d66a208ce67d\\transformed\\jetified-foundation-release\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,89", "endOffsets": "137,227"}, "to": {"startLines": "532,533", "startColumns": "4,4", "startOffsets": "49680,49767", "endColumns": "86,89", "endOffsets": "49762,49852"}}]}]}