package com.tqhit.battery.one.features.emoji.domain.use_case;

import com.tqhit.battery.one.features.emoji.domain.repository.BatteryStyleRepository;
import com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class SaveCustomizationUseCase_Factory implements Factory<SaveCustomizationUseCase> {
  private final Provider<CustomizationRepository> customizationRepositoryProvider;

  private final Provider<BatteryStyleRepository> batteryStyleRepositoryProvider;

  public SaveCustomizationUseCase_Factory(
      Provider<CustomizationRepository> customizationRepositoryProvider,
      Provider<BatteryStyleRepository> batteryStyleRepositoryProvider) {
    this.customizationRepositoryProvider = customizationRepositoryProvider;
    this.batteryStyleRepositoryProvider = batteryStyleRepositoryProvider;
  }

  @Override
  public SaveCustomizationUseCase get() {
    return newInstance(customizationRepositoryProvider.get(), batteryStyleRepositoryProvider.get());
  }

  public static SaveCustomizationUseCase_Factory create(
      Provider<CustomizationRepository> customizationRepositoryProvider,
      Provider<BatteryStyleRepository> batteryStyleRepositoryProvider) {
    return new SaveCustomizationUseCase_Factory(customizationRepositoryProvider, batteryStyleRepositoryProvider);
  }

  public static SaveCustomizationUseCase newInstance(
      CustomizationRepository customizationRepository,
      BatteryStyleRepository batteryStyleRepository) {
    return new SaveCustomizationUseCase(customizationRepository, batteryStyleRepository);
  }
}
