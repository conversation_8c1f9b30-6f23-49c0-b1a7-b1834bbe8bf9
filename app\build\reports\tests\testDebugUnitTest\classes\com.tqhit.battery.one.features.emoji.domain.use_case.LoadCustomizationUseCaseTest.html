<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - Class com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>Class com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; 
<a href="../packages/com.tqhit.battery.one.features.emoji.domain.use_case.html">com.tqhit.battery.one.features.emoji.domain.use_case</a> &gt; LoadCustomizationUseCaseTest</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">19</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">19</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.047s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox failures" id="successRate">
<div class="percent">0%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div id="tabs">
<ul class="tabLinks">
<li>
<a href="#tab0">Failed tests</a>
</li>
<li>
<a href="#tab1">Tests</a>
</li>
</ul>
<div id="tab0" class="tab">
<h2>Failed tests</h2>
<div class="test">
<a name="featureStatusFlow should emit correct status changes"></a>
<h3 class="failures">featureStatusFlow should emit correct status changes</h3>
<span class="code">
<pre>io.mockk.MockKException: no answer found for CustomizationRepository(#226).getUserCustomizationFlow() among the configured answers: ()
	at app//io.mockk.impl.stub.MockKStub.defaultAnswer(MockKStub.kt:91)
	at app//io.mockk.impl.stub.MockKStub.answer(MockKStub.kt:42)
	at app//io.mockk.impl.recording.states.AnsweringState.call(AnsweringState.kt:16)
	at app//io.mockk.impl.recording.CommonCallRecorder.call(CommonCallRecorder.kt:53)
	at app//io.mockk.impl.stub.MockKStub.handleInvocation(MockKStub.kt:269)
	at app//io.mockk.impl.instantiation.JvmMockFactoryHelper$mockHandler$1.invocation(JvmMockFactoryHelper.kt:24)
	at app//io.mockk.proxy.jvm.advice.Interceptor.call(Interceptor.kt:21)
	at app//io.mockk.proxy.jvm.advice.BaseAdvice.handle(BaseAdvice.kt:42)
	at app//io.mockk.proxy.jvm.advice.jvm.JvmMockKProxyInterceptor.interceptNoSuper(JvmMockKProxyInterceptor.java:45)
	at app//com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository$Subclass7.getUserCustomizationFlow(Unknown Source)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase.&lt;init&gt;(LoadCustomizationUseCase.kt:47)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest.setUp(LoadCustomizationUseCaseTest.kt:72)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at app//org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at app//org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at app//org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at app//org.junit.internal.runners.statements.RunBefores.invokeMethod(RunBefores.java:33)
	at app//org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:24)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at app//org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at app//org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at app//org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at app//org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at app//org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at app//org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.runTestClass(JUnitTestClassExecutor.java:112)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:58)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:40)
	at org.gradle.api.internal.tasks.testing.junit.AbstractJUnitTestClassProcessor.processTestClass(AbstractJUnitTestClassProcessor.java:54)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.processTestClass(SuiteTestClassProcessor.java:53)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.processTestClass(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$2.run(TestWorker.java:183)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
</pre>
</span>
</div>
<div class="test">
<a name="getCurrentCustomizationConfig should return current configuration"></a>
<h3 class="failures">getCurrentCustomizationConfig should return current configuration</h3>
<span class="code">
<pre>io.mockk.MockKException: no answer found for CustomizationRepository(#230).getUserCustomizationFlow() among the configured answers: ()
	at app//io.mockk.impl.stub.MockKStub.defaultAnswer(MockKStub.kt:91)
	at app//io.mockk.impl.stub.MockKStub.answer(MockKStub.kt:42)
	at app//io.mockk.impl.recording.states.AnsweringState.call(AnsweringState.kt:16)
	at app//io.mockk.impl.recording.CommonCallRecorder.call(CommonCallRecorder.kt:53)
	at app//io.mockk.impl.stub.MockKStub.handleInvocation(MockKStub.kt:269)
	at app//io.mockk.impl.instantiation.JvmMockFactoryHelper$mockHandler$1.invocation(JvmMockFactoryHelper.kt:24)
	at app//io.mockk.proxy.jvm.advice.Interceptor.call(Interceptor.kt:21)
	at app//io.mockk.proxy.jvm.advice.BaseAdvice.handle(BaseAdvice.kt:42)
	at app//io.mockk.proxy.jvm.advice.jvm.JvmMockKProxyInterceptor.interceptNoSuper(JvmMockKProxyInterceptor.java:45)
	at app//com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository$Subclass7.getUserCustomizationFlow(Unknown Source)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase.&lt;init&gt;(LoadCustomizationUseCase.kt:47)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest.setUp(LoadCustomizationUseCaseTest.kt:72)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at app//org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at app//org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at app//org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at app//org.junit.internal.runners.statements.RunBefores.invokeMethod(RunBefores.java:33)
	at app//org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:24)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at app//org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at app//org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at app//org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at app//org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at app//org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at app//org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.runTestClass(JUnitTestClassExecutor.java:112)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:58)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:40)
	at org.gradle.api.internal.tasks.testing.junit.AbstractJUnitTestClassProcessor.processTestClass(AbstractJUnitTestClassProcessor.java:54)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.processTestClass(SuiteTestClassProcessor.java:53)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.processTestClass(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$2.run(TestWorker.java:183)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
</pre>
</span>
</div>
<div class="test">
<a name="getCurrentCustomizationConfig should return default on error"></a>
<h3 class="failures">getCurrentCustomizationConfig should return default on error</h3>
<span class="code">
<pre>io.mockk.MockKException: no answer found for CustomizationRepository(#228).getUserCustomizationFlow() among the configured answers: ()
	at app//io.mockk.impl.stub.MockKStub.defaultAnswer(MockKStub.kt:91)
	at app//io.mockk.impl.stub.MockKStub.answer(MockKStub.kt:42)
	at app//io.mockk.impl.recording.states.AnsweringState.call(AnsweringState.kt:16)
	at app//io.mockk.impl.recording.CommonCallRecorder.call(CommonCallRecorder.kt:53)
	at app//io.mockk.impl.stub.MockKStub.handleInvocation(MockKStub.kt:269)
	at app//io.mockk.impl.instantiation.JvmMockFactoryHelper$mockHandler$1.invocation(JvmMockFactoryHelper.kt:24)
	at app//io.mockk.proxy.jvm.advice.Interceptor.call(Interceptor.kt:21)
	at app//io.mockk.proxy.jvm.advice.BaseAdvice.handle(BaseAdvice.kt:42)
	at app//io.mockk.proxy.jvm.advice.jvm.JvmMockKProxyInterceptor.interceptNoSuper(JvmMockKProxyInterceptor.java:45)
	at app//com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository$Subclass7.getUserCustomizationFlow(Unknown Source)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase.&lt;init&gt;(LoadCustomizationUseCase.kt:47)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest.setUp(LoadCustomizationUseCaseTest.kt:72)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at app//org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at app//org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at app//org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at app//org.junit.internal.runners.statements.RunBefores.invokeMethod(RunBefores.java:33)
	at app//org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:24)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at app//org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at app//org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at app//org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at app//org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at app//org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at app//org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.runTestClass(JUnitTestClassExecutor.java:112)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:58)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:40)
	at org.gradle.api.internal.tasks.testing.junit.AbstractJUnitTestClassProcessor.processTestClass(AbstractJUnitTestClassProcessor.java:54)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.processTestClass(SuiteTestClassProcessor.java:53)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.processTestClass(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$2.run(TestWorker.java:183)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
</pre>
</span>
</div>
<div class="test">
<a name="getCurrentEnrichedUserCustomization should return enriched data"></a>
<h3 class="failures">getCurrentEnrichedUserCustomization should return enriched data</h3>
<span class="code">
<pre>io.mockk.MockKException: no answer found for CustomizationRepository(#212).getUserCustomizationFlow() among the configured answers: ()
	at app//io.mockk.impl.stub.MockKStub.defaultAnswer(MockKStub.kt:91)
	at app//io.mockk.impl.stub.MockKStub.answer(MockKStub.kt:42)
	at app//io.mockk.impl.recording.states.AnsweringState.call(AnsweringState.kt:16)
	at app//io.mockk.impl.recording.CommonCallRecorder.call(CommonCallRecorder.kt:53)
	at app//io.mockk.impl.stub.MockKStub.handleInvocation(MockKStub.kt:269)
	at app//io.mockk.impl.instantiation.JvmMockFactoryHelper$mockHandler$1.invocation(JvmMockFactoryHelper.kt:24)
	at app//io.mockk.proxy.jvm.advice.Interceptor.call(Interceptor.kt:21)
	at app//io.mockk.proxy.jvm.advice.BaseAdvice.handle(BaseAdvice.kt:42)
	at app//io.mockk.proxy.jvm.advice.jvm.JvmMockKProxyInterceptor.interceptNoSuper(JvmMockKProxyInterceptor.java:45)
	at app//com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository$Subclass7.getUserCustomizationFlow(Unknown Source)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase.&lt;init&gt;(LoadCustomizationUseCase.kt:47)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest.setUp(LoadCustomizationUseCaseTest.kt:72)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at app//org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at app//org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at app//org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at app//org.junit.internal.runners.statements.RunBefores.invokeMethod(RunBefores.java:33)
	at app//org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:24)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at app//org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at app//org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at app//org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at app//org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at app//org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at app//org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.runTestClass(JUnitTestClassExecutor.java:112)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:58)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:40)
	at org.gradle.api.internal.tasks.testing.junit.AbstractJUnitTestClassProcessor.processTestClass(AbstractJUnitTestClassProcessor.java:54)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.processTestClass(SuiteTestClassProcessor.java:53)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.processTestClass(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$2.run(TestWorker.java:183)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
</pre>
</span>
</div>
<div class="test">
<a name="getCurrentSelectedStyle should return null when no style selected"></a>
<h3 class="failures">getCurrentSelectedStyle should return null when no style selected</h3>
<span class="code">
<pre>io.mockk.MockKException: no answer found for CustomizationRepository(#214).getUserCustomizationFlow() among the configured answers: ()
	at app//io.mockk.impl.stub.MockKStub.defaultAnswer(MockKStub.kt:91)
	at app//io.mockk.impl.stub.MockKStub.answer(MockKStub.kt:42)
	at app//io.mockk.impl.recording.states.AnsweringState.call(AnsweringState.kt:16)
	at app//io.mockk.impl.recording.CommonCallRecorder.call(CommonCallRecorder.kt:53)
	at app//io.mockk.impl.stub.MockKStub.handleInvocation(MockKStub.kt:269)
	at app//io.mockk.impl.instantiation.JvmMockFactoryHelper$mockHandler$1.invocation(JvmMockFactoryHelper.kt:24)
	at app//io.mockk.proxy.jvm.advice.Interceptor.call(Interceptor.kt:21)
	at app//io.mockk.proxy.jvm.advice.BaseAdvice.handle(BaseAdvice.kt:42)
	at app//io.mockk.proxy.jvm.advice.jvm.JvmMockKProxyInterceptor.interceptNoSuper(JvmMockKProxyInterceptor.java:45)
	at app//com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository$Subclass7.getUserCustomizationFlow(Unknown Source)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase.&lt;init&gt;(LoadCustomizationUseCase.kt:47)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest.setUp(LoadCustomizationUseCaseTest.kt:72)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at app//org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at app//org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at app//org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at app//org.junit.internal.runners.statements.RunBefores.invokeMethod(RunBefores.java:33)
	at app//org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:24)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at app//org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at app//org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at app//org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at app//org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at app//org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at app//org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.runTestClass(JUnitTestClassExecutor.java:112)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:58)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:40)
	at org.gradle.api.internal.tasks.testing.junit.AbstractJUnitTestClassProcessor.processTestClass(AbstractJUnitTestClassProcessor.java:54)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.processTestClass(SuiteTestClassProcessor.java:53)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.processTestClass(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$2.run(TestWorker.java:183)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
</pre>
</span>
</div>
<div class="test">
<a name="getCurrentSelectedStyle should return null when style not found"></a>
<h3 class="failures">getCurrentSelectedStyle should return null when style not found</h3>
<span class="code">
<pre>io.mockk.MockKException: no answer found for CustomizationRepository(#232).getUserCustomizationFlow() among the configured answers: ()
	at app//io.mockk.impl.stub.MockKStub.defaultAnswer(MockKStub.kt:91)
	at app//io.mockk.impl.stub.MockKStub.answer(MockKStub.kt:42)
	at app//io.mockk.impl.recording.states.AnsweringState.call(AnsweringState.kt:16)
	at app//io.mockk.impl.recording.CommonCallRecorder.call(CommonCallRecorder.kt:53)
	at app//io.mockk.impl.stub.MockKStub.handleInvocation(MockKStub.kt:269)
	at app//io.mockk.impl.instantiation.JvmMockFactoryHelper$mockHandler$1.invocation(JvmMockFactoryHelper.kt:24)
	at app//io.mockk.proxy.jvm.advice.Interceptor.call(Interceptor.kt:21)
	at app//io.mockk.proxy.jvm.advice.BaseAdvice.handle(BaseAdvice.kt:42)
	at app//io.mockk.proxy.jvm.advice.jvm.JvmMockKProxyInterceptor.interceptNoSuper(JvmMockKProxyInterceptor.java:45)
	at app//com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository$Subclass7.getUserCustomizationFlow(Unknown Source)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase.&lt;init&gt;(LoadCustomizationUseCase.kt:47)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest.setUp(LoadCustomizationUseCaseTest.kt:72)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at app//org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at app//org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at app//org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at app//org.junit.internal.runners.statements.RunBefores.invokeMethod(RunBefores.java:33)
	at app//org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:24)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at app//org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at app//org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at app//org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at app//org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at app//org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at app//org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.runTestClass(JUnitTestClassExecutor.java:112)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:58)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:40)
	at org.gradle.api.internal.tasks.testing.junit.AbstractJUnitTestClassProcessor.processTestClass(AbstractJUnitTestClassProcessor.java:54)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.processTestClass(SuiteTestClassProcessor.java:53)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.processTestClass(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$2.run(TestWorker.java:183)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
</pre>
</span>
</div>
<div class="test">
<a name="getCurrentSelectedStyle should return selected style when available"></a>
<h3 class="failures">getCurrentSelectedStyle should return selected style when available</h3>
<span class="code">
<pre>io.mockk.MockKException: no answer found for CustomizationRepository(#202).getUserCustomizationFlow() among the configured answers: ()
	at app//io.mockk.impl.stub.MockKStub.defaultAnswer(MockKStub.kt:91)
	at app//io.mockk.impl.stub.MockKStub.answer(MockKStub.kt:42)
	at app//io.mockk.impl.recording.states.AnsweringState.call(AnsweringState.kt:16)
	at app//io.mockk.impl.recording.CommonCallRecorder.call(CommonCallRecorder.kt:53)
	at app//io.mockk.impl.stub.MockKStub.handleInvocation(MockKStub.kt:269)
	at app//io.mockk.impl.instantiation.JvmMockFactoryHelper$mockHandler$1.invocation(JvmMockFactoryHelper.kt:24)
	at app//io.mockk.proxy.jvm.advice.Interceptor.call(Interceptor.kt:21)
	at app//io.mockk.proxy.jvm.advice.BaseAdvice.handle(BaseAdvice.kt:42)
	at app//io.mockk.proxy.jvm.advice.jvm.JvmMockKProxyInterceptor.interceptNoSuper(JvmMockKProxyInterceptor.java:45)
	at app//com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository$Subclass7.getUserCustomizationFlow(Unknown Source)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase.&lt;init&gt;(LoadCustomizationUseCase.kt:47)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest.setUp(LoadCustomizationUseCaseTest.kt:72)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at app//org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at app//org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at app//org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at app//org.junit.internal.runners.statements.RunBefores.invokeMethod(RunBefores.java:33)
	at app//org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:24)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at app//org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at app//org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at app//org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at app//org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at app//org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at app//org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.runTestClass(JUnitTestClassExecutor.java:112)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:58)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:40)
	at org.gradle.api.internal.tasks.testing.junit.AbstractJUnitTestClassProcessor.processTestClass(AbstractJUnitTestClassProcessor.java:54)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.processTestClass(SuiteTestClassProcessor.java:53)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.processTestClass(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$2.run(TestWorker.java:183)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
</pre>
</span>
</div>
<div class="test">
<a name="getCurrentUserCustomization should return current user customization"></a>
<h3 class="failures">getCurrentUserCustomization should return current user customization</h3>
<span class="code">
<pre>io.mockk.MockKException: no answer found for CustomizationRepository(#200).getUserCustomizationFlow() among the configured answers: ()
	at app//io.mockk.impl.stub.MockKStub.defaultAnswer(MockKStub.kt:91)
	at app//io.mockk.impl.stub.MockKStub.answer(MockKStub.kt:42)
	at app//io.mockk.impl.recording.states.AnsweringState.call(AnsweringState.kt:16)
	at app//io.mockk.impl.recording.CommonCallRecorder.call(CommonCallRecorder.kt:53)
	at app//io.mockk.impl.stub.MockKStub.handleInvocation(MockKStub.kt:269)
	at app//io.mockk.impl.instantiation.JvmMockFactoryHelper$mockHandler$1.invocation(JvmMockFactoryHelper.kt:24)
	at app//io.mockk.proxy.jvm.advice.Interceptor.call(Interceptor.kt:21)
	at app//io.mockk.proxy.jvm.advice.BaseAdvice.handle(BaseAdvice.kt:42)
	at app//io.mockk.proxy.jvm.advice.jvm.JvmMockKProxyInterceptor.interceptNoSuper(JvmMockKProxyInterceptor.java:45)
	at app//com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository$Subclass7.getUserCustomizationFlow(Unknown Source)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase.&lt;init&gt;(LoadCustomizationUseCase.kt:47)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest.setUp(LoadCustomizationUseCaseTest.kt:72)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at app//org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at app//org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at app//org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at app//org.junit.internal.runners.statements.RunBefores.invokeMethod(RunBefores.java:33)
	at app//org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:24)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at app//org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at app//org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at app//org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at app//org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at app//org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at app//org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.runTestClass(JUnitTestClassExecutor.java:112)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:58)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:40)
	at org.gradle.api.internal.tasks.testing.junit.AbstractJUnitTestClassProcessor.processTestClass(AbstractJUnitTestClassProcessor.java:54)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.processTestClass(SuiteTestClassProcessor.java:53)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.processTestClass(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$2.run(TestWorker.java:183)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
</pre>
</span>
</div>
<div class="test">
<a name="getFeatureStatus should return correct status"></a>
<h3 class="failures">getFeatureStatus should return correct status</h3>
<span class="code">
<pre>io.mockk.MockKException: no answer found for CustomizationRepository(#206).getUserCustomizationFlow() among the configured answers: ()
	at app//io.mockk.impl.stub.MockKStub.defaultAnswer(MockKStub.kt:91)
	at app//io.mockk.impl.stub.MockKStub.answer(MockKStub.kt:42)
	at app//io.mockk.impl.recording.states.AnsweringState.call(AnsweringState.kt:16)
	at app//io.mockk.impl.recording.CommonCallRecorder.call(CommonCallRecorder.kt:53)
	at app//io.mockk.impl.stub.MockKStub.handleInvocation(MockKStub.kt:269)
	at app//io.mockk.impl.instantiation.JvmMockFactoryHelper$mockHandler$1.invocation(JvmMockFactoryHelper.kt:24)
	at app//io.mockk.proxy.jvm.advice.Interceptor.call(Interceptor.kt:21)
	at app//io.mockk.proxy.jvm.advice.BaseAdvice.handle(BaseAdvice.kt:42)
	at app//io.mockk.proxy.jvm.advice.jvm.JvmMockKProxyInterceptor.interceptNoSuper(JvmMockKProxyInterceptor.java:45)
	at app//com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository$Subclass7.getUserCustomizationFlow(Unknown Source)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase.&lt;init&gt;(LoadCustomizationUseCase.kt:47)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest.setUp(LoadCustomizationUseCaseTest.kt:72)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at app//org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at app//org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at app//org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at app//org.junit.internal.runners.statements.RunBefores.invokeMethod(RunBefores.java:33)
	at app//org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:24)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at app//org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at app//org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at app//org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at app//org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at app//org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at app//org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.runTestClass(JUnitTestClassExecutor.java:112)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:58)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:40)
	at org.gradle.api.internal.tasks.testing.junit.AbstractJUnitTestClassProcessor.processTestClass(AbstractJUnitTestClassProcessor.java:54)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.processTestClass(SuiteTestClassProcessor.java:53)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.processTestClass(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$2.run(TestWorker.java:183)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
</pre>
</span>
</div>
<div class="test">
<a name="getFeatureStatus should return permissions required when missing permissions"></a>
<h3 class="failures">getFeatureStatus should return permissions required when missing permissions</h3>
<span class="code">
<pre>io.mockk.MockKException: no answer found for CustomizationRepository(#224).getUserCustomizationFlow() among the configured answers: ()
	at app//io.mockk.impl.stub.MockKStub.defaultAnswer(MockKStub.kt:91)
	at app//io.mockk.impl.stub.MockKStub.answer(MockKStub.kt:42)
	at app//io.mockk.impl.recording.states.AnsweringState.call(AnsweringState.kt:16)
	at app//io.mockk.impl.recording.CommonCallRecorder.call(CommonCallRecorder.kt:53)
	at app//io.mockk.impl.stub.MockKStub.handleInvocation(MockKStub.kt:269)
	at app//io.mockk.impl.instantiation.JvmMockFactoryHelper$mockHandler$1.invocation(JvmMockFactoryHelper.kt:24)
	at app//io.mockk.proxy.jvm.advice.Interceptor.call(Interceptor.kt:21)
	at app//io.mockk.proxy.jvm.advice.BaseAdvice.handle(BaseAdvice.kt:42)
	at app//io.mockk.proxy.jvm.advice.jvm.JvmMockKProxyInterceptor.interceptNoSuper(JvmMockKProxyInterceptor.java:45)
	at app//com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository$Subclass7.getUserCustomizationFlow(Unknown Source)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase.&lt;init&gt;(LoadCustomizationUseCase.kt:47)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest.setUp(LoadCustomizationUseCaseTest.kt:72)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at app//org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at app//org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at app//org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at app//org.junit.internal.runners.statements.RunBefores.invokeMethod(RunBefores.java:33)
	at app//org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:24)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at app//org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at app//org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at app//org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at app//org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at app//org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at app//org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.runTestClass(JUnitTestClassExecutor.java:112)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:58)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:40)
	at org.gradle.api.internal.tasks.testing.junit.AbstractJUnitTestClassProcessor.processTestClass(AbstractJUnitTestClassProcessor.java:54)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.processTestClass(SuiteTestClassProcessor.java:53)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.processTestClass(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$2.run(TestWorker.java:183)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
</pre>
</span>
</div>
<div class="test">
<a name="isFeatureReady should return false when permissions missing"></a>
<h3 class="failures">isFeatureReady should return false when permissions missing</h3>
<span class="code">
<pre>io.mockk.MockKException: no answer found for CustomizationRepository(#204).getUserCustomizationFlow() among the configured answers: ()
	at app//io.mockk.impl.stub.MockKStub.defaultAnswer(MockKStub.kt:91)
	at app//io.mockk.impl.stub.MockKStub.answer(MockKStub.kt:42)
	at app//io.mockk.impl.recording.states.AnsweringState.call(AnsweringState.kt:16)
	at app//io.mockk.impl.recording.CommonCallRecorder.call(CommonCallRecorder.kt:53)
	at app//io.mockk.impl.stub.MockKStub.handleInvocation(MockKStub.kt:269)
	at app//io.mockk.impl.instantiation.JvmMockFactoryHelper$mockHandler$1.invocation(JvmMockFactoryHelper.kt:24)
	at app//io.mockk.proxy.jvm.advice.Interceptor.call(Interceptor.kt:21)
	at app//io.mockk.proxy.jvm.advice.BaseAdvice.handle(BaseAdvice.kt:42)
	at app//io.mockk.proxy.jvm.advice.jvm.JvmMockKProxyInterceptor.interceptNoSuper(JvmMockKProxyInterceptor.java:45)
	at app//com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository$Subclass7.getUserCustomizationFlow(Unknown Source)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase.&lt;init&gt;(LoadCustomizationUseCase.kt:47)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest.setUp(LoadCustomizationUseCaseTest.kt:72)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at app//org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at app//org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at app//org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at app//org.junit.internal.runners.statements.RunBefores.invokeMethod(RunBefores.java:33)
	at app//org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:24)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at app//org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at app//org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at app//org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at app//org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at app//org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at app//org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.runTestClass(JUnitTestClassExecutor.java:112)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:58)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:40)
	at org.gradle.api.internal.tasks.testing.junit.AbstractJUnitTestClassProcessor.processTestClass(AbstractJUnitTestClassProcessor.java:54)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.processTestClass(SuiteTestClassProcessor.java:53)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.processTestClass(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$2.run(TestWorker.java:183)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
</pre>
</span>
</div>
<div class="test">
<a name="isFeatureReady should return true when all conditions met"></a>
<h3 class="failures">isFeatureReady should return true when all conditions met</h3>
<span class="code">
<pre>io.mockk.MockKException: no answer found for CustomizationRepository(#216).getUserCustomizationFlow() among the configured answers: ()
	at app//io.mockk.impl.stub.MockKStub.defaultAnswer(MockKStub.kt:91)
	at app//io.mockk.impl.stub.MockKStub.answer(MockKStub.kt:42)
	at app//io.mockk.impl.recording.states.AnsweringState.call(AnsweringState.kt:16)
	at app//io.mockk.impl.recording.CommonCallRecorder.call(CommonCallRecorder.kt:53)
	at app//io.mockk.impl.stub.MockKStub.handleInvocation(MockKStub.kt:269)
	at app//io.mockk.impl.instantiation.JvmMockFactoryHelper$mockHandler$1.invocation(JvmMockFactoryHelper.kt:24)
	at app//io.mockk.proxy.jvm.advice.Interceptor.call(Interceptor.kt:21)
	at app//io.mockk.proxy.jvm.advice.BaseAdvice.handle(BaseAdvice.kt:42)
	at app//io.mockk.proxy.jvm.advice.jvm.JvmMockKProxyInterceptor.interceptNoSuper(JvmMockKProxyInterceptor.java:45)
	at app//com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository$Subclass7.getUserCustomizationFlow(Unknown Source)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase.&lt;init&gt;(LoadCustomizationUseCase.kt:47)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest.setUp(LoadCustomizationUseCaseTest.kt:72)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at app//org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at app//org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at app//org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at app//org.junit.internal.runners.statements.RunBefores.invokeMethod(RunBefores.java:33)
	at app//org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:24)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at app//org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at app//org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at app//org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at app//org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at app//org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at app//org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.runTestClass(JUnitTestClassExecutor.java:112)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:58)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:40)
	at org.gradle.api.internal.tasks.testing.junit.AbstractJUnitTestClassProcessor.processTestClass(AbstractJUnitTestClassProcessor.java:54)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.processTestClass(SuiteTestClassProcessor.java:53)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.processTestClass(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$2.run(TestWorker.java:183)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
</pre>
</span>
</div>
<div class="test">
<a name="isFeatureReadyFlow should emit correct readiness state"></a>
<h3 class="failures">isFeatureReadyFlow should emit correct readiness state</h3>
<span class="code">
<pre>io.mockk.MockKException: no answer found for CustomizationRepository(#210).getUserCustomizationFlow() among the configured answers: ()
	at app//io.mockk.impl.stub.MockKStub.defaultAnswer(MockKStub.kt:91)
	at app//io.mockk.impl.stub.MockKStub.answer(MockKStub.kt:42)
	at app//io.mockk.impl.recording.states.AnsweringState.call(AnsweringState.kt:16)
	at app//io.mockk.impl.recording.CommonCallRecorder.call(CommonCallRecorder.kt:53)
	at app//io.mockk.impl.stub.MockKStub.handleInvocation(MockKStub.kt:269)
	at app//io.mockk.impl.instantiation.JvmMockFactoryHelper$mockHandler$1.invocation(JvmMockFactoryHelper.kt:24)
	at app//io.mockk.proxy.jvm.advice.Interceptor.call(Interceptor.kt:21)
	at app//io.mockk.proxy.jvm.advice.BaseAdvice.handle(BaseAdvice.kt:42)
	at app//io.mockk.proxy.jvm.advice.jvm.JvmMockKProxyInterceptor.interceptNoSuper(JvmMockKProxyInterceptor.java:45)
	at app//com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository$Subclass7.getUserCustomizationFlow(Unknown Source)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase.&lt;init&gt;(LoadCustomizationUseCase.kt:47)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest.setUp(LoadCustomizationUseCaseTest.kt:72)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at app//org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at app//org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at app//org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at app//org.junit.internal.runners.statements.RunBefores.invokeMethod(RunBefores.java:33)
	at app//org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:24)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at app//org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at app//org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at app//org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at app//org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at app//org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at app//org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.runTestClass(JUnitTestClassExecutor.java:112)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:58)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:40)
	at org.gradle.api.internal.tasks.testing.junit.AbstractJUnitTestClassProcessor.processTestClass(AbstractJUnitTestClassProcessor.java:54)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.processTestClass(SuiteTestClassProcessor.java:53)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.processTestClass(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$2.run(TestWorker.java:183)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
</pre>
</span>
</div>
<div class="test">
<a name="userCustomizationFlow should emit enriched customization data"></a>
<h3 class="failures">userCustomizationFlow should emit enriched customization data</h3>
<span class="code">
<pre>io.mockk.MockKException: no answer found for CustomizationRepository(#220).getUserCustomizationFlow() among the configured answers: ()
	at app//io.mockk.impl.stub.MockKStub.defaultAnswer(MockKStub.kt:91)
	at app//io.mockk.impl.stub.MockKStub.answer(MockKStub.kt:42)
	at app//io.mockk.impl.recording.states.AnsweringState.call(AnsweringState.kt:16)
	at app//io.mockk.impl.recording.CommonCallRecorder.call(CommonCallRecorder.kt:53)
	at app//io.mockk.impl.stub.MockKStub.handleInvocation(MockKStub.kt:269)
	at app//io.mockk.impl.instantiation.JvmMockFactoryHelper$mockHandler$1.invocation(JvmMockFactoryHelper.kt:24)
	at app//io.mockk.proxy.jvm.advice.Interceptor.call(Interceptor.kt:21)
	at app//io.mockk.proxy.jvm.advice.BaseAdvice.handle(BaseAdvice.kt:42)
	at app//io.mockk.proxy.jvm.advice.jvm.JvmMockKProxyInterceptor.interceptNoSuper(JvmMockKProxyInterceptor.java:45)
	at app//com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository$Subclass7.getUserCustomizationFlow(Unknown Source)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase.&lt;init&gt;(LoadCustomizationUseCase.kt:47)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest.setUp(LoadCustomizationUseCaseTest.kt:72)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at app//org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at app//org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at app//org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at app//org.junit.internal.runners.statements.RunBefores.invokeMethod(RunBefores.java:33)
	at app//org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:24)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at app//org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at app//org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at app//org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at app//org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at app//org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at app//org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.runTestClass(JUnitTestClassExecutor.java:112)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:58)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:40)
	at org.gradle.api.internal.tasks.testing.junit.AbstractJUnitTestClassProcessor.processTestClass(AbstractJUnitTestClassProcessor.java:54)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.processTestClass(SuiteTestClassProcessor.java:53)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.processTestClass(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$2.run(TestWorker.java:183)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
</pre>
</span>
</div>
<div class="test">
<a name="userCustomizationFlow should handle empty style list"></a>
<h3 class="failures">userCustomizationFlow should handle empty style list</h3>
<span class="code">
<pre>io.mockk.MockKException: no answer found for CustomizationRepository(#218).getUserCustomizationFlow() among the configured answers: ()
	at app//io.mockk.impl.stub.MockKStub.defaultAnswer(MockKStub.kt:91)
	at app//io.mockk.impl.stub.MockKStub.answer(MockKStub.kt:42)
	at app//io.mockk.impl.recording.states.AnsweringState.call(AnsweringState.kt:16)
	at app//io.mockk.impl.recording.CommonCallRecorder.call(CommonCallRecorder.kt:53)
	at app//io.mockk.impl.stub.MockKStub.handleInvocation(MockKStub.kt:269)
	at app//io.mockk.impl.instantiation.JvmMockFactoryHelper$mockHandler$1.invocation(JvmMockFactoryHelper.kt:24)
	at app//io.mockk.proxy.jvm.advice.Interceptor.call(Interceptor.kt:21)
	at app//io.mockk.proxy.jvm.advice.BaseAdvice.handle(BaseAdvice.kt:42)
	at app//io.mockk.proxy.jvm.advice.jvm.JvmMockKProxyInterceptor.interceptNoSuper(JvmMockKProxyInterceptor.java:45)
	at app//com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository$Subclass7.getUserCustomizationFlow(Unknown Source)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase.&lt;init&gt;(LoadCustomizationUseCase.kt:47)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest.setUp(LoadCustomizationUseCaseTest.kt:72)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at app//org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at app//org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at app//org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at app//org.junit.internal.runners.statements.RunBefores.invokeMethod(RunBefores.java:33)
	at app//org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:24)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at app//org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at app//org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at app//org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at app//org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at app//org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at app//org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.runTestClass(JUnitTestClassExecutor.java:112)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:58)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:40)
	at org.gradle.api.internal.tasks.testing.junit.AbstractJUnitTestClassProcessor.processTestClass(AbstractJUnitTestClassProcessor.java:54)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.processTestClass(SuiteTestClassProcessor.java:53)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.processTestClass(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$2.run(TestWorker.java:183)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
</pre>
</span>
</div>
<div class="test">
<a name="userCustomizationFlow should handle missing selected style"></a>
<h3 class="failures">userCustomizationFlow should handle missing selected style</h3>
<span class="code">
<pre>io.mockk.MockKException: no answer found for CustomizationRepository(#208).getUserCustomizationFlow() among the configured answers: ()
	at app//io.mockk.impl.stub.MockKStub.defaultAnswer(MockKStub.kt:91)
	at app//io.mockk.impl.stub.MockKStub.answer(MockKStub.kt:42)
	at app//io.mockk.impl.recording.states.AnsweringState.call(AnsweringState.kt:16)
	at app//io.mockk.impl.recording.CommonCallRecorder.call(CommonCallRecorder.kt:53)
	at app//io.mockk.impl.stub.MockKStub.handleInvocation(MockKStub.kt:269)
	at app//io.mockk.impl.instantiation.JvmMockFactoryHelper$mockHandler$1.invocation(JvmMockFactoryHelper.kt:24)
	at app//io.mockk.proxy.jvm.advice.Interceptor.call(Interceptor.kt:21)
	at app//io.mockk.proxy.jvm.advice.BaseAdvice.handle(BaseAdvice.kt:42)
	at app//io.mockk.proxy.jvm.advice.jvm.JvmMockKProxyInterceptor.interceptNoSuper(JvmMockKProxyInterceptor.java:45)
	at app//com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository$Subclass7.getUserCustomizationFlow(Unknown Source)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase.&lt;init&gt;(LoadCustomizationUseCase.kt:47)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest.setUp(LoadCustomizationUseCaseTest.kt:72)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at app//org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at app//org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at app//org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at app//org.junit.internal.runners.statements.RunBefores.invokeMethod(RunBefores.java:33)
	at app//org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:24)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at app//org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at app//org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at app//org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at app//org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at app//org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at app//org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.runTestClass(JUnitTestClassExecutor.java:112)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:58)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:40)
	at org.gradle.api.internal.tasks.testing.junit.AbstractJUnitTestClassProcessor.processTestClass(AbstractJUnitTestClassProcessor.java:54)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.processTestClass(SuiteTestClassProcessor.java:53)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.processTestClass(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$2.run(TestWorker.java:183)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
</pre>
</span>
</div>
<div class="test">
<a name="validateCurrentConfiguration should handle validation errors"></a>
<h3 class="failures">validateCurrentConfiguration should handle validation errors</h3>
<span class="code">
<pre>io.mockk.MockKException: no answer found for CustomizationRepository(#234).getUserCustomizationFlow() among the configured answers: ()
	at app//io.mockk.impl.stub.MockKStub.defaultAnswer(MockKStub.kt:91)
	at app//io.mockk.impl.stub.MockKStub.answer(MockKStub.kt:42)
	at app//io.mockk.impl.recording.states.AnsweringState.call(AnsweringState.kt:16)
	at app//io.mockk.impl.recording.CommonCallRecorder.call(CommonCallRecorder.kt:53)
	at app//io.mockk.impl.stub.MockKStub.handleInvocation(MockKStub.kt:269)
	at app//io.mockk.impl.instantiation.JvmMockFactoryHelper$mockHandler$1.invocation(JvmMockFactoryHelper.kt:24)
	at app//io.mockk.proxy.jvm.advice.Interceptor.call(Interceptor.kt:21)
	at app//io.mockk.proxy.jvm.advice.BaseAdvice.handle(BaseAdvice.kt:42)
	at app//io.mockk.proxy.jvm.advice.jvm.JvmMockKProxyInterceptor.interceptNoSuper(JvmMockKProxyInterceptor.java:45)
	at app//com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository$Subclass7.getUserCustomizationFlow(Unknown Source)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase.&lt;init&gt;(LoadCustomizationUseCase.kt:47)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest.setUp(LoadCustomizationUseCaseTest.kt:72)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at app//org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at app//org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at app//org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at app//org.junit.internal.runners.statements.RunBefores.invokeMethod(RunBefores.java:33)
	at app//org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:24)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at app//org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at app//org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at app//org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at app//org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at app//org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at app//org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.runTestClass(JUnitTestClassExecutor.java:112)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:58)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:40)
	at org.gradle.api.internal.tasks.testing.junit.AbstractJUnitTestClassProcessor.processTestClass(AbstractJUnitTestClassProcessor.java:54)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.processTestClass(SuiteTestClassProcessor.java:53)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.processTestClass(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$2.run(TestWorker.java:183)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
</pre>
</span>
</div>
<div class="test">
<a name="validateCurrentConfiguration should return empty list for valid configuration"></a>
<h3 class="failures">validateCurrentConfiguration should return empty list for valid configuration</h3>
<span class="code">
<pre>io.mockk.MockKException: no answer found for CustomizationRepository(#236).getUserCustomizationFlow() among the configured answers: ()
	at app//io.mockk.impl.stub.MockKStub.defaultAnswer(MockKStub.kt:91)
	at app//io.mockk.impl.stub.MockKStub.answer(MockKStub.kt:42)
	at app//io.mockk.impl.recording.states.AnsweringState.call(AnsweringState.kt:16)
	at app//io.mockk.impl.recording.CommonCallRecorder.call(CommonCallRecorder.kt:53)
	at app//io.mockk.impl.stub.MockKStub.handleInvocation(MockKStub.kt:269)
	at app//io.mockk.impl.instantiation.JvmMockFactoryHelper$mockHandler$1.invocation(JvmMockFactoryHelper.kt:24)
	at app//io.mockk.proxy.jvm.advice.Interceptor.call(Interceptor.kt:21)
	at app//io.mockk.proxy.jvm.advice.BaseAdvice.handle(BaseAdvice.kt:42)
	at app//io.mockk.proxy.jvm.advice.jvm.JvmMockKProxyInterceptor.interceptNoSuper(JvmMockKProxyInterceptor.java:45)
	at app//com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository$Subclass7.getUserCustomizationFlow(Unknown Source)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase.&lt;init&gt;(LoadCustomizationUseCase.kt:47)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest.setUp(LoadCustomizationUseCaseTest.kt:72)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at app//org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at app//org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at app//org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at app//org.junit.internal.runners.statements.RunBefores.invokeMethod(RunBefores.java:33)
	at app//org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:24)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at app//org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at app//org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at app//org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at app//org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at app//org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at app//org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.runTestClass(JUnitTestClassExecutor.java:112)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:58)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:40)
	at org.gradle.api.internal.tasks.testing.junit.AbstractJUnitTestClassProcessor.processTestClass(AbstractJUnitTestClassProcessor.java:54)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.processTestClass(SuiteTestClassProcessor.java:53)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.processTestClass(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$2.run(TestWorker.java:183)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
</pre>
</span>
</div>
<div class="test">
<a name="validateCurrentConfiguration should return issues for invalid configuration"></a>
<h3 class="failures">validateCurrentConfiguration should return issues for invalid configuration</h3>
<span class="code">
<pre>io.mockk.MockKException: no answer found for CustomizationRepository(#222).getUserCustomizationFlow() among the configured answers: ()
	at app//io.mockk.impl.stub.MockKStub.defaultAnswer(MockKStub.kt:91)
	at app//io.mockk.impl.stub.MockKStub.answer(MockKStub.kt:42)
	at app//io.mockk.impl.recording.states.AnsweringState.call(AnsweringState.kt:16)
	at app//io.mockk.impl.recording.CommonCallRecorder.call(CommonCallRecorder.kt:53)
	at app//io.mockk.impl.stub.MockKStub.handleInvocation(MockKStub.kt:269)
	at app//io.mockk.impl.instantiation.JvmMockFactoryHelper$mockHandler$1.invocation(JvmMockFactoryHelper.kt:24)
	at app//io.mockk.proxy.jvm.advice.Interceptor.call(Interceptor.kt:21)
	at app//io.mockk.proxy.jvm.advice.BaseAdvice.handle(BaseAdvice.kt:42)
	at app//io.mockk.proxy.jvm.advice.jvm.JvmMockKProxyInterceptor.interceptNoSuper(JvmMockKProxyInterceptor.java:45)
	at app//com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository$Subclass7.getUserCustomizationFlow(Unknown Source)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase.&lt;init&gt;(LoadCustomizationUseCase.kt:47)
	at app//com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest.setUp(LoadCustomizationUseCaseTest.kt:72)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at app//org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at app//org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at app//org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at app//org.junit.internal.runners.statements.RunBefores.invokeMethod(RunBefores.java:33)
	at app//org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:24)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at app//org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at app//org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at app//org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at app//org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at app//org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at app//org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at app//org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at app//org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at app//org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.runTestClass(JUnitTestClassExecutor.java:112)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:58)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:40)
	at org.gradle.api.internal.tasks.testing.junit.AbstractJUnitTestClassProcessor.processTestClass(AbstractJUnitTestClassProcessor.java:54)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.processTestClass(SuiteTestClassProcessor.java:53)
	at java.base@21.0.6/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Unknown Source)
	at java.base@21.0.6/java.lang.reflect.Method.invoke(Unknown Source)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.processTestClass(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$2.run(TestWorker.java:183)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at app//worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
</pre>
</span>
</div>
</div>
<div id="tab1" class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Duration</th>
<th>Result</th>
</tr>
</thead>
<tr>
<td class="failures">featureStatusFlow should emit correct status changes</td>
<td class="failures">0s</td>
<td class="failures">failed</td>
</tr>
<tr>
<td class="failures">getCurrentCustomizationConfig should return current configuration</td>
<td class="failures">0s</td>
<td class="failures">failed</td>
</tr>
<tr>
<td class="failures">getCurrentCustomizationConfig should return default on error</td>
<td class="failures">0s</td>
<td class="failures">failed</td>
</tr>
<tr>
<td class="failures">getCurrentEnrichedUserCustomization should return enriched data</td>
<td class="failures">0s</td>
<td class="failures">failed</td>
</tr>
<tr>
<td class="failures">getCurrentSelectedStyle should return null when no style selected</td>
<td class="failures">0s</td>
<td class="failures">failed</td>
</tr>
<tr>
<td class="failures">getCurrentSelectedStyle should return null when style not found</td>
<td class="failures">0.001s</td>
<td class="failures">failed</td>
</tr>
<tr>
<td class="failures">getCurrentSelectedStyle should return selected style when available</td>
<td class="failures">0s</td>
<td class="failures">failed</td>
</tr>
<tr>
<td class="failures">getCurrentUserCustomization should return current user customization</td>
<td class="failures">0.044s</td>
<td class="failures">failed</td>
</tr>
<tr>
<td class="failures">getFeatureStatus should return correct status</td>
<td class="failures">0s</td>
<td class="failures">failed</td>
</tr>
<tr>
<td class="failures">getFeatureStatus should return permissions required when missing permissions</td>
<td class="failures">0.001s</td>
<td class="failures">failed</td>
</tr>
<tr>
<td class="failures">isFeatureReady should return false when permissions missing</td>
<td class="failures">0s</td>
<td class="failures">failed</td>
</tr>
<tr>
<td class="failures">isFeatureReady should return true when all conditions met</td>
<td class="failures">0.001s</td>
<td class="failures">failed</td>
</tr>
<tr>
<td class="failures">isFeatureReadyFlow should emit correct readiness state</td>
<td class="failures">0s</td>
<td class="failures">failed</td>
</tr>
<tr>
<td class="failures">userCustomizationFlow should emit enriched customization data</td>
<td class="failures">0s</td>
<td class="failures">failed</td>
</tr>
<tr>
<td class="failures">userCustomizationFlow should handle empty style list</td>
<td class="failures">0s</td>
<td class="failures">failed</td>
</tr>
<tr>
<td class="failures">userCustomizationFlow should handle missing selected style</td>
<td class="failures">0s</td>
<td class="failures">failed</td>
</tr>
<tr>
<td class="failures">validateCurrentConfiguration should handle validation errors</td>
<td class="failures">0s</td>
<td class="failures">failed</td>
</tr>
<tr>
<td class="failures">validateCurrentConfiguration should return empty list for valid configuration</td>
<td class="failures">0s</td>
<td class="failures">failed</td>
</tr>
<tr>
<td class="failures">validateCurrentConfiguration should return issues for invalid configuration</td>
<td class="failures">0s</td>
<td class="failures">failed</td>
</tr>
</table>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="http://www.gradle.org">Gradle 8.11.1</a> at Jun 20, 2025, 4:39:53 PM</p>
</div>
</div>
</body>
</html>
