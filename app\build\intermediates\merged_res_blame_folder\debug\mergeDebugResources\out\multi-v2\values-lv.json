{"logs": [{"outputFile": "com.tqhit.battery.one.app-mergeDebugResources-99:/values-lv/values-lv.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5b80dcbf636dc26335bd1b8e4f16f918\\transformed\\material-1.12.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,324,411,496,577,682,770,871,1005,1088,1149,1214,1308,1381,1442,1567,1633,1701,1762,1834,1894,1948,2068,2128,2190,2244,2321,2451,2538,2615,2705,2788,2870,3011,3091,3176,3303,3394,3470,3524,3577,3643,3717,3798,3869,3949,4022,4099,4176,4250,4360,4453,4528,4618,4709,4781,4859,4950,5004,5087,5155,5239,5326,5388,5452,5515,5587,5697,5810,5913,6022,6080,6137,6214,6299,6377", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "12,86,84,80,104,87,100,133,82,60,64,93,72,60,124,65,67,60,71,59,53,119,59,61,53,76,129,86,76,89,82,81,140,79,84,126,90,75,53,52,65,73,80,70,79,72,76,76,73,109,92,74,89,90,71,77,90,53,82,67,83,86,61,63,62,71,109,112,102,108,57,56,76,84,77,73", "endOffsets": "319,406,491,572,677,765,866,1000,1083,1144,1209,1303,1376,1437,1562,1628,1696,1757,1829,1889,1943,2063,2123,2185,2239,2316,2446,2533,2610,2700,2783,2865,3006,3086,3171,3298,3389,3465,3519,3572,3638,3712,3793,3864,3944,4017,4094,4171,4245,4355,4448,4523,4613,4704,4776,4854,4945,4999,5082,5150,5234,5321,5383,5447,5510,5582,5692,5805,5908,6017,6075,6132,6209,6294,6372,6446"}, "to": {"startLines": "21,53,54,55,56,57,65,66,67,92,93,144,148,151,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,285,296,297,299", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "926,4034,4121,4206,4287,4392,5211,5312,5446,8412,8473,12319,12730,12977,19397,19522,19588,19656,19717,19789,19849,19903,20023,20083,20145,20199,20276,20406,20493,20570,20660,20743,20825,20966,21046,21131,21258,21349,21425,21479,21532,21598,21672,21753,21824,21904,21977,22054,22131,22205,22315,22408,22483,22573,22664,22736,22814,22905,22959,23042,23110,23194,23281,23343,23407,23470,23542,23652,23765,23868,23977,24035,25687,26409,26494,26645", "endLines": "25,53,54,55,56,57,65,66,67,92,93,144,148,151,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,285,296,297,299", "endColumns": "12,86,84,80,104,87,100,133,82,60,64,93,72,60,124,65,67,60,71,59,53,119,59,61,53,76,129,86,76,89,82,81,140,79,84,126,90,75,53,52,65,73,80,70,79,72,76,76,73,109,92,74,89,90,71,77,90,53,82,67,83,86,61,63,62,71,109,112,102,108,57,56,76,84,77,73", "endOffsets": "1145,4116,4201,4282,4387,4475,5307,5441,5524,8468,8533,12408,12798,13033,19517,19583,19651,19712,19784,19844,19898,20018,20078,20140,20194,20271,20401,20488,20565,20655,20738,20820,20961,21041,21126,21253,21344,21420,21474,21527,21593,21667,21748,21819,21899,21972,22049,22126,22200,22310,22403,22478,22568,22659,22731,22809,22900,22954,23037,23105,23189,23276,23338,23402,23465,23537,23647,23760,23863,23972,24030,24087,25759,26489,26567,26714"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\179e6486bd57a16ea175623aa423e7ed\\transformed\\jetified-material3-release\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,182,309,424,549,658,758,875,1013,1131,1278,1364,1462,1556,1657,1776,1900,2003,2141,2272,2410,2593,2725,2844,2971,3091,3186,3285,3406,3541,3643,3757,3863,3998,4143,4252,4355,4438,4533,4627,4737,4827,4914,5025,5105,5191,5286,5390,5481,5579,5668,5775,5877,5977,6130,6210,6315", "endColumns": "126,126,114,124,108,99,116,137,117,146,85,97,93,100,118,123,102,137,130,137,182,131,118,126,119,94,98,120,134,101,113,105,134,144,108,102,82,94,93,109,89,86,110,79,85,94,103,90,97,88,106,101,99,152,79,104,98", "endOffsets": "177,304,419,544,653,753,870,1008,1126,1273,1359,1457,1551,1652,1771,1895,1998,2136,2267,2405,2588,2720,2839,2966,3086,3181,3280,3401,3536,3638,3752,3858,3993,4138,4247,4350,4433,4528,4622,4732,4822,4909,5020,5100,5186,5281,5385,5476,5574,5663,5770,5872,5972,6125,6205,6310,6409"}, "to": {"startLines": "152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13038,13165,13292,13407,13532,13641,13741,13858,13996,14114,14261,14347,14445,14539,14640,14759,14883,14986,15124,15255,15393,15576,15708,15827,15954,16074,16169,16268,16389,16524,16626,16740,16846,16981,17126,17235,17338,17421,17516,17610,17720,17810,17897,18008,18088,18174,18269,18373,18464,18562,18651,18758,18860,18960,19113,19193,19298", "endColumns": "126,126,114,124,108,99,116,137,117,146,85,97,93,100,118,123,102,137,130,137,182,131,118,126,119,94,98,120,134,101,113,105,134,144,108,102,82,94,93,109,89,86,110,79,85,94,103,90,97,88,106,101,99,152,79,104,98", "endOffsets": "13160,13287,13402,13527,13636,13736,13853,13991,14109,14256,14342,14440,14534,14635,14754,14878,14981,15119,15250,15388,15571,15703,15822,15949,16069,16164,16263,16384,16519,16621,16735,16841,16976,17121,17230,17333,17416,17511,17605,17715,17805,17892,18003,18083,18169,18264,18368,18459,18557,18646,18753,18855,18955,19108,19188,19293,19392"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\42f95d9fa807b14415e836fc15872a54\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-lv\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "161", "endOffsets": "356"}, "to": {"startLines": "78", "startColumns": "4", "startOffsets": "6721", "endColumns": "165", "endOffsets": "6882"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8bd7feaae90e869538df51f29dd16595\\transformed\\jetified-media3-ui-1.6.1\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,567,831,915,998,1081,1176,1271,1344,1411,1505,1599,1665,1732,1795,1871,1977,2088,2195,2269,2351,2425,2498,2598,2697,2763,2829,2882,2940,2988,3049,3107,3183,3247,3312,3377,3434,3500,3552,3616,3694,3772,3827", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "17,12,12,83,82,82,94,94,72,66,93,93,65,66,62,75,105,110,106,73,81,73,72,99,98,65,65,52,57,47,60,57,75,63,64,64,56,65,51,63,77,77,54,66", "endOffsets": "281,562,826,910,993,1076,1171,1266,1339,1406,1500,1594,1660,1727,1790,1866,1972,2083,2190,2264,2346,2420,2493,2593,2692,2758,2824,2877,2935,2983,3044,3102,3178,3242,3307,3372,3429,3495,3547,3611,3689,3767,3822,3889"}, "to": {"startLines": "2,11,16,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,381,662,8538,8622,8705,8788,8883,8978,9051,9118,9212,9306,9372,9439,9502,9578,9684,9795,9902,9976,10058,10132,10205,10305,10404,10470,11254,11307,11365,11413,11474,11532,11608,11672,11737,11802,11859,11925,11977,12041,12119,12197,12252", "endLines": "10,15,20,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143", "endColumns": "17,12,12,83,82,82,94,94,72,66,93,93,65,66,62,75,105,110,106,73,81,73,72,99,98,65,65,52,57,47,60,57,75,63,64,64,56,65,51,63,77,77,54,66", "endOffsets": "376,657,921,8617,8700,8783,8878,8973,9046,9113,9207,9301,9367,9434,9497,9573,9679,9790,9897,9971,10053,10127,10200,10300,10399,10465,10531,11302,11360,11408,11469,11527,11603,11667,11732,11797,11854,11920,11972,12036,12114,12192,12247,12314"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5444be4bc77930bd89cfbb9f2224d8e4\\transformed\\navigation-ui-2.8.9\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,169", "endColumns": "113,122", "endOffsets": "164,287"}, "to": {"startLines": "270,271", "startColumns": "4,4", "startOffsets": "24262,24376", "endColumns": "113,122", "endOffsets": "24371,24494"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\237635df39b25799c092d66a208ce67d\\transformed\\jetified-foundation-release\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,89", "endOffsets": "140,230"}, "to": {"startLines": "307,308", "startColumns": "4,4", "startOffsets": "27317,27407", "endColumns": "89,89", "endOffsets": "27402,27492"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f908cdc45776521b403beeef1508641c\\transformed\\core-1.16.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,563,671,786", "endColumns": "97,101,99,100,106,107,114,100", "endOffsets": "148,250,350,451,558,666,781,882"}, "to": {"startLines": "58,59,60,61,62,63,64,303", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4480,4578,4680,4780,4881,4988,5096,26948", "endColumns": "97,101,99,100,106,107,114,100", "endOffsets": "4573,4675,4775,4876,4983,5091,5206,27044"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d56ddc8f70c1b6c4f2dfff25a6818549\\transformed\\jetified-play-services-base-18.5.0\\res\\values-lv\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,453,582,686,824,951,1064,1166,1337,1442,1607,1738,1903,2054,2114,2178", "endColumns": "102,156,128,103,137,126,112,101,170,104,164,130,164,150,59,63,84", "endOffsets": "295,452,581,685,823,950,1063,1165,1336,1441,1606,1737,1902,2053,2113,2177,2262"}, "to": {"startLines": "70,71,72,73,74,75,76,77,79,80,81,82,83,84,85,86,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5716,5823,5984,6117,6225,6367,6498,6615,6887,7062,7171,7340,7475,7644,7799,7863,7931", "endColumns": "106,160,132,107,141,130,116,105,174,108,168,134,168,154,63,67,88", "endOffsets": "5818,5979,6112,6220,6362,6493,6610,6716,7057,7166,7335,7470,7639,7794,7858,7926,8015"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9e0a763189144907fb0197c2b097244b\\transformed\\jetified-ui-release\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,203,292,388,491,581,667,755,848,932,1017,1104,1177,1253,1330,1406,1484,1552", "endColumns": "97,88,95,102,89,85,87,92,83,84,86,72,75,76,75,77,67,121", "endOffsets": "198,287,383,486,576,662,750,843,927,1012,1099,1172,1248,1325,1401,1479,1547,1669"}, "to": {"startLines": "68,69,89,90,91,149,150,272,273,286,287,298,300,301,302,304,305,306", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5529,5627,8123,8219,8322,12803,12889,24499,24592,25764,25849,26572,26719,26795,26872,27049,27127,27195", "endColumns": "97,88,95,102,89,85,87,92,83,84,86,72,75,76,75,77,67,121", "endOffsets": "5622,5711,8214,8317,8407,12884,12972,24587,24671,25844,25931,26640,26790,26867,26943,27122,27190,27312"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5c5c72c6ff4a7863322da50648a25e99\\transformed\\browser-1.8.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,257,373", "endColumns": "102,98,115,101", "endOffsets": "153,252,368,470"}, "to": {"startLines": "88,145,146,147", "startColumns": "4,4,4,4", "startOffsets": "8020,12413,12512,12628", "endColumns": "102,98,115,101", "endOffsets": "8118,12507,12623,12725"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\93d3043f0a8b9466a00a736e170a6ddc\\transformed\\appcompat-1.7.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,335,444,530,634,756,838,918,1028,1136,1242,1351,1462,1565,1677,1784,1889,1989,2074,2183,2294,2393,2504,2611,2716,2890,2989", "endColumns": "119,109,108,85,103,121,81,79,109,107,105,108,110,102,111,106,104,99,84,108,110,98,110,106,104,173,98,82", "endOffsets": "220,330,439,525,629,751,833,913,1023,1131,1237,1346,1457,1560,1672,1779,1884,1984,2069,2178,2289,2388,2499,2606,2711,2885,2984,3067"}, "to": {"startLines": "26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,295", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1150,1270,1380,1489,1575,1679,1801,1883,1963,2073,2181,2287,2396,2507,2610,2722,2829,2934,3034,3119,3228,3339,3438,3549,3656,3761,3935,26326", "endColumns": "119,109,108,85,103,121,81,79,109,107,105,108,110,102,111,106,104,99,84,108,110,98,110,106,104,173,98,82", "endOffsets": "1265,1375,1484,1570,1674,1796,1878,1958,2068,2176,2282,2391,2502,2605,2717,2824,2929,3029,3114,3223,3334,3433,3544,3651,3756,3930,4029,26404"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b572512e02266e069f95737c22215ab9\\transformed\\jetified-media3-exoplayer-1.6.1\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,196,268,338,418,495,596,694", "endColumns": "74,65,71,69,79,76,100,97,78", "endOffsets": "125,191,263,333,413,490,591,689,768"}, "to": {"startLines": "118,119,120,121,122,123,124,125,126", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "10536,10611,10677,10749,10819,10899,10976,11077,11175", "endColumns": "74,65,71,69,79,76,100,97,78", "endOffsets": "10606,10672,10744,10814,10894,10971,11072,11170,11249"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\237e0b5db534c615c4317f1b214e3e7f\\transformed\\jetified-play-services-ads-24.2.0\\res\\values-lv\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,247,298,357,421,487,582,645,775,889,1011,1063,1122,1232,1324,1370,1443,1479,1515,1569,1645,1686", "endColumns": "47,50,58,63,65,94,62,129,113,121,51,58,109,91,45,72,35,35,53,75,40,55", "endOffsets": "246,297,356,420,486,581,644,774,888,1010,1062,1121,1231,1323,1369,1442,1478,1514,1568,1644,1685,1741"}, "to": {"startLines": "267,268,269,274,275,276,277,278,279,280,281,282,283,284,288,289,290,291,292,293,294,309", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "24092,24144,24199,24676,24744,24814,24913,24980,25114,25232,25358,25414,25477,25591,25936,25986,26063,26103,26143,26201,26281,27497", "endColumns": "51,54,62,67,69,98,66,133,117,125,55,62,113,95,49,76,39,39,57,79,44,59", "endOffsets": "24139,24194,24257,24739,24809,24908,24975,25109,25227,25353,25409,25472,25586,25682,25981,26058,26098,26138,26196,26276,26321,27552"}}]}]}