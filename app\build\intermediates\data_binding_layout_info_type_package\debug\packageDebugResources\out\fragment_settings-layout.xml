<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_settings" modulePackage="com.tqhit.battery.one" filePath="app\src\main\res\layout\fragment_settings.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.core.widget.NestedScrollView" rootNodeViewId="@+id/scroll_view"><Targets><Target id="@+id/scroll_view" tag="layout/fragment_settings_0" view="androidx.core.widget.NestedScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="1380" endOffset="39"/></Target><Target id="@+id/p6" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="21" startOffset="12" endLine="63" endOffset="63"/></Target><Target id="@+id/p_12334" view="TextView"><Expressions/><location startLine="29" startOffset="16" endLine="47" endOffset="62"/></Target><Target id="@+id/switch_vibration" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="48" startOffset="16" endLine="62" endOffset="55"/></Target><Target id="@+id/p5" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="64" startOffset="12" endLine="107" endOffset="63"/></Target><Target id="@+id/p_11" view="TextView"><Expressions/><location startLine="73" startOffset="16" endLine="91" endOffset="62"/></Target><Target id="@+id/switch_info" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="92" startOffset="16" endLine="106" endOffset="55"/></Target><Target id="@+id/p_t" view="TextView"><Expressions/><location startLine="108" startOffset="12" endLine="117" endOffset="58"/></Target><Target id="@+id/change_theme" view="TextView"><Expressions/><location startLine="118" startOffset="12" endLine="136" endOffset="63"/></Target><Target id="@+id/change_icon" view="TextView"><Expressions/><location startLine="137" startOffset="12" endLine="156" endOffset="85"/></Target><Target id="@+id/change_second_color_theme" view="TextView"><Expressions/><location startLine="157" startOffset="12" endLine="175" endOffset="72"/></Target><Target id="@+id/change_lang" view="TextView"><Expressions/><location startLine="176" startOffset="12" endLine="194" endOffset="71"/></Target><Target id="@+id/change_temp" view="TextView"><Expressions/><location startLine="195" startOffset="12" endLine="214" endOffset="71"/></Target><Target id="@+id/n_t" view="TextView"><Expressions/><location startLine="229" startOffset="12" endLine="238" endOffset="58"/></Target><Target id="@+id/n_1.1" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="239" startOffset="12" endLine="281" endOffset="63"/></Target><Target id="@+id/n1.1" view="TextView"><Expressions/><location startLine="248" startOffset="16" endLine="268" endOffset="62"/></Target><Target id="@+id/switch_is_charge_notify" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="269" startOffset="16" endLine="280" endOffset="81"/></Target><Target id="@+id/n_1.2" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="282" startOffset="12" endLine="324" endOffset="63"/></Target><Target id="@+id/n1.2" view="TextView"><Expressions/><location startLine="291" startOffset="16" endLine="311" endOffset="62"/></Target><Target id="@+id/switch_is_discharge_notify" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="312" startOffset="16" endLine="323" endOffset="81"/></Target><Target id="@+id/n_2" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="325" startOffset="12" endLine="367" endOffset="63"/></Target><Target id="@+id/n2" view="TextView"><Expressions/><location startLine="334" startOffset="16" endLine="354" endOffset="62"/></Target><Target id="@+id/switch_is_showed_on_lockscreen" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="355" startOffset="16" endLine="366" endOffset="81"/></Target><Target id="@+id/n_3" view="TextView"><Expressions/><location startLine="368" startOffset="12" endLine="387" endOffset="63"/></Target><Target id="@+id/button_change_notify" view="TextView"><Expressions/><location startLine="388" startOffset="12" endLine="409" endOffset="63"/></Target><Target id="@+id/button_change_frequency" view="TextView"><Expressions/><location startLine="410" startOffset="12" endLine="431" endOffset="80"/></Target><Target id="@+id/button_change_notify_icon" view="TextView"><Expressions/><location startLine="432" startOffset="12" endLine="454" endOffset="83"/></Target><Target id="@+id/button_settings_notify" view="TextView"><Expressions/><location startLine="455" startOffset="12" endLine="475" endOffset="85"/></Target><Target id="@+id/animation_title_block" view="TextView"><Expressions/><location startLine="490" startOffset="12" endLine="499" endOffset="58"/></Target><Target id="@+id/switch_animation_block" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="500" startOffset="12" endLine="542" endOffset="63"/></Target><Target id="@+id/switch_animation_title" view="TextView"><Expressions/><location startLine="509" startOffset="16" endLine="529" endOffset="62"/></Target><Target id="@+id/switch_enable_animation" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="530" startOffset="16" endLine="541" endOffset="81"/></Target><Target id="@+id/switch_animation_time_block" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="543" startOffset="12" endLine="585" endOffset="63"/></Target><Target id="@+id/switch_animation_time_title" view="TextView"><Expressions/><location startLine="552" startOffset="16" endLine="572" endOffset="62"/></Target><Target id="@+id/switch_enable_animation_time" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="573" startOffset="16" endLine="584" endOffset="81"/></Target><Target id="@+id/anti_thief_block" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="587" startOffset="8" endLine="707" endOffset="59"/></Target><Target id="@+id/anti_thief_title_block" view="TextView"><Expressions/><location startLine="601" startOffset="12" endLine="610" endOffset="58"/></Target><Target id="@+id/anti_thief_info" view="ImageView"><Expressions/><location startLine="611" startOffset="12" endLine="620" endOffset="58"/></Target><Target id="@+id/switch_anti_thief_block" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="621" startOffset="12" endLine="663" endOffset="63"/></Target><Target id="@+id/switch_anti_thief_title" view="TextView"><Expressions/><location startLine="630" startOffset="16" endLine="650" endOffset="62"/></Target><Target id="@+id/switch_enable_anti_thief" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="651" startOffset="16" endLine="662" endOffset="81"/></Target><Target id="@+id/switch_anti_thief_sound_block" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="664" startOffset="12" endLine="706" endOffset="63"/></Target><Target id="@+id/switch_anti_thief_sound_title" view="TextView"><Expressions/><location startLine="673" startOffset="16" endLine="693" endOffset="62"/></Target><Target id="@+id/switch_enable_anti_thief_sound" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="694" startOffset="16" endLine="705" endOffset="81"/></Target><Target id="@+id/change_BLM_button" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="721" startOffset="12" endLine="764" endOffset="63"/></Target><Target id="@+id/switch_BLM" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="752" startOffset="16" endLine="763" endOffset="81"/></Target><Target id="@+id/hand_reset_button" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="765" startOffset="12" endLine="807" endOffset="63"/></Target><Target id="@+id/switch_hand_reset_session" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="795" startOffset="16" endLine="806" endOffset="81"/></Target><Target id="@+id/import_database" view="TextView"><Expressions/><location startLine="808" startOffset="12" endLine="828" endOffset="74"/></Target><Target id="@+id/export_database" view="TextView"><Expressions/><location startLine="829" startOffset="12" endLine="849" endOffset="75"/></Target><Target id="@+id/change_dual_battery" view="TextView"><Expressions/><location startLine="850" startOffset="12" endLine="867" endOffset="75"/></Target><Target id="@+id/d1" view="TextView"><Expressions/><location startLine="868" startOffset="12" endLine="877" endOffset="58"/></Target><Target id="@+id/current_session_amperage_button" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="878" startOffset="12" endLine="920" endOffset="63"/></Target><Target id="@+id/current_session_amperage_session" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="908" startOffset="16" endLine="919" endOffset="81"/></Target><Target id="@+id/heand_stab_database" view="TextView"><Expressions/><location startLine="921" startOffset="12" endLine="941" endOffset="91"/></Target><Target id="@+id/auto_stab_database" view="TextView"><Expressions/><location startLine="942" startOffset="12" endLine="962" endOffset="79"/></Target><Target id="@+id/clear_database" view="TextView"><Expressions/><location startLine="963" startOffset="12" endLine="983" endOffset="78"/></Target><Target id="@+id/change_capacity" view="TextView"><Expressions/><location startLine="984" startOffset="12" endLine="1001" endOffset="79"/></Target><Target id="@+id/indent_down" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1003" startOffset="8" endLine="1329" endOffset="59"/></Target><Target id="@+id/battery_info" view="TextView"><Expressions/><location startLine="1018" startOffset="12" endLine="1037" endOffset="63"/></Target><Target id="@+id/privacy_setting_button" view="TextView"><Expressions/><location startLine="1038" startOffset="12" endLine="1056" endOffset="84"/></Target><Target id="@+id/privacy_button" view="TextView"><Expressions/><location startLine="1057" startOffset="12" endLine="1075" endOffset="82"/></Target><Target id="@+id/debugger_button" view="TextView"><Expressions/><location startLine="1076" startOffset="12" endLine="1095" endOffset="74"/></Target><Target id="@+id/mediation_debugger_button" view="TextView"><Expressions/><location startLine="1097" startOffset="12" endLine="1116" endOffset="75"/></Target><Target id="@+id/about_translations" view="TextView"><Expressions/><location startLine="1117" startOffset="12" endLine="1136" endOffset="68"/></Target><Target id="@+id/write_me" view="TextView"><Expressions/><location startLine="1137" startOffset="12" endLine="1156" endOffset="77"/></Target><Target id="@+id/a_t" view="TextView"><Expressions/><location startLine="1157" startOffset="12" endLine="1168" endOffset="58"/></Target><Target id="@+id/remove_ads" view="TextView"><Expressions/><location startLine="1169" startOffset="12" endLine="1189" endOffset="72"/></Target><Target id="@+id/buy_advance_access" view="TextView"><Expressions/><location startLine="1190" startOffset="12" endLine="1209" endOffset="70"/></Target><Target id="@+id/work_in_backgound_button" view="TextView"><Expressions/><location startLine="1210" startOffset="12" endLine="1228" endOffset="78"/></Target><Target id="@+id/reset_purchases_button" view="TextView"><Expressions/><location startLine="1229" startOffset="12" endLine="1248" endOffset="74"/></Target><Target id="@+id/rate_button" view="TextView"><Expressions/><location startLine="1249" startOffset="12" endLine="1268" endOffset="82"/></Target><Target id="@+id/support_me_button" view="TextView"><Expressions/><location startLine="1269" startOffset="12" endLine="1288" endOffset="71"/></Target><Target id="@+id/version_app_button" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1289" startOffset="12" endLine="1328" endOffset="63"/></Target><Target id="@+id/ertretr" view="LinearLayout"><Expressions/><location startLine="1301" startOffset="16" endLine="1327" endOffset="30"/></Target><Target id="@+id/version_app" view="TextView"><Expressions/><location startLine="1320" startOffset="20" endLine="1326" endOffset="52"/></Target><Target id="@+id/debug_title" view="TextView"><Expressions/><location startLine="1346" startOffset="12" endLine="1355" endOffset="58"/></Target><Target id="@+id/test_new_discharge" view="TextView"><Expressions/><location startLine="1357" startOffset="12" endLine="1375" endOffset="71"/></Target></Targets></Layout>