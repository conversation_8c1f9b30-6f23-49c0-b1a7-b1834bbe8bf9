package com.tqhit.battery.one.features.navigation

import android.util.Log
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner

/**
 * Optimizes fragment lifecycle management for cached fragments.
 * Handles proper pause/resume cycles and memory management for reused fragments.
 */
class FragmentLifecycleOptimizer {
    companion object {
        private const val TAG = "FragmentLifecycleOptimizer"
    }
    
    private val fragmentStates = mutableMapOf<Fragment, FragmentState>()
    
    /**
     * Represents the state of a cached fragment
     */
    private data class FragmentState(
        var isVisible: Boolean = false,
        var lastActiveTime: Long = 0L,
        var pauseCount: Int = 0,
        var resumeCount: Int = 0
    )
    
    /**
     * Registers a fragment for lifecycle optimization.
     */
    fun registerFragment(fragment: Fragment) {
        if (fragmentStates.containsKey(fragment)) {
            Log.v(TAG, "Fragment ${fragment.javaClass.simpleName} already registered")
            return
        }
        
        val state = FragmentState()
        fragmentStates[fragment] = state
        
        // Add lifecycle observer to track fragment state
        fragment.lifecycle.addObserver(object : LifecycleEventObserver {
            override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
                when (event) {
                    Lifecycle.Event.ON_RESUME -> {
                        state.resumeCount++
                        state.lastActiveTime = System.currentTimeMillis()
                        Log.d(TAG, "FRAGMENT_LIFECYCLE: ${fragment.javaClass.simpleName} resumed (count: ${state.resumeCount})")
                    }
                    Lifecycle.Event.ON_PAUSE -> {
                        state.pauseCount++
                        Log.d(TAG, "FRAGMENT_LIFECYCLE: ${fragment.javaClass.simpleName} paused (count: ${state.pauseCount})")
                    }
                    Lifecycle.Event.ON_DESTROY -> {
                        unregisterFragment(fragment)
                    }
                    else -> { /* No action needed for other events */ }
                }
            }
        })
        
        Log.d(TAG, "FRAGMENT_LIFECYCLE: Registered fragment ${fragment.javaClass.simpleName} for optimization")
    }
    
    /**
     * Unregisters a fragment from lifecycle optimization.
     */
    fun unregisterFragment(fragment: Fragment) {
        fragmentStates.remove(fragment)
        Log.d(TAG, "FRAGMENT_LIFECYCLE: Unregistered fragment ${fragment.javaClass.simpleName}")
    }
    
    /**
     * Notifies that a fragment is now visible.
     */
    fun onFragmentVisible(fragment: Fragment) {
        // First, hide all other fragments to ensure only one is visible
        fragmentStates.keys.forEach { otherFragment ->
            if (otherFragment != fragment) {
                val otherState = fragmentStates[otherFragment]
                if (otherState?.isVisible == true) {
                    otherState.isVisible = false
                    Log.d(TAG, "FRAGMENT_LIFECYCLE: Auto-hiding fragment ${otherFragment.javaClass.simpleName} to ensure single visibility")

                    // Special handling for AnimationGridFragment
                    if (otherFragment.javaClass.simpleName == "AnimationGridFragment") {
                        Log.w(TAG, "FRAGMENT_LIFECYCLE: Specifically hiding AnimationGridFragment to prevent overlay")
                    }
                }
            }
        }

        val state = fragmentStates[fragment] ?: return

        if (!state.isVisible) {
            state.isVisible = true
            state.lastActiveTime = System.currentTimeMillis()

            Log.d(TAG, "FRAGMENT_LIFECYCLE: Fragment ${fragment.javaClass.simpleName} is now visible")

            // Special logging for AnimationGridFragment
            if (fragment.javaClass.simpleName == "AnimationGridFragment") {
                Log.d(TAG, "FRAGMENT_LIFECYCLE: AnimationGridFragment is now the active visible fragment")
            }

            // Trigger any necessary refresh operations for the fragment
            refreshFragmentIfNeeded(fragment, state)
        }
    }

    /**
     * Notifies that a fragment is no longer visible.
     */
    fun onFragmentHidden(fragment: Fragment) {
        val state = fragmentStates[fragment] ?: return

        if (state.isVisible) {
            state.isVisible = false

            Log.d(TAG, "FRAGMENT_LIFECYCLE: Fragment ${fragment.javaClass.simpleName} is now hidden")

            // Special logging for AnimationGridFragment
            if (fragment.javaClass.simpleName == "AnimationGridFragment") {
                Log.d(TAG, "FRAGMENT_LIFECYCLE: AnimationGridFragment is now hidden - overlay should be resolved")
            }

            // Perform any cleanup operations for hidden fragment
            pauseFragmentOperations(fragment)
        }
    }
    
    /**
     * Refreshes fragment data if it has been inactive for too long.
     */
    private fun refreshFragmentIfNeeded(fragment: Fragment, state: FragmentState) {
        val timeSinceLastActive = System.currentTimeMillis() - state.lastActiveTime
        val refreshThreshold = 30_000L // 30 seconds
        
        if (timeSinceLastActive > refreshThreshold) {
            Log.d(TAG, "FRAGMENT_LIFECYCLE: Fragment ${fragment.javaClass.simpleName} needs refresh (inactive for ${timeSinceLastActive}ms)")
            
            // Trigger fragment-specific refresh logic
            when (fragment) {
                is com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment -> {
                    // Charge fragment refresh logic could be added here
                    Log.d(TAG, "FRAGMENT_LIFECYCLE: Refreshing charge fragment data")
                }
                is com.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment -> {
                    // Discharge fragment refresh logic could be added here
                    Log.d(TAG, "FRAGMENT_LIFECYCLE: Refreshing discharge fragment data")
                }
                is com.tqhit.battery.one.fragment.main.HealthFragment -> {
                    // Health fragment refresh logic could be added here
                    Log.d(TAG, "FRAGMENT_LIFECYCLE: Refreshing health fragment data")
                }
                else -> {
                    Log.v(TAG, "FRAGMENT_LIFECYCLE: No specific refresh logic for ${fragment.javaClass.simpleName}")
                }
            }
        }
    }
    
    /**
     * Pauses non-essential operations for hidden fragments.
     */
    private fun pauseFragmentOperations(fragment: Fragment) {
        Log.d(TAG, "FRAGMENT_LIFECYCLE: Pausing operations for ${fragment.javaClass.simpleName}")
        
        // Fragment-specific pause logic could be implemented here
        // For example, stopping timers, pausing animations, etc.
    }
    
    /**
     * Gets performance statistics for all managed fragments.
     */
    fun getLifecycleStats(): String {
        val stats = StringBuilder()
        stats.append("Fragment Lifecycle Stats:\n")
        
        fragmentStates.forEach { (fragment, state) ->
            val timeSinceActive = System.currentTimeMillis() - state.lastActiveTime
            stats.append("- ${fragment.javaClass.simpleName}: visible=${state.isVisible}, ")
            stats.append("pauses=${state.pauseCount}, resumes=${state.resumeCount}, ")
            stats.append("inactive=${timeSinceActive}ms\n")
        }
        
        return stats.toString()
    }
    
    /**
     * Clears all fragment states.
     */
    fun clear() {
        Log.d(TAG, "FRAGMENT_LIFECYCLE: Clearing all fragment states (${fragmentStates.size} fragments)")
        fragmentStates.clear()
    }
}
