package com.tqhit.battery.one.features.stats.charge.di

import com.tqhit.battery.one.features.stats.charge.cache.PrefsStatsChargeCache
import com.tqhit.battery.one.features.stats.charge.cache.StatsChargeCache
import com.tqhit.battery.one.features.stats.charge.repository.DefaultStatsChargeRepository
import com.tqhit.battery.one.features.stats.charge.repository.StatsChargeRepository
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Hilt module for providing dependencies for the stats charge module.
 * This module binds interfaces to their implementations.
 * Note: Gson is already provided by the existing SharedPreferencesModule.
 */
@Module
@InstallIn(SingletonComponent::class)
abstract class StatsChargeDIModule {

    /**
     * Binds the StatsChargeRepository interface to DefaultStatsChargeRepository.
     * This ensures that whenever StatsChargeRepository is injected,
     * the DefaultStatsChargeRepository implementation will be provided.
     *
     * @param defaultStatsChargeRepository The default implementation
     * @return The StatsChargeRepository interface
     */
    @Binds
    @Singleton
    abstract fun bindStatsChargeRepository(
        defaultStatsChargeRepository: DefaultStatsChargeRepository
    ): StatsChargeRepository

    /**
     * Binds the StatsChargeCache interface to PrefsStatsChargeCache.
     * This ensures that whenever StatsChargeCache is injected,
     * the PrefsStatsChargeCache implementation will be provided.
     *
     * @param prefsStatsChargeCache The SharedPreferences implementation
     * @return The StatsChargeCache interface
     */
    @Binds
    @Singleton
    abstract fun bindStatsChargeCache(
        prefsStatsChargeCache: PrefsStatsChargeCache
    ): StatsChargeCache
}
