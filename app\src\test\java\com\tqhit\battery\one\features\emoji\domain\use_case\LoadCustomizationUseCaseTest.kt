package com.tqhit.battery.one.features.emoji.domain.use_case

import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfig
import com.tqhit.battery.one.features.emoji.domain.model.CustomizationConfig
import com.tqhit.battery.one.features.emoji.domain.model.FeatureStatus
import com.tqhit.battery.one.features.emoji.domain.model.UserCustomization
import com.tqhit.battery.one.features.emoji.domain.repository.BatteryStyleRepository
import com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.runTest
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test

/**
 * Unit tests for LoadCustomizationUseCase.
 * 
 * Tests the business logic for loading and observing user customization data
 * including data enrichment, validation, and status calculation.
 */
class LoadCustomizationUseCaseTest {
    
    private lateinit var loadCustomizationUseCase: LoadCustomizationUseCase
    private lateinit var mockCustomizationRepository: CustomizationRepository
    private lateinit var mockBatteryStyleRepository: BatteryStyleRepository
    
    // Test data
    private val testStyleConfig = BatteryStyleConfig(
        showEmoji = true,
        showPercentage = true,
        percentageFontSizeDp = 14,
        emojiSizeScale = 1.0f,
        percentageColor = 0xFFFFFFFF.toInt()
    )
    
    private val testBatteryStyle = BatteryStyle(
        id = "test_style_1",
        name = "Test Style",
        category = BatteryStyleCategory.CUTE,
        batteryImageUrl = "https://example.com/battery.png",
        emojiImageUrl = "https://example.com/emoji.png",
        isPremium = false,
        isPopular = true,
        defaultConfig = testStyleConfig
    )
    
    private val testCustomizationConfig = CustomizationConfig(
        selectedStyleId = "test_style_1",
        selectedBatteryImageUrl = "https://example.com/battery.png",
        selectedEmojiImageUrl = "https://example.com/emoji.png",
        styleConfig = testStyleConfig,
        isFeatureEnabled = true
    )
    
    private val testUserCustomization = UserCustomization(
        customizationConfig = testCustomizationConfig,
        hasAccessibilityPermission = true,
        hasOverlayPermission = true,
        isAccessibilityServiceEnabled = true
    )
    
    @Before
    fun setUp() {
        mockCustomizationRepository = mockk()
        mockBatteryStyleRepository = mockk()
        
        loadCustomizationUseCase = LoadCustomizationUseCase(
            customizationRepository = mockCustomizationRepository,
            batteryStyleRepository = mockBatteryStyleRepository
        )
    }
    
    @Test
    fun `userCustomizationFlow should emit enriched customization data`() = runTest {
        // Given
        coEvery { mockCustomizationRepository.userCustomizationFlow } returns flowOf(testUserCustomization)
        coEvery { mockBatteryStyleRepository.batteryStylesFlow } returns flowOf(listOf(testBatteryStyle))
        
        // When
        val enrichedCustomization = loadCustomizationUseCase.userCustomizationFlow.first()
        
        // Then
        assertNotNull("Should emit enriched customization", enrichedCustomization)
        assertEquals("Should have correct user customization", testUserCustomization, enrichedCustomization.userCustomization)
        assertEquals("Should have selected style", testBatteryStyle, enrichedCustomization.selectedStyle)
        assertTrue("Should indicate style is available", enrichedCustomization.isSelectedStyleAvailable)
        assertEquals("Should have correct feature status", FeatureStatus.ACTIVE, enrichedCustomization.featureStatus)
        assertEquals("Should have correct available styles count", 1, enrichedCustomization.availableStylesCount)
    }
    
    @Test
    fun `userCustomizationFlow should handle missing selected style`() = runTest {
        // Given
        val customizationWithMissingStyle = testUserCustomization.copy(
            customizationConfig = testCustomizationConfig.copy(selectedStyleId = "missing_style")
        )
        coEvery { mockCustomizationRepository.userCustomizationFlow } returns flowOf(customizationWithMissingStyle)
        coEvery { mockBatteryStyleRepository.batteryStylesFlow } returns flowOf(listOf(testBatteryStyle))
        
        // When
        val enrichedCustomization = loadCustomizationUseCase.userCustomizationFlow.first()
        
        // Then
        assertNull("Should have no selected style", enrichedCustomization.selectedStyle)
        assertFalse("Should indicate style is not available", enrichedCustomization.isSelectedStyleAvailable)
        assertTrue("Should need attention", enrichedCustomization.needsAttention())
    }
    
    @Test
    fun `userCustomizationFlow should handle empty style list`() = runTest {
        // Given
        coEvery { mockCustomizationRepository.userCustomizationFlow } returns flowOf(testUserCustomization)
        coEvery { mockBatteryStyleRepository.batteryStylesFlow } returns flowOf(emptyList())
        
        // When
        val enrichedCustomization = loadCustomizationUseCase.userCustomizationFlow.first()
        
        // Then
        assertNull("Should have no selected style", enrichedCustomization.selectedStyle)
        assertFalse("Should indicate style is not available", enrichedCustomization.isSelectedStyleAvailable)
        assertEquals("Should have zero available styles", 0, enrichedCustomization.availableStylesCount)
    }
    
    @Test
    fun `getCurrentCustomizationConfig should return current configuration`() = runTest {
        // Given
        coEvery { mockCustomizationRepository.customizationConfigFlow } returns flowOf(testCustomizationConfig)
        
        // When
        val config = loadCustomizationUseCase.getCurrentCustomizationConfig()
        
        // Then
        assertEquals("Should return current configuration", testCustomizationConfig, config)
    }
    
    @Test
    fun `getCurrentCustomizationConfig should return default on error`() = runTest {
        // Given
        coEvery { mockCustomizationRepository.customizationConfigFlow } returns flowOf()
        
        // When
        val config = loadCustomizationUseCase.getCurrentCustomizationConfig()
        
        // Then
        assertEquals("Should return default configuration", CustomizationConfig.createDefault(), config)
    }
    
    @Test
    fun `getCurrentUserCustomization should return current user customization`() = runTest {
        // Given
        coEvery { mockCustomizationRepository.userCustomizationFlow } returns flowOf(testUserCustomization)
        
        // When
        val userCustomization = loadCustomizationUseCase.getCurrentUserCustomization()
        
        // Then
        assertEquals("Should return current user customization", testUserCustomization, userCustomization)
    }
    
    @Test
    fun `getCurrentEnrichedUserCustomization should return enriched data`() = runTest {
        // Given
        coEvery { mockCustomizationRepository.userCustomizationFlow } returns flowOf(testUserCustomization)
        coEvery { mockBatteryStyleRepository.batteryStylesFlow } returns flowOf(listOf(testBatteryStyle))
        
        // When
        val enrichedCustomization = loadCustomizationUseCase.getCurrentEnrichedUserCustomization()
        
        // Then
        assertEquals("Should have correct user customization", testUserCustomization, enrichedCustomization.userCustomization)
        assertEquals("Should have selected style", testBatteryStyle, enrichedCustomization.selectedStyle)
        assertTrue("Should indicate style is available", enrichedCustomization.isSelectedStyleAvailable)
        assertEquals("Should have correct available styles count", 1, enrichedCustomization.availableStylesCount)
    }
    
    @Test
    fun `getCurrentSelectedStyle should return selected style when available`() = runTest {
        // Given
        coEvery { mockCustomizationRepository.customizationConfigFlow } returns flowOf(testCustomizationConfig)
        coEvery { mockBatteryStyleRepository.batteryStylesFlow } returns flowOf(listOf(testBatteryStyle))
        
        // When
        val selectedStyle = loadCustomizationUseCase.getCurrentSelectedStyle()
        
        // Then
        assertEquals("Should return selected style", testBatteryStyle, selectedStyle)
    }
    
    @Test
    fun `getCurrentSelectedStyle should return null when no style selected`() = runTest {
        // Given
        val configWithNoStyle = testCustomizationConfig.copy(selectedStyleId = "")
        coEvery { mockCustomizationRepository.customizationConfigFlow } returns flowOf(configWithNoStyle)
        coEvery { mockBatteryStyleRepository.batteryStylesFlow } returns flowOf(listOf(testBatteryStyle))
        
        // When
        val selectedStyle = loadCustomizationUseCase.getCurrentSelectedStyle()
        
        // Then
        assertNull("Should return null when no style selected", selectedStyle)
    }
    
    @Test
    fun `getCurrentSelectedStyle should return null when style not found`() = runTest {
        // Given
        val configWithMissingStyle = testCustomizationConfig.copy(selectedStyleId = "missing_style")
        coEvery { mockCustomizationRepository.customizationConfigFlow } returns flowOf(configWithMissingStyle)
        coEvery { mockBatteryStyleRepository.batteryStylesFlow } returns flowOf(listOf(testBatteryStyle))
        
        // When
        val selectedStyle = loadCustomizationUseCase.getCurrentSelectedStyle()
        
        // Then
        assertNull("Should return null when style not found", selectedStyle)
    }
    
    @Test
    fun `isFeatureReady should return true when all conditions met`() = runTest {
        // Given
        coEvery { mockCustomizationRepository.userCustomizationFlow } returns flowOf(testUserCustomization)
        
        // When
        val isReady = loadCustomizationUseCase.isFeatureReady()
        
        // Then
        assertTrue("Should be ready when all conditions met", isReady)
    }
    
    @Test
    fun `isFeatureReady should return false when permissions missing`() = runTest {
        // Given
        val customizationWithoutPermissions = testUserCustomization.copy(
            hasAccessibilityPermission = false
        )
        coEvery { mockCustomizationRepository.userCustomizationFlow } returns flowOf(customizationWithoutPermissions)
        
        // When
        val isReady = loadCustomizationUseCase.isFeatureReady()
        
        // Then
        assertFalse("Should not be ready when permissions missing", isReady)
    }
    
    @Test
    fun `getFeatureStatus should return correct status`() = runTest {
        // Given
        coEvery { mockCustomizationRepository.userCustomizationFlow } returns flowOf(testUserCustomization)
        
        // When
        val status = loadCustomizationUseCase.getFeatureStatus()
        
        // Then
        assertEquals("Should return active status", FeatureStatus.ACTIVE, status)
    }
    
    @Test
    fun `getFeatureStatus should return permissions required when missing permissions`() = runTest {
        // Given
        val customizationWithoutPermissions = testUserCustomization.copy(
            hasAccessibilityPermission = false
        )
        coEvery { mockCustomizationRepository.userCustomizationFlow } returns flowOf(customizationWithoutPermissions)
        
        // When
        val status = loadCustomizationUseCase.getFeatureStatus()
        
        // Then
        assertEquals("Should return permissions required", FeatureStatus.PERMISSIONS_REQUIRED, status)
    }
    
    @Test
    fun `validateCurrentConfiguration should return empty list for valid configuration`() = runTest {
        // Given
        coEvery { mockCustomizationRepository.validateAndFixConfiguration() } returns Result.success(emptyList())
        
        // When
        val issues = loadCustomizationUseCase.validateCurrentConfiguration()
        
        // Then
        assertTrue("Should return empty list for valid configuration", issues.isEmpty())
    }
    
    @Test
    fun `validateCurrentConfiguration should return issues for invalid configuration`() = runTest {
        // Given
        val validationIssues = listOf("Invalid configuration detected", "Configuration automatically fixed")
        coEvery { mockCustomizationRepository.validateAndFixConfiguration() } returns Result.success(validationIssues)
        
        // When
        val issues = loadCustomizationUseCase.validateCurrentConfiguration()
        
        // Then
        assertEquals("Should return validation issues", validationIssues, issues)
    }
    
    @Test
    fun `validateCurrentConfiguration should handle validation errors`() = runTest {
        // Given
        val validationError = RuntimeException("Validation failed")
        coEvery { mockCustomizationRepository.validateAndFixConfiguration() } returns Result.failure(validationError)
        
        // When
        val issues = loadCustomizationUseCase.validateCurrentConfiguration()
        
        // Then
        assertEquals("Should return error message", 1, issues.size)
        assertTrue("Should contain error message", issues.first().contains("validation failed"))
    }
    
    @Test
    fun `featureStatusFlow should emit correct status changes`() = runTest {
        // Given
        val activeCustomization = testUserCustomization
        val inactiveCustomization = testUserCustomization.copy(
            customizationConfig = testCustomizationConfig.copy(isFeatureEnabled = false)
        )
        
        coEvery { mockCustomizationRepository.userCustomizationFlow } returns flowOf(activeCustomization)
        coEvery { mockBatteryStyleRepository.batteryStylesFlow } returns flowOf(listOf(testBatteryStyle))
        
        // When
        val status = loadCustomizationUseCase.featureStatusFlow.first()
        
        // Then
        assertEquals("Should emit active status", FeatureStatus.ACTIVE, status)
    }
    
    @Test
    fun `isFeatureReadyFlow should emit correct readiness state`() = runTest {
        // Given
        coEvery { mockCustomizationRepository.userCustomizationFlow } returns flowOf(testUserCustomization)
        coEvery { mockBatteryStyleRepository.batteryStylesFlow } returns flowOf(listOf(testBatteryStyle))
        
        // When
        val isReady = loadCustomizationUseCase.isFeatureReadyFlow.first()
        
        // Then
        assertTrue("Should emit ready state", isReady)
    }
}
