package com.tqhit.battery.one.features.stats.discharge.presentation

import android.content.Context
import android.text.SpannableString
import android.text.style.ForegroundColorSpan
import android.util.Log
import android.view.View
import com.tqhit.battery.one.R
import com.tqhit.battery.one.databinding.NewFragmentDischargeBinding
import com.tqhit.battery.one.features.stats.discharge.domain.TimeConverter

/**
 * Handles UI updates for the discharge screen
 */
class DischargeUiUpdater(
    private val context: Context,
    private val binding: NewFragmentDischargeBinding,
    private val timeConverter: TimeConverter
) {
    companion object {
        private const val TAG = "DischargeUiUpdater"
        private const val STALENESS_TAG = "UI_STALENESS"
        private const val REFRESH_TAG = "UI_REFRESH"

        // Validation intervals
        private const val NORMAL_VALIDATION_INTERVAL_MS = 30000L // 30 seconds for normal checks
        private const val PERIODIC_STALENESS_CHECK_MS = 7200000L // 2 hours for deep staleness check
        private const val EXTENDED_BACKGROUND_THRESHOLD_MS = 1800000L // 30 minutes background threshold

        // Trigger thresholds
        private const val SIGNIFICANT_BATTERY_DROP = 5 // 5% battery drop
        private const val MAX_REFRESH_ATTEMPTS = 3 // Prevent infinite loops
    }

    // Validation state tracking
    private var lastValidationTime = 0L
    private var lastPeriodicStalenessCheck = 0L
    private var lastSuccessfulUIUpdate = System.currentTimeMillis()
    private var lastKnownBatteryPercentage = -1
    private var fragmentBackgroundTime = 0L

    // Staleness detection metrics
    private var stalenessDetectionCount = 0
    private var lastStalenessReportTime = 0L
    private var refreshAttemptCount = 0
    private var lastRefreshAttemptTime = 0L

    // UI state tracking
    private var lastKnownState: DischargeUiState? = null

    /**
     * Updates the status and estimates section
     */
    fun updateStatusAndEstimates(state: DischargeUiState) {
        val currentTime = System.currentTimeMillis()

        // Store state for validation and track successful updates
        lastKnownState = state

        // Check for significant battery changes that should trigger validation
        checkForSignificantBatteryChange(state)

        // Check for 100% battery state (full charge trigger)
        checkForFullChargeState(state)

        // Perform comprehensive staleness validation
        performStalenessValidation(state, currentTime)

        if (state.isLoadingInitial) {
            // Display loading placeholders
            binding.includeStatusAndEstimates.saeTvPercentage.text = "..."
            binding.includeStatusAndEstimates.saeTvFormattedStatusText.text = context.getString(R.string.charging, "...")
            Log.d(TAG, "Showing loading placeholders")
        } else {
            // Set the battery percentage directly (animation is handled separately)
            val percentageText = "${state.batteryPercentage}%"
            binding.includeStatusAndEstimates.saeTvPercentage.text = percentageText

            setColoredChargingText(state.batteryPercentage, state.isCharging)

            // Update time estimates
            val screenOnText = timeConverter.formatMillisToHoursMinutes(state.screenOnTimeRemainingMs)
            val mixedText = timeConverter.formatMillisToHoursMinutes(state.mixedUsageTimeRemainingMs)
            val screenOffText = timeConverter.formatMillisToHoursMinutes(state.screenOffTimeRemainingMs)

            binding.includeStatusAndEstimates.saeTvScreenOnTime.text = screenOnText
            binding.includeStatusAndEstimates.saeTvMixedUsageTime.text = mixedText
            binding.includeStatusAndEstimates.saeTvScreenOffTime.text = screenOffText

            Log.d(TAG, "Updated time estimations display - " +
                 "Screen ON: ${timeConverter.formatMillisToHoursMinutes(state.screenOnTimeRemainingMs)}, " +
                 "Mixed: ${timeConverter.formatMillisToHoursMinutes(state.mixedUsageTimeRemainingMs)}, " +
                 "Screen OFF: ${timeConverter.formatMillisToHoursMinutes(state.screenOffTimeRemainingMs)}")

            // Mark successful UI update
            lastSuccessfulUIUpdate = currentTime
        }
        
        // Show or hide the charging message based on charging state
        if (state.isCharging) {
            binding.dischargeChargingMessage.visibility = View.VISIBLE
            Log.d(TAG, "Device is charging. Showing charging message and hiding detailed sections.")
        } else {
            binding.dischargeChargingMessage.visibility = View.GONE
        }
    }

    /**
     * Updates the loss of charge section
     */
    fun updateLossOfCharge(state: DischargeUiState) {
        val session = state.currentSession
        if (session == null || state.isLoadingInitial || state.isCharging) {
            // Hide section or show placeholders if no active session
            binding.includeLossOfCharge.root.visibility = View.GONE
            return
        }

        binding.includeLossOfCharge.root.visibility = View.VISIBLE

        // Safely calculate percentage dropped to prevent division by zero
        val hasConsumptionData = session.totalMahConsumed > 0.0
        val screenOnPercentDropped = if (hasConsumptionData) session.screenOnPercentageDropped else 0.0
        val screenOffPercentDropped = if (hasConsumptionData) session.screenOffPercentageDropped else 0.0

        // Screen on consumption
        if (session.screenOnMahConsumed > 0 || screenOnPercentDropped > 0) {
            binding.includeLossOfCharge.locTvScreenOnPercentageDropped.text =
                String.format("%.1f", screenOnPercentDropped)
            binding.includeLossOfCharge.locTvScreenOnMahConsumed.text =
                String.format("%.1f", session.screenOnMahConsumed)
        } else {
            binding.includeLossOfCharge.locTvScreenOnPercentageDropped.text = "0.0"
            binding.includeLossOfCharge.locTvScreenOnMahConsumed.text = "0.0"
        }

        // Screen off consumption
        if (session.screenOffMahConsumed > 0 || screenOffPercentDropped > 0) {
            binding.includeLossOfCharge.locTvScreenOffPercentageDropped.text =
                String.format("%.1f", screenOffPercentDropped)
            binding.includeLossOfCharge.locTvScreenOffMahConsumed.text =
                String.format("%.1f", session.screenOffMahConsumed)
        } else {
            binding.includeLossOfCharge.locTvScreenOffPercentageDropped.text = "0.0"
            binding.includeLossOfCharge.locTvScreenOffMahConsumed.text = "0.0"
        }

        // Update screen time displays from UI state with validation
        val totalScreenTime = state.screenOnTimeUI + state.screenOffTimeUI
        val sessionDuration = session.durationMillis

        // Validate mathematical constraint: screen time sum ≤ session duration
        if (totalScreenTime > sessionDuration) {
            Log.w(TAG, "MATH_CONSTRAINT_VIOLATION: Screen time sum (${totalScreenTime/1000}s) exceeds session duration (${sessionDuration/1000}s). " +
                  "ON: ${state.screenOnTimeUI/1000}s, OFF: ${state.screenOffTimeUI/1000}s")
        }

        val screenOnTimeFormatted = timeConverter.formatMillisToHoursMinutesSeconds(state.screenOnTimeUI)
        val screenOffTimeFormatted = timeConverter.formatMillisToHoursMinutesSeconds(state.screenOffTimeUI)

        binding.includeLossOfCharge.locTvScreenOnTime.text = screenOnTimeFormatted
        binding.includeLossOfCharge.locTvScreenOffTime.text = screenOffTimeFormatted

        // Log every 30 seconds to reduce noise
        if ((state.screenOnTimeUI / 1000) % 30 == 0L || (state.screenOffTimeUI / 1000) % 30 == 0L) {
            Log.d(TAG, "UI_TIME_DISPLAY: Updated screen time UI display - " +
                "ON: $screenOnTimeFormatted (${state.screenOnTimeUI/1000}s), " +
                "OFF: $screenOffTimeFormatted (${state.screenOffTimeUI/1000}s), " +
                "Total: ${totalScreenTime/1000}s, Session: ${sessionDuration/1000}s")
        }

        Log.d(TAG, "TC2.2: Updated loss of charge section - " +
             "Screen ON: ${String.format("%.1f", screenOnPercentDropped)}%, " +
             "${String.format("%.1f", session.screenOnMahConsumed)} mAh, " +
             "${state.screenOnTimeUI/1000}s, " +
             "Screen OFF: ${String.format("%.1f", screenOffPercentDropped)}%, " +
             "${String.format("%.1f", session.screenOffMahConsumed)} mAh, " +
             "${state.screenOffTimeUI/1000}s")
    }

    /**
     * Updates the current session details section
     */
    fun updateCurrentSessionDetails(state: DischargeUiState) {
        val session = state.currentSession
        if (session == null || state.isLoadingInitial || state.isCharging) {
            // Hide section if no active session
            binding.includeCurrentSessionDetails.root.visibility = View.GONE
            return
        }

        // If the session block was previously hidden, this is a good sign that our caching is working
        if (binding.includeCurrentSessionDetails.root.visibility != View.VISIBLE) {
            Log.d(TAG, "TC2.1: Session block becoming visible - validation that session data is displayed after being restored/created")
        }

        binding.includeCurrentSessionDetails.root.visibility = View.VISIBLE

        // Total time and current rate
        val durationFormatted = timeConverter.formatMillisToHoursMinutes(session.durationMillis)
        binding.includeCurrentSessionDetails.csdTvTotalTimeValue.text = durationFormatted
        binding.includeCurrentSessionDetails.csdTvSessionStartTime.text =
            timeConverter.formatTimestamp(session.startTimeEpochMillis)
        binding.includeCurrentSessionDetails.csdTvCurrentRateValue.text =
            String.format("%.1f", session.currentDischargeRate)

        // Average speed and total consumed
        binding.includeCurrentSessionDetails.csdTvAvgSpeedPercentValue.text =
            String.format("%.1f", session.avgPercentPerHour)
        binding.includeCurrentSessionDetails.csdTvAvgSpeedMahValue.text =
            String.format("%.1f", session.avgMixedDischargeRateMahPerHour)

        binding.includeCurrentSessionDetails.csdTvTotalConsumedPercentValue.text =
            session.percentageDropped.toString()
        binding.includeCurrentSessionDetails.csdTvTotalConsumedMahValue.text =
            String.format("%.1f", session.totalMahConsumed)

        // Screen off/on specific stats
        binding.includeCurrentSessionDetails.csdTvScreenOffPercentValue.text =
            String.format("%.1f", session.avgScreenOffDischargeRatePercentPerHour)
        binding.includeCurrentSessionDetails.csdTvScreenOffMahValue.text =
            String.format("%.1f", session.avgScreenOffDischargeRateMahPerHour)

        binding.includeCurrentSessionDetails.csdTvScreenOnPercentValue.text =
            String.format("%.1f", session.avgScreenOnDischargeRatePercentPerHour)
        binding.includeCurrentSessionDetails.csdTvScreenOnMahValue.text =
            String.format("%.1f", session.avgScreenOnDischargeRateMahPerHour)

        Log.d(TAG, "TC2.2: Displaying session data - isActive=${session.isActive}, " +
               "duration=${durationFormatted}, " +
               "battery: ${session.startPercentage}% → ${session.currentPercentage}% (${session.percentageDropped}% drop)")
    }

    /**
     * Sets the colored charging text with percentage highlighted
     */
    private fun setColoredChargingText(percent: Int, isCharging: Boolean) {
        if (isCharging) {
            // Charging string has format parameter
            val fullText = context.getString(R.string.charging, percent.toString())
            val percentStr = "$percent%"
            val start = fullText.indexOf(percentStr)

            if (start >= 0) {
                val end = start + percentStr.length
                val spannable = SpannableString(fullText)

                val greenColor = getThemeColor(context, R.attr.colorr)
                spannable.setSpan(
                    ForegroundColorSpan(greenColor),
                    start,
                    end,
                    android.text.Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                )

                binding.includeStatusAndEstimates.saeTvFormattedStatusText.text = spannable
            } else {
                // If percentage not found in string, just set the text without formatting
                binding.includeStatusAndEstimates.saeTvFormattedStatusText.text = fullText
                Log.w(TAG, "Could not find percentage text '$percentStr' in string '$fullText'")
            }
        } else {
            // Discharging string doesn't have format parameter
            val dischargingText = context.getString(R.string.discharging)
            // Add the percentage after the discharging text and append "is enough for"
            val fullText = "$dischargingText $percent% is enough for"

            val spannable = SpannableString(fullText)
            val start = fullText.indexOf("$percent%")

            if (start >= 0) {
                val end = start + "$percent%".length
                val greenColor = getThemeColor(context, R.attr.colorr)
                spannable.setSpan(
                    ForegroundColorSpan(greenColor),
                    start,
                    end,
                    android.text.Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                )
            }

            binding.includeStatusAndEstimates.saeTvFormattedStatusText.text = spannable
        }
    }

    /**
     * Comprehensive staleness validation with multiple trigger conditions
     */
    private fun performStalenessValidation(currentState: DischargeUiState, currentTime: Long) {
        // Regular validation (every 30 seconds)
        if (currentTime - lastValidationTime >= NORMAL_VALIDATION_INTERVAL_MS) {
            validateUIConsistency(currentState, currentTime)
            lastValidationTime = currentTime
        }

        // Periodic deep staleness check (every 2 hours)
        if (currentTime - lastPeriodicStalenessCheck >= PERIODIC_STALENESS_CHECK_MS) {
            performPeriodicStalenessCheck(currentState, currentTime)
            lastPeriodicStalenessCheck = currentTime
        }

        // Extended background validation
        if (fragmentBackgroundTime > 0 &&
            currentTime - fragmentBackgroundTime >= EXTENDED_BACKGROUND_THRESHOLD_MS) {
            validateAfterExtendedBackground(currentState, currentTime)
            fragmentBackgroundTime = 0L // Reset after validation
        }
    }

    /**
     * Regular UI consistency validation
     */
    private fun validateUIConsistency(currentState: DischargeUiState, currentTime: Long) {
        if (currentState.isLoadingInitial) return

        val timeSinceLastUpdate = currentTime - lastSuccessfulUIUpdate
        val displayedPercentage = binding.includeStatusAndEstimates.saeTvPercentage.text.toString()
        val expectedPercentage = "${currentState.batteryPercentage}%"

        // Comprehensive staleness detection
        val stalenessIssues = mutableListOf<String>()

        // Check battery percentage
        if (displayedPercentage != expectedPercentage) {
            stalenessIssues.add("Battery: displayed='$displayedPercentage', expected='$expectedPercentage'")
        }

        // Check for critical "0" values
        if (displayedPercentage == "0" && currentState.batteryPercentage > 0) {
            stalenessIssues.add("Critical: Battery shows '0%' but actual is ${currentState.batteryPercentage}%")
        }

        // Check time estimates
        val displayedScreenOn = binding.includeStatusAndEstimates.saeTvScreenOnTime.text.toString()
        val expectedScreenOn = timeConverter.formatMillisToHoursMinutes(currentState.screenOnTimeRemainingMs)

        if (displayedScreenOn == "0" && expectedScreenOn != "0") {
            stalenessIssues.add("Screen ON time: displayed='0', expected='$expectedScreenOn'")
        }

        val displayedMixed = binding.includeStatusAndEstimates.saeTvMixedUsageTime.text.toString()
        val expectedMixed = timeConverter.formatMillisToHoursMinutes(currentState.mixedUsageTimeRemainingMs)

        if (displayedMixed == "0" && expectedMixed != "0") {
            stalenessIssues.add("Mixed usage time: displayed='0', expected='$expectedMixed'")
        }

        val displayedScreenOff = binding.includeStatusAndEstimates.saeTvScreenOffTime.text.toString()
        val expectedScreenOff = timeConverter.formatMillisToHoursMinutes(currentState.screenOffTimeRemainingMs)

        if (displayedScreenOff == "0" && expectedScreenOff != "0") {
            stalenessIssues.add("Screen OFF time: displayed='0', expected='$expectedScreenOff'")
        }

        // Log and handle staleness issues
        if (stalenessIssues.isNotEmpty()) {
            stalenessDetectionCount++

            Log.w(STALENESS_TAG, "UI staleness detected (Issue #$stalenessDetectionCount):")
            Log.w(STALENESS_TAG, "  Time since last successful update: ${timeSinceLastUpdate}ms")
            stalenessIssues.forEach { issue ->
                Log.w(STALENESS_TAG, "  - $issue")
            }

            // Attempt targeted refresh
            attemptTargetedUIRefresh(currentState, stalenessIssues)
        }
    }

    /**
     * Check for significant battery percentage changes
     */
    private fun checkForSignificantBatteryChange(state: DischargeUiState) {
        if (lastKnownBatteryPercentage >= 0) {
            val batteryDrop = lastKnownBatteryPercentage - state.batteryPercentage
            if (batteryDrop >= SIGNIFICANT_BATTERY_DROP) {
                Log.i(STALENESS_TAG, "Significant battery drop detected: ${lastKnownBatteryPercentage}% → ${state.batteryPercentage}% (${batteryDrop}% drop)")
                Log.i(STALENESS_TAG, "Triggering immediate UI validation due to significant battery change")
                validateUIConsistency(state, System.currentTimeMillis())
            }
        }
        lastKnownBatteryPercentage = state.batteryPercentage
    }

    /**
     * Check for 100% battery state (full charge trigger)
     */
    private fun checkForFullChargeState(state: DischargeUiState) {
        if (state.batteryPercentage == 100 && lastKnownBatteryPercentage != 100) {
            Log.i(STALENESS_TAG, "Battery reached 100% - triggering full charge state UI validation")
            validateUIConsistency(state, System.currentTimeMillis())
        }
    }

    /**
     * Periodic deep staleness check (every 2 hours)
     */
    private fun performPeriodicStalenessCheck(state: DischargeUiState, currentTime: Long) {
        val timeSinceLastUpdate = currentTime - lastSuccessfulUIUpdate
        val hoursRunning = timeSinceLastUpdate / 3600000.0 // Convert to hours

        Log.i(STALENESS_TAG, "Performing periodic staleness check (${String.format("%.1f", hoursRunning)} hours since last successful update)")

        // More thorough validation during periodic check
        if (!state.isLoadingInitial) {
            val allZeroValues = checkForAllZeroValues(state)
            if (allZeroValues.isNotEmpty()) {
                Log.e(STALENESS_TAG, "PERIODIC CHECK: Critical staleness detected after ${String.format("%.1f", hoursRunning)} hours:")
                allZeroValues.forEach { issue ->
                    Log.e(STALENESS_TAG, "  - $issue")
                }

                // Force complete refresh for periodic detection
                forceCompleteUIRefresh(state, "Periodic staleness check")
            } else {
                Log.i(STALENESS_TAG, "Periodic check passed - UI appears consistent")
            }
        }
    }

    /**
     * Validate UI after extended background period
     */
    private fun validateAfterExtendedBackground(state: DischargeUiState, currentTime: Long) {
        val backgroundDuration = currentTime - fragmentBackgroundTime
        val minutesInBackground = backgroundDuration / 60000.0

        Log.i(STALENESS_TAG, "Validating UI after ${String.format("%.1f", minutesInBackground)} minutes in background")

        // Comprehensive validation after background period
        validateUIConsistency(state, currentTime)

        // Check for any zero values that might have appeared during background
        val zeroValues = checkForAllZeroValues(state)
        if (zeroValues.isNotEmpty()) {
            Log.w(STALENESS_TAG, "Background validation detected issues:")
            zeroValues.forEach { issue ->
                Log.w(STALENESS_TAG, "  - $issue")
            }
            attemptTargetedUIRefresh(state, zeroValues)
        }
    }

    /**
     * Check for all zero values that shouldn't be zero
     */
    private fun checkForAllZeroValues(state: DischargeUiState): List<String> {
        val issues = mutableListOf<String>()

        if (!state.isLoadingInitial && state.batteryPercentage > 0) {
            val displayedPercentage = binding.includeStatusAndEstimates.saeTvPercentage.text.toString()
            if (displayedPercentage == "0") {
                issues.add("Battery percentage shows '0' but actual is ${state.batteryPercentage}%")
            }

            // Check time estimates
            if (state.screenOnTimeRemainingMs > 0) {
                val displayedScreenOn = binding.includeStatusAndEstimates.saeTvScreenOnTime.text.toString()
                if (displayedScreenOn == "0") {
                    val expected = timeConverter.formatMillisToHoursMinutes(state.screenOnTimeRemainingMs)
                    issues.add("Screen ON time shows '0' but should be '$expected'")
                }
            }

            if (state.mixedUsageTimeRemainingMs > 0) {
                val displayedMixed = binding.includeStatusAndEstimates.saeTvMixedUsageTime.text.toString()
                if (displayedMixed == "0") {
                    val expected = timeConverter.formatMillisToHoursMinutes(state.mixedUsageTimeRemainingMs)
                    issues.add("Mixed usage time shows '0' but should be '$expected'")
                }
            }

            if (state.screenOffTimeRemainingMs > 0) {
                val displayedScreenOff = binding.includeStatusAndEstimates.saeTvScreenOffTime.text.toString()
                if (displayedScreenOff == "0") {
                    val expected = timeConverter.formatMillisToHoursMinutes(state.screenOffTimeRemainingMs)
                    issues.add("Screen OFF time shows '0' but should be '$expected'")
                }
            }
        }

        return issues
    }

    /**
     * Attempt targeted UI refresh for specific components
     */
    private fun attemptTargetedUIRefresh(state: DischargeUiState, issues: List<String>) {
        val currentTime = System.currentTimeMillis()

        // Prevent too frequent refresh attempts
        if (currentTime - lastRefreshAttemptTime < 5000) { // 5 seconds minimum between attempts
            Log.d(REFRESH_TAG, "Skipping refresh attempt - too soon since last attempt")
            return
        }

        // Prevent infinite refresh loops
        if (refreshAttemptCount >= MAX_REFRESH_ATTEMPTS) {
            Log.e(REFRESH_TAG, "Maximum refresh attempts ($MAX_REFRESH_ATTEMPTS) reached - stopping to prevent infinite loop")
            return
        }

        refreshAttemptCount++
        lastRefreshAttemptTime = currentTime

        Log.i(REFRESH_TAG, "Attempting targeted UI refresh (Attempt #$refreshAttemptCount)")

        var refreshSuccess = true

        // Targeted refresh for each detected issue
        issues.forEach { issue ->
            try {
                when {
                    issue.contains("Battery percentage") -> {
                        val percentageText = "${state.batteryPercentage}%"
                        binding.includeStatusAndEstimates.saeTvPercentage.text = percentageText
                        setColoredChargingText(state.batteryPercentage, state.isCharging)
                        Log.d(REFRESH_TAG, "Refreshed battery percentage: $percentageText")
                    }

                    issue.contains("Screen ON time") -> {
                        val screenOnText = timeConverter.formatMillisToHoursMinutes(state.screenOnTimeRemainingMs)
                        binding.includeStatusAndEstimates.saeTvScreenOnTime.text = screenOnText
                        Log.d(REFRESH_TAG, "Refreshed screen ON time: $screenOnText")
                    }

                    issue.contains("Mixed usage time") -> {
                        val mixedText = timeConverter.formatMillisToHoursMinutes(state.mixedUsageTimeRemainingMs)
                        binding.includeStatusAndEstimates.saeTvMixedUsageTime.text = mixedText
                        Log.d(REFRESH_TAG, "Refreshed mixed usage time: $mixedText")
                    }

                    issue.contains("Screen OFF time") -> {
                        val screenOffText = timeConverter.formatMillisToHoursMinutes(state.screenOffTimeRemainingMs)
                        binding.includeStatusAndEstimates.saeTvScreenOffTime.text = screenOffText
                        Log.d(REFRESH_TAG, "Refreshed screen OFF time: $screenOffText")
                    }
                }
            } catch (e: Exception) {
                Log.e(REFRESH_TAG, "Error during targeted refresh for: $issue", e)
                refreshSuccess = false
            }
        }

        // Validate refresh success
        if (refreshSuccess) {
            // Wait a moment and verify the refresh worked
            android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                validateRefreshSuccess(state, issues)
            }, 100)
        } else {
            Log.w(REFRESH_TAG, "Targeted refresh failed - falling back to complete refresh")
            forceCompleteUIRefresh(state, "Targeted refresh failure")
        }
    }

    /**
     * Validate that the refresh was successful
     */
    private fun validateRefreshSuccess(state: DischargeUiState, originalIssues: List<String>) {
        val remainingIssues = checkForAllZeroValues(state)

        if (remainingIssues.isEmpty()) {
            Log.i(REFRESH_TAG, "Targeted refresh successful - all issues resolved")
            refreshAttemptCount = 0 // Reset counter on success
            lastSuccessfulUIUpdate = System.currentTimeMillis()
        } else {
            Log.w(REFRESH_TAG, "Targeted refresh partially failed - ${remainingIssues.size} issues remain:")
            remainingIssues.forEach { issue ->
                Log.w(REFRESH_TAG, "  - $issue")
            }

            // If we still have attempts left, try complete refresh
            if (refreshAttemptCount < MAX_REFRESH_ATTEMPTS) {
                Log.i(REFRESH_TAG, "Attempting complete UI refresh as fallback")
                forceCompleteUIRefresh(state, "Targeted refresh partial failure")
            } else {
                Log.e(REFRESH_TAG, "All refresh attempts exhausted - UI staleness may persist")
            }
        }
    }

    /**
     * Forces a complete UI refresh when staleness is detected
     */
    private fun forceCompleteUIRefresh(state: DischargeUiState, reason: String = "Staleness detection") {
        val currentTime = System.currentTimeMillis()

        Log.w(REFRESH_TAG, "Performing complete UI refresh due to: $reason")
        Log.w(REFRESH_TAG, "  Battery: ${state.batteryPercentage}%, Charging: ${state.isCharging}")
        Log.w(REFRESH_TAG, "  Time estimates: ON=${timeConverter.formatMillisToHoursMinutes(state.screenOnTimeRemainingMs)}, " +
            "Mixed=${timeConverter.formatMillisToHoursMinutes(state.mixedUsageTimeRemainingMs)}, " +
            "OFF=${timeConverter.formatMillisToHoursMinutes(state.screenOffTimeRemainingMs)}")

        try {
            // Re-apply all UI updates with fresh binding access
            updateStatusAndEstimates(state)
            updateLossOfCharge(state)
            updateCurrentSessionDetails(state)

            // Validate complete refresh success
            android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                val remainingIssues = checkForAllZeroValues(state)
                if (remainingIssues.isEmpty()) {
                    Log.i(REFRESH_TAG, "Complete UI refresh successful")
                    refreshAttemptCount = 0
                    lastSuccessfulUIUpdate = currentTime
                } else {
                    Log.e(REFRESH_TAG, "Complete UI refresh failed - persistent staleness detected:")
                    remainingIssues.forEach { issue ->
                        Log.e(REFRESH_TAG, "  - $issue")
                    }
                }
            }, 200)

        } catch (e: Exception) {
            Log.e(REFRESH_TAG, "Error during complete UI refresh", e)
        }
    }

    /**
     * Called when fragment goes to background
     */
    fun onFragmentPaused() {
        fragmentBackgroundTime = System.currentTimeMillis()
        Log.d(STALENESS_TAG, "Fragment paused - tracking background time for extended validation")
    }

    /**
     * Called when fragment comes to foreground
     */
    fun onFragmentResumed() {
        if (fragmentBackgroundTime > 0) {
            val backgroundDuration = System.currentTimeMillis() - fragmentBackgroundTime
            val minutesInBackground = backgroundDuration / 60000.0
            Log.d(STALENESS_TAG, "Fragment resumed after ${String.format("%.1f", minutesInBackground)} minutes in background")

            // Reset background time - validation will be triggered in next update if needed
            fragmentBackgroundTime = 0L
        }
    }

    /**
     * Get staleness detection metrics for debugging
     */
    fun getStalenessMetrics(): String {
        val currentTime = System.currentTimeMillis()
        val timeSinceLastUpdate = currentTime - lastSuccessfulUIUpdate
        val hoursRunning = timeSinceLastUpdate / 3600000.0

        return "Staleness Metrics: " +
            "Detections=$stalenessDetectionCount, " +
            "RefreshAttempts=$refreshAttemptCount, " +
            "HoursSinceLastSuccess=${String.format("%.1f", hoursRunning)}"
    }

    /**
     * Gets a color from the current theme
     */
    private fun getThemeColor(context: Context, attr: Int): Int {
        val typedValue = android.util.TypedValue()
        val theme = context.theme
        theme.resolveAttribute(attr, typedValue, true)
        return typedValue.data
    }
}
