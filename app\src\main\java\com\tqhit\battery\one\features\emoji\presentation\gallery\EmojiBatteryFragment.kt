package com.tqhit.battery.one.features.emoji.presentation.gallery

import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.Toast
import androidx.core.widget.doOnTextChanged
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.chip.Chip
import com.google.android.material.snackbar.Snackbar
import com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment
import com.tqhit.battery.one.R
import com.tqhit.battery.one.databinding.FragmentEmojiBatteryBinding
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory
import com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapter
import com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeFragment
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

/**
 * Fragment for displaying the emoji battery gallery.
 * 
 * This fragment follows the established stats module architecture pattern:
 * - Uses ViewBinding for type-safe view access
 * - Implements MVI pattern with ViewModel
 * - Integrates with CoreBatteryStatsService through ViewModel
 * - Provides comprehensive error handling and loading states
 * - Supports filtering, searching, and categorization
 * - Handles premium content and monetization flows
 * - Follows Material 3 design guidelines
 */
@AndroidEntryPoint
class EmojiBatteryFragment : AdLibBaseFragment<FragmentEmojiBatteryBinding>() {
    
    override val binding by lazy { 
        FragmentEmojiBatteryBinding.inflate(layoutInflater) 
    }
    
    private val viewModel: BatteryGalleryViewModel by viewModels()
    
    private lateinit var batteryStyleAdapter: BatteryStyleAdapter
    
    companion object {
        private const val TAG = "EmojiBatteryFragment"
        private const val GRID_SPAN_COUNT = 2
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        Log.d(TAG, "LIFECYCLE: onViewCreated")
        
        setupUI()
        setupData()
        observeViewModel()
    }
    
    override fun setupUI() {
        super.setupUI()
        Log.d(TAG, "SETUP: Setting up UI components")
        
        setupRecyclerView()
        setupFeatureToggle()
        setupFilters()
        setupSearch()
        setupCategoryChips()
        // setupSwipeRefresh() // Removed for now to fix compilation
        setupClickListeners()
    }
    
    override fun setupData() {
        super.setupData()
        Log.d(TAG, "SETUP: Setting up data")
        
        // Initial data load will be triggered by ViewModel initialization
        // No additional setup needed here as ViewModel handles data loading
    }
    
    /**
     * Sets up the RecyclerView with adapter and layout manager.
     */
    private fun setupRecyclerView() {
        Log.d(TAG, "SETUP_RV: Setting up RecyclerView")
        
        batteryStyleAdapter = BatteryStyleAdapter(
            onStyleClick = { style ->
                Log.d(TAG, "CLICK: Style clicked: ${style.name}")
                viewModel.handleEvent(BatteryGalleryEvent.SelectStyle(style))
            },
            onStyleLongClick = { style ->
                Log.d(TAG, "LONG_CLICK: Style long clicked: ${style.name}")
                viewModel.handleEvent(BatteryGalleryEvent.PreviewStyle(style))
            },
            onPremiumUnlockClick = { style ->
                Log.d(TAG, "PREMIUM_CLICK: Premium unlock clicked: ${style.name}")
                viewModel.handleEvent(BatteryGalleryEvent.UnlockPremiumStyle(style))
            },
            onImageLoadError = { styleId, imageType ->
                Log.w(TAG, "IMAGE_ERROR: Failed to load $imageType image for style: $styleId")
                viewModel.handleEvent(BatteryGalleryEvent.ImageLoadFailed(styleId, imageType))
            }
        )
        
        binding.stylesRecyclerView.apply {
            layoutManager = GridLayoutManager(requireContext(), GRID_SPAN_COUNT)
            adapter = batteryStyleAdapter
            
            // Add item decoration for spacing
            val spacing = resources.getDimensionPixelSize(R.dimen.grid_spacing)
            addItemDecoration(GridSpacingItemDecoration(GRID_SPAN_COUNT, spacing))
            
            // Optimize RecyclerView performance
            setHasFixedSize(true)
            setItemViewCacheSize(20)
        }
    }
    
    /**
     * Sets up the feature toggle switch and permission handling.
     */
    private fun setupFeatureToggle() {
        Log.d(TAG, "SETUP_TOGGLE: Setting up feature toggle")
        
        binding.featureToggleSwitch.setOnCheckedChangeListener { _, isChecked ->
            Log.d(TAG, "TOGGLE: Feature toggle changed to: $isChecked")
            viewModel.handleEvent(BatteryGalleryEvent.ToggleEmojiBatteryFeature(isChecked))
        }
        
        binding.requestPermissionsButton.setOnClickListener {
            Log.d(TAG, "PERMISSIONS: Request permissions clicked")
            viewModel.handleEvent(BatteryGalleryEvent.RequestPermissions)
        }
    }
    
    /**
     * Sets up filter chips and their click listeners.
     */
    private fun setupFilters() {
        Log.d(TAG, "SETUP_FILTERS: Setting up filter chips")
        
        binding.chipAll.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                Log.d(TAG, "FILTER: All styles selected")
                viewModel.handleEvent(BatteryGalleryEvent.ClearAllFilters)
            }
        }
        
        binding.chipPopular.setOnCheckedChangeListener { _, isChecked ->
            Log.d(TAG, "FILTER: Popular filter changed to: $isChecked")
            viewModel.handleEvent(BatteryGalleryEvent.ToggleShowOnlyPopular(isChecked))
        }
        
        binding.chipFree.setOnCheckedChangeListener { _, isChecked ->
            Log.d(TAG, "FILTER: Free filter changed to: $isChecked")
            viewModel.handleEvent(BatteryGalleryEvent.ToggleShowOnlyFree(isChecked))
        }
        
        binding.chipPremium.setOnCheckedChangeListener { _, isChecked ->
            Log.d(TAG, "FILTER: Premium filter changed to: $isChecked")
            viewModel.handleEvent(BatteryGalleryEvent.ToggleShowOnlyPremium(isChecked))
        }
    }
    
    /**
     * Sets up search functionality with text change listener.
     */
    private fun setupSearch() {
        Log.d(TAG, "SETUP_SEARCH: Setting up search functionality")
        
        binding.searchEditText.doOnTextChanged { text, _, _, _ ->
            val query = text?.toString() ?: ""
            Log.d(TAG, "SEARCH: Query changed to: '$query'")
            viewModel.handleEvent(BatteryGalleryEvent.SearchStyles(query))
        }
    }
    
    /**
     * Sets up category chips dynamically based on available categories.
     */
    private fun setupCategoryChips() {
        Log.d(TAG, "SETUP_CATEGORIES: Setting up category chips")
        
        // Add "All" chip first
        val allChip = createCategoryChip("All", null, true)
        binding.categoryChipGroup.addView(allChip)
        
        // Add category chips for each category
        BatteryStyleCategory.values().forEach { category ->
            val chip = createCategoryChip(category.displayName, category, false)
            binding.categoryChipGroup.addView(chip)
        }
        
        // Set up selection listener
        binding.categoryChipGroup.setOnCheckedStateChangeListener { group, checkedIds ->
            if (checkedIds.isNotEmpty()) {
                val checkedChip = group.findViewById<Chip>(checkedIds.first())
                val category = checkedChip.tag as? BatteryStyleCategory
                Log.d(TAG, "CATEGORY: Selected category: ${category?.displayName ?: "All"}")
                viewModel.handleEvent(BatteryGalleryEvent.FilterByCategory(category))
            }
        }
    }
    
    /**
     * Creates a category chip with the specified parameters.
     */
    private fun createCategoryChip(
        text: String, 
        category: BatteryStyleCategory?, 
        isChecked: Boolean
    ): Chip {
        return Chip(requireContext()).apply {
            this.text = text
            this.tag = category
            this.isCheckable = true
            this.isChecked = isChecked
            setChipBackgroundColorResource(R.color.chip_background_selector)
            setTextColor(resources.getColorStateList(R.color.chip_text_selector, null))
        }
    }
    
    /**
     * Sets up swipe-to-refresh functionality.
     * TODO: Re-implement when SwipeRefreshLayout dependency is available
     */
    private fun setupSwipeRefresh() {
        Log.d(TAG, "SETUP_REFRESH: Swipe refresh setup skipped - dependency not available")
        // TODO: Add SwipeRefreshLayout dependency and implement
    }
    
    /**
     * Sets up additional click listeners.
     */
    private fun setupClickListeners() {
        Log.d(TAG, "SETUP_CLICKS: Setting up click listeners")
        
        binding.retryButton.setOnClickListener {
            Log.d(TAG, "RETRY: Retry button clicked")
            viewModel.handleEvent(BatteryGalleryEvent.RetryLoading)
        }
    }
    
    /**
     * Observes ViewModel state changes and updates UI accordingly.
     */
    private fun observeViewModel() {
        Log.d(TAG, "OBSERVE: Setting up ViewModel observation")
        
        viewLifecycleOwner.lifecycleScope.launch {
            viewModel.uiState.collect { state ->
                Log.d(TAG, "STATE_UPDATE: UI state updated - loading: ${state.isLoading}, styles: ${state.displayedStyles.size}")
                updateUI(state)
            }
        }
    }
    
    /**
     * Updates the UI based on the current state.
     */
    private fun updateUI(state: BatteryGalleryState) {
        updateLoadingState(state)
        updateContentState(state)
        updateErrorState(state)
        updateFeatureToggleState(state)
        updateFilterState(state)
        handleNavigationEvent(state)
    }
    
    /**
     * Updates loading-related UI elements.
     */
    private fun updateLoadingState(state: BatteryGalleryState) {
        binding.loadingOverlay.visibility = if (state.isLoading) View.VISIBLE else View.GONE
        // TODO: Re-enable when SwipeRefreshLayout is available
        // binding.swipeRefreshLayout.isRefreshing = state.isRefreshing

        if (state.isLoading) {
            binding.loadingText.text = getString(R.string.loading_styles)
        }
    }
    
    /**
     * Updates content-related UI elements.
     */
    private fun updateContentState(state: BatteryGalleryState) {
        // Update adapter with new styles
        batteryStyleAdapter.submitList(state.displayedStyles)
        
        // Show/hide empty state
        val isEmpty = state.isEmpty()
        binding.emptyStateLayout.visibility = if (isEmpty) View.VISIBLE else View.GONE
        binding.stylesRecyclerView.visibility = if (isEmpty) View.GONE else View.VISIBLE
        
        if (isEmpty && !state.isLoading) {
            updateEmptyState(state)
        }
    }
    
    /**
     * Updates empty state UI elements.
     */
    private fun updateEmptyState(state: BatteryGalleryState) {
        val hasFilters = state.hasActiveFilters()
        
        if (hasFilters) {
            binding.emptyStateTitle.text = getString(R.string.no_styles_match_filter)
            binding.emptyStateMessage.text = getString(R.string.try_different_filter)
        } else {
            binding.emptyStateTitle.text = getString(R.string.no_styles_found)
            binding.emptyStateMessage.text = getString(R.string.no_styles_message)
        }
    }
    
    /**
     * Updates error state UI elements.
     */
    private fun updateErrorState(state: BatteryGalleryState) {
        state.errorMessage?.let { errorMessage ->
            Log.e(TAG, "ERROR_STATE: Showing error: $errorMessage")
            
            Snackbar.make(binding.root, state.getDisplayErrorMessage() ?: errorMessage, Snackbar.LENGTH_LONG)
                .setAction(getString(R.string.retry)) {
                    viewModel.handleEvent(BatteryGalleryEvent.RetryLoading)
                }
                .show()
            
            // Clear error after showing
            viewModel.handleEvent(BatteryGalleryEvent.DismissError)
        }
    }
    
    /**
     * Updates feature toggle state.
     */
    private fun updateFeatureToggleState(state: BatteryGalleryState) {
        binding.featureToggleSwitch.isChecked = state.isEmojiBatteryEnabled
        
        // Show/hide permission status
        val showPermissionStatus = state.isEmojiBatteryEnabled && !state.hasPermissions
        binding.permissionStatusLayout.visibility = if (showPermissionStatus) View.VISIBLE else View.GONE
    }
    
    /**
     * Updates filter state.
     */
    private fun updateFilterState(state: BatteryGalleryState) {
        // Update search text if needed (avoid infinite loop)
        if (binding.searchEditText.text.toString() != state.searchQuery) {
            binding.searchEditText.setText(state.searchQuery)
        }
        
        // Update filter chips
        binding.chipAll.isChecked = !state.hasActiveFilters()
        binding.chipPopular.isChecked = state.showOnlyPopular
        binding.chipFree.isChecked = state.showOnlyFree
        binding.chipPremium.isChecked = state.showOnlyPremium
    }

    /**
     * Handles navigation events from the ViewModel.
     */
    private fun handleNavigationEvent(state: BatteryGalleryState) {
        state.navigationEvent?.let { event ->
            Log.d(TAG, "NAVIGATION: Handling navigation event: ${event::class.simpleName}")

            when (event) {
                is NavigationEvent.NavigateToCustomization -> {
                    navigateToCustomization(event.style)
                }
                is NavigationEvent.NavigateToSettings -> {
                    navigateToSettings()
                }
                is NavigationEvent.ShowFeatureInfo -> {
                    showFeatureInfo()
                }
            }

            // Clear the navigation event after handling
            viewModel.clearNavigationEvent()
        }
    }

    /**
     * Navigates to the customization screen with the selected style.
     */
    private fun navigateToCustomization(style: BatteryStyle) {
        Log.d(TAG, "NAVIGATION: Navigating to customization for style: ${style.name}")

        try {
            val customizeFragment = CustomizeFragment.newInstance()

            // Use fragment transaction for navigation
            parentFragmentManager.beginTransaction()
                .setCustomAnimations(
                    R.anim.slide_in_right,
                    R.anim.slide_out_left,
                    R.anim.slide_in_left,
                    R.anim.slide_out_right
                )
                .replace(R.id.fragment_container, customizeFragment)
                .addToBackStack("customization")
                .commit()

            Log.d(TAG, "NAVIGATION: Successfully navigated to customization screen")
        } catch (e: Exception) {
            Log.e(TAG, "NAVIGATION: Failed to navigate to customization", e)
            Toast.makeText(requireContext(), "Failed to open customization screen", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * Navigates to the settings screen.
     */
    private fun navigateToSettings() {
        Log.d(TAG, "NAVIGATION: Navigating to settings")
        // TODO: Implement settings navigation
        Toast.makeText(requireContext(), "Settings navigation not implemented yet", Toast.LENGTH_SHORT).show()
    }

    /**
     * Shows feature information dialog.
     */
    private fun showFeatureInfo() {
        Log.d(TAG, "NAVIGATION: Showing feature info")
        // TODO: Implement feature info dialog
        Toast.makeText(requireContext(), "Feature info not implemented yet", Toast.LENGTH_SHORT).show()
    }

    override fun onResume() {
        super.onResume()
        Log.d(TAG, "LIFECYCLE: onResume")
        viewModel.handleEvent(BatteryGalleryEvent.OnResume)
    }

    override fun onPause() {
        super.onPause()
        Log.d(TAG, "LIFECYCLE: onPause")
        viewModel.handleEvent(BatteryGalleryEvent.OnPause)
    }
}
