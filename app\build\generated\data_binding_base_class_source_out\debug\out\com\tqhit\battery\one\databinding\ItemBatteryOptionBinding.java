// Generated by view binder compiler. Do not edit!
package com.tqhit.battery.one.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.ProgressBar;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.card.MaterialCardView;
import com.tqhit.battery.one.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemBatteryOptionBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final ImageView batteryImageView;

  @NonNull
  public final ProgressBar loadingIndicator;

  @NonNull
  public final ImageView premiumBadge;

  @NonNull
  public final ImageView selectionIndicator;

  private ItemBatteryOptionBinding(@NonNull MaterialCardView rootView,
      @NonNull ImageView batteryImageView, @NonNull ProgressBar loadingIndicator,
      @NonNull ImageView premiumBadge, @NonNull ImageView selectionIndicator) {
    this.rootView = rootView;
    this.batteryImageView = batteryImageView;
    this.loadingIndicator = loadingIndicator;
    this.premiumBadge = premiumBadge;
    this.selectionIndicator = selectionIndicator;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemBatteryOptionBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemBatteryOptionBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_battery_option, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemBatteryOptionBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.batteryImageView;
      ImageView batteryImageView = ViewBindings.findChildViewById(rootView, id);
      if (batteryImageView == null) {
        break missingId;
      }

      id = R.id.loadingIndicator;
      ProgressBar loadingIndicator = ViewBindings.findChildViewById(rootView, id);
      if (loadingIndicator == null) {
        break missingId;
      }

      id = R.id.premiumBadge;
      ImageView premiumBadge = ViewBindings.findChildViewById(rootView, id);
      if (premiumBadge == null) {
        break missingId;
      }

      id = R.id.selectionIndicator;
      ImageView selectionIndicator = ViewBindings.findChildViewById(rootView, id);
      if (selectionIndicator == null) {
        break missingId;
      }

      return new ItemBatteryOptionBinding((MaterialCardView) rootView, batteryImageView,
          loadingIndicator, premiumBadge, selectionIndicator);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
