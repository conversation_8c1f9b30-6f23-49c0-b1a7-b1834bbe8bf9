1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.fc.p.tj.charginganimation.batterycharging.chargeeffect"
4    android:versionCode="42"
5    android:versionName="1.2.0.20250617" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:5:5-67
11-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:6:5-79
12-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:6:22-76
13    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
13-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:7:5-77
13-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:7:22-74
14    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
14-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:8:5-95
14-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:8:22-92
15    <uses-permission android:name="android.permission.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
15-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:9:5-101
15-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:9:22-99
16    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
16-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:10:5-76
16-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:10:22-74
17    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" />
17-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:11:5-88
17-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:11:22-86
18    <uses-permission android:name="android.permission.VIBRATE" />
18-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:12:5-65
18-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:12:22-63
19    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
19-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:13:5-78
19-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:13:22-75
20    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
20-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:14:5-81
20-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:14:22-78
21    <uses-permission android:name="android.permission.PACKAGE_USAGE_STATS" />
21-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:15:5-16:47
21-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:15:22-75
22
23    <permission
23-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:18:5-20:47
24        android:name="com.tqhit.battery.one.permission.FINISH_OVERLAY"
24-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:19:9-71
25        android:protectionLevel="signature" />
25-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:20:9-44
26
27    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
27-->[com.github.tqhit:AdLib:1.2.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a649bc6d741f1561035089684f0b179\transformed\jetified-AdLib-1.2.7\AndroidManifest.xml:32:5-76
27-->[com.github.tqhit:AdLib:1.2.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a649bc6d741f1561035089684f0b179\transformed\jetified-AdLib-1.2.7\AndroidManifest.xml:32:22-73
28    <uses-permission android:name="android.permission.WAKE_LOCK" />
28-->[com.github.tqhit:AdLib:1.2.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a649bc6d741f1561035089684f0b179\transformed\jetified-AdLib-1.2.7\AndroidManifest.xml:33:5-68
28-->[com.github.tqhit:AdLib:1.2.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a649bc6d741f1561035089684f0b179\transformed\jetified-AdLib-1.2.7\AndroidManifest.xml:33:22-65
29    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
29-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\430c56e4d5b03e6b74c8f45faf04d96d\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:26:5-110
29-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\430c56e4d5b03e6b74c8f45faf04d96d\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:26:22-107
30    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
30-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\382e0d6a1af511ca59a11019348ab80b\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:25:5-79
30-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\382e0d6a1af511ca59a11019348ab80b\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:25:22-76
31    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
31-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\382e0d6a1af511ca59a11019348ab80b\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:26:5-88
31-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\382e0d6a1af511ca59a11019348ab80b\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:26:22-85
32    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
32-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\382e0d6a1af511ca59a11019348ab80b\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:27:5-82
32-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\382e0d6a1af511ca59a11019348ab80b\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:27:22-79
33    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" /> <!-- Android package visibility setting -->
33-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:29:5-83
33-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:29:22-80
34    <queries>
34-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:35:5-68:15
35
36        <!-- For browser content -->
37        <intent>
37-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:38:9-44:18
38            <action android:name="android.intent.action.VIEW" />
38-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:89:17-69
38-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:89:25-66
39
40            <category android:name="android.intent.category.BROWSABLE" />
40-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:91:17-78
40-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:91:27-75
41
42            <data android:scheme="https" />
42-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:92:17-80
42-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:92:23-47
43        </intent>
44        <!-- End of browser content -->
45        <!-- For CustomTabsService -->
46        <intent>
46-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:47:9-49:18
47            <action android:name="android.support.customtabs.action.CustomTabsService" />
47-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:48:13-90
47-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:48:21-87
48        </intent>
49        <!-- End of CustomTabsService -->
50        <!-- For MRAID capabilities -->
51        <intent>
51-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:52:9-56:18
52            <action android:name="android.intent.action.INSERT" />
52-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:53:13-67
52-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:53:21-64
53
54            <data android:mimeType="vnd.android.cursor.dir/event" />
54-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:92:17-80
55        </intent>
56        <intent>
56-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:57:9-61:18
57            <action android:name="android.intent.action.VIEW" />
57-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:89:17-69
57-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:89:25-66
58
59            <data android:scheme="sms" />
59-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:92:17-80
59-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:92:23-47
60        </intent>
61        <intent>
61-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:62:9-66:18
62            <action android:name="android.intent.action.DIAL" />
62-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:63:13-65
62-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:63:21-62
63
64            <data android:path="tel:" />
64-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:92:17-80
65        </intent>
66        <!-- End of MRAID capabilities -->
67    </queries>
68
69    <permission
69-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f908cdc45776521b403beeef1508641c\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
70        android:name="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
70-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f908cdc45776521b403beeef1508641c\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
71        android:protectionLevel="signature" />
71-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f908cdc45776521b403beeef1508641c\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
72
73    <uses-permission android:name="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
73-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f908cdc45776521b403beeef1508641c\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
73-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f908cdc45776521b403beeef1508641c\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
74
75    <application
75-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:22:5-127:19
76        android:name="com.tqhit.battery.one.BatteryApplication"
76-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:23:9-64
77        android:allowBackup="true"
77-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:24:9-35
78        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
78-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f908cdc45776521b403beeef1508641c\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
79        android:dataExtractionRules="@xml/data_extraction_rules"
79-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:25:9-65
80        android:debuggable="true"
81        android:extractNativeLibs="false"
82        android:fullBackupContent="@xml/backup_rules"
82-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:26:9-54
83        android:icon="@mipmap/ic_launcher"
83-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:27:9-43
84        android:label="@string/app_name"
84-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:28:9-41
85        android:roundIcon="@mipmap/ic_launcher_round"
85-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:29:9-54
86        android:screenOrientation="portrait"
86-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:30:9-45
87        android:supportsRtl="true"
87-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:31:9-35
88        android:theme="@style/Theme.BatteryOne" >
88-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:32:9-48
89        <!-- Sample AdMob app ID: ca-app-pub-3940256099942544~3347511713 -->
90        <meta-data
90-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:35:9-37:70
91            android:name="com.google.android.gms.ads.APPLICATION_ID"
91-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:36:13-69
92            android:value="ca-app-pub-9844172086883515~3386117176" />
92-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:37:13-67
93
94        <activity
94-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:39:9-47:20
95            android:name="com.tqhit.battery.one.activity.splash.SplashActivity"
95-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:40:13-80
96            android:exported="true"
96-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:41:13-36
97            android:theme="@style/Theme.BatteryOne.Splash" >
97-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:42:13-59
98            <intent-filter>
98-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:43:13-46:29
99                <action android:name="android.intent.action.MAIN" />
99-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:44:17-69
99-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:44:25-66
100
101                <category android:name="android.intent.category.LAUNCHER" />
101-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:45:17-77
101-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:45:27-74
102            </intent-filter>
103        </activity>
104        <activity
104-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:49:9-53:54
105            android:name="com.tqhit.battery.one.activity.starting.StartingActivity"
105-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:50:13-84
106            android:exported="false"
106-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:51:13-37
107            android:screenOrientation="portrait"
107-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:52:13-49
108            android:theme="@style/Theme.BatteryOne" />
108-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:53:13-52
109        <activity
109-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:55:9-59:51
110            android:name="com.tqhit.battery.one.activity.main.MainActivity"
110-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:56:13-76
111            android:exported="true"
111-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:57:13-36
112            android:launchMode="singleTask"
112-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:58:13-44
113            android:screenOrientation="portrait" />
113-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:59:13-49
114        <activity
114-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:61:9-65:51
115            android:name="com.tqhit.battery.one.activity.animation.AnimationActivity"
115-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:62:13-86
116            android:configChanges="orientation|screenSize"
116-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:63:13-59
117            android:launchMode="singleTask"
117-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:64:13-44
118            android:screenOrientation="portrait" />
118-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:65:13-49
119        <activity
119-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:67:9-73:51
120            android:name="com.tqhit.battery.one.activity.overlay.ChargingOverlayActivity"
120-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:68:13-90
121            android:configChanges="orientation|screenSize"
121-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:71:13-59
122            android:exported="false"
122-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:69:13-37
123            android:launchMode="singleTask"
123-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:72:13-44
124            android:screenOrientation="portrait"
124-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:73:13-49
125            android:theme="@style/Theme.AppCompat.NoActionBar" />
125-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:70:13-63
126        <activity
126-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:75:9-81:51
127            android:name="com.tqhit.battery.one.activity.password.EnterPasswordActivity"
127-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:76:13-89
128            android:configChanges="orientation|screenSize"
128-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:79:13-59
129            android:exported="false"
129-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:77:13-37
130            android:launchMode="singleTask"
130-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:80:13-44
131            android:screenOrientation="portrait"
131-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:81:13-49
132            android:theme="@style/Theme.AppCompat.NoActionBar" />
132-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:78:13-63
133        <activity
133-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:83:9-94:20
134            android:name="com.tqhit.battery.one.features.new_discharge.presentation.TestNewDischargeActivity"
134-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:84:13-110
135            android:exported="true"
135-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:85:13-36
136            android:screenOrientation="portrait"
136-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:87:13-49
137            android:theme="@style/Theme.AppCompat" >
137-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:86:13-51
138            <intent-filter>
138-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:88:13-93:29
139                <action android:name="android.intent.action.VIEW" />
139-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:89:17-69
139-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:89:25-66
140
141                <category android:name="android.intent.category.DEFAULT" />
141-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:90:17-76
141-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:90:27-73
142                <category android:name="android.intent.category.BROWSABLE" />
142-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:91:17-78
142-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:91:27-75
143
144                <data
144-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:92:17-80
145                    android:host="test_discharge"
145-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:92:48-77
146                    android:scheme="battery" />
146-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:92:23-47
147            </intent-filter>
148        </activity> <!-- Legacy TestNewChargeActivity removed - was in legacy directory -->
149        <!-- DebugActivity will be conditionally included via build variant manifests -->
150        <service
150-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:100:9-104:59
151            android:name="com.tqhit.battery.one.service.BatteryMonitorService"
151-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:101:13-79
152            android:enabled="true"
152-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:102:13-35
153            android:exported="false"
153-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:103:13-37
154            android:foregroundServiceType="specialUse" />
154-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:104:13-55
155        <service
155-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:105:9-109:59
156            android:name="com.tqhit.battery.one.service.ChargingOverlayService"
156-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:106:13-80
157            android:enabled="true"
157-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:107:13-35
158            android:exported="false"
158-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:108:13-37
159            android:foregroundServiceType="specialUse" /> <!-- Legacy services removed - replaced by CoreBatteryStatsService and UnifiedBatteryNotificationService -->
159-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:109:13-55
160        <!-- NewChargeMonitorService, DischargeTimerService, and BatteryStatusService were in legacy directory -->
161        <service
161-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:112:9-116:59
162            android:name="com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerService"
162-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:113:13-112
163            android:enabled="true"
163-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:114:13-35
164            android:exported="false"
164-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:115:13-37
165            android:foregroundServiceType="specialUse" />
165-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:116:13-55
166        <service
166-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:117:9-121:59
167            android:name="com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService"
167-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:118:13-108
168            android:enabled="true"
168-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:119:13-35
169            android:exported="false"
169-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:120:13-37
170            android:foregroundServiceType="specialUse" />
170-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:121:13-55
171        <service
171-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:122:9-126:59
172            android:name="com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationService"
172-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:123:13-112
173            android:enabled="true"
173-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:124:13-35
174            android:exported="false"
174-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:125:13-37
175            android:foregroundServiceType="specialUse" />
175-->D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:126:13-55
176
177        <activity
177-->[cat.ereza:customactivityoncrash:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2821057fb80181e87f67e0a41ee8161d\transformed\jetified-customactivityoncrash-2.4.0\AndroidManifest.xml:10:9-12:49
178            android:name="cat.ereza.customactivityoncrash.activity.DefaultErrorActivity"
178-->[cat.ereza:customactivityoncrash:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2821057fb80181e87f67e0a41ee8161d\transformed\jetified-customactivityoncrash-2.4.0\AndroidManifest.xml:11:13-89
179            android:process=":error_activity" />
179-->[cat.ereza:customactivityoncrash:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2821057fb80181e87f67e0a41ee8161d\transformed\jetified-customactivityoncrash-2.4.0\AndroidManifest.xml:12:13-46
180
181        <provider
181-->[cat.ereza:customactivityoncrash:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2821057fb80181e87f67e0a41ee8161d\transformed\jetified-customactivityoncrash-2.4.0\AndroidManifest.xml:14:9-18:39
182            android:name="cat.ereza.customactivityoncrash.provider.CaocInitProvider"
182-->[cat.ereza:customactivityoncrash:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2821057fb80181e87f67e0a41ee8161d\transformed\jetified-customactivityoncrash-2.4.0\AndroidManifest.xml:15:13-85
183            android:authorities="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.customactivityoncrashinitprovider"
183-->[cat.ereza:customactivityoncrash:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2821057fb80181e87f67e0a41ee8161d\transformed\jetified-customactivityoncrash-2.4.0\AndroidManifest.xml:16:13-85
184            android:exported="false"
184-->[cat.ereza:customactivityoncrash:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2821057fb80181e87f67e0a41ee8161d\transformed\jetified-customactivityoncrash-2.4.0\AndroidManifest.xml:17:13-37
185            android:initOrder="101" />
185-->[cat.ereza:customactivityoncrash:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2821057fb80181e87f67e0a41ee8161d\transformed\jetified-customactivityoncrash-2.4.0\AndroidManifest.xml:18:13-36
186
187        <receiver
187-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\430c56e4d5b03e6b74c8f45faf04d96d\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:29:9-33:20
188            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
188-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\430c56e4d5b03e6b74c8f45faf04d96d\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:30:13-85
189            android:enabled="true"
189-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\430c56e4d5b03e6b74c8f45faf04d96d\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:31:13-35
190            android:exported="false" >
190-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\430c56e4d5b03e6b74c8f45faf04d96d\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:32:13-37
191        </receiver>
192
193        <service
193-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\430c56e4d5b03e6b74c8f45faf04d96d\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:35:9-38:40
194            android:name="com.google.android.gms.measurement.AppMeasurementService"
194-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\430c56e4d5b03e6b74c8f45faf04d96d\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:36:13-84
195            android:enabled="true"
195-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\430c56e4d5b03e6b74c8f45faf04d96d\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:37:13-35
196            android:exported="false" />
196-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\430c56e4d5b03e6b74c8f45faf04d96d\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:38:13-37
197        <service
197-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\430c56e4d5b03e6b74c8f45faf04d96d\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:39:9-43:72
198            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
198-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\430c56e4d5b03e6b74c8f45faf04d96d\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:40:13-87
199            android:enabled="true"
199-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\430c56e4d5b03e6b74c8f45faf04d96d\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:41:13-35
200            android:exported="false"
200-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\430c56e4d5b03e6b74c8f45faf04d96d\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:42:13-37
201            android:permission="android.permission.BIND_JOB_SERVICE" />
201-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\430c56e4d5b03e6b74c8f45faf04d96d\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:43:13-69
202        <service
202-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\382e0d6a1af511ca59a11019348ab80b\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:30:9-36:19
203            android:name="com.google.firebase.components.ComponentDiscoveryService"
203-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\382e0d6a1af511ca59a11019348ab80b\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:31:13-84
204            android:directBootAware="true"
204-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\42c987edc6bac2d568a7305ae38ce788\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
205            android:exported="false" >
205-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\382e0d6a1af511ca59a11019348ab80b\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:32:13-37
206            <meta-data
206-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\382e0d6a1af511ca59a11019348ab80b\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:33:13-35:85
207                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
207-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\382e0d6a1af511ca59a11019348ab80b\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:34:17-139
208                android:value="com.google.firebase.components.ComponentRegistrar" />
208-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\382e0d6a1af511ca59a11019348ab80b\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:35:17-82
209            <meta-data
209-->[com.google.firebase:firebase-crashlytics:19.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7827a62f7a18bcb72aaa3ce93f2a42ed\transformed\jetified-firebase-crashlytics-19.4.0\AndroidManifest.xml:15:13-17:85
210                android:name="com.google.firebase.components:com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar"
210-->[com.google.firebase:firebase-crashlytics:19.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7827a62f7a18bcb72aaa3ce93f2a42ed\transformed\jetified-firebase-crashlytics-19.4.0\AndroidManifest.xml:16:17-126
211                android:value="com.google.firebase.components.ComponentRegistrar" />
211-->[com.google.firebase:firebase-crashlytics:19.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7827a62f7a18bcb72aaa3ce93f2a42ed\transformed\jetified-firebase-crashlytics-19.4.0\AndroidManifest.xml:17:17-82
212            <meta-data
212-->[com.google.firebase:firebase-crashlytics:19.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7827a62f7a18bcb72aaa3ce93f2a42ed\transformed\jetified-firebase-crashlytics-19.4.0\AndroidManifest.xml:18:13-20:85
213                android:name="com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar"
213-->[com.google.firebase:firebase-crashlytics:19.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7827a62f7a18bcb72aaa3ce93f2a42ed\transformed\jetified-firebase-crashlytics-19.4.0\AndroidManifest.xml:19:17-115
214                android:value="com.google.firebase.components.ComponentRegistrar" />
214-->[com.google.firebase:firebase-crashlytics:19.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7827a62f7a18bcb72aaa3ce93f2a42ed\transformed\jetified-firebase-crashlytics-19.4.0\AndroidManifest.xml:20:17-82
215            <meta-data
215-->[com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\647741b0d36db01281989d0b55b41c1e\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:29:13-31:85
216                android:name="com.google.firebase.components:com.google.firebase.remoteconfig.FirebaseRemoteConfigKtxRegistrar"
216-->[com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\647741b0d36db01281989d0b55b41c1e\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:30:17-128
217                android:value="com.google.firebase.components.ComponentRegistrar" />
217-->[com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\647741b0d36db01281989d0b55b41c1e\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:31:17-82
218            <meta-data
218-->[com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\647741b0d36db01281989d0b55b41c1e\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:32:13-34:85
219                android:name="com.google.firebase.components:com.google.firebase.remoteconfig.RemoteConfigRegistrar"
219-->[com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\647741b0d36db01281989d0b55b41c1e\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:33:17-117
220                android:value="com.google.firebase.components.ComponentRegistrar" />
220-->[com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\647741b0d36db01281989d0b55b41c1e\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:34:17-82
221            <meta-data
221-->[com.google.firebase:firebase-sessions:2.0.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d72e4ec5a5740afe66ba66375b2f440d\transformed\jetified-firebase-sessions-2.0.8\AndroidManifest.xml:29:13-31:85
222                android:name="com.google.firebase.components:com.google.firebase.sessions.FirebaseSessionsRegistrar"
222-->[com.google.firebase:firebase-sessions:2.0.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d72e4ec5a5740afe66ba66375b2f440d\transformed\jetified-firebase-sessions-2.0.8\AndroidManifest.xml:30:17-117
223                android:value="com.google.firebase.components.ComponentRegistrar" />
223-->[com.google.firebase:firebase-sessions:2.0.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d72e4ec5a5740afe66ba66375b2f440d\transformed\jetified-firebase-sessions-2.0.8\AndroidManifest.xml:31:17-82
224            <meta-data
224-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d824c942e0670731cb8b0e5390339c22\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
225                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
225-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d824c942e0670731cb8b0e5390339c22\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
226                android:value="com.google.firebase.components.ComponentRegistrar" />
226-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d824c942e0670731cb8b0e5390339c22\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
227            <meta-data
227-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d824c942e0670731cb8b0e5390339c22\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
228                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
228-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d824c942e0670731cb8b0e5390339c22\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
229                android:value="com.google.firebase.components.ComponentRegistrar" />
229-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d824c942e0670731cb8b0e5390339c22\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
230            <meta-data
230-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf6d9f69f61c325a990b48b77dab7869\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
231                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
231-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf6d9f69f61c325a990b48b77dab7869\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
232                android:value="com.google.firebase.components.ComponentRegistrar" />
232-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf6d9f69f61c325a990b48b77dab7869\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
233            <meta-data
233-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\42c987edc6bac2d568a7305ae38ce788\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
234                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
234-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\42c987edc6bac2d568a7305ae38ce788\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
235                android:value="com.google.firebase.components.ComponentRegistrar" />
235-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\42c987edc6bac2d568a7305ae38ce788\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
236            <meta-data
236-->[com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d23f72c165873084e0061094322da6f\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:12:13-14:85
237                android:name="com.google.firebase.components:com.google.firebase.abt.component.AbtRegistrar"
237-->[com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d23f72c165873084e0061094322da6f\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:13:17-109
238                android:value="com.google.firebase.components.ComponentRegistrar" />
238-->[com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d23f72c165873084e0061094322da6f\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:14:17-82
239            <meta-data
239-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\167f60ddd189c968da5456a2b3b7711e\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:25:13-27:85
240                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
240-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\167f60ddd189c968da5456a2b3b7711e\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:26:17-115
241                android:value="com.google.firebase.components.ComponentRegistrar" />
241-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\167f60ddd189c968da5456a2b3b7711e\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:27:17-82
242        </service> <!-- Include the AdActivity and InAppPurchaseActivity configChanges and themes. -->
243        <activity
243-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:73:9-78:43
244            android:name="com.google.android.gms.ads.AdActivity"
244-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:74:13-65
245            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
245-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:75:13-122
246            android:exported="false"
246-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:76:13-37
247            android:theme="@android:style/Theme.Translucent" />
247-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:77:13-61
248
249        <provider
249-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:80:9-85:43
250            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
250-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:81:13-76
251            android:authorities="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.mobileadsinitprovider"
251-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:82:13-73
252            android:exported="false"
252-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:83:13-37
253            android:initOrder="100" />
253-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:84:13-36
254
255        <service
255-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:87:9-91:43
256            android:name="com.google.android.gms.ads.AdService"
256-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:88:13-64
257            android:enabled="true"
257-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:89:13-35
258            android:exported="false" />
258-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:90:13-37
259
260        <activity
260-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:93:9-97:43
261            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
261-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:94:13-82
262            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
262-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:95:13-122
263            android:exported="false" />
263-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:96:13-37
264        <activity
264-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:98:9-105:43
265            android:name="com.google.android.gms.ads.NotificationHandlerActivity"
265-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:99:13-82
266            android:excludeFromRecents="true"
266-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:100:13-46
267            android:exported="false"
267-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:101:13-37
268            android:launchMode="singleTask"
268-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:102:13-44
269            android:taskAffinity=""
269-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:103:13-36
270            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
270-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:104:13-72
271
272        <meta-data
272-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:107:9-109:36
273            android:name="com.google.android.gms.ads.flag.OPTIMIZE_AD_LOADING"
273-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:108:13-79
274            android:value="true" />
274-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:109:13-33
275        <meta-data
275-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:110:9-112:36
276            android:name="com.google.android.gms.ads.flag.OPTIMIZE_INITIALIZATION"
276-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:111:13-83
277            android:value="true" />
277-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:112:13-33
278
279        <service
279-->[com.google.firebase:firebase-sessions:2.0.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d72e4ec5a5740afe66ba66375b2f440d\transformed\jetified-firebase-sessions-2.0.8\AndroidManifest.xml:22:9-25:40
280            android:name="com.google.firebase.sessions.SessionLifecycleService"
280-->[com.google.firebase:firebase-sessions:2.0.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d72e4ec5a5740afe66ba66375b2f440d\transformed\jetified-firebase-sessions-2.0.8\AndroidManifest.xml:23:13-80
281            android:enabled="true"
281-->[com.google.firebase:firebase-sessions:2.0.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d72e4ec5a5740afe66ba66375b2f440d\transformed\jetified-firebase-sessions-2.0.8\AndroidManifest.xml:24:13-35
282            android:exported="false" />
282-->[com.google.firebase:firebase-sessions:2.0.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d72e4ec5a5740afe66ba66375b2f440d\transformed\jetified-firebase-sessions-2.0.8\AndroidManifest.xml:25:13-37
283
284        <provider
284-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\42c987edc6bac2d568a7305ae38ce788\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
285            android:name="com.google.firebase.provider.FirebaseInitProvider"
285-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\42c987edc6bac2d568a7305ae38ce788\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
286            android:authorities="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.firebaseinitprovider"
286-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\42c987edc6bac2d568a7305ae38ce788\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
287            android:directBootAware="true"
287-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\42c987edc6bac2d568a7305ae38ce788\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
288            android:exported="false"
288-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\42c987edc6bac2d568a7305ae38ce788\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
289            android:initOrder="100" />
289-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\42c987edc6bac2d568a7305ae38ce788\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
290
291        <activity
291-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d56ddc8f70c1b6c4f2dfff25a6818549\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
292            android:name="com.google.android.gms.common.api.GoogleApiActivity"
292-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d56ddc8f70c1b6c4f2dfff25a6818549\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
293            android:exported="false"
293-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d56ddc8f70c1b6c4f2dfff25a6818549\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
294            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
294-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d56ddc8f70c1b6c4f2dfff25a6818549\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
295
296        <provider
296-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\04e6ea113b6b9423e1db617895bd977a\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
297            android:name="androidx.startup.InitializationProvider"
297-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\04e6ea113b6b9423e1db617895bd977a\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
298            android:authorities="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.androidx-startup"
298-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\04e6ea113b6b9423e1db617895bd977a\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
299            android:exported="false" >
299-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\04e6ea113b6b9423e1db617895bd977a\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
300            <meta-data
300-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\04e6ea113b6b9423e1db617895bd977a\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
301                android:name="androidx.emoji2.text.EmojiCompatInitializer"
301-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\04e6ea113b6b9423e1db617895bd977a\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
302                android:value="androidx.startup" />
302-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\04e6ea113b6b9423e1db617895bd977a\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
303            <meta-data
303-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:36:13-38:52
304                android:name="androidx.work.WorkManagerInitializer"
304-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:37:17-68
305                android:value="androidx.startup" />
305-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:38:17-49
306            <meta-data
306-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25d2fd9aae62b446b90a4fd8f9d80408\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
307                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
307-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25d2fd9aae62b446b90a4fd8f9d80408\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
308                android:value="androidx.startup" />
308-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25d2fd9aae62b446b90a4fd8f9d80408\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
309            <meta-data
309-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea1b0c1bbaf4c424ba812ea50539ea72\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
310                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
310-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea1b0c1bbaf4c424ba812ea50539ea72\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
311                android:value="androidx.startup" />
311-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea1b0c1bbaf4c424ba812ea50539ea72\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
312        </provider>
313
314        <service
314-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:41:9-46:35
315            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
315-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:42:13-88
316            android:directBootAware="false"
316-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:43:13-44
317            android:enabled="@bool/enable_system_alarm_service_default"
317-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:44:13-72
318            android:exported="false" />
318-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:45:13-37
319        <service
319-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:47:9-53:35
320            android:name="androidx.work.impl.background.systemjob.SystemJobService"
320-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:48:13-84
321            android:directBootAware="false"
321-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:49:13-44
322            android:enabled="@bool/enable_system_job_service_default"
322-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:50:13-70
323            android:exported="true"
323-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:51:13-36
324            android:permission="android.permission.BIND_JOB_SERVICE" />
324-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:52:13-69
325        <service
325-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:54:9-59:35
326            android:name="androidx.work.impl.foreground.SystemForegroundService"
326-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:55:13-81
327            android:directBootAware="false"
327-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:56:13-44
328            android:enabled="@bool/enable_system_foreground_service_default"
328-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:57:13-77
329            android:exported="false" />
329-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:58:13-37
330
331        <receiver
331-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:61:9-66:35
332            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
332-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:62:13-88
333            android:directBootAware="false"
333-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:63:13-44
334            android:enabled="true"
334-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:64:13-35
335            android:exported="false" />
335-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:65:13-37
336        <receiver
336-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:67:9-77:20
337            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
337-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:68:13-106
338            android:directBootAware="false"
338-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:69:13-44
339            android:enabled="false"
339-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:70:13-36
340            android:exported="false" >
340-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:71:13-37
341            <intent-filter>
341-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:73:13-76:29
342                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
342-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:17-87
342-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:25-84
343                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
343-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:17-90
343-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:25-87
344            </intent-filter>
345        </receiver>
346        <receiver
346-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:78:9-88:20
347            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
347-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:79:13-104
348            android:directBootAware="false"
348-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:80:13-44
349            android:enabled="false"
349-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:81:13-36
350            android:exported="false" >
350-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:82:13-37
351            <intent-filter>
351-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:84:13-87:29
352                <action android:name="android.intent.action.BATTERY_OKAY" />
352-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:17-77
352-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:25-74
353                <action android:name="android.intent.action.BATTERY_LOW" />
353-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:17-76
353-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:25-73
354            </intent-filter>
355        </receiver>
356        <receiver
356-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:89:9-99:20
357            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
357-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:90:13-104
358            android:directBootAware="false"
358-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:91:13-44
359            android:enabled="false"
359-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:92:13-36
360            android:exported="false" >
360-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:93:13-37
361            <intent-filter>
361-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:95:13-98:29
362                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
362-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:17-83
362-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:25-80
363                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
363-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:17-82
363-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:25-79
364            </intent-filter>
365        </receiver>
366        <receiver
366-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:100:9-109:20
367            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
367-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:101:13-103
368            android:directBootAware="false"
368-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:102:13-44
369            android:enabled="false"
369-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:103:13-36
370            android:exported="false" >
370-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:104:13-37
371            <intent-filter>
371-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:106:13-108:29
372                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
372-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:17-79
372-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:25-76
373            </intent-filter>
374        </receiver>
375        <receiver
375-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:110:9-121:20
376            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
376-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:111:13-88
377            android:directBootAware="false"
377-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:112:13-44
378            android:enabled="false"
378-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:113:13-36
379            android:exported="false" >
379-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:114:13-37
380            <intent-filter>
380-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:116:13-120:29
381                <action android:name="android.intent.action.BOOT_COMPLETED" />
381-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:17-79
381-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:25-76
382                <action android:name="android.intent.action.TIME_SET" />
382-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:17-73
382-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:25-70
383                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
383-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:17-81
383-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:25-78
384            </intent-filter>
385        </receiver>
386        <receiver
386-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:122:9-131:20
387            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
387-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:123:13-99
388            android:directBootAware="false"
388-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:124:13-44
389            android:enabled="@bool/enable_system_alarm_service_default"
389-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:125:13-72
390            android:exported="false" >
390-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:126:13-37
391            <intent-filter>
391-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:128:13-130:29
392                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
392-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:17-98
392-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:25-95
393            </intent-filter>
394        </receiver>
395        <receiver
395-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:132:9-142:20
396            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
396-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:133:13-78
397            android:directBootAware="false"
397-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:134:13-44
398            android:enabled="true"
398-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:135:13-35
399            android:exported="true"
399-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:136:13-36
400            android:permission="android.permission.DUMP" >
400-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:137:13-57
401            <intent-filter>
401-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:139:13-141:29
402                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
402-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:17-88
402-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:25-85
403            </intent-filter>
404        </receiver>
405
406        <uses-library
406-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69fa008bb70ecc0d8e73621c41f47f2d\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
407            android:name="androidx.window.extensions"
407-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69fa008bb70ecc0d8e73621c41f47f2d\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
408            android:required="false" />
408-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69fa008bb70ecc0d8e73621c41f47f2d\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
409        <uses-library
409-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69fa008bb70ecc0d8e73621c41f47f2d\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
410            android:name="androidx.window.sidecar"
410-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69fa008bb70ecc0d8e73621c41f47f2d\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
411            android:required="false" />
411-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69fa008bb70ecc0d8e73621c41f47f2d\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
412        <uses-library
412-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe8b59d760258b64cc2e08caaf757d3\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:23:9-25:40
413            android:name="android.ext.adservices"
413-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe8b59d760258b64cc2e08caaf757d3\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:24:13-50
414            android:required="false" />
414-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe8b59d760258b64cc2e08caaf757d3\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:25:13-37
415
416        <meta-data
416-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\42f95d9fa807b14415e836fc15872a54\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:21:9-23:69
417            android:name="com.google.android.gms.version"
417-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\42f95d9fa807b14415e836fc15872a54\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:22:13-58
418            android:value="@integer/google_play_services_version" />
418-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\42f95d9fa807b14415e836fc15872a54\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:23:13-66
419
420        <service
420-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29198f9de8a02a6dbf7704c477a13803\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:26:9-32:19
421            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
421-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29198f9de8a02a6dbf7704c477a13803\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:27:13-103
422            android:exported="false" >
422-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29198f9de8a02a6dbf7704c477a13803\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:28:13-37
423            <meta-data
423-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29198f9de8a02a6dbf7704c477a13803\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:29:13-31:39
424                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
424-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29198f9de8a02a6dbf7704c477a13803\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:30:17-94
425                android:value="cct" />
425-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29198f9de8a02a6dbf7704c477a13803\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:31:17-36
426        </service>
427
428        <provider
428-->[com.squareup.picasso:picasso:2.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3db596f3ce001dc72c702061cbd417cf\transformed\jetified-picasso-2.8\AndroidManifest.xml:8:9-11:40
429            android:name="com.squareup.picasso.PicassoProvider"
429-->[com.squareup.picasso:picasso:2.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3db596f3ce001dc72c702061cbd417cf\transformed\jetified-picasso-2.8\AndroidManifest.xml:9:13-64
430            android:authorities="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.com.squareup.picasso"
430-->[com.squareup.picasso:picasso:2.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3db596f3ce001dc72c702061cbd417cf\transformed\jetified-picasso-2.8\AndroidManifest.xml:10:13-72
431            android:exported="false" />
431-->[com.squareup.picasso:picasso:2.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3db596f3ce001dc72c702061cbd417cf\transformed\jetified-picasso-2.8\AndroidManifest.xml:11:13-37
432
433        <receiver
433-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea1b0c1bbaf4c424ba812ea50539ea72\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
434            android:name="androidx.profileinstaller.ProfileInstallReceiver"
434-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea1b0c1bbaf4c424ba812ea50539ea72\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
435            android:directBootAware="false"
435-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea1b0c1bbaf4c424ba812ea50539ea72\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
436            android:enabled="true"
436-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea1b0c1bbaf4c424ba812ea50539ea72\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
437            android:exported="true"
437-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea1b0c1bbaf4c424ba812ea50539ea72\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
438            android:permission="android.permission.DUMP" >
438-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea1b0c1bbaf4c424ba812ea50539ea72\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
439            <intent-filter>
439-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea1b0c1bbaf4c424ba812ea50539ea72\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
440                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
440-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea1b0c1bbaf4c424ba812ea50539ea72\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
440-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea1b0c1bbaf4c424ba812ea50539ea72\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
441            </intent-filter>
442            <intent-filter>
442-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea1b0c1bbaf4c424ba812ea50539ea72\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
443                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
443-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea1b0c1bbaf4c424ba812ea50539ea72\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
443-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea1b0c1bbaf4c424ba812ea50539ea72\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
444            </intent-filter>
445            <intent-filter>
445-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea1b0c1bbaf4c424ba812ea50539ea72\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
446                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
446-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea1b0c1bbaf4c424ba812ea50539ea72\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
446-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea1b0c1bbaf4c424ba812ea50539ea72\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
447            </intent-filter>
448            <intent-filter>
448-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea1b0c1bbaf4c424ba812ea50539ea72\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
449                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
449-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea1b0c1bbaf4c424ba812ea50539ea72\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
449-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea1b0c1bbaf4c424ba812ea50539ea72\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
450            </intent-filter>
451        </receiver>
452
453        <service
453-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fa1b4c3d3ee8d16b849865caadfb65e\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:24:9-28:19
454            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
454-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fa1b4c3d3ee8d16b849865caadfb65e\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:25:13-117
455            android:exported="false"
455-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fa1b4c3d3ee8d16b849865caadfb65e\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:26:13-37
456            android:permission="android.permission.BIND_JOB_SERVICE" >
456-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fa1b4c3d3ee8d16b849865caadfb65e\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:27:13-69
457        </service>
458
459        <receiver
459-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fa1b4c3d3ee8d16b849865caadfb65e\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:30:9-32:40
460            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
460-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fa1b4c3d3ee8d16b849865caadfb65e\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:31:13-132
461            android:exported="false" />
461-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fa1b4c3d3ee8d16b849865caadfb65e\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:32:13-37
462
463        <service
463-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4344bc802b6747759d9e037220bf761a\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
464            android:name="androidx.room.MultiInstanceInvalidationService"
464-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4344bc802b6747759d9e037220bf761a\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
465            android:directBootAware="true"
465-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4344bc802b6747759d9e037220bf761a\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
466            android:exported="false" />
466-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4344bc802b6747759d9e037220bf761a\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
467
468        <provider
468-->[com.adjust.sdk:adjust-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\efac1c2d606ea39a514ba9d42b351722\transformed\jetified-adjust-android-5.1.0\AndroidManifest.xml:11:9-14:40
469            android:name="com.adjust.sdk.SystemLifecycleContentProvider"
469-->[com.adjust.sdk:adjust-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\efac1c2d606ea39a514ba9d42b351722\transformed\jetified-adjust-android-5.1.0\AndroidManifest.xml:12:13-73
470            android:authorities="com.fc.p.tj.charginganimation.batterycharging.chargeeffect.adjust-lifecycle-provider"
470-->[com.adjust.sdk:adjust-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\efac1c2d606ea39a514ba9d42b351722\transformed\jetified-adjust-android-5.1.0\AndroidManifest.xml:13:13-77
471            android:exported="false" />
471-->[com.adjust.sdk:adjust-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\efac1c2d606ea39a514ba9d42b351722\transformed\jetified-adjust-android-5.1.0\AndroidManifest.xml:14:13-37
472    </application>
473
474</manifest>
