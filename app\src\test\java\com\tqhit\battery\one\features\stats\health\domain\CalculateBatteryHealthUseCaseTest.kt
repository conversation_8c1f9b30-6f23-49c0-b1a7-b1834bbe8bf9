package com.tqhit.battery.one.features.stats.health.domain

import com.tqhit.battery.one.features.stats.health.data.HealthCalculationMode
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test

/**
 * Unit tests for CalculateBatteryHealthUseCase.
 * Tests the health calculation algorithm specified in the PRD.
 */
class CalculateBatteryHealthUseCaseTest {
    
    private lateinit var calculateBatteryHealthUseCase: CalculateBatteryHealthUseCase
    
    @Before
    fun setUp() {
        calculateBatteryHealthUseCase = CalculateBatteryHealthUseCase()
    }
    
    @Test
    fun `calculateCumulativeHealth with zero sessions returns 100 percent`() {
        // Arrange
        val totalSessions = 0
        
        // Act
        val result = calculateBatteryHealthUseCase.calculateCumulativeHealth(totalSessions)
        
        // Assert
        assertEquals(100, result)
    }
    
    @Test
    fun `calculateCumulativeHealth with 250 sessions returns 60 percent`() {
        // Arrange
        val totalSessions = 250 // Half of 500 sessions
        
        // Act
        val result = calculateBatteryHealthUseCase.calculateCumulativeHealth(totalSessions)
        
        // Assert
        // Formula: 100 - (250 / 500 * 80) = 100 - 40 = 60
        assertEquals(60, result)
    }
    
    @Test
    fun `calculateCumulativeHealth with 500 sessions returns 20 percent`() {
        // Arrange
        val totalSessions = 500 // Maximum sessions for full degradation
        
        // Act
        val result = calculateBatteryHealthUseCase.calculateCumulativeHealth(totalSessions)
        
        // Assert
        // Formula: 100 - (500 / 500 * 80) = 100 - 80 = 20
        assertEquals(20, result)
    }
    
    @Test
    fun `calculateCumulativeHealth with 1000 sessions returns 0 percent (clamped)`() {
        // Arrange
        val totalSessions = 1000 // More than maximum sessions
        
        // Act
        val result = calculateBatteryHealthUseCase.calculateCumulativeHealth(totalSessions)
        
        // Assert
        // Formula would give negative value, but should be clamped to 0
        assertEquals(0, result)
    }
    
    @Test
    fun `calculateSingularHealth always returns 0 percent (no data)`() {
        // Arrange
        val fullChargeCycles = 100
        
        // Act
        val result = calculateBatteryHealthUseCase.calculateSingularHealth(fullChargeCycles)
        
        // Assert
        // As specified in PRD, singular mode returns 0 to show "no data"
        assertEquals(0, result)
    }
    
    @Test
    fun `calculateEffectiveCapacity with 100 percent health returns full capacity`() {
        // Arrange
        val designCapacityMah = 3000
        val healthPercentage = 100
        
        // Act
        val result = calculateBatteryHealthUseCase.calculateEffectiveCapacity(designCapacityMah, healthPercentage)
        
        // Assert
        assertEquals(3000, result)
    }
    
    @Test
    fun `calculateEffectiveCapacity with 80 percent health returns 80 percent of capacity`() {
        // Arrange
        val designCapacityMah = 3000
        val healthPercentage = 80
        
        // Act
        val result = calculateBatteryHealthUseCase.calculateEffectiveCapacity(designCapacityMah, healthPercentage)
        
        // Assert
        // 3000 * 80 / 100 = 2400
        assertEquals(2400, result)
    }
    
    @Test
    fun `calculateEffectiveCapacity with 0 percent health returns 0 capacity`() {
        // Arrange
        val designCapacityMah = 3000
        val healthPercentage = 0
        
        // Act
        val result = calculateBatteryHealthUseCase.calculateEffectiveCapacity(designCapacityMah, healthPercentage)
        
        // Assert
        assertEquals(0, result)
    }
    
    @Test
    fun `calculateHealthStatus with cumulative mode returns correct health status`() {
        // Arrange
        val totalSessions = 100
        val designCapacityMah = 4000
        val calculationMode = HealthCalculationMode.CUMULATIVE
        
        // Act
        val result = calculateBatteryHealthUseCase.calculateHealthStatus(
            totalSessions, designCapacityMah, calculationMode
        )
        
        // Assert
        assertEquals(84, result.healthPercentage) // 100 - (100/500*80) = 84
        assertEquals(100, result.totalSessions)
        assertEquals(4000, result.designCapacityMah)
        assertEquals(3360, result.effectiveCapacityMah) // 4000 * 84 / 100
        assertEquals(HealthCalculationMode.CUMULATIVE, result.calculationMode)
        assertTrue(result.isValid())
    }
    
    @Test
    fun `calculateHealthStatus with singular mode returns zero health`() {
        // Arrange
        val totalSessions = 100
        val designCapacityMah = 4000
        val calculationMode = HealthCalculationMode.SINGULAR
        
        // Act
        val result = calculateBatteryHealthUseCase.calculateHealthStatus(
            totalSessions, designCapacityMah, calculationMode
        )
        
        // Assert
        assertEquals(0, result.healthPercentage) // Singular mode returns 0 as per PRD
        assertEquals(100, result.totalSessions)
        assertEquals(4000, result.designCapacityMah)
        assertEquals(0, result.effectiveCapacityMah) // 4000 * 0 / 100
        assertEquals(HealthCalculationMode.SINGULAR, result.calculationMode)
        assertTrue(result.isValid())
    }
    
    @Test
    fun `isFullChargeCycle with valid cycle returns true`() {
        // Arrange
        val startPercentage = 10
        val endPercentage = 100
        
        // Act
        val result = calculateBatteryHealthUseCase.isFullChargeCycle(startPercentage, endPercentage)
        
        // Assert
        assertTrue(result)
    }
    
    @Test
    fun `isFullChargeCycle with invalid start percentage returns false`() {
        // Arrange
        val startPercentage = 20 // Above 15% threshold
        val endPercentage = 100
        
        // Act
        val result = calculateBatteryHealthUseCase.isFullChargeCycle(startPercentage, endPercentage)
        
        // Assert
        assertFalse(result)
    }
    
    @Test
    fun `isFullChargeCycle with invalid end percentage returns false`() {
        // Arrange
        val startPercentage = 10
        val endPercentage = 90 // Below 100% threshold
        
        // Act
        val result = calculateBatteryHealthUseCase.isFullChargeCycle(startPercentage, endPercentage)
        
        // Assert
        assertFalse(result)
    }
    
    @Test
    fun `estimateRemainingLifespan with valid parameters returns reasonable estimate`() {
        // Arrange
        val currentHealthPercentage = 80
        val averageSessionsPerMonth = 30.0
        
        // Act
        val result = calculateBatteryHealthUseCase.estimateRemainingLifespan(
            currentHealthPercentage, averageSessionsPerMonth
        )
        
        // Assert
        assertTrue("Remaining lifespan should be positive", result > 0)
        assertTrue("Remaining lifespan should be reasonable", result < 100) // Less than 100 months
    }
    
    @Test
    fun `estimateRemainingLifespan with low health returns zero`() {
        // Arrange
        val currentHealthPercentage = 15 // Below 20% threshold
        val averageSessionsPerMonth = 30.0
        
        // Act
        val result = calculateBatteryHealthUseCase.estimateRemainingLifespan(
            currentHealthPercentage, averageSessionsPerMonth
        )
        
        // Assert
        assertEquals(0, result)
    }
    
    @Test
    fun `calculateDegradationRate with valid parameters returns correct rate`() {
        // Arrange
        val totalSessions = 100
        val currentHealthPercentage = 90
        
        // Act
        val result = calculateBatteryHealthUseCase.calculateDegradationRate(
            totalSessions, currentHealthPercentage
        )
        
        // Assert
        // Total degradation = 100 - 90 = 10%
        // Rate = 10% / 100 sessions = 0.1% per session
        assertEquals(0.1, result, 0.001)
    }
    
    @Test
    fun `calculateDegradationRate with zero sessions returns zero`() {
        // Arrange
        val totalSessions = 0
        val currentHealthPercentage = 100
        
        // Act
        val result = calculateBatteryHealthUseCase.calculateDegradationRate(
            totalSessions, currentHealthPercentage
        )
        
        // Assert
        assertEquals(0.0, result, 0.001)
    }
}
