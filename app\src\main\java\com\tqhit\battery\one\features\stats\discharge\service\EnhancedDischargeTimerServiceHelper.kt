package com.tqhit.battery.one.features.stats.discharge.service

import android.app.ActivityManager
import android.content.Context
import android.content.Intent
import android.os.Build
import android.util.Log
import androidx.core.content.ContextCompat
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Helper class for managing the EnhancedDischargeTimerService
 * Follows the CoreBatteryStatsService architecture pattern
 */
@Singleton
class EnhancedDischargeTimerServiceHelper @Inject constructor(
    @ApplicationContext private val context: Context
) {
    companion object {
        private const val TAG = "EnhancedDischargeHelper"
    }
    
    /**
     * Checks if the EnhancedDischargeTimerService is currently running
     */
    fun isServiceRunning(): Boolean {
        val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val isRunning = activityManager.getRunningServices(Integer.MAX_VALUE)
            .any { it.service.className == EnhancedDischargeTimerService::class.java.name }
        
        Log.d(TAG, "isServiceRunning: $isRunning")
        return isRunning
    }
    
    /**
     * Starts the EnhancedDischargeTimerService with improved reliability
     */
    fun startService() {
        Log.d(TAG, "Starting EnhancedDischargeTimerService")
        val serviceIntent = Intent(context, EnhancedDischargeTimerService::class.java).apply {
            action = EnhancedDischargeTimerService.ACTION_START_SERVICE
        }
        
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                Log.d(TAG, "Starting as foreground service (Android O+)")
                ContextCompat.startForegroundService(context, serviceIntent)
            } else {
                Log.d(TAG, "Starting as background service (pre-Android O)")
                context.startService(serviceIntent)
            }
            Log.d(TAG, "EnhancedDischargeTimerService started successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to start EnhancedDischargeTimerService", e)
        }
    }
    
    /**
     * Stops the EnhancedDischargeTimerService
     */
    fun stopService() {
        Log.d(TAG, "Stopping EnhancedDischargeTimerService")
        val serviceIntent = Intent(context, EnhancedDischargeTimerService::class.java).apply {
            action = EnhancedDischargeTimerService.ACTION_STOP_SERVICE
        }
        
        try {
            context.startService(serviceIntent)
            Log.d(TAG, "EnhancedDischargeTimerService stop command sent")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to stop EnhancedDischargeTimerService", e)
            
            // Fallback: try direct stop
            try {
                val fallbackIntent = Intent(context, EnhancedDischargeTimerService::class.java)
                context.stopService(fallbackIntent)
                Log.d(TAG, "EnhancedDischargeTimerService stopped via fallback method")
            } catch (fallbackException: Exception) {
                Log.e(TAG, "Fallback stop also failed", fallbackException)
            }
        }
    }
    
    /**
     * Restart the service (stop and start)
     */
    fun restartService() {
        Log.d(TAG, "Restarting EnhancedDischargeTimerService")
        stopService()
        
        // Give some time for the service to stop
        Thread.sleep(1000)
        
        startService()
    }
    
    /**
     * Get service status information for debugging
     */
    fun getServiceStatus(): String {
        val isRunning = isServiceRunning()
        return "EnhancedDischargeTimerService: ${if (isRunning) "RUNNING" else "STOPPED"}"
    }
}
