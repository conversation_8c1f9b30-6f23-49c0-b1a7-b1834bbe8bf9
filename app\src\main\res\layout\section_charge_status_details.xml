<?xml version="1.0" encoding="utf-8"?>
<!-- 
 * Charging status details section
 * This section contains:
 * - Section title
 * - Rows for voltage, power, amperage, temperature
 * - Current charging speed
 */
-->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/charge_status_details_root"
    android:orientation="vertical"
    android:background="@drawable/white_block"
    android:padding="8dp"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="9dp"
    android:layout_marginEnd="9dp"
    android:layout_marginBottom="14dp">

    <!-- Section Title -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/charging_status"
        android:textColor="?attr/black"
        android:textSize="19sp"
        android:layout_marginBottom="8dp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="?android:attr/listDivider"
        android:layout_marginBottom="8dp" />

    <!-- Voltage row -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="6dp">
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/voltage_label"
            android:textColor="?attr/black"
            android:textSize="14sp" />
        <TextView
            android:id="@+id/val_voltage"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="-"
            android:textColor="?attr/colorr"
            android:textSize="14sp" />
    </LinearLayout>

    <!-- Power row -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="6dp">
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/power_label"
            android:textColor="?attr/black"
            android:textSize="14sp" />
        <TextView
            android:id="@+id/val_power"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="-"
            android:textColor="?attr/colorr"
            android:textSize="14sp" />
    </LinearLayout>

    <!-- Amperage row -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="6dp">
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/amperage_label"
            android:textColor="?attr/black"
            android:textSize="14sp" />
        <TextView
            android:id="@+id/val_amperage"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="-"
            android:textColor="?attr/colorr"
            android:textSize="14sp" />
    </LinearLayout>

    <!-- Temperature row -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="6dp">
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/temperature_label"
            android:textColor="?attr/black"
            android:textSize="14sp" />
        <TextView
            android:id="@+id/val_temperature"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="-"
            android:textColor="?attr/colorr"
            android:textSize="14sp" />
    </LinearLayout>

    <!-- Current charging speed row -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/current_charging_speed"
            android:textColor="?attr/black"
            android:textSize="14sp" />
        <TextView
            android:id="@+id/val_charging_speed"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="-"
            android:textColor="?attr/colorr"
            android:textSize="14sp" />
    </LinearLayout>
</LinearLayout>