package com.tqhit.battery.one.dialog.theme

import android.app.Activity
import android.graphics.Color
import android.view.ViewGroup
import android.view.WindowManager
import androidx.core.graphics.drawable.toDrawable
import com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog
import com.tqhit.battery.one.databinding.DialogSelectColorThemeBinding
import com.tqhit.battery.one.manager.theme.ThemeManager

class SelectColorDialog(
    private val activity: Activity,
) : AdLibBaseDialog<DialogSelectColorThemeBinding>(activity) {
    override val binding by lazy { DialogSelectColorThemeBinding.inflate(layoutInflater) }

    override fun initWindow() {
        window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        window?.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
        val layoutParams = WindowManager.LayoutParams()
        layoutParams.copyFrom(window?.attributes)
        window?.attributes = layoutParams

        setCanceledOnTouchOutside(false)
    }

    override fun setupListener() {
        super.setupListener()
        setupColorButtons()
        setupCloseButton()
    }

    private fun setupColorButtons() {
        // First row colors
        binding.blueBtn.setOnClickListener { saveColor("blue") }
        binding.greenBtn.setOnClickListener { saveColor("green") }
        binding.orangeBtn.setOnClickListener { saveColor("orange") }
        binding.lightBlueBtn.setOnClickListener { saveColor("light_blue") }
        binding.redBtn.setOnClickListener { saveColor("red") }

        // Second row colors
        binding.pinkBtn.setOnClickListener { saveColor("pink") }
        binding.lightGreenBtn.setOnClickListener { saveColor("light_green") }
        binding.teloBtn.setOnClickListener { saveColor("telo") }
        binding.goldBtn.setOnClickListener { saveColor("gold") }
        binding.nightBlueBtn.setOnClickListener { saveColor("night_blue") }

        // Third row colors
        binding.colorBtn1.setOnClickListener { saveColor("color1") }
        binding.colorBtn2.setOnClickListener { saveColor("color2") }
        binding.colorBtn3.setOnClickListener { saveColor("color3") }
        binding.colorBtn4.setOnClickListener { saveColor("color4") }
        binding.colorBtn5.setOnClickListener { saveColor("color5") }

        // Fourth row colors
        binding.colorBtn6.setOnClickListener { saveColor("color6") }
        binding.colorBtn7.setOnClickListener { saveColor("color7") }
        binding.colorBtn8.setOnClickListener { saveColor("color8") }
        binding.colorBtn9.setOnClickListener { saveColor("color9") }
        binding.colorBtn10.setOnClickListener { saveColor("color10") }
    }

    private fun setupCloseButton() {
        binding.exitColor.setOnClickListener {
            dismiss()
        }
    }

    private fun saveColor(colorName: String) {
        ThemeManager.saveColor(colorName)
        ThemeManager.applyTheme(activity)
        dismiss()
        activity.recreate()
    }
}