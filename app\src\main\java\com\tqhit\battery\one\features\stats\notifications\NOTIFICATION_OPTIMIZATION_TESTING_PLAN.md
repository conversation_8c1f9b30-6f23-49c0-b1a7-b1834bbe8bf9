# TJ_BatteryOne Notification Service Optimization Testing Plan

## Overview
This document outlines the comprehensive testing strategy for the optimized notification service implementation in TJ_BatteryOne. The testing focuses on verifying the enhanced performance, battery efficiency, and user experience improvements.

## 1. Automated Unit Testing

### ChargeNotificationManager Tests
**Location**: `app/src/test/java/com/tqhit/battery/one/features/legacy/charge/common/ChargeNotificationManagerTest.kt`

**Test Coverage**:
- ✅ Notification content caching optimization
- ✅ Power calculation accuracy (Power = Voltage × Current / 1,000,000)
- ✅ Charging indicator display (⚡ symbol)
- ✅ Content formatting for edge cases (zero values)
- ✅ User preference respect (vibration, notification settings)

**Run Command**:
```bash
./gradlew testDebugUnitTest --tests="*ChargeNotificationManagerTest*"
```

### UnifiedBatteryNotificationService Tests
**Location**: `app/src/test/java/com/tqhit/battery/one/features/stats/notifications/UnifiedBatteryNotificationServiceTest.kt`

**Test Coverage**:
- ✅ Service lifecycle management
- ✅ Dependency injection verification
- ✅ Battery status flow processing
- ✅ Resource cleanup on destroy

**Run Command**:
```bash
./gradlew testDebugUnitTest --tests="*UnifiedBatteryNotificationServiceTest*"
```

## 2. ADB Testing Strategy

### Prerequisites
- ADB Path: `E:\IDE\Android\SDK\platform-tools\adb.exe`
- Device with TJ_BatteryOne installed
- Bundle ID: `com.fc.p.tj.charginganimation.batterycharging.chargeeffect`

### 2.1 Service Consolidation Verification

**Verify Only CoreBatteryStatsService Runs**:
```bash
E:\IDE\Android\SDK\platform-tools\adb.exe shell dumpsys activity services | findstr "BatteryStatsService\|BatteryMonitorService\|ChargeMonitorService"
```

**Expected Output**:
- ✅ `CoreBatteryStatsService` should be running
- ❌ `BatteryStatusService` should NOT be running
- ❌ `BatteryMonitorService` should NOT be running  
- ❌ `NewChargeMonitorService` should NOT be running

### 2.2 Notification Display Testing

**Monitor Notification Updates**:
```bash
E:\IDE\Android\SDK\platform-tools\adb.exe logcat -s "UnifiedBatteryNotificationService:D" "ChargeNotificationManager:D"
```

**Test Scenarios**:
1. **Charging State Changes**:
   - Connect charger → Verify "charging started" notification
   - Disconnect charger → Verify "charging stopped" notification

2. **Target Percentage Alerts**:
   - Set target percentage in app (e.g., 80%)
   - Monitor charging until target reached
   - Verify target alert notification appears

3. **Full Charge Notification**:
   - Charge device to 100%
   - Verify full charge notification appears

### 2.3 Adaptive Update Frequency Testing

**Monitor Update Intervals**:
```bash
E:\IDE\Android\SDK\platform-tools\adb.exe logcat -s "UnifiedBatteryNotificationService:D" | findstr "Updating service notification"
```

**Expected Behavior**:
- **Charging**: Updates every ~15 seconds
- **Screen Off**: Updates every ~60 seconds  
- **Stable State**: Updates every ~45 seconds
- **Significant Changes**: Immediate updates regardless of interval

### 2.4 Battery Efficiency Verification

**Monitor CPU and Battery Usage**:
```bash
# Check service CPU usage
E:\IDE\Android\SDK\platform-tools\adb.exe shell top -n 1 | findstr "com.fc.p.tj.charginganimation"

# Monitor battery drain during testing
E:\IDE\Android\SDK\platform-tools\adb.exe shell dumpsys battery
```

**Performance Metrics**:
- CPU usage should be minimal (<1% during normal operation)
- Memory usage should remain stable
- No memory leaks during extended operation

## 3. Manual Testing Scenarios

### 3.1 Notification Content Accuracy

**Test Steps**:
1. Connect charger and observe notification content
2. Verify displayed metrics match actual battery status:
   - Current (mA) - should match charging rate
   - Power (W) - should be calculated correctly
   - Temperature (°C) - should reflect actual battery temperature
   - Charging indicator (⚡) - should appear when charging

### 3.2 User Experience Testing

**Notification Responsiveness**:
1. Change charging state rapidly (connect/disconnect)
2. Verify notifications appear promptly
3. Check notification content updates correctly

**Settings Integration**:
1. Disable charge notifications in settings
2. Verify charging started/stopped notifications don't appear
3. Enable vibration and verify target alerts vibrate

### 3.3 Edge Case Testing

**Low Battery Scenarios**:
1. Test notifications at very low battery levels (1-5%)
2. Verify service continues operating correctly

**High Temperature Scenarios**:
1. Monitor notifications during device heating
2. Verify temperature readings are accurate

**Rapid State Changes**:
1. Rapidly connect/disconnect charger multiple times
2. Verify service handles state changes gracefully

## 4. Performance Benchmarking

### 4.1 Startup Time Measurement

**Command**:
```bash
E:\IDE\Android\SDK\platform-tools\adb.exe shell am start -W com.fc.p.tj.charginganimation.batterycharging.chargeeffect/.activity.main.MainActivity
```

**Metrics to Track**:
- Total startup time
- Service initialization time
- First notification display time

### 4.2 Memory Usage Monitoring

**Command**:
```bash
E:\IDE\Android\SDK\platform-tools\adb.exe shell dumpsys meminfo com.fc.p.tj.charginganimation.batterycharging.chargeeffect
```

**Monitor Over Time**:
- Initial memory usage
- Memory usage after 1 hour of operation
- Memory usage after 24 hours of operation
- Check for memory leaks

### 4.3 Battery Impact Assessment

**Before/After Comparison**:
1. Measure battery drain with legacy services
2. Measure battery drain with optimized service
3. Calculate improvement percentage

## 5. Regression Testing

### 5.1 Existing Functionality Verification

**Core Features**:
- ✅ Battery percentage monitoring
- ✅ Charging rate calculation
- ✅ Temperature monitoring
- ✅ Anti-theft functionality
- ✅ Target percentage alerts
- ✅ Full charge notifications

### 5.2 UI/UX Consistency

**Notification Appearance**:
- Verify notification icons are correct
- Check notification text formatting
- Ensure notification actions work properly

## 6. Success Criteria

### Performance Improvements
- [ ] 50%+ reduction in notification update CPU usage
- [ ] 30%+ reduction in memory usage compared to legacy implementation
- [ ] No increase in battery drain from notification service

### Functionality Requirements
- [ ] All existing notification types work correctly
- [ ] Target percentage alerts trigger accurately
- [ ] Charging state changes are detected promptly
- [ ] Anti-theft functionality operates as expected

### Code Quality
- [ ] Unit test coverage >80% for modified components
- [ ] No new lint warnings or errors
- [ ] All ADB verification tests pass

## 7. Test Execution Schedule

1. **Unit Tests**: Run automatically on each build
2. **ADB Integration Tests**: Run daily during development
3. **Manual Testing**: Run before each release
4. **Performance Benchmarking**: Run weekly
5. **Regression Testing**: Run before major releases

## 8. Issue Reporting

**Log Collection Command**:
```bash
E:\IDE\Android\SDK\platform-tools\adb.exe logcat -d > battery_notification_logs.txt
```

**Required Information for Bug Reports**:
- Device model and Android version
- App version and build number
- Steps to reproduce
- Expected vs actual behavior
- Relevant log excerpts
- Performance metrics if applicable

## 9. Rollback Plan

If critical issues are discovered:
1. Revert to legacy service implementation
2. Disable UnifiedBatteryNotificationService
3. Re-enable NewChargeMonitorService temporarily
4. Investigate and fix issues in development branch
5. Re-test thoroughly before re-deployment
