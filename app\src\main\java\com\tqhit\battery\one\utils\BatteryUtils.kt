package com.tqhit.battery.one.utils

import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.BatteryManager
import android.os.PowerManager
import java.text.SimpleDateFormat
import kotlin.math.exp

object BatteryUtils {
    fun isCharging(context: Context): Boolean {
        val batteryStatus =
                context.registerReceiver(null, IntentFilter(Intent.ACTION_BATTERY_CHANGED))
        val status = batteryStatus?.getIntExtra(BatteryManager.EXTRA_STATUS, -1) ?: -1
        return status == BatteryManager.BATTERY_STATUS_CHARGING ||
                status == BatteryManager.BATTERY_STATUS_FULL
    }

    fun getBatteryCapacity(context: Context): Int {
        return try {
            val powerProfileClass = Class.forName("com.android.internal.os.PowerProfile")
            val powerProfile =
                    powerProfileClass.getConstructor(Context::class.java).newInstance(context)
            val batteryCapacity =
                    powerProfileClass.getMethod("getBatteryCapacity").invoke(powerProfile) as Double
            batteryCapacity.toInt()
        } catch (e: Exception) {
            0
        }
    }

    fun isIgnoringBatteryOptimizations(context: Context): Boolean {
        val powerManager = context.getSystemService(Context.POWER_SERVICE) as PowerManager
        return powerManager.isIgnoringBatteryOptimizations(context.packageName)
    }

    fun getBatteryPolarity(context: Context): String {
        val batteryStatus =
                context.registerReceiver(null, IntentFilter(Intent.ACTION_BATTERY_CHANGED))
        val health = batteryStatus?.getIntExtra(BatteryManager.EXTRA_HEALTH, -1) ?: -1

        return when (health) {
            BatteryManager.BATTERY_HEALTH_GOOD,
            BatteryManager.BATTERY_HEALTH_OVERHEAT,
            BatteryManager.BATTERY_HEALTH_DEAD,
            BatteryManager.BATTERY_HEALTH_OVER_VOLTAGE,
            BatteryManager.BATTERY_HEALTH_UNSPECIFIED_FAILURE,
            BatteryManager.BATTERY_HEALTH_COLD -> "Positive"
            else -> "Not identified"
        }
    }

    fun calculateWearCycles(currentPercent: Int, selectedPercent: Int): Double {
        if (currentPercent <= 0 ||
                        selectedPercent < 20 ||
                        selectedPercent > 100 ||
                        currentPercent > 100 ||
                        selectedPercent <= currentPercent
        ) {
            return 0.0
        }

        return ((exp((selectedPercent / 100.0) * 3.5 - 3.5) -
                        exp((currentPercent / 100.0) * 3.5 - 3.5)) * 100.0)
                .toInt() / 100.0
    }

    /**
     * Formats time in milliseconds to "0h 00m" format, "00m" if less than 1 hour, or "Xm" if less
     * than 10 minutes
     * @param timeMs Time in milliseconds
     * @return Formatted string in "0h 00m", "00m", or "Xm" format
     */
    fun formatTimeToHoursMinutesFromMillis(timeMs: Long): String {
        if (timeMs <= 0) return "0m"

        val seconds = timeMs / 1000
        val minutes = seconds / 60
        val hours = minutes / 60
        val remainingMinutes = minutes % 60

        return when {
            hours > 0 -> String.format("%dh %02dm", hours, remainingMinutes)
            minutes >= 10 -> String.format("%02dm", minutes)
            else -> "${minutes}m"
        }
    }

    /**
     * Formats time in seconds to "00h 00m" format, "00m" if less than 1 hour, or "Xm" if less than
     * 10 minutes
     * @param timeSeconds Time in seconds
     * @return Formatted string in "00h 00m", "00m", or "Xm" format
     */
    fun formatTimeToHoursMinutesFromSeconds(timeSeconds: Long): String {
        return formatTimeToHoursMinutesFromMillis(timeSeconds * 1000)
    }

    /**
     * Converts milliseconds to total minutes
     * @param timeMs Time in milliseconds
     * @return Total number of minutes (including hours converted to minutes)
     */
    fun getTotalMinutesFromMillis(timeMs: Long): Long {
        if (timeMs <= 0) return 0
        return timeMs / (1000 * 60)
    }

    /** Formats elapsed time in ms to: xxs, xm xxs, xxm xxs, xh xxm, xxh xxm, xd xxh, xxd xxh */
    fun formatElapsedTime(timeMs: Long): String {
        if (timeMs < 0) return "0s"
        val seconds = (timeMs / 1000) % 60
        val minutes = (timeMs / (1000 * 60)) % 60
        val hours = (timeMs / (1000 * 60 * 60)) % 24
        val days = (timeMs / (1000 * 60 * 60 * 24))
        return when {
            days > 0 -> String.format("%dd %02dh", days, hours)
            hours > 0 -> String.format("%dh %02dm", hours, minutes)
            minutes > 0 -> String.format("%dm %02ds", minutes, seconds)
            else -> String.format("%ds", seconds)
        }
    }

    /**
     * Formats session time range as: from hh:mm to hh:mm (if endTime != null), or from hh:mm (if
     * running) Always in 24-hour format
     */
    fun formatSessionTimeRange(startTime: Long, endTime: Long): Pair<String?, String?> {
        if (startTime <= 0 && endTime <= 0) return Pair(null, null)
        val start = SimpleDateFormat("HH:mm").format(java.util.Date(startTime))
        return if (endTime > 0) {
            val end = SimpleDateFormat("HH:mm").format(java.util.Date(endTime))
            Pair(start, end)
        } else {
            Pair(start, null)
        }
    }
}
