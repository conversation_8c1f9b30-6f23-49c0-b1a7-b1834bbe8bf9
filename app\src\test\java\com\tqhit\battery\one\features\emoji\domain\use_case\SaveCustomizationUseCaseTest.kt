package com.tqhit.battery.one.features.emoji.domain.use_case

import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfig
import com.tqhit.battery.one.features.emoji.domain.model.CustomizationConfig
import com.tqhit.battery.one.features.emoji.domain.repository.BatteryStyleRepository
import com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.runTest
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test

/**
 * Unit tests for SaveCustomizationUseCase.
 * 
 * Tests the business logic for saving user customization configurations
 * including validation, style verification, and error handling.
 */
class SaveCustomizationUseCaseTest {
    
    private lateinit var saveCustomizationUseCase: SaveCustomizationUseCase
    private lateinit var mockCustomizationRepository: CustomizationRepository
    private lateinit var mockBatteryStyleRepository: BatteryStyleRepository
    
    // Test data
    private val testStyleConfig = BatteryStyleConfig(
        showEmoji = true,
        showPercentage = true,
        percentageFontSizeDp = 14,
        emojiSizeScale = 1.0f,
        percentageColor = 0xFFFFFFFF.toInt()
    )
    
    private val testBatteryStyle = BatteryStyle(
        id = "test_style_1",
        name = "Test Style",
        category = BatteryStyleCategory.CUTE,
        batteryImageUrl = "https://example.com/battery.png",
        emojiImageUrl = "https://example.com/emoji.png",
        isPremium = false,
        isPopular = true,
        defaultConfig = testStyleConfig
    )
    
    private val testCustomizationConfig = CustomizationConfig(
        selectedStyleId = "test_style_1",
        selectedBatteryImageUrl = "https://example.com/battery.png",
        selectedEmojiImageUrl = "https://example.com/emoji.png",
        styleConfig = testStyleConfig,
        isFeatureEnabled = false
    )
    
    @Before
    fun setUp() {
        mockCustomizationRepository = mockk()
        mockBatteryStyleRepository = mockk()
        
        saveCustomizationUseCase = SaveCustomizationUseCase(
            customizationRepository = mockCustomizationRepository,
            batteryStyleRepository = mockBatteryStyleRepository
        )
    }
    
    @Test
    fun `saveCustomizationConfig should save valid configuration successfully`() = runTest {
        // Given
        val expectedConfig = testCustomizationConfig
        coEvery { mockBatteryStyleRepository.batteryStylesFlow } returns flowOf(listOf(testBatteryStyle))
        coEvery { mockCustomizationRepository.saveCustomizationConfig(any()) } returns Result.success(Unit)
        coEvery { mockCustomizationRepository.recordStyleUsage(any()) } returns Result.success(Unit)
        
        // When
        val result = saveCustomizationUseCase.saveCustomizationConfig(expectedConfig)
        
        // Then
        assertTrue("Should succeed for valid configuration", result.isSuccess)
        coVerify { mockCustomizationRepository.saveCustomizationConfig(expectedConfig) }
        coVerify { mockCustomizationRepository.recordStyleUsage("test_style_1") }
    }
    
    @Test
    fun `saveCustomizationConfig should fail for invalid configuration`() = runTest {
        // Given
        val invalidConfig = testCustomizationConfig.copy(
            selectedStyleId = "", // Invalid empty style ID
            selectedBatteryImageUrl = "" // Invalid empty URL
        )
        
        // When
        val result = saveCustomizationUseCase.saveCustomizationConfig(invalidConfig)
        
        // Then
        assertTrue("Should fail for invalid configuration", result.isFailure)
        assertTrue("Should contain validation error", 
            result.exceptionOrNull()?.message?.contains("invalid") == true)
    }
    
    @Test
    fun `saveCustomizationConfig should fail when selected style not found`() = runTest {
        // Given
        val configWithMissingStyle = testCustomizationConfig.copy(selectedStyleId = "missing_style")
        coEvery { mockBatteryStyleRepository.batteryStylesFlow } returns flowOf(listOf(testBatteryStyle))
        
        // When
        val result = saveCustomizationUseCase.saveCustomizationConfig(configWithMissingStyle)
        
        // Then
        assertTrue("Should fail when style not found", result.isFailure)
        assertTrue("Should contain style not found error",
            result.exceptionOrNull()?.message?.contains("not found") == true)
    }
    
    @Test
    fun `saveCustomizationFromStyle should create and save configuration from style`() = runTest {
        // Given
        val enableFeature = true
        coEvery { mockCustomizationRepository.saveCustomizationConfig(any()) } returns Result.success(Unit)
        coEvery { mockCustomizationRepository.recordStyleUsage(any()) } returns Result.success(Unit)
        coEvery { mockBatteryStyleRepository.batteryStylesFlow } returns flowOf(listOf(testBatteryStyle))
        
        // When
        val result = saveCustomizationUseCase.saveCustomizationFromStyle(testBatteryStyle, enableFeature)
        
        // Then
        assertTrue("Should succeed for valid style", result.isSuccess)
        coVerify { 
            mockCustomizationRepository.saveCustomizationConfig(
                match { config ->
                    config.selectedStyleId == testBatteryStyle.id &&
                    config.isFeatureEnabled == enableFeature &&
                    config.selectedBatteryImageUrl == testBatteryStyle.batteryImageUrl &&
                    config.selectedEmojiImageUrl == testBatteryStyle.emojiImageUrl
                }
            )
        }
    }
    
    @Test
    fun `saveCustomizationFromStyle should fail for invalid style`() = runTest {
        // Given
        val invalidStyle = testBatteryStyle.copy(
            id = "", // Invalid empty ID
            batteryImageUrl = "" // Invalid empty URL
        )
        
        // When
        val result = saveCustomizationUseCase.saveCustomizationFromStyle(invalidStyle)
        
        // Then
        assertTrue("Should fail for invalid style", result.isFailure)
        assertTrue("Should contain validation error",
            result.exceptionOrNull()?.message?.contains("Invalid") == true)
    }
    
    @Test
    fun `updateStyleConfig should update configuration with new style settings`() = runTest {
        // Given
        val newStyleConfig = testStyleConfig.copy(
            showEmoji = false,
            percentageFontSizeDp = 20
        )
        val currentConfig = testCustomizationConfig
        
        coEvery { mockCustomizationRepository.getCurrentCustomizationConfig() } returns currentConfig
        coEvery { mockCustomizationRepository.saveCustomizationConfig(any()) } returns Result.success(Unit)
        coEvery { mockCustomizationRepository.recordStyleUsage(any()) } returns Result.success(Unit)
        coEvery { mockBatteryStyleRepository.batteryStylesFlow } returns flowOf(listOf(testBatteryStyle))
        
        // When
        val result = saveCustomizationUseCase.updateStyleConfig(newStyleConfig)
        
        // Then
        assertTrue("Should succeed for valid style config", result.isSuccess)
        coVerify {
            mockCustomizationRepository.saveCustomizationConfig(
                match { config ->
                    config.styleConfig.showEmoji == false &&
                    config.styleConfig.percentageFontSizeDp == 20 &&
                    config.selectedStyleId == currentConfig.selectedStyleId
                }
            )
        }
    }
    
    @Test
    fun `updateStyleConfig should fail for invalid style configuration`() = runTest {
        // Given
        val invalidStyleConfig = testStyleConfig.copy(
            percentageFontSizeDp = -1, // Invalid negative font size
            emojiSizeScale = -0.5f // Invalid negative scale
        )
        
        // When
        val result = saveCustomizationUseCase.updateStyleConfig(invalidStyleConfig)
        
        // Then
        assertTrue("Should fail for invalid style config", result.isFailure)
        assertTrue("Should contain validation error",
            result.exceptionOrNull()?.message?.contains("Invalid") == true)
    }
    
    @Test
    fun `setFeatureEnabled should update feature enablement state`() = runTest {
        // Given
        val enabled = true
        coEvery { mockCustomizationRepository.setFeatureEnabled(enabled) } returns Result.success(Unit)
        
        // When
        val result = saveCustomizationUseCase.setFeatureEnabled(enabled)
        
        // Then
        assertTrue("Should succeed for feature enablement", result.isSuccess)
        coVerify { mockCustomizationRepository.setFeatureEnabled(enabled) }
    }
    
    @Test
    fun `setFeatureEnabled should handle repository errors`() = runTest {
        // Given
        val enabled = true
        val repositoryError = RuntimeException("Repository error")
        coEvery { mockCustomizationRepository.setFeatureEnabled(enabled) } returns Result.failure(repositoryError)
        
        // When
        val result = saveCustomizationUseCase.setFeatureEnabled(enabled)
        
        // Then
        assertTrue("Should fail when repository fails", result.isFailure)
        assertEquals("Should preserve repository error", repositoryError, result.exceptionOrNull())
    }
    
    @Test
    fun `saveCustomizationConfig should handle repository save errors`() = runTest {
        // Given
        val repositoryError = RuntimeException("Save failed")
        coEvery { mockBatteryStyleRepository.batteryStylesFlow } returns flowOf(listOf(testBatteryStyle))
        coEvery { mockCustomizationRepository.saveCustomizationConfig(any()) } returns Result.failure(repositoryError)
        
        // When
        val result = saveCustomizationUseCase.saveCustomizationConfig(testCustomizationConfig)
        
        // Then
        assertTrue("Should fail when repository save fails", result.isFailure)
        assertEquals("Should preserve repository error", repositoryError, result.exceptionOrNull())
    }
    
    @Test
    fun `saveCustomizationFromStyle should allow premium styles during development`() = runTest {
        // Given
        val premiumStyle = testBatteryStyle.copy(isPremium = true)
        coEvery { mockCustomizationRepository.saveCustomizationConfig(any()) } returns Result.success(Unit)
        coEvery { mockCustomizationRepository.recordStyleUsage(any()) } returns Result.success(Unit)
        coEvery { mockBatteryStyleRepository.batteryStylesFlow } returns flowOf(listOf(premiumStyle))
        
        // When
        val result = saveCustomizationUseCase.saveCustomizationFromStyle(premiumStyle)
        
        // Then
        assertTrue("Should succeed for premium style during development", result.isSuccess)
        coVerify { mockCustomizationRepository.saveCustomizationConfig(any()) }
    }
}
