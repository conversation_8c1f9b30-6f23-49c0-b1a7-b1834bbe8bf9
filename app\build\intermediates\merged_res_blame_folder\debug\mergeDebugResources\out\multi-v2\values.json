{"logs": [{"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tqhit.battery.one.app-mergeDebugResources-99:\\values\\values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\cd1152147d00b2d5b451be6d0860a83e\\transformed\\navigation-runtime-2.8.9\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2944", "startColumns": "4", "startOffsets": "173757", "endColumns": "52", "endOffsets": "173805"}}, {"source": "D:\\Duc\\AndroidStudioProjects\\TJ_BatteryOne\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,337,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,339,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,338,-1,342,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,334,347,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,344,343,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,335,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,345,-1,-1,-1,346,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,340,-1,341,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,336,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,28344,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,28470,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,28409,-1,28655,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,28155,28920,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,28777,28706,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,28210,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,28840,-1,-1,-1,28881,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,28543,-1,28594,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,28283,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,63,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,71,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,59,-1,49,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,53,43,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,61,69,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,71,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,39,-1,-1,-1,37,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,49,-1,59,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,59,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,28403,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,28537,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,28464,-1,28700,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,28204,28959,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,28834,28771,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,28277,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,28875,-1,-1,-1,28914,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,28588,-1,28649,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,28338,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "3101,3102,3103,3104,3105,3106,3107,3108,3109,3110,3111,3112,3113,3114,3115,3116,3119,3120,3123,3124,3135,3136,3138,3139,3140,3143,3144,3145,3146,3147,3148,3149,3150,3151,3152,3153,3154,3155,3157,3158,3159,3160,3161,3162,3163,3165,3166,3167,3168,3169,3170,3171,3172,3173,3174,3175,3176,3177,3178,3179,3180,3181,3182,3183,3184,3185,3186,3187,3188,3189,3190,3191,3192,3193,3194,3201,3202,3203,3204,3205,3213,3214,3215,3219,3220,3221,3222,3223,3224,3225,3226,3227,3228,3229,3230,3231,3232,3233,3234,3235,3237,3260,3261,3262,3263,3265,3266,3267,3268,3269,3270,3271,3272,3273,3274,3284,3285,3286,3287,3288,3289,3290,3291,3294,3295,3296,3297,3298,3299,3301,3302,3303,3304,3305,3306,3307,3308,3309,3310,3311,3312,3313,3314,3315,3316,3317,3319,3320,3321,3322,3323,3324,3325,3326,3327,3328,3332,3336,3337,3390,3397,3398,3399,3400,3401,3407,3408,3409,3410,3411,3412,3413,3414,3415,3416,3417,3418,3419,3420,3421,3423,3424,3425,3427,3428,3430,3432,3433,3434,3436,3437,3438,3439,3440,3442,3443,3444,3445,3446,3447,3448,3449,3527,3528,3529,3530,3553,3554,3623,3636,3637,3638,3639,3640,3641,3642,3643,3644,3645,3646,3648,3649,3650,3651,3652,3653,3654,3655,3672,3673,3674,3675,3676,3677,3678,3684,3685,3686,3687,3688,3689,3690,3691,3692,3693,3694,3695,3696,3697,3699,3700,3701,3702,3703,3704,3705,3706,3708,3709,3712,3713,3714,3715,3716,3717,3718,3719,3720,3722,3730,3731,3732,3733,3734,3736,3742,3744,3745,3746,3747,3748,3749,3750,3751,3752,3753,3754,3757,3758,3759,3760,3761,3762,3763,3764,3768,3769,3770,3771,3774,3775,3776,3778,3780,3781,3782,3783,3784,3785,3788,3789,3790,3791,3793,3794,3795,3796,3797,3798,3799,3800,3801,3802,3803,3804,3808,3809,3810,3811,3812,3813,3814,3815", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "183302,183363,183413,183462,183511,183560,183610,183658,183705,183759,183817,183866,183915,183964,184012,184064,184284,184362,184571,184619,185304,185356,185503,185572,185647,185936,186006,186072,186122,186186,186224,186381,186424,186479,186564,186606,186666,186750,186886,186934,187179,187229,187532,187576,187643,187891,187935,187975,188035,188083,188121,188204,188260,188332,188709,189095,189159,189233,189305,189345,189407,189469,190146,190232,190316,190380,190436,190507,190575,190779,190833,191140,191211,191261,191300,192446,192514,192563,192631,192701,193282,193324,193388,193760,193802,193876,194037,194105,194195,194272,194339,194425,194536,194608,194674,194733,194877,194937,194995,195053,195190,197906,197959,198003,198072,198232,198282,198642,198709,198763,198838,198893,198953,199527,200126,201132,201204,201252,201318,201365,201503,201565,201865,202082,202584,202644,202781,202928,203075,203193,203236,203284,203358,203454,203538,203603,203656,203795,203887,203945,204013,204083,204390,204635,204831,204971,205115,205186,205299,205363,205423,205475,205525,205593,205648,205706,205906,206198,206246,210401,211079,211136,211174,211403,211450,212058,212120,212172,212225,212373,212430,212571,212791,212829,212885,212922,212959,212994,213029,213098,213293,213343,213379,213583,213643,213768,213895,213962,214011,214122,214168,214208,214281,214335,214434,214495,214954,215006,215077,215245,215323,215392,221786,221820,221860,221902,225092,225152,235686,236767,236843,236984,237018,237109,237169,237248,237339,237411,237490,237619,237731,237785,237868,237935,238009,238105,238225,238291,240042,240116,240409,240506,240667,240748,240845,241693,241732,241786,241836,241898,241968,242030,242125,242210,242292,242378,242471,242520,242586,242785,242832,242876,242916,242963,243007,243079,243186,243336,243427,243624,243673,243739,243946,244022,244096,244154,244194,244250,244359,244755,244793,244863,244913,244974,245086,245653,245759,245822,246007,246087,246187,246260,246306,246370,246436,246486,246553,246961,247005,247063,247109,247223,247585,247639,247711,247910,247968,248040,248100,248242,248307,248359,248508,248632,248690,248752,248834,248897,249109,249300,249367,249432,249526,249653,249720,249771,249984,250056,250127,250190,250228,250287,250339,250397,250441,250661,250702,250755,250812,250882,250930,250966,251001", "endColumns": "60,49,48,48,48,49,47,46,53,57,48,48,48,47,51,75,77,53,47,45,51,72,68,74,73,69,65,49,63,37,156,42,54,84,41,59,83,52,47,244,49,302,43,66,97,43,39,59,47,37,82,55,71,376,385,63,73,71,39,61,61,676,85,83,63,55,70,67,203,53,306,70,49,38,56,67,48,67,69,69,41,63,73,41,73,160,67,89,76,66,85,110,71,65,58,143,59,57,57,57,39,52,43,68,91,49,359,66,53,74,54,59,573,598,75,71,47,65,46,137,61,299,49,501,59,136,146,146,60,42,47,73,95,83,64,52,138,91,57,67,69,306,244,195,139,85,70,112,63,59,51,49,67,54,57,39,85,47,67,59,56,37,228,46,73,61,51,52,147,56,140,219,37,55,36,36,34,34,68,41,49,35,49,59,72,66,66,48,51,45,39,72,53,43,60,458,51,70,167,77,68,35,33,39,41,57,59,69,56,75,140,33,90,59,78,90,71,78,128,57,53,82,66,73,95,119,65,41,73,292,96,160,80,96,45,38,53,49,61,69,61,94,84,81,85,92,48,65,81,46,43,39,46,43,71,106,59,90,92,48,65,206,75,73,57,39,55,56,39,37,69,49,60,58,58,59,62,184,79,99,72,45,63,65,49,66,59,43,57,45,113,361,53,71,45,57,71,59,58,64,51,58,39,57,61,81,62,211,79,66,64,93,62,66,50,212,71,70,62,37,58,51,57,43,50,40,52,56,69,47,35,34,43", "endOffsets": "183358,183408,183457,183506,183555,183605,183653,183700,183754,183812,183861,183910,183959,184007,184059,184135,184357,184411,184614,184660,185351,185424,185567,185642,185716,186001,186067,186117,186181,186219,186376,186419,186474,186559,186601,186661,186745,186798,186929,187174,187224,187527,187571,187638,187736,187930,187970,188030,188078,188116,188199,188255,188327,188704,189090,189154,189228,189300,189340,189402,189464,190141,190227,190311,190375,190431,190502,190570,190774,190828,191135,191206,191256,191295,191352,192509,192558,192626,192696,192766,193319,193383,193457,193797,193871,194032,194100,194190,194267,194334,194420,194531,194603,194669,194728,194872,194932,194990,195048,195106,195225,197954,197998,198067,198159,198277,198637,198704,198758,198833,198888,198948,199522,200121,200197,201199,201247,201313,201360,201498,201560,201860,201910,202579,202639,202776,202923,203070,203131,203231,203279,203353,203449,203533,203598,203651,203790,203882,203940,204008,204078,204385,204630,204826,204966,205052,205181,205294,205358,205418,205470,205520,205588,205643,205701,205741,205987,206241,206309,210456,211131,211169,211398,211445,211519,212115,212167,212220,212368,212425,212566,212786,212824,212880,212917,212954,212989,213024,213093,213135,213338,213374,213424,213638,213711,213830,213957,214006,214058,214163,214203,214276,214330,214374,214490,214949,215001,215072,215240,215318,215387,215423,221815,221855,221897,221955,225147,225217,235738,236838,236979,237013,237104,237164,237243,237334,237406,237485,237614,237672,237780,237863,237930,238004,238100,238220,238286,238328,240111,240404,240501,240662,240743,240840,240886,241727,241781,241831,241893,241963,242025,242120,242205,242287,242373,242466,242515,242581,242663,242827,242871,242911,242958,243002,243074,243181,243241,243422,243515,243668,243734,243941,244017,244091,244149,244189,244245,244302,244394,244788,244858,244908,244969,245028,245140,245708,245817,246002,246082,246182,246255,246301,246365,246431,246481,246548,246608,247000,247058,247104,247218,247580,247634,247706,247752,247963,248035,248095,248154,248302,248354,248413,248543,248685,248747,248829,248892,249104,249184,249362,249427,249521,249584,249715,249766,249979,250051,250122,250185,250223,250282,250334,250392,250436,250487,250697,250750,250807,250877,250925,250961,250996,251040"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7a26d25b3618e3c60f978a7b1cb6a100\\transformed\\jetified-play-services-ads-api-24.2.0\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "8632", "startColumns": "4", "startOffsets": "575874", "endLines": "8639", "endColumns": "8", "endOffsets": "576267"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8bd7feaae90e869538df51f29dd16595\\transformed\\jetified-media3-ui-1.6.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,11,12,23,30,35,36,38,42,62,140,148,194,268,269,279,280,281,293,528,546,556,557,558,559,563,576,577,578,579,584,591,594,595,596,597,598,615,620,621,622,623,624,625,626,627,628,629,630,649,650,651,700,714,727,730,731,742,869,870,871,872,873,874,875,2076,2077,2078,2079,2080,2081,2082,2083,2084,2085,2086,2087,2088,2089,2090,2091,2092,2093,2094,2095,2096,2097,2098,2099,2100,2101,2102,2103,2104,2105,2106,2107,2108,2109,2110,2786,2787,2788,2789,2790,2791,2792,2793,2794,2795,2796,2797,2798,2799,2800,2801,2802,2803,2804,2805,2806,2807,2808,2809,2810,2811,2812,2813,2814,2815,2816,2817,2818,2819,2820,2821,2822,2823,2824,2825,2826,2827,2828,2829,2830,2831,2878,2879,2880,2881,2882,2883,2884,2885,2886,2887,2888,2889,2890,2891,2892,2893,2894,2895,2896,2897,2898,2899,2900,2901,2902,2903,2904,2905,2906,2907,2908,2909,2910,2911,2912,2913,2914,2915,2916,2917,2918,2919,2920,3025,3026,3089,3093,3338,3339,3340,3341,3342,3343,3344,3345,3346,3347,3348,3349,3350,3351,3352,3353,3354,3355,3356,3357,3358,3359,3360,3361,3362,3363,3373,3374,3375,3376,3377,3378,3379,3380,3381,3382,3383,3384,3385,3386,3387,3388,3389,7393,7398,7402,7406,7410,7414,7418,7422,7426,7427,7433,7444,7448,7452,7456,7460,7464,7468,7472,7476,7480,7484,7488,7501,7506,7511,7516,7529,7537,7547,7551,7555", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,430,913,1209,1372,1418,1516,1642,2590,6893,7245,9579,13126,13186,13679,13731,13781,14351,26547,27282,27677,27725,27782,27829,28056,28658,28712,28766,28820,28968,29206,29362,29411,29472,29532,29588,30543,30713,30773,30826,30883,30938,30994,31051,31100,31151,31206,31260,32171,32227,32282,35165,35762,36396,36556,36604,36996,44979,45036,45093,45155,45222,45294,45338,108930,108986,109049,109122,109192,109251,109308,109355,109410,109455,109504,109559,109613,109663,109714,109768,109827,109877,109935,109991,110044,110107,110172,110235,110287,110347,110411,110477,110535,110607,110668,110738,110808,110873,110938,163642,163737,163842,163945,164026,164109,164190,164279,164372,164465,164558,164643,164738,164831,164908,165000,165078,165158,165236,165322,165404,165497,165575,165666,165747,165836,165939,166040,166124,166220,166317,166412,166505,166597,166690,166783,166876,166959,167046,167141,167234,167336,167428,167509,167604,167697,170667,170711,170752,170797,170845,170889,170932,170981,171028,171072,171128,171181,171223,171270,171318,171378,171416,171466,171510,171549,171599,171651,171689,171736,171783,171824,171863,171901,171945,171993,172035,172073,172115,172169,172216,172253,172302,172344,172385,172426,172468,172511,172549,178108,178186,182415,182712,206314,206396,206478,206620,206698,206785,206870,206937,207000,207092,207184,207249,207312,207374,207445,207555,207666,207776,207843,207923,207994,208061,208146,208231,208294,208382,209092,209234,209334,209382,209525,209588,209650,209715,209786,209844,209902,209968,210020,210082,210158,210234,210288,497857,498136,498367,498577,498790,499000,499222,499438,499642,499680,500034,500821,501062,501302,501559,501812,502065,502300,502547,502786,503030,503251,503446,504118,504409,504705,505008,505674,506208,506682,506893,507093", "endLines": "10,11,12,23,34,35,36,41,42,62,140,148,194,268,269,279,280,284,293,528,546,556,557,558,559,563,576,577,578,583,590,591,594,595,596,597,598,619,620,621,622,623,624,625,626,627,628,629,630,649,650,657,700,714,727,730,731,742,869,870,871,872,873,874,875,2076,2077,2078,2079,2080,2081,2082,2083,2084,2085,2086,2087,2088,2089,2090,2091,2092,2093,2094,2095,2096,2097,2098,2099,2100,2101,2102,2103,2104,2105,2106,2107,2108,2109,2110,2786,2787,2788,2789,2790,2791,2792,2793,2794,2795,2796,2797,2798,2799,2800,2801,2802,2803,2804,2805,2806,2807,2808,2809,2810,2811,2812,2813,2814,2815,2816,2817,2818,2819,2820,2821,2822,2823,2824,2825,2826,2827,2828,2829,2830,2831,2878,2879,2880,2881,2882,2883,2884,2885,2886,2887,2888,2889,2890,2891,2892,2893,2894,2895,2896,2897,2898,2899,2900,2901,2902,2903,2904,2905,2906,2907,2908,2909,2910,2911,2912,2913,2914,2915,2916,2917,2918,2919,2920,3025,3026,3092,3096,3338,3339,3340,3341,3342,3343,3344,3345,3346,3347,3348,3349,3350,3351,3352,3353,3354,3355,3356,3357,3358,3359,3360,3361,3362,3363,3373,3374,3375,3376,3377,3378,3379,3380,3381,3382,3383,3384,3385,3386,3387,3388,3389,7397,7401,7405,7409,7413,7417,7421,7425,7426,7432,7443,7447,7451,7455,7459,7463,7467,7471,7475,7479,7483,7487,7500,7505,7510,7515,7528,7536,7546,7550,7554,7558", "endColumns": "17,49,53,53,9,45,48,9,48,48,58,53,54,59,58,51,49,9,64,47,48,47,56,46,54,51,53,53,53,9,9,49,48,60,59,55,59,9,59,52,56,54,55,56,48,50,54,53,58,55,54,9,64,57,48,47,50,45,56,56,61,66,71,43,56,55,62,72,69,58,56,46,54,44,48,54,53,49,50,53,58,49,57,55,52,62,64,62,51,59,63,65,57,71,60,69,69,64,64,70,94,104,102,80,82,80,88,92,92,92,84,94,92,76,91,77,79,77,85,81,92,77,90,80,88,102,100,83,95,96,94,92,91,92,92,92,82,86,94,92,101,91,80,94,92,76,43,40,44,47,43,42,48,46,43,55,52,41,46,47,59,37,49,43,38,49,51,37,46,46,40,38,37,43,47,41,37,41,53,46,36,48,41,40,40,41,42,37,35,77,77,12,12,81,81,141,77,86,84,66,62,91,91,64,62,61,70,109,110,109,66,79,70,66,84,84,62,87,63,141,99,47,142,62,61,64,70,57,57,65,51,61,75,75,53,112,10,10,10,10,10,10,10,10,37,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10", "endOffsets": "375,425,479,962,1367,1413,1462,1637,1686,2634,6947,7294,9629,13181,13240,13726,13776,13904,14411,26590,27326,27720,27777,27824,27879,28103,28707,28761,28815,28963,29201,29251,29406,29467,29527,29583,29643,30708,30768,30821,30878,30933,30989,31046,31095,31146,31201,31255,31314,32222,32277,32564,35225,35815,36440,36599,36650,37037,45031,45088,45150,45217,45289,45333,45390,108981,109044,109117,109187,109246,109303,109350,109405,109450,109499,109554,109608,109658,109709,109763,109822,109872,109930,109986,110039,110102,110167,110230,110282,110342,110406,110472,110530,110602,110663,110733,110803,110868,110933,111004,163732,163837,163940,164021,164104,164185,164274,164367,164460,164553,164638,164733,164826,164903,164995,165073,165153,165231,165317,165399,165492,165570,165661,165742,165831,165934,166035,166119,166215,166312,166407,166500,166592,166685,166778,166871,166954,167041,167136,167229,167331,167423,167504,167599,167692,167769,170706,170747,170792,170840,170884,170927,170976,171023,171067,171123,171176,171218,171265,171313,171373,171411,171461,171505,171544,171594,171646,171684,171731,171778,171819,171858,171896,171940,171988,172030,172068,172110,172164,172211,172248,172297,172339,172380,172421,172463,172506,172544,172580,178181,178259,182707,182977,206391,206473,206615,206693,206780,206865,206932,206995,207087,207179,207244,207307,207369,207440,207550,207661,207771,207838,207918,207989,208056,208141,208226,208289,208377,208441,209229,209329,209377,209520,209583,209645,209710,209781,209839,209897,209963,210015,210077,210153,210229,210283,210396,498131,498362,498572,498785,498995,499217,499433,499637,499675,500029,500816,501057,501297,501554,501807,502060,502295,502542,502781,503025,503246,503441,504113,504404,504700,505003,505669,506203,506677,506888,507088,507264"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2689e40363c086e26e0a6a841af9a39c\\transformed\\cardview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "63,801,802,803,804,2011,2012,2013,3940,7365,7367,7370", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2639,40554,40615,40677,40739,104642,104701,104758,258398,496342,496406,496532", "endLines": "63,801,802,803,804,2011,2012,2013,3946,7366,7369,7372", "endColumns": "51,60,61,61,63,58,56,53,12,12,12,12", "endOffsets": "2686,40610,40672,40734,40798,104696,104753,104807,258807,496401,496527,496655"}}, {"source": "D:\\Duc\\AndroidStudioProjects\\TJ_BatteryOne\\app\\build\\generated\\res\\injectCrashlyticsMappingFileIdDebug\\values\\com_google_firebase_crashlytics_mappingfileid.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "3241", "startColumns": "4", "startOffsets": "195397", "endColumns": "175", "endOffsets": "195568"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5444be4bc77930bd89cfbb9f2224d8e4\\transformed\\navigation-ui-2.8.9\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1"}, "to": {"startLines": "3020,3300,3633,3634", "startColumns": "4,4,4,4", "startOffsets": "177795,203136,236542,236618", "endColumns": "52,56,75,86", "endOffsets": "177843,203188,236613,236700"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\69fa008bb70ecc0d8e73621c41f47f2d\\transformed\\jetified-window-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1"}, "to": {"startLines": "637,642,643,644,2874", "startColumns": "4,4,4,4,4", "startOffsets": "31643,31822,31882,31934,170453", "endLines": "641,642,643,644,2874", "endColumns": "11,59,51,44,59", "endOffsets": "31817,31877,31929,31974,170508"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\42f95d9fa807b14415e836fc15872a54\\transformed\\jetified-play-services-basement-18.5.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "3027,3250", "startColumns": "4,4", "startOffsets": "178264,196613", "endColumns": "67,166", "endOffsets": "178327,196775"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\237e0b5db534c615c4317f1b214e3e7f\\transformed\\jetified-play-services-ads-24.2.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2952,2953,3624,3627,3630,3656,3658,3660,3662,3664,3666,3668,3670,3723,3724,3725,3726,3727,3728,3729,3805", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "174255,174315,235743,235999,236257,238333,238541,238773,239001,239268,239443,239630,239853,244399,244441,244518,244556,244595,244648,244716,250492", "endLines": "2952,2953,3626,3629,3632,3657,3659,3661,3663,3665,3667,3669,3671,3723,3724,3725,3726,3727,3728,3729,3807", "endColumns": "59,49,11,11,11,19,86,68,62,17,24,81,48,41,76,37,38,52,67,38,11", "endOffsets": "174310,174360,235994,236252,236537,238536,238768,238996,239263,239438,239625,239848,240037,244436,244513,244551,244590,244643,244711,244750,250656"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\66d600e9b1169a1e725381a5921a9d13\\transformed\\jetified-glide-4.16.0\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2924", "startColumns": "4", "startOffsets": "172729", "endColumns": "57", "endOffsets": "172782"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\93d3043f0a8b9466a00a736e170a6ddc\\transformed\\appcompat-1.7.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "179,276,285,701,765,766,772,773,774,775,776,777,778,781,782,783,784,787,788,789,790,791,792,797,798,862,863,864,865,867,868,876,877,881,882,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1069,1070,1131,1132,1133,1134,1135,1136,1137,1138,1145,1146,1147,1148,1149,1150,1151,1152,1153,1154,1158,1159,1921,1922,1923,1924,1925,1926,1927,1928,1929,1930,1931,1932,1933,1934,1935,1936,1937,1938,1939,1940,1941,1942,1943,1944,1945,1946,1947,1948,1949,1950,1951,1952,1953,1954,1955,1956,1957,1958,1959,1960,1961,1962,1963,1964,1965,1966,1967,1968,1969,1970,1971,1972,1973,1974,1975,1976,1977,1978,1979,1980,1981,1982,1983,1984,1985,1986,1987,1988,1989,1990,1991,1992,1993,1994,1995,1996,1997,1998,1999,2000,2001,2074,2075,2115,2116,2117,2118,2119,2120,2121,2778,2779,2780,2781,2782,2783,2784,2785,2869,2870,2871,2872,2927,2958,2959,2970,3005,3015,3016,3019,3021,3117,3118,3121,3122,3125,3126,3127,3128,3129,3130,3131,3132,3133,3134,3137,3141,3142,3735,3824,3825,3880,3881,3882,3919,3927,3928,3932,3936,3947,3952,3981,3988,3992,3996,4001,4005,4009,4013,4017,4021,4025,4031,4035,4041,4045,4051,4055,4060,4064,4067,4071,4077,4081,4087,4091,4097,4100,4104,4108,4112,4116,4120,4121,4122,4123,4126,4129,4132,4135,4139,4140,4141,4142,4183,4186,4188,4190,4192,4197,4198,4202,4208,4212,4213,4215,4227,4228,4232,4238,4242,4322,4323,4327,4354,4358,4359,4363,6165,6337,6363,6534,6560,6591,6599,6605,6621,6643,6648,6653,6663,6672,6681,6685,6692,6711,6718,6719,6728,6731,6734,6738,6742,6746,6749,6750,6755,6760,6770,6775,6782,6788,6789,6792,6796,6801,6803,6805,6808,6811,6813,6817,6820,6827,6830,6833,6837,6839,6843,6845,6847,6849,6853,6861,6869,6881,6887,6896,6899,6910,6913,6914,6919,6920,7800,7869,7943,7944,7954,7963,7969,7971,7975,7978,7981,7984,7987,7990,7993,7996,8000,8003,8006,8009,8013,8016,8020,8175,8176,8177,8178,8179,8180,8181,8182,8183,8184,8185,8186,8187,8188,8189,8190,8191,8192,8193,8194,8195,8197,8199,8200,8201,8202,8203,8204,8205,8206,8208,8209,8211,8212,8214,8216,8217,8219,8220,8221,8222,8223,8224,8226,8227,8228,8229,8230,8515,8517,8519,8540,8541,8542,8543,8544,8545,8546,8547,8548,8549,8550,8551,8552,8554,8555,8556,8557,8558,8559,8560,8562,8566,8811,8812,8813,8814,8815,8816,8820,8821,8822,9363,9365,9367,9369,9371,9373,9374,9375,9376,9378,9380,9382,9383,9384,9385,9386,9387,9388,9389,9390,9391,9392,9393,9396,9397,9398,9399,9401,9403,9404,9406,9407,9409,9411,9413,9414,9415,9416,9417,9418,9419,9420,9421,9422,9423,9424,9426,9427,9428,9429,9431,9432,9433,9434,9435,9437,9439,9441,9443,9444,9445,9446,9447,9448,9449,9450,9451,9452,9453,9454,9455,9456,9457", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8629,13526,13909,35230,38033,38088,38472,38536,38606,38667,38742,38818,38895,39133,39218,39300,39376,39533,39610,39688,39794,39900,39979,40308,40365,44536,44610,44685,44750,44858,44918,45395,45467,45669,45736,59161,59220,59279,59338,59397,59456,59510,59564,59617,59671,59725,59779,64349,64423,64502,64575,64649,64720,64792,64864,65223,65280,65338,65411,65485,65559,65634,65706,65779,65849,66049,66109,98680,98749,98818,98888,98962,99038,99102,99179,99255,99332,99397,99466,99543,99618,99687,99755,99832,99898,99959,100056,100121,100190,100289,100360,100419,100477,100534,100593,100657,100728,100800,100872,100944,101016,101083,101151,101219,101278,101341,101405,101495,101586,101646,101712,101779,101845,101915,101979,102032,102099,102160,102227,102340,102398,102461,102526,102591,102666,102739,102811,102855,102902,102948,102997,103058,103119,103180,103242,103306,103370,103434,103499,103562,103622,103683,103749,103808,103868,103930,104001,104061,108757,108843,111215,111305,111392,111480,111562,111645,111735,163189,163241,163299,163344,163410,163474,163531,163588,170174,170231,170279,170328,172904,174575,174622,175163,176989,177481,177545,177735,177848,184140,184214,184416,184486,184665,184726,184789,184855,184919,184990,185053,185118,185182,185243,185429,185721,185795,245033,251511,251589,254475,254563,254659,256947,257529,257618,257865,258146,258812,259097,260906,261383,261605,261827,262103,262330,262560,262790,263020,263250,263477,263896,264122,264547,264777,265205,265424,265707,265915,266046,266273,266699,266924,267351,267572,267997,268117,268393,268694,269018,269309,269623,269760,269891,269996,270238,270405,270609,270817,271088,271200,271312,271417,273510,273724,273870,274010,274096,274444,274532,274778,275196,275445,275527,275625,276282,276382,276634,277058,277313,283469,283558,283795,285819,286061,286163,286416,421985,432666,434182,444877,446405,448162,448788,449208,450469,451734,451990,452226,452773,453267,453872,454070,454650,456018,456393,456511,457049,457206,457402,457675,457931,458101,458242,458306,458671,459038,459714,459978,460316,460669,460763,460949,461255,461517,461642,461769,462008,462219,462338,462531,462708,463163,463344,463466,463725,463838,464025,464127,464234,464363,464638,465146,465642,466519,466813,467383,467532,468264,468436,468520,468856,468948,520719,525950,531665,531727,532305,532889,533163,533276,533505,533665,533817,533988,534154,534323,534490,534653,534896,535066,535239,535410,535684,535883,536088,545742,545826,545922,546018,546116,546216,546318,546420,546522,546624,546726,546826,546922,547034,547163,547286,547417,547548,547646,547760,547854,547994,548128,548224,548336,548436,548552,548648,548760,548860,549000,549136,549300,549430,549588,549738,549879,550023,550158,550270,550420,550548,550676,550812,550944,551074,551204,551316,568974,569120,569264,570365,570431,570521,570597,570701,570791,570893,571001,571109,571209,571289,571381,571479,571589,571641,571719,571825,571917,572021,572131,572253,572416,589556,589636,589736,589826,589936,590026,590267,590361,590467,630346,630446,630558,630672,630788,630904,630998,631112,631224,631326,631446,631568,631650,631754,631874,632000,632098,632192,632280,632392,632508,632630,632742,632917,633033,633119,633211,633323,633447,633514,633640,633708,633836,633980,634108,634177,634272,634387,634500,634599,634708,634819,634930,635031,635136,635236,635366,635457,635580,635674,635786,635872,635976,636072,636160,636278,636382,636486,636612,636700,636808,636908,636998,637108,637192,637294,637378,637432,637496,637602,637688,637798,637882", "endLines": "179,276,285,701,765,766,772,773,774,775,776,777,778,781,782,783,784,787,788,789,790,791,792,797,798,862,863,864,865,867,868,876,877,881,882,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1069,1070,1131,1132,1133,1134,1135,1136,1137,1138,1145,1146,1147,1148,1149,1150,1151,1152,1153,1154,1158,1159,1921,1922,1923,1924,1925,1926,1927,1928,1929,1930,1931,1932,1933,1934,1935,1936,1937,1938,1939,1940,1941,1942,1943,1944,1945,1946,1947,1948,1949,1950,1951,1952,1953,1954,1955,1956,1957,1958,1959,1960,1961,1962,1963,1964,1965,1966,1967,1968,1969,1970,1971,1972,1973,1974,1975,1976,1977,1978,1979,1980,1981,1982,1983,1984,1985,1986,1987,1988,1989,1990,1991,1992,1993,1994,1995,1996,1997,1998,1999,2000,2001,2074,2075,2115,2116,2117,2118,2119,2120,2121,2778,2779,2780,2781,2782,2783,2784,2785,2869,2870,2871,2872,2927,2958,2959,2970,3005,3015,3016,3019,3021,3117,3118,3121,3122,3125,3126,3127,3128,3129,3130,3131,3132,3133,3134,3137,3141,3142,3735,3824,3825,3880,3881,3882,3926,3927,3931,3935,3939,3951,3957,3987,3991,3995,4000,4004,4008,4012,4016,4020,4024,4030,4034,4040,4044,4050,4054,4059,4063,4066,4070,4076,4080,4086,4090,4096,4099,4103,4107,4111,4115,4119,4120,4121,4122,4125,4128,4131,4134,4138,4139,4140,4141,4142,4185,4187,4189,4191,4196,4197,4201,4207,4211,4212,4214,4226,4227,4231,4237,4241,4242,4322,4326,4353,4357,4358,4362,4390,6336,6362,6533,6559,6590,6598,6604,6620,6642,6647,6652,6662,6671,6680,6684,6691,6710,6717,6718,6727,6730,6733,6737,6741,6745,6748,6749,6754,6759,6769,6774,6781,6787,6788,6791,6795,6800,6802,6804,6807,6810,6812,6816,6819,6826,6829,6832,6836,6838,6842,6844,6846,6848,6852,6860,6868,6880,6886,6895,6898,6909,6912,6913,6918,6919,6924,7868,7938,7943,7953,7962,7963,7970,7974,7977,7980,7983,7986,7989,7992,7995,7999,8002,8005,8008,8012,8015,8019,8023,8175,8176,8177,8178,8179,8180,8181,8182,8183,8184,8185,8186,8187,8188,8189,8190,8191,8192,8193,8194,8196,8198,8199,8200,8201,8202,8203,8204,8205,8207,8208,8210,8211,8213,8215,8216,8218,8219,8220,8221,8222,8223,8225,8226,8227,8228,8229,8230,8516,8518,8520,8540,8541,8542,8543,8544,8545,8546,8547,8548,8549,8550,8551,8553,8554,8555,8556,8557,8558,8559,8561,8565,8569,8811,8812,8813,8814,8815,8819,8820,8821,8822,9364,9366,9368,9370,9372,9373,9374,9375,9377,9379,9381,9382,9383,9384,9385,9386,9387,9388,9389,9390,9391,9392,9395,9396,9397,9398,9400,9402,9403,9405,9406,9408,9410,9412,9413,9414,9415,9416,9417,9418,9419,9420,9421,9422,9423,9425,9426,9427,9428,9430,9431,9432,9433,9434,9436,9438,9440,9442,9443,9444,9445,9446,9447,9448,9449,9450,9451,9452,9453,9454,9455,9456,9457", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,69,84,60,62,65,63,70,62,64,63,60,60,73,73,140,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119", "endOffsets": "8679,13566,13953,35266,38083,38145,38531,38601,38662,38737,38813,38890,38968,39213,39295,39371,39447,39605,39683,39789,39895,39974,40054,40360,40418,44605,44680,44745,44811,44913,44974,45462,45535,45731,45799,59215,59274,59333,59392,59451,59505,59559,59612,59666,59720,59774,59828,64418,64497,64570,64644,64715,64787,64859,64932,65275,65333,65406,65480,65554,65629,65701,65774,65844,65915,66104,66165,98744,98813,98883,98957,99033,99097,99174,99250,99327,99392,99461,99538,99613,99682,99750,99827,99893,99954,100051,100116,100185,100284,100355,100414,100472,100529,100588,100652,100723,100795,100867,100939,101011,101078,101146,101214,101273,101336,101400,101490,101581,101641,101707,101774,101840,101910,101974,102027,102094,102155,102222,102335,102393,102456,102521,102586,102661,102734,102806,102850,102897,102943,102992,103053,103114,103175,103237,103301,103365,103429,103494,103557,103617,103678,103744,103803,103863,103925,103996,104056,104124,108838,108925,111300,111387,111475,111557,111640,111730,111821,163236,163294,163339,163405,163469,163526,163583,163637,170226,170274,170323,170374,172933,174617,174666,175204,177016,177540,177602,177790,177900,184209,184279,184481,184566,184721,184784,184850,184914,184985,185048,185113,185177,185238,185299,185498,185790,185931,245081,251584,251674,254558,254654,254744,257524,257613,257860,258141,258393,259092,259485,261378,261600,261822,262098,262325,262555,262785,263015,263245,263472,263891,264117,264542,264772,265200,265419,265702,265910,266041,266268,266694,266919,267346,267567,267992,268112,268388,268689,269013,269304,269618,269755,269886,269991,270233,270400,270604,270812,271083,271195,271307,271412,271529,273719,273865,274005,274091,274439,274527,274773,275191,275440,275522,275620,276277,276377,276629,277053,277308,277402,283553,283790,285814,286056,286158,286411,288567,432661,434177,444872,446400,448157,448783,449203,450464,451729,451985,452221,452768,453262,453867,454065,454645,456013,456388,456506,457044,457201,457397,457670,457926,458096,458237,458301,458666,459033,459709,459973,460311,460664,460758,460944,461250,461512,461637,461764,462003,462214,462333,462526,462703,463158,463339,463461,463720,463833,464020,464122,464229,464358,464633,465141,465637,466514,466808,467378,467527,468259,468431,468515,468851,468943,469221,525945,531316,531722,532300,532884,532975,533271,533500,533660,533812,533983,534149,534318,534485,534648,534891,535061,535234,535405,535679,535878,536083,536413,545821,545917,546013,546111,546211,546313,546415,546517,546619,546721,546821,546917,547029,547158,547281,547412,547543,547641,547755,547849,547989,548123,548219,548331,548431,548547,548643,548755,548855,548995,549131,549295,549425,549583,549733,549874,550018,550153,550265,550415,550543,550671,550807,550939,551069,551199,551311,551451,569115,569259,569397,570426,570516,570592,570696,570786,570888,570996,571104,571204,571284,571376,571474,571584,571636,571714,571820,571912,572016,572126,572248,572411,572568,589631,589731,589821,589931,590021,590262,590356,590462,590554,630441,630553,630667,630783,630899,630993,631107,631219,631321,631441,631563,631645,631749,631869,631995,632093,632187,632275,632387,632503,632625,632737,632912,633028,633114,633206,633318,633442,633509,633635,633703,633831,633975,634103,634172,634267,634382,634495,634594,634703,634814,634925,635026,635131,635231,635361,635452,635575,635669,635781,635867,635971,636067,636155,636273,636377,636481,636607,636695,636803,636903,636993,637103,637187,637289,637373,637427,637491,637597,637683,637793,637877,637997"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f908cdc45776521b403beeef1508641c\\transformed\\core-1.16.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "527,779,780,799,800,1126,1127,2015,2016,2017,2018,2019,2020,2021,2751,2752,2753,2754,2755,2756,2757,2758,2759,2760,2761,2762,2763,2764,2765,2834,2835,2836,2931,2932,2971,2972,2973,2974,2975,2976,2977,2978,2979,2980,2981,2982,2983,2984,2985,2986,2987,2996,3088,3206,3207,3208,3209,3210,3211,3212,3767,8231,8232,8236,8237,8241,9458,9459", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "26487,38973,39045,40423,40488,64084,64153,104867,104937,105005,105077,105147,105208,105282,161453,161514,161575,161637,161701,161763,161824,161892,161992,162052,162118,162191,162260,162317,162369,168051,168123,168199,173113,173148,175209,175264,175327,175382,175440,175496,175554,175615,175678,175735,175786,175844,175894,175955,176012,176078,176112,176535,182345,192771,192838,192910,192979,193048,193122,193194,247839,551456,551573,551774,551884,552085,638002,638074", "endLines": "527,779,780,799,800,1126,1127,2015,2016,2017,2018,2019,2020,2021,2751,2752,2753,2754,2755,2756,2757,2758,2759,2760,2761,2762,2763,2764,2765,2834,2835,2836,2931,2932,2971,2972,2973,2974,2975,2976,2977,2978,2979,2980,2981,2982,2983,2984,2985,2986,2987,2996,3088,3206,3207,3208,3209,3210,3211,3212,3767,8231,8235,8236,8240,8241,9458,9459", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66", "endOffsets": "26542,39040,39128,40483,40549,64148,64211,104932,105000,105072,105142,105203,105277,105350,161509,161570,161632,161696,161758,161819,161887,161987,162047,162113,162186,162255,162312,162364,162426,168118,168194,168259,173143,173178,175259,175322,175377,175435,175491,175549,175610,175673,175730,175781,175839,175889,175950,176007,176073,176107,176142,176565,182410,192833,192905,192974,193043,193117,193189,193277,247905,551568,551769,551879,552080,552209,638069,638136"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4a649bc6d741f1561035089684f0b179\\transformed\\jetified-AdLib-1.2.7\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "785,884,3238,3333,3335,3441,3698,3721,3779,3792,3816,3822,3904,7373,8521", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "39452,45861,195230,205992,206115,214379,242668,244307,248548,249589,251045,251429,256168,496660,569402", "endLines": "785,884,3238,3333,3335,3441,3698,3721,3779,3792,3821,3823,3918,7376,8539", "endColumns": "41,52,47,57,82,54,116,51,83,63,12,12,12,12,12", "endOffsets": "39489,45909,195273,206045,206193,214429,242780,244354,248627,249648,251424,251506,256942,496872,570360"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9e0a763189144907fb0197c2b097244b\\transformed\\jetified-ui-release\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2837,2838,2839,2840,2841,2842,2843,2844,2845,2846,2847,2848,2849,2850,2851,2852,2853,2854,2855,2856,2857,2858,2859,2860,2861,2862,2863,2864,2865,2866,2867,2868,2873,2875,2876,2926,2928,3014,3239,3240,3292,3293,3318,3429,3431,3635,3647,3710,3711,3743,3765,3766,3772,3773,3777,7377,7559,7562", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "168264,168323,168382,168442,168502,168562,168622,168682,168742,168802,168862,168922,168982,169041,169101,169161,169221,169281,169341,169401,169461,169521,169581,169641,169700,169760,169820,169879,169938,169997,170056,170115,170379,170513,170571,172853,172938,177428,195278,195343,201915,201981,205057,213716,213835,236705,237677,243520,243570,245713,247757,247799,248159,248206,248418,496877,507269,507380", "endLines": "2837,2838,2839,2840,2841,2842,2843,2844,2845,2846,2847,2848,2849,2850,2851,2852,2853,2854,2855,2856,2857,2858,2859,2860,2861,2862,2863,2864,2865,2866,2867,2868,2873,2875,2876,2926,2928,3014,3239,3240,3292,3293,3318,3429,3431,3635,3647,3710,3711,3743,3765,3766,3772,3773,3777,7379,7561,7565", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,41,39,46,35,89,12,12,12", "endOffsets": "168318,168377,168437,168497,168557,168617,168677,168737,168797,168857,168917,168977,169036,169096,169156,169216,169276,169336,169396,169456,169516,169576,169636,169695,169755,169815,169874,169933,169992,170051,170110,170169,170448,170566,170621,172899,172988,177476,195338,195392,201976,202077,205110,213763,213890,236762,237726,243565,243619,245754,247794,247834,248201,248237,248503,496984,507375,507570"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9bed8d4710c38689abca92b67b225ab3\\transformed\\coordinatorlayout-1.1.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "141,11514", "startColumns": "4,4", "startOffsets": "6952,771882", "endLines": "141,11516", "endColumns": "60,12", "endOffsets": "7008,772022"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9ae9e6fb794aab6f23f82d9d2a805731\\transformed\\recyclerview-1.3.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "575,2111,2112,2113,2125,2126,2127,2930", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "28602,111009,111068,111116,111953,112028,112104,173047", "endColumns": "55,58,47,55,74,75,71,65", "endOffsets": "28653,111063,111111,111167,112023,112099,112171,173108"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\60a813200b74c1dc84d254b8806ebd17\\transformed\\transition-1.5.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2922,2923,2956,2962,2963,2997,2998,2999,3000,3001,3002,3003,3004", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "172642,172682,174465,174756,174811,176570,176615,176669,176725,176777,176829,176878,176939", "endColumns": "39,46,42,54,46,44,53,55,51,51,48,60,49", "endOffsets": "172677,172724,174503,174806,174853,176610,176664,176720,176772,176824,176873,176934,176984"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fc35ffdefd2abfa5aac2f445dfc58ee4\\transformed\\jetified-sdp-android-1.1.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "1161,1163,1164,1165,1166,1167,1168,1169,1170,1171,1172,1174,1175,1176,1177,1178,1179,1180,1181,1182,1183,1184,1186,1187,1188,1189,1190,1191,1192,1193,1194,1195,1196,1198,1199,1200,1201,1202,1203,1204,1205,1206,1207,1208,1210,1211,1212,1213,1214,1215,1216,1217,1218,1219,1220,1222,1223,1224,1225,1226,1227,1228,1229,1230,1231,1232,1234,1235,1236,1237,1238,1239,1240,1241,1242,1243,1244,1246,1247,1248,1249,1250,1251,1252,1253,1254,1255,1256,1258,1259,1260,1261,1262,1263,1264,1265,1266,1267,1268,1270,1271,1272,1273,1274,1275,1276,1277,1278,1279,1280,1282,1284,1285,1286,1287,1288,1289,1290,1291,1292,1293,1294,1296,1297,1298,1299,1300,1301,1302,1303,1304,1305,1306,1308,1309,1310,1311,1312,1313,1314,1315,1316,1317,1318,1320,1321,1322,1323,1324,1325,1326,1327,1328,1329,1330,1332,1333,1334,1335,1336,1337,1338,1339,1340,1341,1342,1344,1345,1346,1347,1348,1349,1350,1351,1352,1353,1354,1356,1357,1358,1359,1360,1361,1362,1363,1364,1365,1366,1368,1369,1370,1371,1372,1373,1374,1375,1376,1377,1378,1380,1381,1382,1383,1384,1385,1386,1387,1388,1389,1390,1392,1393,1394,1395,1396,1397,1398,1399,1400,1401,1402,1404,1406,1407,1408,1409,1410,1411,1412,1413,1414,1415,1416,1418,1419,1420,1421,1422,1423,1424,1425,1426,1427,1428,1430,1431,1432,1433,1434,1435,1436,1437,1438,1439,1440,1442,1443,1444,1445,1446,1447,1448,1449,1450,1451,1452,1454,1455,1456,1457,1458,1459,1460,1461,1462,1463,1464,1466,1467,1468,1469,1470,1471,1472,1473,1474,1475,1476,1478,1479,1480,1481,1482,1483,1484,1485,1486,1487,1488,1490,1491,1492,1493,1494,1495,1496,1497,1498,1499,1500,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1526,1528,1529,1530,1531,1532,1533,1534,1535,1536,1537,1538,1540,1541,1542,1543,1544,1545,1546,1547,1548,1549,1550,1552,1553,1554,1555,1556,1557,1558,1559,1560,1561,1562,1564,1565,1566,1567,1568,1569,1570,1571,1572,1573,1574,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1586,1588,1589,1590,1591,1592,1593,1594,1595,1596,1597,1598,1600,1601,1602,1603,1604,1605,1606,1607,1608,1609,1610,1612,1613,1614,1615,1616,1617,1618,1619,1620,1621,1622,1624,1625,1626,1627,1628,1629,1630,1631,1632,1633,1634,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1646,1648,1650,1651,1652,1653,1654,1655,1656,1657,1658,1659,1660,1662,1663,1664,1665,1666,1667,1668,1669,1670,1671,1672,1674,1675,1676,1677,1678,1679,1680,1681,1682,1683,1684,1686,1687,1688,1689,1690,1691,1692,1693,1694,1695,1696,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1710,1711,1712,1713,1714,1715,1716,1717,1718,1719,1720,1722,1723,1724,1725,1726,1727,1728,1729,1730,1731,1732,1734,1735,1736,1737,1738,1739,1740,1741,1742,1743,1744,1746,1747,1748,1749,1750,1751,1752,1753,1754,1755,1756,1758,1759,1760,1761,1762,1763,1764,1765,1766,1767,1768,1770,1772,1773,1775,1777,1779,1781,1783,1785,1787,1789,1791,1793,1795,1797,1799,1801,1803,1805,1807,1809,1811,1813,1815,1817,1819,1821,1823,1825,1827,1829,1831,1833,1835,1837,1839,1841,1843,1845,1847,1849,1851,1853,1855,1857,1859,1861,1862,1863,1864,1865,1866,1867,1868,1869,1870,1871,1872,1873,1874,1875,1876,1877,1878,1879,1880,1881,1882,1883,1884,1885,1886,1887,1888,1889,1890,1891,1892,1893,1894,1895,1896,1897,1898,1899,1900,1901,1902,1903,1904,1905,1906,1907,1908,1909,1910,1911,1912,1913,1914,1915,1916,1917,1918,1919,1920", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "66210,66296,66339,66382,66425,66468,66511,66554,66597,66640,66683,66765,66808,66851,66894,66937,66980,67023,67066,67109,67152,67195,67277,67320,67363,67406,67449,67492,67535,67578,67621,67664,67707,67789,67832,67875,67918,67961,68004,68047,68090,68133,68176,68219,68301,68344,68387,68430,68473,68516,68559,68602,68645,68688,68731,68813,68856,68899,68942,68985,69028,69071,69114,69157,69200,69243,69325,69368,69411,69454,69497,69540,69583,69626,69669,69712,69755,69837,69880,69923,69966,70009,70052,70095,70138,70181,70224,70267,70349,70392,70435,70478,70521,70564,70607,70650,70693,70736,70779,70861,70904,70947,70990,71033,71076,71119,71162,71205,71248,71291,71373,71451,71494,71537,71580,71623,71666,71709,71752,71795,71838,71881,71963,72006,72049,72092,72135,72178,72221,72264,72307,72350,72393,72475,72518,72561,72604,72647,72690,72733,72776,72819,72862,72905,72987,73030,73073,73116,73159,73202,73245,73288,73331,73374,73417,73499,73542,73585,73628,73671,73714,73757,73800,73843,73886,73929,74011,74054,74097,74140,74183,74226,74269,74312,74355,74398,74441,74523,74566,74609,74652,74695,74738,74781,74824,74867,74910,74953,75035,75078,75121,75164,75207,75250,75293,75336,75379,75422,75465,75547,75590,75633,75676,75719,75762,75805,75848,75891,75934,75977,76059,76102,76145,76188,76231,76274,76317,76360,76403,76446,76489,76571,76649,76692,76735,76778,76821,76864,76907,76950,76993,77036,77079,77161,77204,77247,77290,77333,77376,77419,77462,77505,77548,77591,77673,77716,77759,77802,77845,77888,77931,77974,78017,78060,78103,78185,78228,78271,78314,78357,78400,78443,78486,78529,78572,78615,78697,78740,78783,78826,78869,78912,78955,78998,79041,79084,79127,79209,79252,79295,79338,79381,79424,79467,79510,79553,79596,79639,79721,79764,79807,79850,79893,79936,79979,80022,80065,80108,80151,80233,80276,80319,80362,80405,80448,80491,80534,80577,80620,80663,80745,80788,80831,80874,80917,80960,81003,81046,81089,81132,81175,81257,81300,81343,81386,81429,81472,81515,81558,81601,81644,81687,81769,81847,81890,81933,81976,82019,82062,82105,82148,82191,82234,82277,82359,82402,82445,82488,82531,82574,82617,82660,82703,82746,82789,82871,82914,82957,83000,83043,83086,83129,83172,83215,83258,83301,83383,83426,83469,83512,83555,83598,83641,83684,83727,83770,83813,83895,83938,83981,84024,84067,84110,84153,84196,84239,84282,84325,84407,84450,84493,84536,84579,84622,84665,84708,84751,84794,84837,84919,84962,85005,85048,85091,85134,85177,85220,85263,85306,85349,85431,85474,85517,85560,85603,85646,85689,85732,85775,85818,85861,85943,85986,86029,86072,86115,86158,86201,86244,86287,86330,86373,86455,86498,86541,86584,86627,86670,86713,86756,86799,86842,86885,86967,87045,87088,87131,87174,87217,87260,87303,87346,87389,87432,87475,87557,87600,87643,87686,87729,87772,87815,87858,87901,87944,87987,88069,88112,88155,88198,88241,88284,88327,88370,88413,88456,88499,88581,88624,88667,88710,88753,88796,88839,88882,88925,88968,89011,89093,89136,89179,89222,89265,89308,89351,89394,89437,89480,89523,89605,89648,89691,89734,89777,89820,89863,89906,89949,89992,90035,90117,90160,90203,90246,90289,90332,90375,90418,90461,90504,90547,90629,90672,90715,90758,90801,90844,90887,90930,90973,91016,91059,91141,91184,91227,91270,91313,91356,91399,91442,91485,91528,91571,91653,91696,91739,91782,91825,91868,91911,91954,91997,92040,92083,92165,92243,92286,92368,92450,92532,92614,92696,92778,92860,92942,93024,93106,93184,93266,93348,93430,93512,93594,93676,93758,93840,93922,94004,94082,94164,94246,94328,94410,94492,94574,94656,94738,94820,94902,94980,95062,95144,95226,95308,95390,95472,95554,95636,95718,95800,95878,95925,95972,96019,96066,96113,96160,96207,96254,96301,96348,96393,96440,96487,96534,96581,96628,96675,96722,96769,96816,96863,96908,96955,97002,97049,97096,97143,97190,97237,97284,97331,97378,97423,97470,97517,97564,97611,97658,97705,97752,97799,97846,97893,97938,97985,98032,98079,98126,98173,98220,98267,98314,98361,98408,98453,98500,98545,98590,98635", "endColumns": "42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,38,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,38,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,38,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,38,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,38,42,40,40,40,40,40,40,40,40,40,40,38,40,40,40,40,40,40,40,40,40,40,38,40,40,40,40,40,40,40,40,40,40,38,40,40,40,40,40,40,40,40,40,40,38,46,46,46,46,46,46,46,46,46,46,44,46,46,46,46,46,46,46,46,46,46,44,46,46,46,46,46,46,46,46,46,46,44,46,46,46,46,46,46,46,46,46,46,44,46,46,46,46,46,46,46,46,46,46,44,46,44,44,44,44", "endOffsets": "66248,66334,66377,66420,66463,66506,66549,66592,66635,66678,66719,66803,66846,66889,66932,66975,67018,67061,67104,67147,67190,67231,67315,67358,67401,67444,67487,67530,67573,67616,67659,67702,67743,67827,67870,67913,67956,67999,68042,68085,68128,68171,68214,68255,68339,68382,68425,68468,68511,68554,68597,68640,68683,68726,68767,68851,68894,68937,68980,69023,69066,69109,69152,69195,69238,69279,69363,69406,69449,69492,69535,69578,69621,69664,69707,69750,69791,69875,69918,69961,70004,70047,70090,70133,70176,70219,70262,70303,70387,70430,70473,70516,70559,70602,70645,70688,70731,70774,70815,70899,70942,70985,71028,71071,71114,71157,71200,71243,71286,71327,71407,71489,71532,71575,71618,71661,71704,71747,71790,71833,71876,71917,72001,72044,72087,72130,72173,72216,72259,72302,72345,72388,72429,72513,72556,72599,72642,72685,72728,72771,72814,72857,72900,72941,73025,73068,73111,73154,73197,73240,73283,73326,73369,73412,73453,73537,73580,73623,73666,73709,73752,73795,73838,73881,73924,73965,74049,74092,74135,74178,74221,74264,74307,74350,74393,74436,74477,74561,74604,74647,74690,74733,74776,74819,74862,74905,74948,74989,75073,75116,75159,75202,75245,75288,75331,75374,75417,75460,75501,75585,75628,75671,75714,75757,75800,75843,75886,75929,75972,76013,76097,76140,76183,76226,76269,76312,76355,76398,76441,76484,76525,76605,76687,76730,76773,76816,76859,76902,76945,76988,77031,77074,77115,77199,77242,77285,77328,77371,77414,77457,77500,77543,77586,77627,77711,77754,77797,77840,77883,77926,77969,78012,78055,78098,78139,78223,78266,78309,78352,78395,78438,78481,78524,78567,78610,78651,78735,78778,78821,78864,78907,78950,78993,79036,79079,79122,79163,79247,79290,79333,79376,79419,79462,79505,79548,79591,79634,79675,79759,79802,79845,79888,79931,79974,80017,80060,80103,80146,80187,80271,80314,80357,80400,80443,80486,80529,80572,80615,80658,80699,80783,80826,80869,80912,80955,80998,81041,81084,81127,81170,81211,81295,81338,81381,81424,81467,81510,81553,81596,81639,81682,81723,81803,81885,81928,81971,82014,82057,82100,82143,82186,82229,82272,82313,82397,82440,82483,82526,82569,82612,82655,82698,82741,82784,82825,82909,82952,82995,83038,83081,83124,83167,83210,83253,83296,83337,83421,83464,83507,83550,83593,83636,83679,83722,83765,83808,83849,83933,83976,84019,84062,84105,84148,84191,84234,84277,84320,84361,84445,84488,84531,84574,84617,84660,84703,84746,84789,84832,84873,84957,85000,85043,85086,85129,85172,85215,85258,85301,85344,85385,85469,85512,85555,85598,85641,85684,85727,85770,85813,85856,85897,85981,86024,86067,86110,86153,86196,86239,86282,86325,86368,86409,86493,86536,86579,86622,86665,86708,86751,86794,86837,86880,86921,87001,87083,87126,87169,87212,87255,87298,87341,87384,87427,87470,87511,87595,87638,87681,87724,87767,87810,87853,87896,87939,87982,88023,88107,88150,88193,88236,88279,88322,88365,88408,88451,88494,88535,88619,88662,88705,88748,88791,88834,88877,88920,88963,89006,89047,89131,89174,89217,89260,89303,89346,89389,89432,89475,89518,89559,89643,89686,89729,89772,89815,89858,89901,89944,89987,90030,90071,90155,90198,90241,90284,90327,90370,90413,90456,90499,90542,90583,90667,90710,90753,90796,90839,90882,90925,90968,91011,91054,91095,91179,91222,91265,91308,91351,91394,91437,91480,91523,91566,91607,91691,91734,91777,91820,91863,91906,91949,91992,92035,92078,92119,92199,92281,92322,92404,92486,92568,92650,92732,92814,92896,92978,93060,93140,93220,93302,93384,93466,93548,93630,93712,93794,93876,93958,94038,94118,94200,94282,94364,94446,94528,94610,94692,94774,94856,94936,95016,95098,95180,95262,95344,95426,95508,95590,95672,95754,95834,95920,95967,96014,96061,96108,96155,96202,96249,96296,96343,96388,96435,96482,96529,96576,96623,96670,96717,96764,96811,96858,96903,96950,96997,97044,97091,97138,97185,97232,97279,97326,97373,97418,97465,97512,97559,97606,97653,97700,97747,97794,97841,97888,97933,97980,98027,98074,98121,98168,98215,98262,98309,98356,98403,98448,98495,98540,98585,98630,98675"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f1d8c3766b40fb978be1af5d4cc6d27e\\transformed\\navigation-fragment-2.8.9\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1"}, "to": {"startLines": "2766,2945,2965,2966", "startColumns": "4,4,4,4", "startOffsets": "162431,173810,174902,174961", "endColumns": "62,56,58,48", "endOffsets": "162489,173862,174956,175005"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5c5c72c6ff4a7863322da50648a25e99\\transformed\\browser-1.8.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "793,794,795,796,2006,2007,3264,3394,3395,3396", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "40059,40117,40183,40246,104353,104424,198164,210864,210931,211010", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "40112,40178,40241,40303,104419,104491,198227,210926,211005,211074"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\237635df39b25799c092d66a208ce67d\\transformed\\jetified-foundation-release\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "3786,3787", "startColumns": "4,4", "startOffsets": "249189,249245", "endColumns": "55,54", "endOffsets": "249240,249295"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\141e0148a23d0681a92a44c218f71e86\\transformed\\jetified-core-splashscreen-1.0.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "562,636,761,762,763,764,2771,2772,2773,2774,2775,2776,2777,3022,4319,4320,4321,7298,7300,8785,8794,8807", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "27996,31584,37757,37826,37898,37961,162675,162749,162825,162901,162978,163049,163118,177905,283203,283284,283376,492944,493053,588048,588508,589283", "endLines": "562,636,761,762,763,764,2771,2772,2773,2774,2775,2776,2777,3022,4319,4320,4321,7299,7301,8793,8806,8810", "endColumns": "59,58,68,71,62,71,73,75,75,76,70,68,70,67,80,91,92,12,12,12,12,12", "endOffsets": "28051,31638,37821,37893,37956,38028,162744,162820,162896,162973,163044,163113,163184,177968,283279,283371,283464,493048,493169,588503,589278,589551"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0b518e79f85ce070d76134f5e2085a96\\transformed\\jetified-customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "2929,2957", "startColumns": "4,4", "startOffsets": "172993,174508", "endColumns": "53,66", "endOffsets": "173042,174570"}}, {"source": "D:\\Duc\\AndroidStudioProjects\\TJ_BatteryOne\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "11567", "startColumns": "4", "startOffsets": "773898", "endLines": "11569", "endColumns": "12", "endOffsets": "773994"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b572512e02266e069f95737c22215ab9\\transformed\\jetified-media3-exoplayer-1.6.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "3364,3365,3366,3367,3368,3369,3370,3371,3372", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "208446,208516,208578,208643,208707,208784,208849,208939,209023", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "208511,208573,208638,208702,208779,208844,208934,209018,209087"}}, {"source": "D:\\Duc\\AndroidStudioProjects\\TJ_BatteryOne\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "3826,3853,7302,7329,7356,7361,7566,7571,7576,7602,7629,7655,7681,7686,7785,7790,7795,7964,8168,8170,8570,8598,8600,8604,11517,11522,11527,11532,11537,11542,11547,11552,11557,11562", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "251679,253073,493174,494575,495984,496168,507575,507759,507944,509275,510674,512010,513354,513544,520159,520349,520535,532980,545454,545558,572573,574040,574157,574348,772027,772214,772402,772589,772776,772963,773150,773337,773524,773711", "endLines": "3852,3879,7328,7355,7360,7364,7570,7575,7601,7628,7654,7680,7685,7690,7789,7794,7799,7968,8169,8174,8597,8599,8603,8609,11521,11526,11531,11536,11541,11546,11551,11556,11561,11566", "endColumns": "12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12", "endOffsets": "253068,254470,494570,495979,496163,496337,507754,507939,509270,510669,512005,513349,513539,513730,520344,520530,520714,533158,545553,545737,574035,574152,574343,574727,772209,772397,772584,772771,772958,773145,773332,773519,773706,773893"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\347d669cbb58eeb81531bf30e2f5697f\\transformed\\databinding-adapters-8.9.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "2954,2955,2988", "startColumns": "4,4,4", "startOffsets": "174365,174422,176147", "endColumns": "56,42,40", "endOffsets": "174417,174460,176183"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1eebb1a882efcc15d28c2876ceb6fba2\\transformed\\jetified-lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "3009", "startColumns": "4", "startOffsets": "177157", "endColumns": "42", "endOffsets": "177195"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5b80dcbf636dc26335bd1b8e4f16f918\\transformed\\material-1.12.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "24,37,53,57,58,59,60,61,65,66,67,68,69,71,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,151,152,182,184,185,186,187,188,189,190,191,192,193,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,275,277,278,286,287,288,289,290,291,292,294,444,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,524,525,526,561,592,593,601,602,603,604,605,606,607,608,609,610,614,631,632,633,635,645,647,648,658,659,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,691,692,693,694,695,696,697,702,703,715,716,726,729,770,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,928,929,930,931,932,933,934,935,936,937,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,964,965,966,967,968,969,970,971,972,973,974,975,976,977,978,979,980,981,982,983,984,985,986,987,988,989,990,991,992,993,994,995,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,1043,1044,1045,1046,1047,1048,1049,1050,1051,1052,1053,1054,1055,1056,1057,1058,1071,1072,1073,1074,1075,1076,1077,1078,1079,1080,1081,1082,1083,1084,1085,1086,1087,1088,1089,1090,1091,1092,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108,1109,1110,1111,1112,1113,1114,1115,1116,1117,1118,1119,1120,1121,1122,1123,1124,2002,2014,2026,2027,2028,2029,2030,2031,2032,2033,2034,2035,2036,2037,2038,2039,2040,2041,2042,2043,2044,2045,2046,2047,2048,2049,2050,2051,2052,2053,2054,2055,2056,2057,2058,2059,2060,2061,2062,2063,2064,2065,2066,2067,2068,2069,2070,2071,2072,2073,2128,2129,2130,2131,2132,2133,2134,2135,2136,2137,2138,2139,2140,2141,2142,2143,2144,2145,2146,2147,2148,2149,2150,2151,2152,2153,2154,2155,2156,2157,2158,2159,2160,2161,2162,2163,2164,2165,2166,2167,2168,2169,2170,2171,2172,2173,2174,2175,2176,2177,2178,2179,2180,2181,2182,2183,2184,2185,2186,2187,2188,2189,2190,2191,2192,2193,2194,2195,2196,2197,2198,2199,2200,2201,2202,2203,2204,2205,2206,2207,2208,2209,2210,2211,2212,2213,2214,2215,2216,2217,2218,2219,2220,2221,2222,2223,2224,2225,2226,2227,2228,2229,2230,2231,2232,2233,2234,2235,2236,2237,2238,2239,2240,2241,2242,2243,2244,2245,2246,2247,2248,2249,2250,2251,2252,2253,2254,2255,2256,2257,2258,2259,2260,2261,2262,2263,2264,2265,2266,2267,2268,2269,2270,2271,2272,2273,2274,2275,2276,2277,2278,2279,2280,2281,2282,2283,2284,2285,2286,2287,2288,2289,2290,2291,2292,2293,2294,2295,2296,2297,2298,2299,2300,2301,2302,2303,2304,2305,2306,2307,2308,2309,2310,2311,2312,2313,2314,2315,2316,2317,2318,2319,2320,2321,2322,2323,2324,2325,2326,2327,2328,2329,2330,2331,2332,2333,2334,2335,2336,2337,2338,2339,2340,2341,2342,2343,2344,2345,2346,2347,2348,2349,2350,2351,2352,2353,2354,2355,2356,2357,2358,2359,2360,2361,2362,2363,2364,2365,2366,2367,2368,2369,2370,2371,2372,2373,2374,2375,2376,2377,2378,2379,2380,2381,2382,2383,2384,2385,2386,2387,2388,2389,2390,2391,2392,2393,2394,2395,2396,2397,2398,2399,2400,2401,2402,2403,2404,2405,2406,2407,2408,2409,2410,2411,2412,2413,2414,2415,2416,2417,2418,2419,2420,2421,2422,2423,2424,2425,2426,2427,2428,2429,2430,2431,2432,2433,2434,2435,2436,2437,2438,2439,2440,2441,2442,2443,2444,2445,2446,2447,2448,2449,2450,2451,2452,2453,2454,2455,2456,2457,2458,2459,2460,2461,2462,2463,2464,2465,2466,2467,2468,2469,2470,2471,2472,2473,2474,2475,2476,2477,2478,2479,2480,2481,2482,2483,2484,2485,2486,2487,2488,2489,2490,2491,2492,2493,2494,2495,2496,2497,2498,2499,2500,2501,2502,2503,2504,2505,2506,2507,2508,2509,2510,2511,2512,2513,2514,2515,2516,2517,2518,2519,2520,2521,2522,2523,2524,2525,2526,2527,2528,2529,2530,2531,2532,2533,2534,2535,2536,2537,2538,2539,2540,2541,2542,2543,2544,2545,2546,2547,2548,2549,2550,2551,2552,2553,2554,2555,2556,2557,2558,2559,2560,2561,2562,2563,2564,2565,2566,2567,2568,2569,2570,2571,2572,2573,2574,2575,2576,2577,2578,2579,2580,2581,2582,2583,2584,2585,2586,2587,2588,2589,2590,2591,2592,2593,2594,2595,2596,2597,2598,2599,2600,2601,2602,2603,2604,2605,2606,2607,2608,2609,2610,2611,2612,2613,2614,2615,2616,2617,2618,2619,2620,2621,2622,2623,2624,2625,2626,2627,2628,2629,2630,2631,2632,2633,2634,2635,2636,2637,2638,2639,2640,2641,2642,2643,2644,2645,2646,2647,2648,2649,2650,2651,2652,2653,2654,2655,2656,2657,2658,2659,2660,2661,2662,2663,2664,2665,2666,2667,2668,2669,2670,2671,2672,2673,2674,2675,2676,2677,2678,2679,2680,2681,2682,2683,2684,2685,2686,2687,2688,2689,2690,2691,2692,2693,2694,2695,2696,2697,2698,2699,2700,2701,2702,2703,2704,2705,2706,2707,2708,2709,2710,2711,2712,2713,2714,2715,2716,2717,2718,2719,2720,2721,2722,2723,2724,2725,2726,2727,2728,2729,2730,2731,2732,2733,2734,2735,2736,2737,2738,2739,2740,2741,2742,2743,2744,2745,2746,2747,2748,2749,2750,2832,2833,2934,2935,2936,2938,2939,2940,2941,2942,2943,2946,2947,2948,2949,2950,2951,2961,2964,2967,2968,2989,2990,2991,2992,2993,2994,2995,3006,3017,3018,3023,3024,3028,3029,3030,3031,3032,3033,3034,3035,3036,3037,3038,3039,3040,3041,3042,3043,3044,3045,3046,3047,3048,3049,3050,3051,3052,3053,3054,3055,3056,3057,3059,3060,3061,3062,3063,3064,3065,3066,3067,3068,3069,3070,3071,3072,3073,3074,3075,3076,3077,3078,3079,3080,3081,3082,3083,3084,3085,3086,3087,3097,3164,3195,3196,3197,3198,3199,3200,3216,3217,3218,3236,3329,3334,3391,3392,3393,3422,3426,3435,3450,3451,3452,3453,3454,3455,3456,3457,3458,3459,3460,3461,3462,3463,3464,3465,3531,3532,3533,3534,3535,3536,3537,3538,3539,3540,3541,3542,3543,3544,3545,3546,3547,3548,3549,3550,3551,3552,3555,3556,3557,3558,3559,3560,3561,3562,3563,3564,3567,3570,3573,3574,3575,3576,3577,3578,3579,3580,3581,3582,3583,3584,3585,3586,3587,3588,3589,3590,3591,3592,3593,3594,3595,3596,3597,3598,3599,3600,3601,3602,3603,3604,3605,3606,3607,3608,3609,3610,3611,3612,3613,3614,3615,3616,3617,3618,3619,3620,3621,3622,3679,3680,3681,3682,3683,3737,3740,3741,3755,3756,3883,3887,3891,3895,3899,3900,3958,3966,3973,4143,4146,4156,4165,4174,4243,4244,4245,4246,4252,4253,4254,4255,4256,4257,4263,4264,4265,4266,4267,4272,4273,4277,4278,4284,4288,4289,4290,4291,4301,4302,4303,4307,4308,4314,4318,4391,4394,4395,4400,4401,4404,4405,4406,4407,4671,4678,4939,4945,5209,5216,5477,5483,5546,5628,5680,5762,5824,5906,5970,6022,6104,6112,6118,6129,6133,6137,6150,6925,6941,6948,6954,6971,6984,7004,7021,7030,7035,7042,7062,7075,7092,7098,7104,7111,7115,7121,7135,7138,7148,7149,7150,7198,7202,7206,7210,7211,7212,7215,7231,7238,7252,7297,7691,7697,7701,7705,7710,7717,7723,7724,7727,7731,7736,7749,7753,7758,7763,7768,7771,7774,7777,7781,7939,7940,7941,7942,8024,8025,8026,8027,8028,8029,8030,8031,8032,8033,8034,8035,8036,8037,8038,8039,8040,8041,8042,8046,8050,8054,8058,8062,8066,8070,8071,8072,8073,8074,8075,8076,8077,8081,8085,8086,8090,8091,8094,8098,8101,8104,8107,8111,8114,8117,8121,8125,8129,8133,8136,8137,8138,8139,8142,8146,8149,8152,8155,8158,8161,8164,8242,8245,8246,8249,8252,8253,8256,8257,8258,8262,8263,8268,8275,8282,8289,8296,8303,8310,8317,8324,8331,8340,8349,8358,8365,8374,8383,8386,8389,8390,8391,8392,8393,8394,8395,8396,8397,8398,8399,8400,8401,8405,8410,8415,8418,8419,8420,8421,8422,8430,8438,8439,8447,8451,8459,8467,8475,8483,8491,8492,8500,8508,8509,8512,8610,8612,8617,8619,8624,8628,8640,8641,8642,8643,8647,8651,8652,8656,8657,8658,8659,8660,8661,8662,8663,8664,8665,8666,8670,8671,8672,8673,8677,8678,8679,8680,8684,8688,8689,8693,8694,8695,8700,8701,8702,8703,8704,8705,8706,8707,8708,8709,8710,8711,8712,8713,8714,8715,8716,8717,8718,8719,8720,8724,8725,8726,8732,8733,8737,8739,8740,8745,8746,8747,8748,8749,8750,8754,8755,8756,8762,8763,8767,8769,8773,8777,8781,8823,8824,8825,8826,8829,8832,8835,8838,8841,8846,8850,8853,8854,8859,8863,8868,8874,8880,8885,8889,8894,8898,8902,8943,8944,8945,8946,8947,8951,8952,8953,8954,8958,8962,8966,8970,8974,8978,8982,8986,8992,8993,9034,9048,9053,9079,9086,9089,9100,9105,9108,9111,9166,9172,9173,9176,9179,9182,9185,9188,9191,9194,9198,9201,9202,9203,9211,9219,9222,9227,9232,9237,9242,9246,9250,9251,9259,9260,9261,9262,9263,9271,9276,9281,9282,9283,9284,9309,9315,9320,9323,9327,9330,9334,9344,9347,9352,9355,9359,9460,9468,9482,9495,9499,9514,9525,9528,9539,9544,9548,9583,9584,9585,9597,9605,9613,9621,9629,9649,9652,9679,9684,9704,9707,9710,9717,9730,9739,9742,9762,9772,9776,9780,9793,9797,9801,9805,9811,9815,9832,9840,9844,9848,9852,9855,9859,9863,9867,9877,9884,9891,9895,9921,9931,9956,9965,9985,9995,9999,10009,10034,10044,10047,10054,10061,10068,10069,10070,10071,10072,10079,10083,10089,10095,10096,10109,10110,10111,10114,10117,10120,10123,10126,10129,10132,10135,10138,10141,10144,10147,10150,10153,10156,10159,10162,10165,10168,10171,10174,10175,10183,10191,10192,10205,10215,10219,10224,10229,10233,10236,10240,10244,10247,10251,10254,10258,10263,10268,10271,10278,10282,10286,10295,10300,10305,10306,10310,10313,10317,10330,10335,10343,10347,10351,10368,10372,10377,10395,10402,10406,10436,10439,10442,10445,10448,10451,10454,10473,10479,10487,10494,10506,10514,10519,10527,10531,10549,10556,10572,10576,10584,10587,10592,10593,10594,10595,10599,10603,10607,10611,10646,10649,10653,10657,10691,10694,10698,10702,10711,10717,10720,10730,10734,10735,10742,10746,10753,10754,10755,10758,10763,10768,10769,10773,10788,10807,10811,10812,10824,10834,10835,10847,10852,10876,10879,10885,10888,10897,10905,10909,10912,10915,10918,10922,10925,10942,10946,10949,10964,10967,10975,10980,10987,10992,10993,10998,10999,11005,11011,11017,11049,11060,11077,11084,11088,11091,11104,11113,11117,11122,11126,11130,11134,11138,11142,11146,11150,11155,11158,11170,11175,11184,11187,11194,11195,11199,11208,11214,11218,11219,11223,11244,11250,11254,11258,11259,11277,11278,11279,11280,11281,11286,11289,11290,11296,11297,11309,11321,11328,11329,11334,11339,11340,11344,11358,11363,11369,11375,11381,11386,11392,11398,11399,11405,11420,11425,11434,11443,11446,11460,11465,11476,11480,11489,11498,11499,11506,16239,16240,16241,16242,16243,16244,16245,16246,16247,16248,16249,16250,16251,16252,16253,16254,16255,16256,16257,16258,16259,16260,16261,16262,16263,16264,16265,16266,16267,16268,16269,16270,16271,16272,16273,16274,16275,16276,16277,16278,16279,16280,16281,16282,16283,16284,16285,16286,16287,16288,16289,16290,16291,16292,16293,16294,16295,16296,16297,16298,16299,16300,16301,16302,16303,16304,16305,16306,16307,16308,16309,16310,16311,16312,16313,16314,16315,16316,16317,16318,16319,16320,16321,16322,16323,16324,16325,16326,16327,16328,16329,16330,16331,16332,16333,16334,16335,16336,16337,16338,16339,16340,16341,16342,16343,16344,16345,16346,16347,16348,16349,16350,16351,16352,16353,16354,16355,16356,16357,16358,16359,16360,16361,16362,16363,16364,16365,16366,16367,16368,16369,16370,16371,16372,16373,16374,16375,16376,16377,16378,16379,16380,16381,16382,16383,16384,16385,16386,16387,16388,16389,16390,16391,16392,16393,16394,16395,16396,16397,16398,16399,16400,16401,16402,16403,16404,16405,16406,16407,16408,16409,16410,16411,16412,16413,16414,16415,16416,16417,16418,16419,16420,16421,16422,16423,16424,16425,16426,16427,16428,16429,16430,16431,16432,16433,16434,16435,16436,16437,16438,16439,16440,16441,16442,16443,16444,16445,16446,16447,16448,16449,16450,16451,16452,16453,16454,16455,16456,16457,16458,16459,16460,16461,16462,16463,16464,16465,16466,16467,16468,16469,16470,16471,16472,16473,16474,16475,16476,16477,16478,16479,16480,16481,16482,16483,16484,16485,16486,16487,16488,16489,16490,16491,16492,16493,16494,16495,16496,16497,16498,16499,16500,16501,16502,16503,16504,16505,16506,16507,16508,16509,16510,16511,16512,16513,16514,16515,16516,16517,16518,16519,16520,16521,16522,16523,16524,16525,16526,16527,16528,16529,16530,16531,16532,16533,16534,16535,16536,16537,16538,16539,16540,16541,16542,16543,16544,16545,16546,16547,16548,16549,16550,16551,16552,16553,16554,16555,16556,16557,16558,16559,16560,16561,16562,16563,16564,16565,16566,16567,16568,16569,16570,16571,16572,16573,16574,16575,16576,16577,16578,16579,16580,16581,16582,16583,16584,16585,16586,16587,16588,16589,16590,16591,16592,16593,16594,16595,16596,16597,16598,16599,16600,16601,16602,16603,16604,16605,16606,16607,16608,16609,16610,16611,16612,16613", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "967,1467,2095,2293,2349,2409,2470,2535,2739,2789,2839,2892,2950,3049,3419,3467,3538,3610,3682,3755,3822,3871,3925,3962,4013,4073,4120,4176,4225,4283,4337,4398,4454,4505,4565,4621,4684,4733,4789,4845,4895,4954,5009,5071,5118,5172,5228,5280,5335,5389,5443,5497,5546,5604,5658,5715,5771,5818,5871,5927,5987,6050,6109,6171,6221,6275,6329,6377,6434,6487,7400,7454,8785,8896,8958,9014,9074,9127,9188,9267,9348,9420,9499,9634,9710,9788,9857,9933,10010,10081,10154,10230,10308,10377,10453,10530,10594,10665,13475,13571,13624,13958,14025,14078,14130,14180,14238,14303,14416,21357,21487,21553,21611,21680,21738,21807,21877,21950,22024,22092,22159,22229,22295,22368,22428,22504,22564,22624,22699,22767,22833,22901,22961,23020,23077,23143,23205,23262,23330,23403,23473,23535,23596,23664,23726,23796,23865,23921,23980,24042,24104,24171,24228,24289,24350,24411,24472,24528,24584,24640,24696,24754,24812,24870,24928,24985,25042,25099,25156,25215,25274,25332,25415,25498,25571,25625,25694,25750,25831,25912,25983,26318,26371,26429,27938,29256,29302,29765,29819,29889,29959,30024,30090,30155,30223,30292,30360,30490,31319,31378,31436,31534,31979,32075,32121,32569,32625,32719,32777,32835,32897,32960,33022,33081,33141,33206,33272,33337,33399,33461,33523,33585,33647,33709,33775,33842,33908,33971,34035,34098,34166,34227,34289,34351,34414,34478,34594,34658,34736,34795,34861,34941,35002,35271,35329,35820,35865,36332,36497,38349,42113,42187,42258,42324,42398,42467,42538,42611,42682,42750,42823,42899,42969,43047,43115,43181,43242,43311,43375,43441,43509,43575,43638,43706,43777,43842,43915,43978,44059,44123,44189,44259,44329,44399,44469,46068,46125,46183,46242,46302,46361,46420,46479,46538,46597,46656,46715,46774,46833,46892,46952,47013,47075,47136,47197,47258,47319,47380,47441,47501,47562,47623,47683,47744,47805,47866,47927,47988,48049,48110,48171,48232,48293,48354,48422,48491,48561,48630,48699,48768,48837,48906,48975,49044,49113,49182,49251,49311,49372,49434,49495,49556,49617,49678,49739,49800,49861,49922,49983,50044,50106,50169,50233,50296,50359,50422,50485,50548,50611,50674,50737,50800,50863,50924,50986,51049,51111,51173,51235,51297,51359,51421,51483,51545,51607,51669,51726,51812,51892,51982,52077,52169,52261,52351,52434,52527,52614,52711,52802,52903,52990,53093,53182,53281,53373,53473,53557,53651,53739,53837,53920,54011,54105,54204,54306,54404,54504,54591,54691,54777,54873,54961,55042,55133,55229,55322,55415,55506,55591,55685,55774,55872,55965,56067,56155,56259,56350,56450,56543,56644,56729,56824,56913,57012,57097,57189,57284,57384,57487,57586,57689,57778,57879,57966,58063,58151,58247,58339,58439,58529,58627,58712,58801,58890,58983,59070,59833,59899,59975,60044,60123,60196,60276,60356,60433,60501,60579,60655,60726,60807,60880,60963,61038,61123,61196,61277,61358,61432,61516,61586,61664,61734,61814,61892,61964,62046,62116,62193,62273,62358,62446,62530,62617,62691,62769,62847,62918,62999,63090,63173,63269,63367,63474,63539,63605,63658,63734,63800,63887,63963,104129,104812,105657,105711,105790,105868,105941,106006,106069,106135,106206,106277,106347,106409,106478,106544,106604,106671,106738,106794,106845,106898,106950,107004,107075,107138,107197,107259,107318,107391,107458,107528,107588,107651,107726,107798,107894,107965,108021,108092,108149,108206,108272,108336,108407,108464,108517,108580,108632,108690,112176,112245,112311,112370,112453,112512,112569,112636,112706,112780,112842,112911,112981,113080,113177,113276,113362,113448,113529,113604,113693,113784,113868,113927,113973,114039,114096,114163,114220,114302,114367,114433,114556,114640,114761,114826,114888,114986,115060,115143,115232,115296,115375,115449,115511,115607,115672,115731,115787,115843,115903,116010,116057,116117,116178,116242,116303,116363,116421,116464,116513,116565,116616,116668,116717,116766,116831,116897,116957,117018,117074,117133,117182,117230,117288,117345,117447,117504,117579,117627,117678,117740,117805,117857,117931,117994,118057,118125,118175,118237,118297,118354,118414,118463,118531,118637,118739,118808,118879,118935,118984,119084,119155,119265,119356,119438,119536,119592,119693,119803,119902,119965,120071,120148,120260,120387,120499,120626,120696,120810,120941,121038,121106,121224,121327,121445,121506,121580,121647,121752,121874,121948,122015,122125,122224,122297,122394,122516,122634,122752,122813,122935,123052,123120,123226,123328,123408,123479,123575,123642,123716,123790,123876,123966,124044,124121,124221,124292,124413,124534,124598,124723,124797,124921,125045,125112,125221,125349,125461,125540,125618,125719,125790,125912,126034,126099,126225,126337,126443,126511,126610,126714,126777,126843,126927,127040,127153,127271,127349,127421,127557,127693,127778,127918,128056,128194,128336,128418,128504,128581,128654,128763,128874,129002,129130,129262,129392,129522,129656,129745,129807,129903,129970,130087,130208,130305,130387,130474,130561,130692,130823,130958,131035,131112,131223,131337,131411,131520,131632,131699,131772,131837,131939,132035,132139,132207,132272,132366,132438,132548,132654,132727,132818,132920,133023,133118,133225,133330,133452,133574,133700,133759,133817,133941,134065,134193,134311,134429,134551,134637,134734,134868,135002,135082,135220,135352,135484,135620,135695,135771,135874,135948,136061,136142,136199,136260,136319,136379,136437,136498,136556,136606,136655,136722,136781,136840,136889,136960,137044,137114,137185,137265,137334,137397,137465,137531,137599,137664,137730,137807,137885,137991,138097,138193,138322,138411,138538,138604,138674,138760,138826,138909,138983,139081,139177,139273,139371,139480,139575,139664,139726,139786,139851,139908,139989,140043,140100,140197,140307,140368,140483,140604,140699,140791,140884,140940,140999,141048,141140,141189,141243,141297,141351,141405,141459,141514,141624,141734,141842,141952,142062,142172,142282,142390,142496,142600,142704,142808,142903,142998,143091,143184,143288,143394,143498,143602,143695,143788,143881,143974,144082,144188,144294,144400,144497,144592,144687,144782,144888,144994,145100,145206,145304,145399,145495,145592,145657,145761,145819,145883,145944,146006,146066,146131,146193,146261,146319,146382,146445,146512,146587,146660,146726,146778,146831,146883,146940,147024,147119,147204,147285,147365,147442,147521,147598,147672,147746,147817,147897,147969,148044,148109,148170,148230,148305,148379,148452,148522,148594,148664,148737,148801,148871,148917,148986,149038,149123,149206,149263,149329,149396,149462,149543,149618,149674,149727,149788,149846,149896,149945,149994,150043,150105,150157,150202,150283,150334,150388,150441,150495,150546,150595,150661,150712,150773,150834,150896,150946,150987,151064,151123,151182,151241,151302,151358,151414,151481,151542,151607,151662,151727,151796,151864,151942,152011,152071,152142,152216,152281,152353,152423,152490,152574,152643,152710,152780,152843,152910,152978,153061,153140,153230,153307,153375,153442,153520,153577,153634,153702,153768,153824,153884,153943,153997,154047,154097,154145,154207,154258,154331,154411,154491,154555,154622,154693,154751,154812,154878,154937,155004,155064,155124,155187,155255,155316,155383,155461,155531,155580,155637,155706,155767,155855,155943,156031,156119,156206,156293,156380,156467,156525,156599,156669,156725,156796,156861,156923,156998,157071,157161,157227,157293,157354,157418,157480,157538,157609,157692,157751,157822,157888,157953,158014,158073,158144,158210,158275,158358,158434,158509,158590,158650,158719,158789,158858,158913,158969,159025,159086,159144,159200,159259,159313,159368,159430,159487,159581,159650,159751,159802,159872,159935,159991,160049,160108,160162,160248,160332,160402,160471,160541,160656,160777,160844,160911,160986,161053,161112,161166,161220,161274,161327,161379,167774,167911,173230,173279,173329,173420,173468,173524,173582,173644,173699,173867,173938,174002,174061,174123,174189,174713,174858,175010,175055,176188,176239,176286,176331,176382,176433,176484,177021,177607,177673,177973,178036,178332,178389,178443,178498,178556,178611,178670,178726,178795,178864,178933,179003,179066,179129,179192,179255,179320,179385,179450,179515,179578,179642,179706,179770,179821,179899,179977,180048,180120,180193,180338,180404,180470,180538,180606,180672,180739,180813,180876,180933,180993,181058,181125,181190,181247,181308,181366,181470,181580,181689,181793,181871,181936,182003,182069,182139,182186,182238,182288,182982,187741,191357,191488,191672,191850,192088,192277,193462,193560,193675,195111,205746,206050,210461,210550,210707,213140,213429,214063,215428,215615,215711,215801,215897,215987,216153,216276,216399,216569,216675,216790,216905,217007,217113,217230,221960,222042,222215,222383,222531,222690,222845,223018,223135,223252,223420,223532,223646,223818,223994,224152,224285,224397,224543,224695,224827,224970,225222,225400,225536,225632,225768,225863,226030,226123,226215,226402,226558,226736,226900,227082,227399,227581,227763,227953,228185,228375,228552,228714,228871,228981,229164,229301,229505,229689,229873,230033,230191,230375,230602,230805,230976,231196,231418,231573,231773,231957,232060,232250,232391,232556,232727,232927,233131,233333,233498,233703,233902,234101,234298,234389,234538,234688,234772,234921,235066,235218,235359,235525,240891,240969,241270,241436,241591,245145,245303,245467,246613,246836,254749,255026,255298,255576,255821,255883,259490,259941,260397,271534,271682,272196,272633,273067,277407,277492,277613,277712,278117,278214,278331,278418,278541,278642,279048,279147,279266,279359,279466,279809,279916,280161,280282,280691,280939,281039,281144,281263,281772,281919,282038,282289,282422,282837,283091,288572,288819,288944,289352,289473,289701,289822,289955,290102,310824,311316,331787,332211,352978,353472,373988,374414,379255,384672,388763,394194,398936,404313,408297,412289,417680,418227,418660,419416,419646,419889,421056,469226,470130,470714,471187,472617,473361,474554,475608,476086,476379,476762,478277,479042,480185,480626,481067,481663,481937,482348,483364,483542,484295,484432,484523,486717,486983,487305,487515,487624,487743,487927,489045,489515,490266,492849,513735,514111,514339,514595,514854,515430,515784,515906,516045,516337,516597,517525,517811,518214,518616,518959,519171,519372,519585,519874,531321,531394,531481,531566,536418,536530,536636,536759,536891,537014,537144,537268,537401,537532,537657,537774,537894,538026,538154,538268,538386,538499,538620,538808,538995,539176,539359,539543,539708,539890,540010,540130,540238,540348,540460,540568,540678,540843,541009,541161,541326,541427,541547,541718,541879,542042,542203,542370,542489,542606,542786,542968,543149,543332,543487,543632,543754,543889,544052,544245,544371,544523,544665,544835,544991,545163,552214,552409,552501,552674,552836,552931,553100,553194,553283,553526,553615,553908,554324,554744,555165,555591,556008,556424,556841,557259,557673,558143,558616,559088,559499,559970,560442,560632,560838,560944,561052,561158,561270,561384,561496,561610,561726,561840,561948,562058,562166,562428,562807,563211,563358,563466,563576,563684,563798,564207,564621,564737,565155,565396,565826,566261,566671,567093,567503,567625,568034,568450,568572,568790,574732,574800,575144,575224,575580,575730,576272,576348,576460,576550,576812,577077,577185,577337,577445,577521,577633,577723,577825,577933,578041,578141,578249,578334,578500,578604,578732,578819,578986,579064,579178,579270,579534,579801,579911,580064,580174,580258,580647,580745,580853,580947,581077,581185,581307,581443,581551,581671,581805,581927,582055,582197,582323,582463,582589,582707,582839,582937,583047,583347,583459,583577,584041,584157,584460,584586,584682,585083,585193,585317,585455,585565,585687,585999,586123,586253,586729,586857,587172,587310,587472,587688,587844,590559,590627,590711,590815,591018,591207,591408,591601,591806,592119,592331,592497,592613,592859,593075,593388,593814,594276,594513,594665,594925,595069,595211,598443,598557,598677,598793,598887,599208,599307,599425,599526,599805,600090,600369,600651,600904,601163,601416,601672,602096,602172,605422,606777,607221,609075,609650,609858,610868,611248,611414,611555,616575,617001,617113,617248,617401,617598,617769,617952,618127,618314,618586,618744,618828,618932,619419,619975,620133,620352,620583,620806,621041,621263,621529,621667,622266,622380,622518,622630,622754,623325,623820,624366,624511,624604,624696,626623,627193,627491,627680,627886,628079,628289,629173,629318,629710,629868,630085,638141,638573,639448,640068,640265,641213,641978,642101,642874,643095,643295,645272,645372,645462,646148,646901,647666,648429,649204,650417,650582,652195,652516,653579,653789,653959,654529,655424,656057,656223,657709,658325,658561,658782,659740,660005,660270,660517,660931,661167,662452,662901,663088,663337,663579,663755,663996,664229,664454,665049,665524,666048,666309,667660,668135,669361,669831,670879,671331,671575,672032,673277,673760,673910,674465,674917,675317,675470,675615,675758,675828,676256,676544,677048,677557,677673,678575,678697,678809,678986,679252,679522,679788,680056,680312,680572,680828,681086,681338,681594,681846,682100,682332,682568,682820,683076,683328,683582,683814,684048,684160,684812,685267,685391,686483,687298,687494,687818,688207,688559,688800,689014,689313,689505,689820,690027,690373,690673,691074,691293,691706,691943,692313,693037,693392,693661,693801,694055,694199,694476,695468,695877,696509,696855,697223,698297,698660,699060,700568,701153,701471,704006,704200,704418,704644,704856,705055,705262,706466,706761,707318,707708,708340,708817,709062,709549,709795,710991,711388,712394,712616,713039,713230,713609,713697,713805,713913,714226,714551,714870,715201,717904,718092,718353,718602,721186,721378,721643,721896,722428,722836,723035,723619,723854,723978,724390,724604,725006,725109,725239,725414,725666,725862,726002,726196,727207,728276,728564,728694,729471,730128,730274,730980,731218,732758,732908,733325,733490,734176,734646,734842,734933,735017,735161,735395,735562,736490,736776,736936,737551,737710,738038,738265,738777,739139,739218,739557,739662,740027,740398,740759,742633,743262,744338,744762,745015,745167,746215,746952,747155,747401,747648,747866,748108,748429,748693,748998,749221,749532,749721,750436,750705,751199,751425,751865,752024,752308,753053,753418,753723,753881,754119,755438,755836,756064,756284,756426,757716,757822,757952,758090,758214,758502,758671,758771,759056,759170,760053,760808,761247,761371,761617,761810,761944,762135,762914,763132,763423,763702,764019,764241,764536,764819,764923,765264,766080,766396,766957,767463,767668,768454,768859,769520,769709,770260,770826,770946,771348,925001,925096,925189,925252,925334,925427,925520,925607,925705,925796,925887,925975,926059,926155,926255,926361,926464,926565,926669,926775,926874,926980,927082,927189,927298,927409,927540,927660,927776,927894,927993,928100,928216,928335,928463,928552,928647,928724,928813,928904,928997,929071,929168,929263,929361,929460,929564,929660,929762,929865,929965,930068,930153,930254,930352,930442,930537,930624,930730,930832,930926,931017,931111,931187,931279,931368,931471,931582,931665,931751,931846,931943,932039,932127,932228,932329,932432,932538,932636,932733,932828,932926,933029,933129,933232,933337,933455,933571,933666,933759,933844,933940,934034,934126,934209,934313,934418,934518,934619,934724,934824,934925,935024,935126,935220,935327,935429,935532,935625,935721,935823,935926,936022,936124,936227,936324,936427,936525,936629,936734,936831,936939,937053,937168,937276,937390,937505,937607,937712,937820,937930,938046,938163,938258,938355,938454,938559,938665,938764,938869,938975,939075,939181,939282,939389,939508,939607,939712,939814,939916,940016,940119,940214,940318,940403,940507,940611,940709,940813,940919,941017,941122,941220,941333,941427,941516,941605,941688,941779,941862,941960,942050,942146,942235,942329,942417,942513,942598,942706,942807,942908,943006,943112,943203,943302,943399,943497,943593,943686,943796,943894,943989,944099,944191,944291,944390,944477,944581,944686,944785,944892,944999,945098,945207,945299,945410,945521,945632,945736,945851,945967,946094,946214,946309,946404,946501,946600,946692,946791,946883,946982,947068,947162,947265,947361,947464,947560,947663,947760,947858,947961,948054,948144,948245,948328,948419,948504,948596,948699,948794,948890,948983,949077,949156,949263,949354,949453,949546,949649,949753,949854,949955,950059,950153,950257,950361,950474,950580,950686,950794,950911,951012,951120,951220,951323,951428,951535,951631,951710,951800,951884,951976,952049,952141,952230,952322,952407,952504,952597,952692,952791,952888,952979,953070,953162,953257,953364,953472,953574,953671,953768,953861,953948,954032,954129,954226,954319,954406,954497,954596,954695,954790,954879,954960,955059,955163,955260,955365,955462,955546,955645,955749,955846,955951,956048,956146,956247,956353,956452,956559,956658,956757,956848,956937,957026,957108,957201,957292,957403,957504,957604,957716,957829,957927,958035,958129,958229,958318,958410,958521,958631,958726,958842,958968,959094,959213,959341,959466,959591,959709,959836,959945,960054,960167,960290,960413,960529,960654,960751,960859,960981,961097,961213,961322,961410,961511,961600,961701,961788,961876,961973,962065,962171,962271,962347", "endLines": "24,37,53,57,58,59,60,61,65,66,67,68,69,71,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,151,152,182,184,185,186,187,188,189,190,191,192,193,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,275,277,278,286,287,288,289,290,291,292,294,444,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,519,524,525,526,561,592,593,601,602,603,604,605,606,607,608,609,613,614,631,632,633,635,645,647,648,658,659,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,691,692,693,694,695,696,697,702,703,715,716,726,729,770,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,928,929,930,931,932,933,934,935,936,937,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,964,965,966,967,968,969,970,971,972,973,974,975,976,977,978,979,980,981,982,983,984,985,986,987,988,989,990,991,992,993,994,995,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,1043,1044,1045,1046,1047,1048,1049,1050,1051,1052,1053,1054,1055,1056,1057,1058,1071,1072,1073,1074,1075,1076,1077,1078,1079,1080,1081,1082,1083,1084,1085,1086,1087,1088,1089,1090,1091,1092,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108,1109,1110,1111,1112,1113,1114,1115,1116,1117,1118,1119,1120,1121,1122,1123,1124,2002,2014,2026,2027,2028,2029,2030,2031,2032,2033,2034,2035,2036,2037,2038,2039,2040,2041,2042,2043,2044,2045,2046,2047,2048,2049,2050,2051,2052,2053,2054,2055,2056,2057,2058,2059,2060,2061,2062,2063,2064,2065,2066,2067,2068,2069,2070,2071,2072,2073,2128,2129,2130,2131,2132,2133,2134,2135,2136,2137,2138,2139,2140,2141,2142,2143,2144,2145,2146,2147,2148,2149,2150,2151,2152,2153,2154,2155,2156,2157,2158,2159,2160,2161,2162,2163,2164,2165,2166,2167,2168,2169,2170,2171,2172,2173,2174,2175,2176,2177,2178,2179,2180,2181,2182,2183,2184,2185,2186,2187,2188,2189,2190,2191,2192,2193,2194,2195,2196,2197,2198,2199,2200,2201,2202,2203,2204,2205,2206,2207,2208,2209,2210,2211,2212,2213,2214,2215,2216,2217,2218,2219,2220,2221,2222,2223,2224,2225,2226,2227,2228,2229,2230,2231,2232,2233,2234,2235,2236,2237,2238,2239,2240,2241,2242,2243,2244,2245,2246,2247,2248,2249,2250,2251,2252,2253,2254,2255,2256,2257,2258,2259,2260,2261,2262,2263,2264,2265,2266,2267,2268,2269,2270,2271,2272,2273,2274,2275,2276,2277,2278,2279,2280,2281,2282,2283,2284,2285,2286,2287,2288,2289,2290,2291,2292,2293,2294,2295,2296,2297,2298,2299,2300,2301,2302,2303,2304,2305,2306,2307,2308,2309,2310,2311,2312,2313,2314,2315,2316,2317,2318,2319,2320,2321,2322,2323,2324,2325,2326,2327,2328,2329,2330,2331,2332,2333,2334,2335,2336,2337,2338,2339,2340,2341,2342,2343,2344,2345,2346,2347,2348,2349,2350,2351,2352,2353,2354,2355,2356,2357,2358,2359,2360,2361,2362,2363,2364,2365,2366,2367,2368,2369,2370,2371,2372,2373,2374,2375,2376,2377,2378,2379,2380,2381,2382,2383,2384,2385,2386,2387,2388,2389,2390,2391,2392,2393,2394,2395,2396,2397,2398,2399,2400,2401,2402,2403,2404,2405,2406,2407,2408,2409,2410,2411,2412,2413,2414,2415,2416,2417,2418,2419,2420,2421,2422,2423,2424,2425,2426,2427,2428,2429,2430,2431,2432,2433,2434,2435,2436,2437,2438,2439,2440,2441,2442,2443,2444,2445,2446,2447,2448,2449,2450,2451,2452,2453,2454,2455,2456,2457,2458,2459,2460,2461,2462,2463,2464,2465,2466,2467,2468,2469,2470,2471,2472,2473,2474,2475,2476,2477,2478,2479,2480,2481,2482,2483,2484,2485,2486,2487,2488,2489,2490,2491,2492,2493,2494,2495,2496,2497,2498,2499,2500,2501,2502,2503,2504,2505,2506,2507,2508,2509,2510,2511,2512,2513,2514,2515,2516,2517,2518,2519,2520,2521,2522,2523,2524,2525,2526,2527,2528,2529,2530,2531,2532,2533,2534,2535,2536,2537,2538,2539,2540,2541,2542,2543,2544,2545,2546,2547,2548,2549,2550,2551,2552,2553,2554,2555,2556,2557,2558,2559,2560,2561,2562,2563,2564,2565,2566,2567,2568,2569,2570,2571,2572,2573,2574,2575,2576,2577,2578,2579,2580,2581,2582,2583,2584,2585,2586,2587,2588,2589,2590,2591,2592,2593,2594,2595,2596,2597,2598,2599,2600,2601,2602,2603,2604,2605,2606,2607,2608,2609,2610,2611,2612,2613,2614,2615,2616,2617,2618,2619,2620,2621,2622,2623,2624,2625,2626,2627,2628,2629,2630,2631,2632,2633,2634,2635,2636,2637,2638,2639,2640,2641,2642,2643,2644,2645,2646,2647,2648,2649,2650,2651,2652,2653,2654,2655,2656,2657,2658,2659,2660,2661,2662,2663,2664,2665,2666,2667,2668,2669,2670,2671,2672,2673,2674,2675,2676,2677,2678,2679,2680,2681,2682,2683,2684,2685,2686,2687,2688,2689,2690,2691,2692,2693,2694,2695,2696,2697,2698,2699,2700,2701,2702,2703,2704,2705,2706,2707,2708,2709,2710,2711,2712,2713,2714,2715,2716,2717,2718,2719,2720,2721,2722,2723,2724,2725,2726,2727,2728,2729,2730,2731,2732,2733,2734,2735,2736,2737,2738,2739,2740,2741,2742,2743,2744,2745,2746,2747,2748,2749,2750,2832,2833,2934,2935,2936,2938,2939,2940,2941,2942,2943,2946,2947,2948,2949,2950,2951,2961,2964,2967,2968,2989,2990,2991,2992,2993,2994,2995,3006,3017,3018,3023,3024,3028,3029,3030,3031,3032,3033,3034,3035,3036,3037,3038,3039,3040,3041,3042,3043,3044,3045,3046,3047,3048,3049,3050,3051,3052,3053,3054,3055,3056,3057,3059,3060,3061,3062,3063,3064,3065,3066,3067,3068,3069,3070,3071,3072,3073,3074,3075,3076,3077,3078,3079,3080,3081,3082,3083,3084,3085,3086,3087,3100,3164,3195,3196,3197,3198,3199,3200,3216,3217,3218,3236,3331,3334,3391,3392,3393,3422,3426,3435,3450,3451,3452,3453,3454,3455,3456,3457,3458,3459,3460,3461,3462,3463,3464,3465,3531,3532,3533,3534,3535,3536,3537,3538,3539,3540,3541,3542,3543,3544,3545,3546,3547,3548,3549,3550,3551,3552,3555,3556,3557,3558,3559,3560,3561,3562,3563,3566,3569,3572,3573,3574,3575,3576,3577,3578,3579,3580,3581,3582,3583,3584,3585,3586,3587,3588,3589,3590,3591,3592,3593,3594,3595,3596,3597,3598,3599,3600,3601,3602,3603,3604,3605,3606,3607,3608,3609,3610,3611,3612,3613,3614,3615,3616,3617,3618,3619,3620,3621,3622,3679,3680,3681,3682,3683,3739,3740,3741,3755,3756,3886,3890,3894,3898,3899,3903,3965,3972,3980,4145,4155,4164,4173,4182,4243,4244,4245,4251,4252,4253,4254,4255,4256,4262,4263,4264,4265,4266,4271,4272,4276,4277,4283,4287,4288,4289,4290,4300,4301,4302,4306,4307,4313,4317,4318,4393,4394,4399,4400,4403,4404,4405,4406,4670,4677,4938,4944,5208,5215,5476,5482,5545,5627,5679,5761,5823,5905,5969,6021,6103,6111,6117,6128,6132,6136,6149,6164,6940,6947,6953,6970,6983,7003,7020,7029,7034,7041,7061,7074,7091,7097,7103,7110,7114,7120,7134,7137,7147,7148,7149,7197,7201,7205,7209,7210,7211,7214,7230,7237,7251,7296,7297,7696,7700,7704,7709,7716,7722,7723,7726,7730,7735,7748,7752,7757,7762,7767,7770,7773,7776,7780,7784,7939,7940,7941,7942,8024,8025,8026,8027,8028,8029,8030,8031,8032,8033,8034,8035,8036,8037,8038,8039,8040,8041,8045,8049,8053,8057,8061,8065,8069,8070,8071,8072,8073,8074,8075,8076,8080,8084,8085,8089,8090,8093,8097,8100,8103,8106,8110,8113,8116,8120,8124,8128,8132,8135,8136,8137,8138,8141,8145,8148,8151,8154,8157,8160,8163,8167,8244,8245,8248,8251,8252,8255,8256,8257,8261,8262,8267,8274,8281,8288,8295,8302,8309,8316,8323,8330,8339,8348,8357,8364,8373,8382,8385,8388,8389,8390,8391,8392,8393,8394,8395,8396,8397,8398,8399,8400,8404,8409,8414,8417,8418,8419,8420,8421,8429,8437,8438,8446,8450,8458,8466,8474,8482,8490,8491,8499,8507,8508,8511,8514,8611,8616,8618,8623,8627,8631,8640,8641,8642,8646,8650,8651,8655,8656,8657,8658,8659,8660,8661,8662,8663,8664,8665,8669,8670,8671,8672,8676,8677,8678,8679,8683,8687,8688,8692,8693,8694,8699,8700,8701,8702,8703,8704,8705,8706,8707,8708,8709,8710,8711,8712,8713,8714,8715,8716,8717,8718,8719,8723,8724,8725,8731,8732,8736,8738,8739,8744,8745,8746,8747,8748,8749,8753,8754,8755,8761,8762,8766,8768,8772,8776,8780,8784,8823,8824,8825,8828,8831,8834,8837,8840,8845,8849,8852,8853,8858,8862,8867,8873,8879,8884,8888,8893,8897,8901,8942,8943,8944,8945,8946,8950,8951,8952,8953,8957,8961,8965,8969,8973,8977,8981,8985,8991,8992,9033,9047,9052,9078,9085,9088,9099,9104,9107,9110,9165,9171,9172,9175,9178,9181,9184,9187,9190,9193,9197,9200,9201,9202,9210,9218,9221,9226,9231,9236,9241,9245,9249,9250,9258,9259,9260,9261,9262,9270,9275,9280,9281,9282,9283,9308,9314,9319,9322,9326,9329,9333,9343,9346,9351,9354,9358,9362,9467,9481,9494,9498,9513,9524,9527,9538,9543,9547,9582,9583,9584,9596,9604,9612,9620,9628,9648,9651,9678,9683,9703,9706,9709,9716,9729,9738,9741,9761,9771,9775,9779,9792,9796,9800,9804,9810,9814,9831,9839,9843,9847,9851,9854,9858,9862,9866,9876,9883,9890,9894,9920,9930,9955,9964,9984,9994,9998,10008,10033,10043,10046,10053,10060,10067,10068,10069,10070,10071,10078,10082,10088,10094,10095,10108,10109,10110,10113,10116,10119,10122,10125,10128,10131,10134,10137,10140,10143,10146,10149,10152,10155,10158,10161,10164,10167,10170,10173,10174,10182,10190,10191,10204,10214,10218,10223,10228,10232,10235,10239,10243,10246,10250,10253,10257,10262,10267,10270,10277,10281,10285,10294,10299,10304,10305,10309,10312,10316,10329,10334,10342,10346,10350,10367,10371,10376,10394,10401,10405,10435,10438,10441,10444,10447,10450,10453,10472,10478,10486,10493,10505,10513,10518,10526,10530,10548,10555,10571,10575,10583,10586,10591,10592,10593,10594,10598,10602,10606,10610,10645,10648,10652,10656,10690,10693,10697,10701,10710,10716,10719,10729,10733,10734,10741,10745,10752,10753,10754,10757,10762,10767,10768,10772,10787,10806,10810,10811,10823,10833,10834,10846,10851,10875,10878,10884,10887,10896,10904,10908,10911,10914,10917,10921,10924,10941,10945,10948,10963,10966,10974,10979,10986,10991,10992,10997,10998,11004,11010,11016,11048,11059,11076,11083,11087,11090,11103,11112,11116,11121,11125,11129,11133,11137,11141,11145,11149,11154,11157,11169,11174,11183,11186,11193,11194,11198,11207,11213,11217,11218,11222,11243,11249,11253,11257,11258,11276,11277,11278,11279,11280,11285,11288,11289,11295,11296,11308,11320,11327,11328,11333,11338,11339,11343,11357,11362,11368,11374,11380,11385,11391,11397,11398,11404,11419,11424,11433,11442,11445,11459,11464,11475,11479,11488,11497,11498,11505,11513,16239,16240,16241,16242,16243,16244,16245,16246,16247,16248,16249,16250,16251,16252,16253,16254,16255,16256,16257,16258,16259,16260,16261,16262,16263,16264,16265,16266,16267,16268,16269,16270,16271,16272,16273,16274,16275,16276,16277,16278,16279,16280,16281,16282,16283,16284,16285,16286,16287,16288,16289,16290,16291,16292,16293,16294,16295,16296,16297,16298,16299,16300,16301,16302,16303,16304,16305,16306,16307,16308,16309,16310,16311,16312,16313,16314,16315,16316,16317,16318,16319,16320,16321,16322,16323,16324,16325,16326,16327,16328,16329,16330,16331,16332,16333,16334,16335,16336,16337,16338,16339,16340,16341,16342,16343,16344,16345,16346,16347,16348,16349,16350,16351,16352,16353,16354,16355,16356,16357,16358,16359,16360,16361,16362,16363,16364,16365,16366,16367,16368,16369,16370,16371,16372,16373,16374,16375,16376,16377,16378,16379,16380,16381,16382,16383,16384,16385,16386,16387,16388,16389,16390,16391,16392,16393,16394,16395,16396,16397,16398,16399,16400,16401,16402,16403,16404,16405,16406,16407,16408,16409,16410,16411,16412,16413,16414,16415,16416,16417,16418,16419,16420,16421,16422,16423,16424,16425,16426,16427,16428,16429,16430,16431,16432,16433,16434,16435,16436,16437,16438,16439,16440,16441,16442,16443,16444,16445,16446,16447,16448,16449,16450,16451,16452,16453,16454,16455,16456,16457,16458,16459,16460,16461,16462,16463,16464,16465,16466,16467,16468,16469,16470,16471,16472,16473,16474,16475,16476,16477,16478,16479,16480,16481,16482,16483,16484,16485,16486,16487,16488,16489,16490,16491,16492,16493,16494,16495,16496,16497,16498,16499,16500,16501,16502,16503,16504,16505,16506,16507,16508,16509,16510,16511,16512,16513,16514,16515,16516,16517,16518,16519,16520,16521,16522,16523,16524,16525,16526,16527,16528,16529,16530,16531,16532,16533,16534,16535,16536,16537,16538,16539,16540,16541,16542,16543,16544,16545,16546,16547,16548,16549,16550,16551,16552,16553,16554,16555,16556,16557,16558,16559,16560,16561,16562,16563,16564,16565,16566,16567,16568,16569,16570,16571,16572,16573,16574,16575,16576,16577,16578,16579,16580,16581,16582,16583,16584,16585,16586,16587,16588,16589,16590,16591,16592,16593,16594,16595,16596,16597,16598,16599,16600,16601,16602,16603,16604,16605,16606,16607,16608,16609,16610,16611,16612,16613", "endColumns": "55,48,54,55,59,60,64,54,49,49,52,57,47,68,47,70,71,71,72,66,48,53,36,50,59,46,55,48,57,53,60,55,50,59,55,62,48,55,55,49,58,54,61,46,53,55,51,54,53,53,53,48,57,53,56,55,46,52,55,59,62,58,61,49,53,53,47,56,52,55,53,55,62,61,55,59,52,60,78,80,71,78,79,75,77,68,75,76,70,72,75,77,68,75,76,63,70,71,50,52,54,66,52,51,49,57,64,47,50,66,65,57,68,57,68,69,72,73,67,66,69,65,72,59,75,59,59,74,67,65,67,59,58,56,65,61,56,67,72,69,61,60,67,61,69,68,55,58,61,61,66,56,60,60,60,60,55,55,55,55,57,57,57,57,56,56,56,56,58,58,57,82,82,72,53,68,55,80,80,70,9,52,57,57,57,45,59,53,69,69,64,65,64,67,68,67,9,52,58,57,51,49,51,45,49,55,46,57,57,61,62,61,58,59,64,65,64,61,61,61,61,61,61,65,66,65,62,63,62,67,60,61,61,62,63,62,63,77,58,65,79,60,52,57,50,44,60,63,58,61,73,70,65,73,68,70,72,70,67,72,75,69,77,67,65,60,68,63,65,67,65,62,67,70,64,72,62,80,63,65,69,69,69,69,66,56,57,58,59,58,58,58,58,58,58,58,58,58,58,59,60,61,60,60,60,60,60,60,59,60,60,59,60,60,60,60,60,60,60,60,60,60,60,67,68,69,68,68,68,68,68,68,68,68,68,68,59,60,61,60,60,60,60,60,60,60,60,60,60,61,62,63,62,62,62,62,62,62,62,62,62,62,60,61,62,61,61,61,61,61,61,61,61,61,61,56,85,79,89,94,91,91,89,82,92,86,96,90,100,86,102,88,98,91,99,83,93,87,97,82,90,93,98,101,97,99,86,99,85,95,87,80,90,95,92,92,90,84,93,88,97,92,101,87,103,90,99,92,100,84,94,88,98,84,91,94,99,102,98,102,88,100,86,96,87,95,91,99,89,97,84,88,88,92,86,90,65,75,68,78,72,79,79,76,67,77,75,70,80,72,82,74,84,72,80,80,73,83,69,77,69,79,77,71,81,69,76,79,84,87,83,86,73,77,77,70,80,90,82,95,97,106,64,65,52,75,65,86,75,75,64,54,53,78,77,72,64,62,65,70,70,69,61,68,65,59,66,66,55,50,52,51,53,70,62,58,61,58,72,66,69,59,62,74,71,95,70,55,70,56,56,65,63,70,56,52,62,51,57,66,68,65,58,82,58,56,66,69,73,61,68,69,98,96,98,85,85,80,74,88,90,83,58,45,65,56,66,56,81,64,65,122,83,120,64,61,97,73,82,88,63,78,73,61,95,64,58,55,55,59,106,46,59,60,63,60,59,57,42,48,51,50,51,48,48,64,65,59,60,55,58,48,47,57,56,101,56,74,47,50,61,64,51,73,62,62,67,49,61,59,56,59,48,67,105,101,68,70,55,48,99,70,109,90,81,97,55,100,109,98,62,105,76,111,126,111,126,69,113,130,96,67,117,102,117,60,73,66,104,121,73,66,109,98,72,96,121,117,117,60,121,116,67,105,101,79,70,95,66,73,73,85,89,77,76,99,70,120,120,63,124,73,123,123,66,108,127,111,78,77,100,70,121,121,64,125,111,105,67,98,103,62,65,83,112,112,117,77,71,135,135,84,139,137,137,141,81,85,76,72,108,110,127,127,131,129,129,133,88,61,95,66,116,120,96,81,86,86,130,130,134,76,76,110,113,73,108,111,66,72,64,101,95,103,67,64,93,71,109,105,72,90,101,102,94,106,104,121,121,125,58,57,123,123,127,117,117,121,85,96,133,133,79,137,131,131,135,74,75,102,73,112,80,56,60,58,59,57,60,57,49,48,66,58,58,48,70,83,69,70,79,68,62,67,65,67,64,65,76,77,105,105,95,128,88,126,65,69,85,65,82,73,97,95,95,97,108,94,88,61,59,64,56,80,53,56,96,109,60,114,120,94,91,92,55,58,48,91,48,53,53,53,53,53,54,109,109,107,109,109,109,109,107,105,103,103,103,94,94,92,92,103,105,103,103,92,92,92,92,107,105,105,105,96,94,94,94,105,105,105,105,97,94,95,96,64,103,57,63,60,61,59,64,61,67,57,62,62,66,74,72,65,51,52,51,56,83,94,84,80,79,76,78,76,73,73,70,79,71,74,64,60,59,74,73,72,69,71,69,72,63,69,45,68,51,84,82,56,65,66,65,80,74,55,52,60,57,49,48,48,48,61,51,44,80,50,53,52,53,50,48,65,50,60,60,61,49,40,76,58,58,58,60,55,55,66,60,64,54,64,68,67,77,68,59,70,73,64,71,69,66,83,68,66,69,62,66,67,82,78,89,76,67,66,77,56,56,67,65,55,59,58,53,49,49,47,61,50,72,79,79,63,66,70,57,60,65,58,66,59,59,62,67,60,66,77,69,48,56,68,60,87,87,87,87,86,86,86,86,57,73,69,55,70,64,61,74,72,89,65,65,60,63,61,57,70,82,58,70,65,64,60,58,70,65,64,82,75,74,80,59,68,69,68,54,55,55,60,57,55,58,53,54,61,56,93,68,100,50,69,62,55,57,58,53,85,83,69,68,69,114,120,66,66,74,66,58,53,53,53,52,51,73,136,139,48,49,49,47,55,57,61,54,57,70,63,58,61,65,65,42,43,44,42,50,46,44,50,50,50,50,47,65,61,62,71,56,53,54,57,54,58,55,68,68,68,69,62,62,62,62,64,64,64,64,62,63,63,63,50,77,77,70,71,72,71,65,65,67,67,65,66,73,62,56,59,64,66,64,56,60,57,103,109,108,103,77,64,66,65,69,46,51,49,56,12,149,130,183,177,237,188,168,97,114,84,78,11,64,88,156,156,152,153,58,186,95,89,95,89,165,122,122,169,105,114,114,101,105,116,114,81,172,167,147,158,154,172,116,116,167,111,113,171,175,157,132,111,145,151,131,142,121,177,135,95,135,94,166,92,91,186,11,11,11,181,316,181,181,189,231,189,176,161,156,109,182,136,203,183,183,159,157,183,226,202,170,219,221,154,199,183,102,189,140,164,170,199,203,201,164,204,198,198,196,90,148,149,83,148,144,151,140,165,160,77,300,165,154,101,11,163,185,222,124,10,10,10,10,61,10,10,10,10,10,10,10,10,10,84,120,98,10,96,116,86,122,100,10,98,118,92,106,10,106,10,120,10,10,99,104,118,10,146,118,10,132,10,10,111,10,124,10,120,10,120,132,146,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,136,90,10,10,10,10,108,118,10,10,10,10,10,94,10,10,10,10,10,10,121,10,10,10,10,10,10,10,10,10,10,10,10,10,72,86,84,98,111,105,122,131,122,129,123,132,130,124,116,119,131,127,113,117,112,120,10,10,10,10,10,10,10,119,119,107,109,111,107,109,10,10,151,10,100,10,10,10,10,10,10,10,10,10,10,10,10,10,144,121,134,10,10,10,10,10,10,10,10,10,10,91,10,10,94,10,93,88,10,88,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,105,107,105,111,113,111,113,115,113,107,109,107,10,10,10,10,107,109,107,113,10,10,115,10,10,10,10,10,10,10,121,10,10,121,10,10,10,10,10,10,10,10,75,111,89,10,10,107,10,107,75,111,89,101,107,107,99,107,84,10,103,127,86,10,77,113,91,10,10,109,10,109,83,10,97,107,93,129,107,121,135,107,119,133,121,127,141,125,139,125,117,131,97,109,10,111,117,10,115,10,10,95,10,109,123,137,109,121,10,123,129,10,127,10,10,10,10,10,10,67,83,103,10,10,10,10,10,10,10,10,115,10,10,10,10,10,10,10,10,10,10,10,113,119,115,93,10,98,117,100,10,10,10,10,10,10,10,10,10,75,10,10,10,10,10,10,10,10,10,10,10,10,111,10,10,10,10,10,10,10,10,10,83,103,10,10,10,10,10,10,10,10,10,137,10,113,137,111,123,10,10,10,144,92,91,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,99,89,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,152,144,142,69,10,10,10,10,115,10,121,111,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,111,10,10,123,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,139,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,87,107,107,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,123,10,10,10,102,129,10,10,10,139,10,10,10,10,129,10,10,145,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,78,10,104,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,158,10,10,10,10,157,10,10,10,10,10,141,10,105,129,137,123,10,10,99,10,113,10,10,10,123,10,10,133,10,10,10,10,10,10,10,10,10,103,10,10,10,10,10,10,10,10,10,10,10,10,119,10,10,94,92,62,81,92,92,86,97,90,90,87,83,95,99,105,102,100,103,105,98,105,101,106,108,110,130,119,115,117,98,106,115,118,127,88,94,76,88,90,92,73,96,94,97,98,103,95,101,102,99,102,84,100,97,89,94,86,105,101,93,90,93,75,91,88,102,110,82,85,94,96,95,87,100,100,102,105,97,96,94,97,102,99,102,104,117,115,94,92,84,95,93,91,82,103,104,99,100,104,99,100,98,101,93,106,101,102,92,95,101,102,95,101,102,96,102,97,103,104,96,107,113,114,107,113,114,101,104,107,109,115,116,94,96,98,104,105,98,104,105,99,105,100,106,118,98,104,101,101,99,102,94,103,84,103,103,97,103,105,97,104,97,112,93,88,88,82,90,82,97,89,95,88,93,87,95,84,107,100,100,97,105,90,98,96,97,95,92,109,97,94,109,91,99,98,86,103,104,98,106,106,98,108,91,110,110,110,103,114,115,126,119,94,94,96,98,91,98,91,98,85,93,102,95,102,95,102,96,97,102,92,89,100,82,90,84,91,102,94,95,92,93,78,106,90,98,92,102,103,100,100,103,93,103,103,112,105,105,107,116,100,107,99,102,104,106,95,78,89,83,91,72,91,88,91,84,96,92,94,98,96,90,90,91,94,106,107,101,96,96,92,86,83,96,96,92,86,90,98,98,94,88,80,98,103,96,104,96,83,98,103,96,104,96,97,100,105,98,106,98,98,90,88,88,81,92,90,110,100,99,111,112,97,107,93,99,88,91,110,109,94,115,125,125,118,127,124,124,117,126,108,108,112,122,122,115,124,96,107,121,115,115,108,87,100,88,100,86,87,96,91,105,99,75,76", "endOffsets": "1018,1511,2145,2344,2404,2465,2530,2585,2784,2834,2887,2945,2993,3113,3462,3533,3605,3677,3750,3817,3866,3920,3957,4008,4068,4115,4171,4220,4278,4332,4393,4449,4500,4560,4616,4679,4728,4784,4840,4890,4949,5004,5066,5113,5167,5223,5275,5330,5384,5438,5492,5541,5599,5653,5710,5766,5813,5866,5922,5982,6045,6104,6166,6216,6270,6324,6372,6429,6482,6538,7449,7505,8843,8953,9009,9069,9122,9183,9262,9343,9415,9494,9574,9705,9783,9852,9928,10005,10076,10149,10225,10303,10372,10448,10525,10589,10660,10732,13521,13619,13674,14020,14073,14125,14175,14233,14298,14346,14462,21419,21548,21606,21675,21733,21802,21872,21945,22019,22087,22154,22224,22290,22363,22423,22499,22559,22619,22694,22762,22828,22896,22956,23015,23072,23138,23200,23257,23325,23398,23468,23530,23591,23659,23721,23791,23860,23916,23975,24037,24099,24166,24223,24284,24345,24406,24467,24523,24579,24635,24691,24749,24807,24865,24923,24980,25037,25094,25151,25210,25269,25327,25410,25493,25566,25620,25689,25745,25826,25907,25978,26107,26366,26424,26482,27991,29297,29357,29814,29884,29954,30019,30085,30150,30218,30287,30355,30485,30538,31373,31431,31483,31579,32026,32116,32166,32620,32667,32772,32830,32892,32955,33017,33076,33136,33201,33267,33332,33394,33456,33518,33580,33642,33704,33770,33837,33903,33966,34030,34093,34161,34222,34284,34346,34409,34473,34536,34653,34731,34790,34856,34936,34997,35050,35324,35375,35860,35921,36391,36551,38406,42182,42253,42319,42393,42462,42533,42606,42677,42745,42818,42894,42964,43042,43110,43176,43237,43306,43370,43436,43504,43570,43633,43701,43772,43837,43910,43973,44054,44118,44184,44254,44324,44394,44464,44531,46120,46178,46237,46297,46356,46415,46474,46533,46592,46651,46710,46769,46828,46887,46947,47008,47070,47131,47192,47253,47314,47375,47436,47496,47557,47618,47678,47739,47800,47861,47922,47983,48044,48105,48166,48227,48288,48349,48417,48486,48556,48625,48694,48763,48832,48901,48970,49039,49108,49177,49246,49306,49367,49429,49490,49551,49612,49673,49734,49795,49856,49917,49978,50039,50101,50164,50228,50291,50354,50417,50480,50543,50606,50669,50732,50795,50858,50919,50981,51044,51106,51168,51230,51292,51354,51416,51478,51540,51602,51664,51721,51807,51887,51977,52072,52164,52256,52346,52429,52522,52609,52706,52797,52898,52985,53088,53177,53276,53368,53468,53552,53646,53734,53832,53915,54006,54100,54199,54301,54399,54499,54586,54686,54772,54868,54956,55037,55128,55224,55317,55410,55501,55586,55680,55769,55867,55960,56062,56150,56254,56345,56445,56538,56639,56724,56819,56908,57007,57092,57184,57279,57379,57482,57581,57684,57773,57874,57961,58058,58146,58242,58334,58434,58524,58622,58707,58796,58885,58978,59065,59156,59894,59970,60039,60118,60191,60271,60351,60428,60496,60574,60650,60721,60802,60875,60958,61033,61118,61191,61272,61353,61427,61511,61581,61659,61729,61809,61887,61959,62041,62111,62188,62268,62353,62441,62525,62612,62686,62764,62842,62913,62994,63085,63168,63264,63362,63469,63534,63600,63653,63729,63795,63882,63958,64034,104189,104862,105706,105785,105863,105936,106001,106064,106130,106201,106272,106342,106404,106473,106539,106599,106666,106733,106789,106840,106893,106945,106999,107070,107133,107192,107254,107313,107386,107453,107523,107583,107646,107721,107793,107889,107960,108016,108087,108144,108201,108267,108331,108402,108459,108512,108575,108627,108685,108752,112240,112306,112365,112448,112507,112564,112631,112701,112775,112837,112906,112976,113075,113172,113271,113357,113443,113524,113599,113688,113779,113863,113922,113968,114034,114091,114158,114215,114297,114362,114428,114551,114635,114756,114821,114883,114981,115055,115138,115227,115291,115370,115444,115506,115602,115667,115726,115782,115838,115898,116005,116052,116112,116173,116237,116298,116358,116416,116459,116508,116560,116611,116663,116712,116761,116826,116892,116952,117013,117069,117128,117177,117225,117283,117340,117442,117499,117574,117622,117673,117735,117800,117852,117926,117989,118052,118120,118170,118232,118292,118349,118409,118458,118526,118632,118734,118803,118874,118930,118979,119079,119150,119260,119351,119433,119531,119587,119688,119798,119897,119960,120066,120143,120255,120382,120494,120621,120691,120805,120936,121033,121101,121219,121322,121440,121501,121575,121642,121747,121869,121943,122010,122120,122219,122292,122389,122511,122629,122747,122808,122930,123047,123115,123221,123323,123403,123474,123570,123637,123711,123785,123871,123961,124039,124116,124216,124287,124408,124529,124593,124718,124792,124916,125040,125107,125216,125344,125456,125535,125613,125714,125785,125907,126029,126094,126220,126332,126438,126506,126605,126709,126772,126838,126922,127035,127148,127266,127344,127416,127552,127688,127773,127913,128051,128189,128331,128413,128499,128576,128649,128758,128869,128997,129125,129257,129387,129517,129651,129740,129802,129898,129965,130082,130203,130300,130382,130469,130556,130687,130818,130953,131030,131107,131218,131332,131406,131515,131627,131694,131767,131832,131934,132030,132134,132202,132267,132361,132433,132543,132649,132722,132813,132915,133018,133113,133220,133325,133447,133569,133695,133754,133812,133936,134060,134188,134306,134424,134546,134632,134729,134863,134997,135077,135215,135347,135479,135615,135690,135766,135869,135943,136056,136137,136194,136255,136314,136374,136432,136493,136551,136601,136650,136717,136776,136835,136884,136955,137039,137109,137180,137260,137329,137392,137460,137526,137594,137659,137725,137802,137880,137986,138092,138188,138317,138406,138533,138599,138669,138755,138821,138904,138978,139076,139172,139268,139366,139475,139570,139659,139721,139781,139846,139903,139984,140038,140095,140192,140302,140363,140478,140599,140694,140786,140879,140935,140994,141043,141135,141184,141238,141292,141346,141400,141454,141509,141619,141729,141837,141947,142057,142167,142277,142385,142491,142595,142699,142803,142898,142993,143086,143179,143283,143389,143493,143597,143690,143783,143876,143969,144077,144183,144289,144395,144492,144587,144682,144777,144883,144989,145095,145201,145299,145394,145490,145587,145652,145756,145814,145878,145939,146001,146061,146126,146188,146256,146314,146377,146440,146507,146582,146655,146721,146773,146826,146878,146935,147019,147114,147199,147280,147360,147437,147516,147593,147667,147741,147812,147892,147964,148039,148104,148165,148225,148300,148374,148447,148517,148589,148659,148732,148796,148866,148912,148981,149033,149118,149201,149258,149324,149391,149457,149538,149613,149669,149722,149783,149841,149891,149940,149989,150038,150100,150152,150197,150278,150329,150383,150436,150490,150541,150590,150656,150707,150768,150829,150891,150941,150982,151059,151118,151177,151236,151297,151353,151409,151476,151537,151602,151657,151722,151791,151859,151937,152006,152066,152137,152211,152276,152348,152418,152485,152569,152638,152705,152775,152838,152905,152973,153056,153135,153225,153302,153370,153437,153515,153572,153629,153697,153763,153819,153879,153938,153992,154042,154092,154140,154202,154253,154326,154406,154486,154550,154617,154688,154746,154807,154873,154932,154999,155059,155119,155182,155250,155311,155378,155456,155526,155575,155632,155701,155762,155850,155938,156026,156114,156201,156288,156375,156462,156520,156594,156664,156720,156791,156856,156918,156993,157066,157156,157222,157288,157349,157413,157475,157533,157604,157687,157746,157817,157883,157948,158009,158068,158139,158205,158270,158353,158429,158504,158585,158645,158714,158784,158853,158908,158964,159020,159081,159139,159195,159254,159308,159363,159425,159482,159576,159645,159746,159797,159867,159930,159986,160044,160103,160157,160243,160327,160397,160466,160536,160651,160772,160839,160906,160981,161048,161107,161161,161215,161269,161322,161374,161448,167906,168046,173274,173324,173374,173463,173519,173577,173639,173694,173752,173933,173997,174056,174118,174184,174250,174751,174897,175050,175093,176234,176281,176326,176377,176428,176479,176530,177064,177668,177730,178031,178103,178384,178438,178493,178551,178606,178665,178721,178790,178859,178928,178998,179061,179124,179187,179250,179315,179380,179445,179510,179573,179637,179701,179765,179816,179894,179972,180043,180115,180188,180260,180399,180465,180533,180601,180667,180734,180808,180871,180928,180988,181053,181120,181185,181242,181303,181361,181465,181575,181684,181788,181866,181931,181998,182064,182134,182181,182233,182283,182340,183297,187886,191483,191667,191845,192083,192272,192441,193555,193670,193755,195185,205901,206110,210545,210702,210859,213288,213578,214117,215610,215706,215796,215892,215982,216148,216271,216394,216564,216670,216785,216900,217002,217108,217225,217340,222037,222210,222378,222526,222685,222840,223013,223130,223247,223415,223527,223641,223813,223989,224147,224280,224392,224538,224690,224822,224965,225087,225395,225531,225627,225763,225858,226025,226118,226210,226397,226553,226731,226895,227077,227394,227576,227758,227948,228180,228370,228547,228709,228866,228976,229159,229296,229500,229684,229868,230028,230186,230370,230597,230800,230971,231191,231413,231568,231768,231952,232055,232245,232386,232551,232722,232922,233126,233328,233493,233698,233897,234096,234293,234384,234533,234683,234767,234916,235061,235213,235354,235520,235681,240964,241265,241431,241586,241688,245298,245462,245648,246831,246956,255021,255293,255571,255816,255878,256163,259936,260392,260901,271677,272191,272628,273062,273505,277487,277608,277707,278112,278209,278326,278413,278536,278637,279043,279142,279261,279354,279461,279804,279911,280156,280277,280686,280934,281034,281139,281258,281767,281914,282033,282284,282417,282832,283086,283198,288814,288939,289347,289468,289696,289817,289950,290097,310819,311311,331782,332206,352973,353467,373983,374409,379250,384667,388758,394189,398931,404308,408292,412284,417675,418222,418655,419411,419641,419884,421051,421980,470125,470709,471182,472612,473356,474549,475603,476081,476374,476757,478272,479037,480180,480621,481062,481658,481932,482343,483359,483537,484290,484427,484518,486712,486978,487300,487510,487619,487738,487922,489040,489510,490261,492844,492939,514106,514334,514590,514849,515425,515779,515901,516040,516332,516592,517520,517806,518209,518611,518954,519166,519367,519580,519869,520154,531389,531476,531561,531660,536525,536631,536754,536886,537009,537139,537263,537396,537527,537652,537769,537889,538021,538149,538263,538381,538494,538615,538803,538990,539171,539354,539538,539703,539885,540005,540125,540233,540343,540455,540563,540673,540838,541004,541156,541321,541422,541542,541713,541874,542037,542198,542365,542484,542601,542781,542963,543144,543327,543482,543627,543749,543884,544047,544240,544366,544518,544660,544830,544986,545158,545449,552404,552496,552669,552831,552926,553095,553189,553278,553521,553610,553903,554319,554739,555160,555586,556003,556419,556836,557254,557668,558138,558611,559083,559494,559965,560437,560627,560833,560939,561047,561153,561265,561379,561491,561605,561721,561835,561943,562053,562161,562423,562802,563206,563353,563461,563571,563679,563793,564202,564616,564732,565150,565391,565821,566256,566666,567088,567498,567620,568029,568445,568567,568785,568969,574795,575139,575219,575575,575725,575869,576343,576455,576545,576807,577072,577180,577332,577440,577516,577628,577718,577820,577928,578036,578136,578244,578329,578495,578599,578727,578814,578981,579059,579173,579265,579529,579796,579906,580059,580169,580253,580642,580740,580848,580942,581072,581180,581302,581438,581546,581666,581800,581922,582050,582192,582318,582458,582584,582702,582834,582932,583042,583342,583454,583572,584036,584152,584455,584581,584677,585078,585188,585312,585450,585560,585682,585994,586118,586248,586724,586852,587167,587305,587467,587683,587839,588043,590622,590706,590810,591013,591202,591403,591596,591801,592114,592326,592492,592608,592854,593070,593383,593809,594271,594508,594660,594920,595064,595206,598438,598552,598672,598788,598882,599203,599302,599420,599521,599800,600085,600364,600646,600899,601158,601411,601667,602091,602167,605417,606772,607216,609070,609645,609853,610863,611243,611409,611550,616570,616996,617108,617243,617396,617593,617764,617947,618122,618309,618581,618739,618823,618927,619414,619970,620128,620347,620578,620801,621036,621258,621524,621662,622261,622375,622513,622625,622749,623320,623815,624361,624506,624599,624691,626618,627188,627486,627675,627881,628074,628284,629168,629313,629705,629863,630080,630341,638568,639443,640063,640260,641208,641973,642096,642869,643090,643290,645267,645367,645457,646143,646896,647661,648424,649199,650412,650577,652190,652511,653574,653784,653954,654524,655419,656052,656218,657704,658320,658556,658777,659735,660000,660265,660512,660926,661162,662447,662896,663083,663332,663574,663750,663991,664224,664449,665044,665519,666043,666304,667655,668130,669356,669826,670874,671326,671570,672027,673272,673755,673905,674460,674912,675312,675465,675610,675753,675823,676251,676539,677043,677552,677668,678570,678692,678804,678981,679247,679517,679783,680051,680307,680567,680823,681081,681333,681589,681841,682095,682327,682563,682815,683071,683323,683577,683809,684043,684155,684807,685262,685386,686478,687293,687489,687813,688202,688554,688795,689009,689308,689500,689815,690022,690368,690668,691069,691288,691701,691938,692308,693032,693387,693656,693796,694050,694194,694471,695463,695872,696504,696850,697218,698292,698655,699055,700563,701148,701466,704001,704195,704413,704639,704851,705050,705257,706461,706756,707313,707703,708335,708812,709057,709544,709790,710986,711383,712389,712611,713034,713225,713604,713692,713800,713908,714221,714546,714865,715196,717899,718087,718348,718597,721181,721373,721638,721891,722423,722831,723030,723614,723849,723973,724385,724599,725001,725104,725234,725409,725661,725857,725997,726191,727202,728271,728559,728689,729466,730123,730269,730975,731213,732753,732903,733320,733485,734171,734641,734837,734928,735012,735156,735390,735557,736485,736771,736931,737546,737705,738033,738260,738772,739134,739213,739552,739657,740022,740393,740754,742628,743257,744333,744757,745010,745162,746210,746947,747150,747396,747643,747861,748103,748424,748688,748993,749216,749527,749716,750431,750700,751194,751420,751860,752019,752303,753048,753413,753718,753876,754114,755433,755831,756059,756279,756421,757711,757817,757947,758085,758209,758497,758666,758766,759051,759165,760048,760803,761242,761366,761612,761805,761939,762130,762909,763127,763418,763697,764014,764236,764531,764814,764918,765259,766075,766391,766952,767458,767663,768449,768854,769515,769704,770255,770821,770941,771343,771877,925091,925184,925247,925329,925422,925515,925602,925700,925791,925882,925970,926054,926150,926250,926356,926459,926560,926664,926770,926869,926975,927077,927184,927293,927404,927535,927655,927771,927889,927988,928095,928211,928330,928458,928547,928642,928719,928808,928899,928992,929066,929163,929258,929356,929455,929559,929655,929757,929860,929960,930063,930148,930249,930347,930437,930532,930619,930725,930827,930921,931012,931106,931182,931274,931363,931466,931577,931660,931746,931841,931938,932034,932122,932223,932324,932427,932533,932631,932728,932823,932921,933024,933124,933227,933332,933450,933566,933661,933754,933839,933935,934029,934121,934204,934308,934413,934513,934614,934719,934819,934920,935019,935121,935215,935322,935424,935527,935620,935716,935818,935921,936017,936119,936222,936319,936422,936520,936624,936729,936826,936934,937048,937163,937271,937385,937500,937602,937707,937815,937925,938041,938158,938253,938350,938449,938554,938660,938759,938864,938970,939070,939176,939277,939384,939503,939602,939707,939809,939911,940011,940114,940209,940313,940398,940502,940606,940704,940808,940914,941012,941117,941215,941328,941422,941511,941600,941683,941774,941857,941955,942045,942141,942230,942324,942412,942508,942593,942701,942802,942903,943001,943107,943198,943297,943394,943492,943588,943681,943791,943889,943984,944094,944186,944286,944385,944472,944576,944681,944780,944887,944994,945093,945202,945294,945405,945516,945627,945731,945846,945962,946089,946209,946304,946399,946496,946595,946687,946786,946878,946977,947063,947157,947260,947356,947459,947555,947658,947755,947853,947956,948049,948139,948240,948323,948414,948499,948591,948694,948789,948885,948978,949072,949151,949258,949349,949448,949541,949644,949748,949849,949950,950054,950148,950252,950356,950469,950575,950681,950789,950906,951007,951115,951215,951318,951423,951530,951626,951705,951795,951879,951971,952044,952136,952225,952317,952402,952499,952592,952687,952786,952883,952974,953065,953157,953252,953359,953467,953569,953666,953763,953856,953943,954027,954124,954221,954314,954401,954492,954591,954690,954785,954874,954955,955054,955158,955255,955360,955457,955541,955640,955744,955841,955946,956043,956141,956242,956348,956447,956554,956653,956752,956843,956932,957021,957103,957196,957287,957398,957499,957599,957711,957824,957922,958030,958124,958224,958313,958405,958516,958626,958721,958837,958963,959089,959208,959336,959461,959586,959704,959831,959940,960049,960162,960285,960408,960524,960649,960746,960854,960976,961092,961208,961317,961405,961506,961595,961696,961783,961871,961968,962060,962166,962266,962342,962419"}}, {"source": "D:\\Duc\\AndroidStudioProjects\\TJ_BatteryOne\\app\\src\\main\\res\\values\\attrs.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "13,14,54,133,134,135,270,271,272,273,529,599,690,728,760", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "484,524,2150,6543,6587,6631,13245,13290,13329,13376,26595,29648,34541,36445,37717", "endColumns": "39,48,39,43,43,40,44,38,46,46,39,65,52,51,39", "endOffsets": "519,568,2185,6582,6626,6667,13285,13324,13371,13418,26630,29709,34589,36492,37752"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\6718808f332794c6efacee4a0d5b3f88\\transformed\\jetified-lottie-6.6.2\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "445,2933", "startColumns": "4,4", "startOffsets": "21424,173183", "endColumns": "62,46", "endOffsets": "21482,173225"}}, {"source": "D:\\Duc\\AndroidStudioProjects\\TJ_BatteryOne\\app\\src\\main\\res\\values\\ic_launcher_background.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "883", "startColumns": "4", "startOffsets": "45804", "endColumns": "56", "endOffsets": "45856"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\470cb6f36c59533cd863c8252f38a327\\transformed\\jetified-dotsindicator-5.1.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "146,153,154,155,156,157,158,159,160,564,600,646", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7146,7510,7560,7604,7659,7706,7756,7806,7860,28108,29714,32031", "endColumns": "46,49,43,54,46,49,49,53,49,48,50,43", "endOffsets": "7188,7555,7599,7654,7701,7751,7801,7855,7905,28152,29760,32070"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\6ef5a5d4dd255734d1000e88cf2a8846\\transformed\\databinding-runtime-8.9.0\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2877", "startColumns": "4", "startOffsets": "170626", "endColumns": "40", "endOffsets": "170662"}}, {"source": "D:\\Duc\\AndroidStudioProjects\\TJ_BatteryOne\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,39,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,1710,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,62,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,1768,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "786,805,806,807,808,809,810,811,812,813,814,815,816,817,818,866,878,879,880,885,886,887,1125,1128,1129,1130,1139,1140,1141,1142,1143,1144,1155,1156,1157,1160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "39494,40803,40862,40923,40976,41031,41072,41114,41155,41196,41237,41278,41319,41360,41401,44816,45540,45579,45619,45914,45959,46005,64039,64216,64257,64296,64937,64989,65044,65091,65138,65185,65920,65965,66010,66170", "endColumns": "38,58,60,52,54,40,41,40,40,40,40,40,40,40,40,41,38,39,49,44,45,62,44,40,38,52,51,54,46,46,46,37,44,44,38,39", "endOffsets": "39528,40857,40918,40971,41026,41067,41109,41150,41191,41232,41273,41314,41355,41396,41437,44853,45574,45614,45664,45954,46000,46063,64079,64252,64291,64344,64984,65039,65086,65133,65180,65218,65960,66005,66044,66205"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d56ddc8f70c1b6c4f2dfff25a6818549\\transformed\\jetified-play-services-base-18.5.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "819,820,821,822,823,824,825,826,3242,3243,3244,3245,3246,3247,3248,3249,3251,3252,3253,3254,3255,3256,3257,3258,3259", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "41442,41532,41612,41702,41792,41872,41953,42033,195573,195678,195859,195984,196091,196271,196394,196510,196780,196968,197073,197254,197379,197554,197702,197765,197827", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78", "endOffsets": "41527,41607,41697,41787,41867,41948,42028,42108,195673,195854,195979,196086,196266,196389,196505,196608,196963,197068,197249,197374,197549,197697,197760,197822,197901"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f5658b4b85c4a4e32f0ba738fc8faf8b\\transformed\\jetified-ssp-android-1.1.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "1162,1173,1185,1197,1209,1221,1233,1245,1257,1269,1281,1283,1295,1307,1319,1331,1343,1355,1367,1379,1391,1403,1405,1417,1429,1441,1453,1465,1477,1489,1501,1513,1525,1527,1539,1551,1563,1575,1587,1599,1611,1623,1635,1647,1649,1661,1673,1685,1697,1709,1721,1733,1745,1757,1769,1771,1774,1776,1778,1780,1782,1784,1786,1788,1790,1792,1794,1796,1798,1800,1802,1804,1806,1808,1810,1812,1814,1816,1818,1820,1822,1824,1826,1828,1830,1832,1834,1836,1838,1840,1842,1844,1846,1848,1850,1852,1854,1856,1858,1860", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "66253,66724,67236,67748,68260,68772,69284,69796,70308,70820,71332,71412,71922,72434,72946,73458,73970,74482,74994,75506,76018,76530,76610,77120,77632,78144,78656,79168,79680,80192,80704,81216,81728,81808,82318,82830,83342,83854,84366,84878,85390,85902,86414,86926,87006,87516,88028,88540,89052,89564,90076,90588,91100,91612,92124,92204,92327,92409,92491,92573,92655,92737,92819,92901,92983,93065,93145,93225,93307,93389,93471,93553,93635,93717,93799,93881,93963,94043,94123,94205,94287,94369,94451,94533,94615,94697,94779,94861,94941,95021,95103,95185,95267,95349,95431,95513,95595,95677,95759,95839", "endColumns": "42,40,40,40,40,40,40,40,40,40,40,38,40,40,40,40,40,40,40,40,40,40,38,40,40,40,40,40,40,40,40,40,40,38,40,40,40,40,40,40,40,40,40,40,38,40,40,40,40,40,40,40,40,40,40,38,40,40,40,40,40,40,40,40,40,40,38,40,40,40,40,40,40,40,40,40,40,38,40,40,40,40,40,40,40,40,40,40,38,40,40,40,40,40,40,40,40,40,40,38", "endOffsets": "66291,66760,67272,67784,68296,68808,69320,69832,70344,70856,71368,71446,71958,72470,72982,73494,74006,74518,75030,75542,76054,76566,76644,77156,77668,78180,78692,79204,79716,80228,80740,81252,81764,81842,82354,82866,83378,83890,84402,84914,85426,85938,86450,86962,87040,87552,88064,88576,89088,89600,90112,90624,91136,91648,92160,92238,92363,92445,92527,92609,92691,92773,92855,92937,93019,93101,93179,93261,93343,93425,93507,93589,93671,93753,93835,93917,93999,94077,94159,94241,94323,94405,94487,94569,94651,94733,94815,94897,94975,95057,95139,95221,95303,95385,95467,95549,95631,95713,95795,95873"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\705b667089ad425e9a6a8d447e2e4b19\\transformed\\jetified-ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2925", "startColumns": "4", "startOffsets": "172787", "endColumns": "65", "endOffsets": "172848"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4c7b48e4d1f473a8b802d70e4aff0147\\transformed\\jetified-savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "3011", "startColumns": "4", "startOffsets": "177260", "endColumns": "53", "endOffsets": "177309"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\17ade856dd8011987dee0a638e332f55\\transformed\\jetified-lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "3012", "startColumns": "4", "startOffsets": "177314", "endColumns": "49", "endOffsets": "177359"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2821057fb80181e87f67e0a41ee8161d\\transformed\\jetified-customactivityoncrash-2.4.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2022,2023,2024,3275,3276,3277,3278,3279,3280,3281,3282,3283", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105355,105435,105513,200202,200287,200380,200493,200584,200690,200792,200891,201043", "endColumns": "79,77,91,84,92,112,90,105,101,98,151,88", "endOffsets": "105430,105508,105600,200282,200375,200488,200579,200685,200787,200886,201038,201127"}}, {"source": "D:\\Duc\\AndroidStudioProjects\\TJ_BatteryOne\\app\\build\\generated\\res\\processDebugGoogleServices\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "3402,3403,3404,3405,3406,3707", "startColumns": "4,4,4,4,4,4", "startOffsets": "211524,211605,211709,211817,211937,243246", "endColumns": "80,103,107,119,120,89", "endOffsets": "211600,211704,211812,211932,212053,243331"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8007b3eee33a0787235755b3a0af0b83\\transformed\\constraintlayout-2.2.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "15,22,25,43,44,52,55,56,64,70,72,73,74,75,76,136,137,138,139,142,147,149,150,161,171,181,210,211,216,217,222,227,228,229,234,235,240,241,246,247,248,254,255,256,261,267,274,295,296,302,303,304,305,308,311,314,315,318,321,322,323,324,325,328,331,332,333,334,340,345,348,351,352,353,358,359,360,363,366,367,370,373,376,379,380,381,384,387,388,393,394,400,405,408,411,412,413,414,415,416,417,418,419,420,421,422,438,520,521,522,523,530,537,545,547,548,551,560,565,573,574,634,660,698,699,704,705,717,718,719,725,732,738,743,744,745,746,747,756,2937,3007", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "573,857,1023,1691,1752,2043,2190,2240,2691,2998,3118,3173,3233,3298,3357,6672,6724,6785,6847,7013,7193,7299,7349,7910,8317,8740,10737,10796,10993,11050,11245,11426,11480,11537,11729,11787,11983,12039,12233,12290,12341,12563,12615,12670,12860,13076,13423,14467,14523,14729,14790,14850,14920,15053,15184,15312,15380,15509,15635,15697,15760,15828,15895,16018,16143,16210,16275,16340,16629,16810,16931,17052,17118,17185,17395,17464,17530,17655,17781,17848,17974,18101,18226,18353,18409,18474,18600,18723,18788,18996,19063,19351,19531,19651,19771,19836,19898,19960,20024,20086,20145,20205,20266,20327,20386,20446,21106,26112,26163,26212,26260,26635,26927,27235,27331,27391,27497,27884,28157,28492,28546,31488,32672,35055,35106,35380,35432,35926,35985,36039,36277,36655,36857,37042,37088,37143,37188,37232,37580,173379,177069", "endLines": "21,22,29,43,51,52,55,56,64,70,72,73,74,75,76,136,137,138,139,145,147,149,150,170,178,181,210,215,216,221,226,227,228,233,234,239,240,245,246,247,253,254,255,260,266,267,274,295,301,302,303,304,307,310,313,314,317,320,321,322,323,324,327,330,331,332,333,339,344,347,350,351,352,357,358,359,362,365,366,369,372,375,378,379,380,383,386,387,392,393,399,404,407,410,411,412,413,414,415,416,417,418,419,420,421,437,443,520,521,522,523,536,544,545,547,550,555,560,572,573,574,634,660,698,699,704,713,717,718,724,725,737,741,743,744,745,746,755,759,2937,3007", "endColumns": "11,55,11,60,11,51,49,52,47,50,54,59,64,58,61,51,60,61,45,11,51,49,50,11,11,44,58,11,56,11,11,53,56,11,57,11,55,11,56,50,11,51,54,11,11,49,51,55,11,60,59,69,11,11,11,67,11,11,61,62,67,66,11,11,66,64,64,11,11,11,11,65,66,11,68,65,11,11,66,11,11,11,11,55,64,11,11,64,11,66,11,11,11,11,64,61,61,63,61,58,59,60,60,58,59,11,11,50,48,47,57,11,11,46,59,11,11,53,11,53,55,45,46,50,58,51,11,58,53,11,54,11,11,45,54,44,43,11,11,40,44", "endOffsets": "852,908,1204,1747,2038,2090,2235,2288,2734,3044,3168,3228,3293,3352,3414,6719,6780,6842,6888,7141,7240,7344,7395,8312,8624,8780,10791,10988,11045,11240,11421,11475,11532,11724,11782,11978,12034,12228,12285,12336,12558,12610,12665,12855,13071,13121,13470,14518,14724,14785,14845,14915,15048,15179,15307,15375,15504,15630,15692,15755,15823,15890,16013,16138,16205,16270,16335,16624,16805,16926,17047,17113,17180,17390,17459,17525,17650,17776,17843,17969,18096,18221,18348,18404,18469,18595,18718,18783,18991,19058,19346,19526,19646,19766,19831,19893,19955,20019,20081,20140,20200,20261,20322,20381,20441,21101,21352,26158,26207,26255,26313,26922,27230,27277,27386,27492,27672,27933,28487,28541,28597,31529,32714,35101,35160,35427,35757,35980,36034,36272,36327,36852,36991,37083,37138,37183,37227,37575,37712,173415,177109"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0f5d2fdc4a4434be9fcd65932e42964a\\transformed\\jetified-activity-1.10.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "2960,3010", "startColumns": "4,4", "startOffsets": "174671,177200", "endColumns": "41,59", "endOffsets": "174708,177255"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4961d4f566368a726dd9f06399253e9d\\transformed\\fragment-1.6.2\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "2921,2969,3013", "startColumns": "4,4,4", "startOffsets": "172585,175098,177364", "endColumns": "56,64,63", "endOffsets": "172637,175158,177423"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f691554f471de61b2961ded671aeec4e\\transformed\\drawerlayout-1.1.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "180,183,2025", "startColumns": "4,4,4", "startOffsets": "8684,8848,105605", "endColumns": "55,47,51", "endOffsets": "8735,8891,105652"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a57f610ef3585645bfe3fcbe98d165f8\\transformed\\jetified-core-viewtree-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "3008", "startColumns": "4", "startOffsets": "177114", "endColumns": "42", "endOffsets": "177152"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\6df7bf1ddef91663e5063bb6ebc3431d\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "3156", "startColumns": "4", "startOffsets": "186803", "endColumns": "82", "endOffsets": "186881"}}, {"source": "D:\\Duc\\AndroidStudioProjects\\TJ_BatteryOne\\app\\src\\main\\res\\values\\dimens.xml", "from": {"startLines": "-1,-1,-1,-1,-1,8,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,284,-1,-1,-1,-1,-1,-1,-1,-1", "endColumns": "-1,-1,-1,-1,-1,50,-1,-1,-1,-1,-1,-1,-1,-1", "endOffsets": "-1,-1,-1,-1,-1,330,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2003,2004,2005,2008,2009,2010,2114,2122,2123,2124,2767,2768,2769,2770", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "104194,104245,104300,104496,104546,104591,111172,111826,111868,111911,162494,162539,162585,162629", "endColumns": "50,54,52,49,44,50,42,41,42,41,44,45,43,45", "endOffsets": "104240,104295,104348,104541,104586,104637,111210,111863,111906,111948,162534,162580,162624,162670"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1bc3ac1a7cb7052d837b8364241b66b3\\transformed\\work-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1"}, "to": {"startLines": "767,768,769,771", "startColumns": "4,4,4,4", "startOffsets": "38150,38215,38285,38411", "endColumns": "64,69,63,60", "endOffsets": "38210,38280,38344,38467"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\179e6486bd57a16ea175623aa423e7ed\\transformed\\jetified-material3-release\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "3058,3466,3467,3468,3469,3470,3471,3472,3473,3474,3475,3478,3479,3480,3481,3482,3483,3484,3485,3486,3487,3488,3491,3492,3493,3494,3495,3496,3497,3498,3499,3500,3501,3502,3503,3504,3505,3506,3507,3508,3509,3510,3511,3512,3513,3514,3515,3516,3517,3518,3519,3520,3521,3522,3523,3524,3525,3526,7380,7390", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "180265,217345,217433,217519,217600,217684,217753,217818,217901,218007,218093,218213,218267,218336,218397,218466,218555,218650,218724,218821,218914,219012,219161,219252,219340,219436,219534,219598,219666,219753,219847,219914,219986,220058,220159,220268,220344,220413,220461,220527,220591,220665,220722,220779,220851,220901,220955,221026,221097,221167,221236,221294,221370,221441,221515,221601,221651,221721,496989,497704", "endLines": "3058,3466,3467,3468,3469,3470,3471,3472,3473,3474,3477,3478,3479,3480,3481,3482,3483,3484,3485,3486,3487,3490,3491,3492,3493,3494,3495,3496,3497,3498,3499,3500,3501,3502,3503,3504,3505,3506,3507,3508,3509,3510,3511,3512,3513,3514,3515,3516,3517,3518,3519,3520,3521,3522,3523,3524,3525,3526,7389,7392", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "180333,217428,217514,217595,217679,217748,217813,217896,218002,218088,218208,218262,218331,218392,218461,218550,218645,218719,218816,218909,219007,219156,219247,219335,219431,219529,219593,219661,219748,219842,219909,219981,220053,220154,220263,220339,220408,220456,220522,220586,220660,220717,220774,220846,220896,220950,221021,221092,221162,221231,221289,221365,221436,221510,221596,221646,221716,221781,497699,497852"}}]}, {"outputFile": "com.tqhit.battery.one.app-mergeDebugResources-99:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\cd1152147d00b2d5b451be6d0860a83e\\transformed\\navigation-runtime-2.8.9\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2944", "startColumns": "4", "startOffsets": "173757", "endColumns": "52", "endOffsets": "173805"}}, {"source": "D:\\Duc\\AndroidStudioProjects\\TJ_BatteryOne\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,348,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,351,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,350,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,349,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,28965,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,29170,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,29109,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,29042,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,75,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,71,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,59,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,65,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,29036,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,29237,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,29164,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,29103,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "3101,3102,3103,3104,3105,3106,3107,3108,3109,3110,3111,3112,3113,3114,3115,3116,3119,3120,3123,3124,3135,3136,3138,3139,3140,3143,3144,3145,3146,3147,3148,3149,3150,3151,3152,3153,3154,3155,3157,3158,3159,3160,3161,3162,3163,3165,3166,3167,3168,3169,3170,3171,3172,3173,3174,3175,3176,3177,3178,3179,3180,3181,3182,3183,3184,3185,3186,3187,3188,3189,3190,3191,3192,3193,3194,3195,3202,3203,3204,3205,3206,3214,3215,3216,3220,3221,3222,3223,3224,3225,3226,3227,3228,3229,3230,3231,3232,3233,3234,3235,3236,3238,3261,3262,3263,3264,3266,3267,3268,3269,3270,3271,3272,3273,3274,3275,3285,3286,3287,3288,3289,3290,3291,3292,3295,3296,3297,3298,3299,3300,3302,3303,3304,3305,3306,3307,3308,3309,3310,3311,3312,3313,3314,3315,3316,3317,3318,3320,3321,3322,3323,3324,3325,3326,3327,3328,3329,3330,3334,3338,3339,3392,3399,3400,3401,3402,3403,3409,3410,3411,3412,3413,3414,3415,3416,3417,3418,3419,3420,3421,3422,3423,3425,3426,3427,3429,3430,3432,3434,3435,3436,3438,3439,3440,3441,3442,3444,3445,3446,3447,3448,3449,3450,3451,3529,3530,3531,3532,3555,3556,3625,3638,3639,3640,3641,3642,3643,3644,3645,3646,3647,3648,3650,3651,3652,3653,3654,3655,3656,3657,3674,3675,3676,3677,3678,3679,3680,3686,3687,3688,3689,3690,3691,3692,3693,3694,3695,3696,3697,3698,3699,3701,3702,3703,3704,3705,3706,3707,3708,3709,3711,3712,3715,3716,3717,3718,3719,3720,3721,3722,3723,3725,3733,3734,3735,3736,3737,3739,3745,3747,3748,3749,3750,3751,3752,3753,3754,3755,3756,3757,3758,3761,3762,3763,3764,3765,3766,3767,3768,3772,3773,3774,3775,3778,3779,3780,3782,3784,3785,3786,3787,3788,3789,3792,3793,3794,3795,3797,3798,3799,3800,3801,3802,3803,3804,3805,3806,3807,3808,3812,3813,3814,3815,3816,3817,3818,3819", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "183302,183363,183413,183462,183511,183560,183610,183658,183705,183759,183817,183866,183915,183964,184012,184064,184284,184362,184571,184619,185304,185356,185503,185572,185647,185936,186006,186072,186122,186186,186224,186381,186424,186479,186564,186606,186666,186750,186886,186934,187179,187229,187532,187576,187643,187891,187935,187975,188035,188083,188121,188204,188260,188332,188709,189095,189159,189233,189305,189345,189407,189469,190146,190232,190316,190380,190456,190512,190583,190651,190855,190909,191216,191287,191337,191376,192522,192590,192639,192707,192777,193358,193400,193464,193836,193878,193952,194113,194181,194271,194348,194415,194501,194612,194684,194750,194809,194953,195013,195071,195129,195266,197982,198035,198079,198148,198308,198358,198718,198785,198839,198914,198969,199029,199603,200202,201208,201280,201328,201394,201441,201579,201641,201941,202158,202660,202720,202857,203004,203151,203269,203312,203360,203434,203530,203614,203679,203732,203871,203963,204021,204089,204159,204466,204711,204907,205047,205191,205262,205375,205439,205499,205571,205623,205673,205741,205796,205854,206054,206346,206394,210549,211227,211284,211322,211551,211598,212206,212268,212320,212373,212521,212578,212719,212939,212977,213033,213070,213107,213142,213177,213246,213441,213491,213527,213731,213791,213916,214043,214110,214159,214270,214316,214356,214429,214483,214582,214643,215102,215154,215225,215393,215471,215540,221934,221968,222008,222050,225240,225300,235834,236915,236991,237132,237166,237257,237317,237396,237487,237559,237638,237767,237879,237933,238016,238083,238157,238253,238373,238439,240190,240264,240557,240654,240815,240896,240993,241841,241880,241934,241984,242046,242116,242178,242273,242358,242440,242526,242619,242668,242734,242933,242980,243024,243064,243111,243155,243215,243287,243394,243544,243635,243832,243881,243947,244154,244230,244304,244362,244402,244458,244567,244963,245001,245071,245121,245182,245294,245861,245967,246033,246096,246281,246361,246461,246534,246580,246644,246710,246760,246827,247235,247279,247337,247383,247497,247859,247913,247985,248184,248242,248314,248374,248516,248581,248633,248782,248906,248964,249026,249108,249171,249383,249574,249641,249706,249800,249927,249994,250045,250258,250330,250401,250464,250502,250561,250613,250671,250715,250935,250976,251029,251086,251156,251204,251240,251275", "endColumns": "60,49,48,48,48,49,47,46,53,57,48,48,48,47,51,75,77,53,47,45,51,72,68,74,73,69,65,49,63,37,156,42,54,84,41,59,83,52,47,244,49,302,43,66,97,43,39,59,47,37,82,55,71,376,385,63,73,71,39,61,61,676,85,83,63,75,55,70,67,203,53,306,70,49,38,56,67,48,67,69,69,41,63,73,41,73,160,67,89,76,66,85,110,71,65,58,143,59,57,57,57,39,52,43,68,91,49,359,66,53,74,54,59,573,598,75,71,47,65,46,137,61,299,49,501,59,136,146,146,60,42,47,73,95,83,64,52,138,91,57,67,69,306,244,195,139,85,70,112,63,59,71,51,49,67,54,57,39,85,47,67,59,56,37,228,46,73,61,51,52,147,56,140,219,37,55,36,36,34,34,68,41,49,35,49,59,72,66,66,48,51,45,39,72,53,43,60,458,51,70,167,77,68,35,33,39,41,57,59,69,56,75,140,33,90,59,78,90,71,78,128,57,53,82,66,73,95,119,65,41,73,292,96,160,80,96,45,38,53,49,61,69,61,94,84,81,85,92,48,65,81,46,43,39,46,43,59,71,106,59,90,92,48,65,206,75,73,57,39,55,56,39,37,69,49,60,58,58,59,65,62,184,79,99,72,45,63,65,49,66,59,43,57,45,113,361,53,71,45,57,71,59,58,64,51,58,39,57,61,81,62,211,79,66,64,93,62,66,50,212,71,70,62,37,58,51,57,43,50,40,52,56,69,47,35,34,43", "endOffsets": "183358,183408,183457,183506,183555,183605,183653,183700,183754,183812,183861,183910,183959,184007,184059,184135,184357,184411,184614,184660,185351,185424,185567,185642,185716,186001,186067,186117,186181,186219,186376,186419,186474,186559,186601,186661,186745,186798,186929,187174,187224,187527,187571,187638,187736,187930,187970,188030,188078,188116,188199,188255,188327,188704,189090,189154,189228,189300,189340,189402,189464,190141,190227,190311,190375,190451,190507,190578,190646,190850,190904,191211,191282,191332,191371,191428,192585,192634,192702,192772,192842,193395,193459,193533,193873,193947,194108,194176,194266,194343,194410,194496,194607,194679,194745,194804,194948,195008,195066,195124,195182,195301,198030,198074,198143,198235,198353,198713,198780,198834,198909,198964,199024,199598,200197,200273,201275,201323,201389,201436,201574,201636,201936,201986,202655,202715,202852,202999,203146,203207,203307,203355,203429,203525,203609,203674,203727,203866,203958,204016,204084,204154,204461,204706,204902,205042,205128,205257,205370,205434,205494,205566,205618,205668,205736,205791,205849,205889,206135,206389,206457,210604,211279,211317,211546,211593,211667,212263,212315,212368,212516,212573,212714,212934,212972,213028,213065,213102,213137,213172,213241,213283,213486,213522,213572,213786,213859,213978,214105,214154,214206,214311,214351,214424,214478,214522,214638,215097,215149,215220,215388,215466,215535,215571,221963,222003,222045,222103,225295,225365,235886,236986,237127,237161,237252,237312,237391,237482,237554,237633,237762,237820,237928,238011,238078,238152,238248,238368,238434,238476,240259,240552,240649,240810,240891,240988,241034,241875,241929,241979,242041,242111,242173,242268,242353,242435,242521,242614,242663,242729,242811,242975,243019,243059,243106,243150,243210,243282,243389,243449,243630,243723,243876,243942,244149,244225,244299,244357,244397,244453,244510,244602,244996,245066,245116,245177,245236,245348,245916,246028,246091,246276,246356,246456,246529,246575,246639,246705,246755,246822,246882,247274,247332,247378,247492,247854,247908,247980,248026,248237,248309,248369,248428,248576,248628,248687,248817,248959,249021,249103,249166,249378,249458,249636,249701,249795,249858,249989,250040,250253,250325,250396,250459,250497,250556,250608,250666,250710,250761,250971,251024,251081,251151,251199,251235,251270,251314"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7a26d25b3618e3c60f978a7b1cb6a100\\transformed\\jetified-play-services-ads-api-24.2.0\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "8636", "startColumns": "4", "startOffsets": "576148", "endLines": "8643", "endColumns": "8", "endOffsets": "576541"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8bd7feaae90e869538df51f29dd16595\\transformed\\jetified-media3-ui-1.6.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,11,12,23,30,35,36,38,42,62,140,148,194,268,269,279,280,281,293,528,546,556,557,558,559,563,576,577,578,579,584,591,594,595,596,597,598,615,620,621,622,623,624,625,626,627,628,629,630,649,650,651,700,714,727,730,731,742,869,870,871,872,873,874,875,2076,2077,2078,2079,2080,2081,2082,2083,2084,2085,2086,2087,2088,2089,2090,2091,2092,2093,2094,2095,2096,2097,2098,2099,2100,2101,2102,2103,2104,2105,2106,2107,2108,2109,2110,2786,2787,2788,2789,2790,2791,2792,2793,2794,2795,2796,2797,2798,2799,2800,2801,2802,2803,2804,2805,2806,2807,2808,2809,2810,2811,2812,2813,2814,2815,2816,2817,2818,2819,2820,2821,2822,2823,2824,2825,2826,2827,2828,2829,2830,2831,2878,2879,2880,2881,2882,2883,2884,2885,2886,2887,2888,2889,2890,2891,2892,2893,2894,2895,2896,2897,2898,2899,2900,2901,2902,2903,2904,2905,2906,2907,2908,2909,2910,2911,2912,2913,2914,2915,2916,2917,2918,2919,2920,3025,3026,3089,3093,3340,3341,3342,3343,3344,3345,3346,3347,3348,3349,3350,3351,3352,3353,3354,3355,3356,3357,3358,3359,3360,3361,3362,3363,3364,3365,3375,3376,3377,3378,3379,3380,3381,3382,3383,3384,3385,3386,3387,3388,3389,3390,3391,7397,7402,7406,7410,7414,7418,7422,7426,7430,7431,7437,7448,7452,7456,7460,7464,7468,7472,7476,7480,7484,7488,7492,7505,7510,7515,7520,7533,7541,7551,7555,7559", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,430,913,1209,1372,1418,1516,1642,2590,6893,7245,9579,13126,13186,13679,13731,13781,14351,26547,27282,27677,27725,27782,27829,28056,28658,28712,28766,28820,28968,29206,29362,29411,29472,29532,29588,30543,30713,30773,30826,30883,30938,30994,31051,31100,31151,31206,31260,32171,32227,32282,35165,35762,36396,36556,36604,36996,44979,45036,45093,45155,45222,45294,45338,108930,108986,109049,109122,109192,109251,109308,109355,109410,109455,109504,109559,109613,109663,109714,109768,109827,109877,109935,109991,110044,110107,110172,110235,110287,110347,110411,110477,110535,110607,110668,110738,110808,110873,110938,163642,163737,163842,163945,164026,164109,164190,164279,164372,164465,164558,164643,164738,164831,164908,165000,165078,165158,165236,165322,165404,165497,165575,165666,165747,165836,165939,166040,166124,166220,166317,166412,166505,166597,166690,166783,166876,166959,167046,167141,167234,167336,167428,167509,167604,167697,170667,170711,170752,170797,170845,170889,170932,170981,171028,171072,171128,171181,171223,171270,171318,171378,171416,171466,171510,171549,171599,171651,171689,171736,171783,171824,171863,171901,171945,171993,172035,172073,172115,172169,172216,172253,172302,172344,172385,172426,172468,172511,172549,178108,178186,182415,182712,206462,206544,206626,206768,206846,206933,207018,207085,207148,207240,207332,207397,207460,207522,207593,207703,207814,207924,207991,208071,208142,208209,208294,208379,208442,208530,209240,209382,209482,209530,209673,209736,209798,209863,209934,209992,210050,210116,210168,210230,210306,210382,210436,498131,498410,498641,498851,499064,499274,499496,499712,499916,499954,500308,501095,501336,501576,501833,502086,502339,502574,502821,503060,503304,503525,503720,504392,504683,504979,505282,505948,506482,506956,507167,507367", "endLines": "10,11,12,23,34,35,36,41,42,62,140,148,194,268,269,279,280,284,293,528,546,556,557,558,559,563,576,577,578,583,590,591,594,595,596,597,598,619,620,621,622,623,624,625,626,627,628,629,630,649,650,657,700,714,727,730,731,742,869,870,871,872,873,874,875,2076,2077,2078,2079,2080,2081,2082,2083,2084,2085,2086,2087,2088,2089,2090,2091,2092,2093,2094,2095,2096,2097,2098,2099,2100,2101,2102,2103,2104,2105,2106,2107,2108,2109,2110,2786,2787,2788,2789,2790,2791,2792,2793,2794,2795,2796,2797,2798,2799,2800,2801,2802,2803,2804,2805,2806,2807,2808,2809,2810,2811,2812,2813,2814,2815,2816,2817,2818,2819,2820,2821,2822,2823,2824,2825,2826,2827,2828,2829,2830,2831,2878,2879,2880,2881,2882,2883,2884,2885,2886,2887,2888,2889,2890,2891,2892,2893,2894,2895,2896,2897,2898,2899,2900,2901,2902,2903,2904,2905,2906,2907,2908,2909,2910,2911,2912,2913,2914,2915,2916,2917,2918,2919,2920,3025,3026,3092,3096,3340,3341,3342,3343,3344,3345,3346,3347,3348,3349,3350,3351,3352,3353,3354,3355,3356,3357,3358,3359,3360,3361,3362,3363,3364,3365,3375,3376,3377,3378,3379,3380,3381,3382,3383,3384,3385,3386,3387,3388,3389,3390,3391,7401,7405,7409,7413,7417,7421,7425,7429,7430,7436,7447,7451,7455,7459,7463,7467,7471,7475,7479,7483,7487,7491,7504,7509,7514,7519,7532,7540,7550,7554,7558,7562", "endColumns": "17,49,53,53,9,45,48,9,48,48,58,53,54,59,58,51,49,9,64,47,48,47,56,46,54,51,53,53,53,9,9,49,48,60,59,55,59,9,59,52,56,54,55,56,48,50,54,53,58,55,54,9,64,57,48,47,50,45,56,56,61,66,71,43,56,55,62,72,69,58,56,46,54,44,48,54,53,49,50,53,58,49,57,55,52,62,64,62,51,59,63,65,57,71,60,69,69,64,64,70,94,104,102,80,82,80,88,92,92,92,84,94,92,76,91,77,79,77,85,81,92,77,90,80,88,102,100,83,95,96,94,92,91,92,92,92,82,86,94,92,101,91,80,94,92,76,43,40,44,47,43,42,48,46,43,55,52,41,46,47,59,37,49,43,38,49,51,37,46,46,40,38,37,43,47,41,37,41,53,46,36,48,41,40,40,41,42,37,35,77,77,12,12,81,81,141,77,86,84,66,62,91,91,64,62,61,70,109,110,109,66,79,70,66,84,84,62,87,63,141,99,47,142,62,61,64,70,57,57,65,51,61,75,75,53,112,10,10,10,10,10,10,10,10,37,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10", "endOffsets": "375,425,479,962,1367,1413,1462,1637,1686,2634,6947,7294,9629,13181,13240,13726,13776,13904,14411,26590,27326,27720,27777,27824,27879,28103,28707,28761,28815,28963,29201,29251,29406,29467,29527,29583,29643,30708,30768,30821,30878,30933,30989,31046,31095,31146,31201,31255,31314,32222,32277,32564,35225,35815,36440,36599,36650,37037,45031,45088,45150,45217,45289,45333,45390,108981,109044,109117,109187,109246,109303,109350,109405,109450,109499,109554,109608,109658,109709,109763,109822,109872,109930,109986,110039,110102,110167,110230,110282,110342,110406,110472,110530,110602,110663,110733,110803,110868,110933,111004,163732,163837,163940,164021,164104,164185,164274,164367,164460,164553,164638,164733,164826,164903,164995,165073,165153,165231,165317,165399,165492,165570,165661,165742,165831,165934,166035,166119,166215,166312,166407,166500,166592,166685,166778,166871,166954,167041,167136,167229,167331,167423,167504,167599,167692,167769,170706,170747,170792,170840,170884,170927,170976,171023,171067,171123,171176,171218,171265,171313,171373,171411,171461,171505,171544,171594,171646,171684,171731,171778,171819,171858,171896,171940,171988,172030,172068,172110,172164,172211,172248,172297,172339,172380,172421,172463,172506,172544,172580,178181,178259,182707,182977,206539,206621,206763,206841,206928,207013,207080,207143,207235,207327,207392,207455,207517,207588,207698,207809,207919,207986,208066,208137,208204,208289,208374,208437,208525,208589,209377,209477,209525,209668,209731,209793,209858,209929,209987,210045,210111,210163,210225,210301,210377,210431,210544,498405,498636,498846,499059,499269,499491,499707,499911,499949,500303,501090,501331,501571,501828,502081,502334,502569,502816,503055,503299,503520,503715,504387,504678,504974,505277,505943,506477,506951,507162,507362,507538"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2689e40363c086e26e0a6a841af9a39c\\transformed\\cardview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "63,801,802,803,804,2011,2012,2013,3944,7369,7371,7374", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2639,40554,40615,40677,40739,104642,104701,104758,258672,496616,496680,496806", "endLines": "63,801,802,803,804,2011,2012,2013,3950,7370,7373,7376", "endColumns": "51,60,61,61,63,58,56,53,12,12,12,12", "endOffsets": "2686,40610,40672,40734,40798,104696,104753,104807,259081,496675,496801,496929"}}, {"source": "D:\\Duc\\AndroidStudioProjects\\TJ_BatteryOne\\app\\build\\generated\\res\\injectCrashlyticsMappingFileIdDebug\\values\\com_google_firebase_crashlytics_mappingfileid.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "3242", "startColumns": "4", "startOffsets": "195473", "endColumns": "175", "endOffsets": "195644"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5444be4bc77930bd89cfbb9f2224d8e4\\transformed\\navigation-ui-2.8.9\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1"}, "to": {"startLines": "3020,3301,3635,3636", "startColumns": "4,4,4,4", "startOffsets": "177795,203212,236690,236766", "endColumns": "52,56,75,86", "endOffsets": "177843,203264,236761,236848"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\69fa008bb70ecc0d8e73621c41f47f2d\\transformed\\jetified-window-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1"}, "to": {"startLines": "637,642,643,644,2874", "startColumns": "4,4,4,4,4", "startOffsets": "31643,31822,31882,31934,170453", "endLines": "641,642,643,644,2874", "endColumns": "11,59,51,44,59", "endOffsets": "31817,31877,31929,31974,170508"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\42f95d9fa807b14415e836fc15872a54\\transformed\\jetified-play-services-basement-18.5.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "3027,3251", "startColumns": "4,4", "startOffsets": "178264,196689", "endColumns": "67,166", "endOffsets": "178327,196851"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\237e0b5db534c615c4317f1b214e3e7f\\transformed\\jetified-play-services-ads-24.2.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2952,2953,3626,3629,3632,3658,3660,3662,3664,3666,3668,3670,3672,3726,3727,3728,3729,3730,3731,3732,3809", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "174255,174315,235891,236147,236405,238481,238689,238921,239149,239416,239591,239778,240001,244607,244649,244726,244764,244803,244856,244924,250766", "endLines": "2952,2953,3628,3631,3634,3659,3661,3663,3665,3667,3669,3671,3673,3726,3727,3728,3729,3730,3731,3732,3811", "endColumns": "59,49,11,11,11,19,86,68,62,17,24,81,48,41,76,37,38,52,67,38,11", "endOffsets": "174310,174360,236142,236400,236685,238684,238916,239144,239411,239586,239773,239996,240185,244644,244721,244759,244798,244851,244919,244958,250930"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\66d600e9b1169a1e725381a5921a9d13\\transformed\\jetified-glide-4.16.0\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2924", "startColumns": "4", "startOffsets": "172729", "endColumns": "57", "endOffsets": "172782"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\93d3043f0a8b9466a00a736e170a6ddc\\transformed\\appcompat-1.7.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "179,276,285,701,765,766,772,773,774,775,776,777,778,781,782,783,784,787,788,789,790,791,792,797,798,862,863,864,865,867,868,876,877,881,882,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1069,1070,1131,1132,1133,1134,1135,1136,1137,1138,1145,1146,1147,1148,1149,1150,1151,1152,1153,1154,1158,1159,1921,1922,1923,1924,1925,1926,1927,1928,1929,1930,1931,1932,1933,1934,1935,1936,1937,1938,1939,1940,1941,1942,1943,1944,1945,1946,1947,1948,1949,1950,1951,1952,1953,1954,1955,1956,1957,1958,1959,1960,1961,1962,1963,1964,1965,1966,1967,1968,1969,1970,1971,1972,1973,1974,1975,1976,1977,1978,1979,1980,1981,1982,1983,1984,1985,1986,1987,1988,1989,1990,1991,1992,1993,1994,1995,1996,1997,1998,1999,2000,2001,2074,2075,2115,2116,2117,2118,2119,2120,2121,2778,2779,2780,2781,2782,2783,2784,2785,2869,2870,2871,2872,2927,2958,2959,2970,3005,3015,3016,3019,3021,3117,3118,3121,3122,3125,3126,3127,3128,3129,3130,3131,3132,3133,3134,3137,3141,3142,3738,3828,3829,3884,3885,3886,3923,3931,3932,3936,3940,3951,3956,3985,3992,3996,4000,4005,4009,4013,4017,4021,4025,4029,4035,4039,4045,4049,4055,4059,4064,4068,4071,4075,4081,4085,4091,4095,4101,4104,4108,4112,4116,4120,4124,4125,4126,4127,4130,4133,4136,4139,4143,4144,4145,4146,4187,4190,4192,4194,4196,4201,4202,4206,4212,4216,4217,4219,4231,4232,4236,4242,4246,4326,4327,4331,4358,4362,4363,4367,6169,6341,6367,6538,6564,6595,6603,6609,6625,6647,6652,6657,6667,6676,6685,6689,6696,6715,6722,6723,6732,6735,6738,6742,6746,6750,6753,6754,6759,6764,6774,6779,6786,6792,6793,6796,6800,6805,6807,6809,6812,6815,6817,6821,6824,6831,6834,6837,6841,6843,6847,6849,6851,6853,6857,6865,6873,6885,6891,6900,6903,6914,6917,6918,6923,6924,7804,7873,7947,7948,7958,7967,7973,7975,7979,7982,7985,7988,7991,7994,7997,8000,8004,8007,8010,8013,8017,8020,8024,8179,8180,8181,8182,8183,8184,8185,8186,8187,8188,8189,8190,8191,8192,8193,8194,8195,8196,8197,8198,8199,8201,8203,8204,8205,8206,8207,8208,8209,8210,8212,8213,8215,8216,8218,8220,8221,8223,8224,8225,8226,8227,8228,8230,8231,8232,8233,8234,8519,8521,8523,8544,8545,8546,8547,8548,8549,8550,8551,8552,8553,8554,8555,8556,8558,8559,8560,8561,8562,8563,8564,8566,8570,8815,8816,8817,8818,8819,8820,8824,8825,8826,9367,9369,9371,9373,9375,9377,9378,9379,9380,9382,9384,9386,9387,9388,9389,9390,9391,9392,9393,9394,9395,9396,9397,9400,9401,9402,9403,9405,9407,9408,9410,9411,9413,9415,9417,9418,9419,9420,9421,9422,9423,9424,9425,9426,9427,9428,9430,9431,9432,9433,9435,9436,9437,9438,9439,9441,9443,9445,9447,9448,9449,9450,9451,9452,9453,9454,9455,9456,9457,9458,9459,9460,9461", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8629,13526,13909,35230,38033,38088,38472,38536,38606,38667,38742,38818,38895,39133,39218,39300,39376,39533,39610,39688,39794,39900,39979,40308,40365,44536,44610,44685,44750,44858,44918,45395,45467,45669,45736,59161,59220,59279,59338,59397,59456,59510,59564,59617,59671,59725,59779,64349,64423,64502,64575,64649,64720,64792,64864,65223,65280,65338,65411,65485,65559,65634,65706,65779,65849,66049,66109,98680,98749,98818,98888,98962,99038,99102,99179,99255,99332,99397,99466,99543,99618,99687,99755,99832,99898,99959,100056,100121,100190,100289,100360,100419,100477,100534,100593,100657,100728,100800,100872,100944,101016,101083,101151,101219,101278,101341,101405,101495,101586,101646,101712,101779,101845,101915,101979,102032,102099,102160,102227,102340,102398,102461,102526,102591,102666,102739,102811,102855,102902,102948,102997,103058,103119,103180,103242,103306,103370,103434,103499,103562,103622,103683,103749,103808,103868,103930,104001,104061,108757,108843,111215,111305,111392,111480,111562,111645,111735,163189,163241,163299,163344,163410,163474,163531,163588,170174,170231,170279,170328,172904,174575,174622,175163,176989,177481,177545,177735,177848,184140,184214,184416,184486,184665,184726,184789,184855,184919,184990,185053,185118,185182,185243,185429,185721,185795,245241,251785,251863,254749,254837,254933,257221,257803,257892,258139,258420,259086,259371,261180,261657,261879,262101,262377,262604,262834,263064,263294,263524,263751,264170,264396,264821,265051,265479,265698,265981,266189,266320,266547,266973,267198,267625,267846,268271,268391,268667,268968,269292,269583,269897,270034,270165,270270,270512,270679,270883,271091,271362,271474,271586,271691,273784,273998,274144,274284,274370,274718,274806,275052,275470,275719,275801,275899,276556,276656,276908,277332,277587,283743,283832,284069,286093,286335,286437,286690,422259,432940,434456,445151,446679,448436,449062,449482,450743,452008,452264,452500,453047,453541,454146,454344,454924,456292,456667,456785,457323,457480,457676,457949,458205,458375,458516,458580,458945,459312,459988,460252,460590,460943,461037,461223,461529,461791,461916,462043,462282,462493,462612,462805,462982,463437,463618,463740,463999,464112,464299,464401,464508,464637,464912,465420,465916,466793,467087,467657,467806,468538,468710,468794,469130,469222,520993,526224,531939,532001,532579,533163,533437,533550,533779,533939,534091,534262,534428,534597,534764,534927,535170,535340,535513,535684,535958,536157,536362,546016,546100,546196,546292,546390,546490,546592,546694,546796,546898,547000,547100,547196,547308,547437,547560,547691,547822,547920,548034,548128,548268,548402,548498,548610,548710,548826,548922,549034,549134,549274,549410,549574,549704,549862,550012,550153,550297,550432,550544,550694,550822,550950,551086,551218,551348,551478,551590,569248,569394,569538,570639,570705,570795,570871,570975,571065,571167,571275,571383,571483,571563,571655,571753,571863,571915,571993,572099,572191,572295,572405,572527,572690,589830,589910,590010,590100,590210,590300,590541,590635,590741,630620,630720,630832,630946,631062,631178,631272,631386,631498,631600,631720,631842,631924,632028,632148,632274,632372,632466,632554,632666,632782,632904,633016,633191,633307,633393,633485,633597,633721,633788,633914,633982,634110,634254,634382,634451,634546,634661,634774,634873,634982,635093,635204,635305,635410,635510,635640,635731,635854,635948,636060,636146,636250,636346,636434,636552,636656,636760,636886,636974,637082,637182,637272,637382,637466,637568,637652,637706,637770,637876,637962,638072,638156", "endLines": "179,276,285,701,765,766,772,773,774,775,776,777,778,781,782,783,784,787,788,789,790,791,792,797,798,862,863,864,865,867,868,876,877,881,882,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1069,1070,1131,1132,1133,1134,1135,1136,1137,1138,1145,1146,1147,1148,1149,1150,1151,1152,1153,1154,1158,1159,1921,1922,1923,1924,1925,1926,1927,1928,1929,1930,1931,1932,1933,1934,1935,1936,1937,1938,1939,1940,1941,1942,1943,1944,1945,1946,1947,1948,1949,1950,1951,1952,1953,1954,1955,1956,1957,1958,1959,1960,1961,1962,1963,1964,1965,1966,1967,1968,1969,1970,1971,1972,1973,1974,1975,1976,1977,1978,1979,1980,1981,1982,1983,1984,1985,1986,1987,1988,1989,1990,1991,1992,1993,1994,1995,1996,1997,1998,1999,2000,2001,2074,2075,2115,2116,2117,2118,2119,2120,2121,2778,2779,2780,2781,2782,2783,2784,2785,2869,2870,2871,2872,2927,2958,2959,2970,3005,3015,3016,3019,3021,3117,3118,3121,3122,3125,3126,3127,3128,3129,3130,3131,3132,3133,3134,3137,3141,3142,3738,3828,3829,3884,3885,3886,3930,3931,3935,3939,3943,3955,3961,3991,3995,3999,4004,4008,4012,4016,4020,4024,4028,4034,4038,4044,4048,4054,4058,4063,4067,4070,4074,4080,4084,4090,4094,4100,4103,4107,4111,4115,4119,4123,4124,4125,4126,4129,4132,4135,4138,4142,4143,4144,4145,4146,4189,4191,4193,4195,4200,4201,4205,4211,4215,4216,4218,4230,4231,4235,4241,4245,4246,4326,4330,4357,4361,4362,4366,4394,6340,6366,6537,6563,6594,6602,6608,6624,6646,6651,6656,6666,6675,6684,6688,6695,6714,6721,6722,6731,6734,6737,6741,6745,6749,6752,6753,6758,6763,6773,6778,6785,6791,6792,6795,6799,6804,6806,6808,6811,6814,6816,6820,6823,6830,6833,6836,6840,6842,6846,6848,6850,6852,6856,6864,6872,6884,6890,6899,6902,6913,6916,6917,6922,6923,6928,7872,7942,7947,7957,7966,7967,7974,7978,7981,7984,7987,7990,7993,7996,7999,8003,8006,8009,8012,8016,8019,8023,8027,8179,8180,8181,8182,8183,8184,8185,8186,8187,8188,8189,8190,8191,8192,8193,8194,8195,8196,8197,8198,8200,8202,8203,8204,8205,8206,8207,8208,8209,8211,8212,8214,8215,8217,8219,8220,8222,8223,8224,8225,8226,8227,8229,8230,8231,8232,8233,8234,8520,8522,8524,8544,8545,8546,8547,8548,8549,8550,8551,8552,8553,8554,8555,8557,8558,8559,8560,8561,8562,8563,8565,8569,8573,8815,8816,8817,8818,8819,8823,8824,8825,8826,9368,9370,9372,9374,9376,9377,9378,9379,9381,9383,9385,9386,9387,9388,9389,9390,9391,9392,9393,9394,9395,9396,9399,9400,9401,9402,9404,9406,9407,9409,9410,9412,9414,9416,9417,9418,9419,9420,9421,9422,9423,9424,9425,9426,9427,9429,9430,9431,9432,9434,9435,9436,9437,9438,9440,9442,9444,9446,9447,9448,9449,9450,9451,9452,9453,9454,9455,9456,9457,9458,9459,9460,9461", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,69,84,60,62,65,63,70,62,64,63,60,60,73,73,140,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119", "endOffsets": "8679,13566,13953,35266,38083,38145,38531,38601,38662,38737,38813,38890,38968,39213,39295,39371,39447,39605,39683,39789,39895,39974,40054,40360,40418,44605,44680,44745,44811,44913,44974,45462,45535,45731,45799,59215,59274,59333,59392,59451,59505,59559,59612,59666,59720,59774,59828,64418,64497,64570,64644,64715,64787,64859,64932,65275,65333,65406,65480,65554,65629,65701,65774,65844,65915,66104,66165,98744,98813,98883,98957,99033,99097,99174,99250,99327,99392,99461,99538,99613,99682,99750,99827,99893,99954,100051,100116,100185,100284,100355,100414,100472,100529,100588,100652,100723,100795,100867,100939,101011,101078,101146,101214,101273,101336,101400,101490,101581,101641,101707,101774,101840,101910,101974,102027,102094,102155,102222,102335,102393,102456,102521,102586,102661,102734,102806,102850,102897,102943,102992,103053,103114,103175,103237,103301,103365,103429,103494,103557,103617,103678,103744,103803,103863,103925,103996,104056,104124,108838,108925,111300,111387,111475,111557,111640,111730,111821,163236,163294,163339,163405,163469,163526,163583,163637,170226,170274,170323,170374,172933,174617,174666,175204,177016,177540,177602,177790,177900,184209,184279,184481,184566,184721,184784,184850,184914,184985,185048,185113,185177,185238,185299,185498,185790,185931,245289,251858,251948,254832,254928,255018,257798,257887,258134,258415,258667,259366,259759,261652,261874,262096,262372,262599,262829,263059,263289,263519,263746,264165,264391,264816,265046,265474,265693,265976,266184,266315,266542,266968,267193,267620,267841,268266,268386,268662,268963,269287,269578,269892,270029,270160,270265,270507,270674,270878,271086,271357,271469,271581,271686,271803,273993,274139,274279,274365,274713,274801,275047,275465,275714,275796,275894,276551,276651,276903,277327,277582,277676,283827,284064,286088,286330,286432,286685,288841,432935,434451,445146,446674,448431,449057,449477,450738,452003,452259,452495,453042,453536,454141,454339,454919,456287,456662,456780,457318,457475,457671,457944,458200,458370,458511,458575,458940,459307,459983,460247,460585,460938,461032,461218,461524,461786,461911,462038,462277,462488,462607,462800,462977,463432,463613,463735,463994,464107,464294,464396,464503,464632,464907,465415,465911,466788,467082,467652,467801,468533,468705,468789,469125,469217,469495,526219,531590,531996,532574,533158,533249,533545,533774,533934,534086,534257,534423,534592,534759,534922,535165,535335,535508,535679,535953,536152,536357,536687,546095,546191,546287,546385,546485,546587,546689,546791,546893,546995,547095,547191,547303,547432,547555,547686,547817,547915,548029,548123,548263,548397,548493,548605,548705,548821,548917,549029,549129,549269,549405,549569,549699,549857,550007,550148,550292,550427,550539,550689,550817,550945,551081,551213,551343,551473,551585,551725,569389,569533,569671,570700,570790,570866,570970,571060,571162,571270,571378,571478,571558,571650,571748,571858,571910,571988,572094,572186,572290,572400,572522,572685,572842,589905,590005,590095,590205,590295,590536,590630,590736,590828,630715,630827,630941,631057,631173,631267,631381,631493,631595,631715,631837,631919,632023,632143,632269,632367,632461,632549,632661,632777,632899,633011,633186,633302,633388,633480,633592,633716,633783,633909,633977,634105,634249,634377,634446,634541,634656,634769,634868,634977,635088,635199,635300,635405,635505,635635,635726,635849,635943,636055,636141,636245,636341,636429,636547,636651,636755,636881,636969,637077,637177,637267,637377,637461,637563,637647,637701,637765,637871,637957,638067,638151,638271"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f908cdc45776521b403beeef1508641c\\transformed\\core-1.16.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "527,779,780,799,800,1126,1127,2015,2016,2017,2018,2019,2020,2021,2751,2752,2753,2754,2755,2756,2757,2758,2759,2760,2761,2762,2763,2764,2765,2834,2835,2836,2931,2932,2971,2972,2973,2974,2975,2976,2977,2978,2979,2980,2981,2982,2983,2984,2985,2986,2987,2996,3088,3207,3208,3209,3210,3211,3212,3213,3771,8235,8236,8240,8241,8245,9462,9463", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "26487,38973,39045,40423,40488,64084,64153,104867,104937,105005,105077,105147,105208,105282,161453,161514,161575,161637,161701,161763,161824,161892,161992,162052,162118,162191,162260,162317,162369,168051,168123,168199,173113,173148,175209,175264,175327,175382,175440,175496,175554,175615,175678,175735,175786,175844,175894,175955,176012,176078,176112,176535,182345,192847,192914,192986,193055,193124,193198,193270,248113,551730,551847,552048,552158,552359,638276,638348", "endLines": "527,779,780,799,800,1126,1127,2015,2016,2017,2018,2019,2020,2021,2751,2752,2753,2754,2755,2756,2757,2758,2759,2760,2761,2762,2763,2764,2765,2834,2835,2836,2931,2932,2971,2972,2973,2974,2975,2976,2977,2978,2979,2980,2981,2982,2983,2984,2985,2986,2987,2996,3088,3207,3208,3209,3210,3211,3212,3213,3771,8235,8239,8240,8244,8245,9462,9463", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66", "endOffsets": "26542,39040,39128,40483,40549,64148,64211,104932,105000,105072,105142,105203,105277,105350,161509,161570,161632,161696,161758,161819,161887,161987,162047,162113,162186,162255,162312,162364,162426,168118,168194,168259,173143,173178,175259,175322,175377,175435,175491,175549,175610,175673,175730,175781,175839,175889,175950,176007,176073,176107,176142,176565,182410,192909,192981,193050,193119,193193,193265,193353,248179,551842,552043,552153,552354,552483,638343,638410"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4a649bc6d741f1561035089684f0b179\\transformed\\jetified-AdLib-1.2.7\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "785,884,3239,3335,3337,3443,3700,3724,3783,3796,3820,3826,3908,7377,8525", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "39452,45861,195306,206140,206263,214527,242816,244515,248822,249863,251319,251703,256442,496934,569676", "endLines": "785,884,3239,3335,3337,3443,3700,3724,3783,3796,3825,3827,3922,7380,8543", "endColumns": "41,52,47,57,82,54,116,51,83,63,12,12,12,12,12", "endOffsets": "39489,45909,195349,206193,206341,214577,242928,244562,248901,249922,251698,251780,257216,497146,570634"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9e0a763189144907fb0197c2b097244b\\transformed\\jetified-ui-release\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2837,2838,2839,2840,2841,2842,2843,2844,2845,2846,2847,2848,2849,2850,2851,2852,2853,2854,2855,2856,2857,2858,2859,2860,2861,2862,2863,2864,2865,2866,2867,2868,2873,2875,2876,2926,2928,3014,3240,3241,3293,3294,3319,3431,3433,3637,3649,3713,3714,3746,3769,3770,3776,3777,3781,7381,7563,7566", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "168264,168323,168382,168442,168502,168562,168622,168682,168742,168802,168862,168922,168982,169041,169101,169161,169221,169281,169341,169401,169461,169521,169581,169641,169700,169760,169820,169879,169938,169997,170056,170115,170379,170513,170571,172853,172938,177428,195354,195419,201991,202057,205133,213864,213983,236853,237825,243728,243778,245921,248031,248073,248433,248480,248692,497151,507543,507654", "endLines": "2837,2838,2839,2840,2841,2842,2843,2844,2845,2846,2847,2848,2849,2850,2851,2852,2853,2854,2855,2856,2857,2858,2859,2860,2861,2862,2863,2864,2865,2866,2867,2868,2873,2875,2876,2926,2928,3014,3240,3241,3293,3294,3319,3431,3433,3637,3649,3713,3714,3746,3769,3770,3776,3777,3781,7383,7565,7569", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,41,39,46,35,89,12,12,12", "endOffsets": "168318,168377,168437,168497,168557,168617,168677,168737,168797,168857,168917,168977,169036,169096,169156,169216,169276,169336,169396,169456,169516,169576,169636,169695,169755,169815,169874,169933,169992,170051,170110,170169,170448,170566,170621,172899,172988,177476,195414,195468,202052,202153,205186,213911,214038,236910,237874,243773,243827,245962,248068,248108,248475,248511,248777,497258,507649,507844"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9bed8d4710c38689abca92b67b225ab3\\transformed\\coordinatorlayout-1.1.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "141,11518", "startColumns": "4,4", "startOffsets": "6952,772156", "endLines": "141,11520", "endColumns": "60,12", "endOffsets": "7008,772296"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9ae9e6fb794aab6f23f82d9d2a805731\\transformed\\recyclerview-1.3.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "575,2111,2112,2113,2125,2126,2127,2930", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "28602,111009,111068,111116,111953,112028,112104,173047", "endColumns": "55,58,47,55,74,75,71,65", "endOffsets": "28653,111063,111111,111167,112023,112099,112171,173108"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\60a813200b74c1dc84d254b8806ebd17\\transformed\\transition-1.5.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2922,2923,2956,2962,2963,2997,2998,2999,3000,3001,3002,3003,3004", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "172642,172682,174465,174756,174811,176570,176615,176669,176725,176777,176829,176878,176939", "endColumns": "39,46,42,54,46,44,53,55,51,51,48,60,49", "endOffsets": "172677,172724,174503,174806,174853,176610,176664,176720,176772,176824,176873,176934,176984"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fc35ffdefd2abfa5aac2f445dfc58ee4\\transformed\\jetified-sdp-android-1.1.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "1161,1163,1164,1165,1166,1167,1168,1169,1170,1171,1172,1174,1175,1176,1177,1178,1179,1180,1181,1182,1183,1184,1186,1187,1188,1189,1190,1191,1192,1193,1194,1195,1196,1198,1199,1200,1201,1202,1203,1204,1205,1206,1207,1208,1210,1211,1212,1213,1214,1215,1216,1217,1218,1219,1220,1222,1223,1224,1225,1226,1227,1228,1229,1230,1231,1232,1234,1235,1236,1237,1238,1239,1240,1241,1242,1243,1244,1246,1247,1248,1249,1250,1251,1252,1253,1254,1255,1256,1258,1259,1260,1261,1262,1263,1264,1265,1266,1267,1268,1270,1271,1272,1273,1274,1275,1276,1277,1278,1279,1280,1282,1284,1285,1286,1287,1288,1289,1290,1291,1292,1293,1294,1296,1297,1298,1299,1300,1301,1302,1303,1304,1305,1306,1308,1309,1310,1311,1312,1313,1314,1315,1316,1317,1318,1320,1321,1322,1323,1324,1325,1326,1327,1328,1329,1330,1332,1333,1334,1335,1336,1337,1338,1339,1340,1341,1342,1344,1345,1346,1347,1348,1349,1350,1351,1352,1353,1354,1356,1357,1358,1359,1360,1361,1362,1363,1364,1365,1366,1368,1369,1370,1371,1372,1373,1374,1375,1376,1377,1378,1380,1381,1382,1383,1384,1385,1386,1387,1388,1389,1390,1392,1393,1394,1395,1396,1397,1398,1399,1400,1401,1402,1404,1406,1407,1408,1409,1410,1411,1412,1413,1414,1415,1416,1418,1419,1420,1421,1422,1423,1424,1425,1426,1427,1428,1430,1431,1432,1433,1434,1435,1436,1437,1438,1439,1440,1442,1443,1444,1445,1446,1447,1448,1449,1450,1451,1452,1454,1455,1456,1457,1458,1459,1460,1461,1462,1463,1464,1466,1467,1468,1469,1470,1471,1472,1473,1474,1475,1476,1478,1479,1480,1481,1482,1483,1484,1485,1486,1487,1488,1490,1491,1492,1493,1494,1495,1496,1497,1498,1499,1500,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1526,1528,1529,1530,1531,1532,1533,1534,1535,1536,1537,1538,1540,1541,1542,1543,1544,1545,1546,1547,1548,1549,1550,1552,1553,1554,1555,1556,1557,1558,1559,1560,1561,1562,1564,1565,1566,1567,1568,1569,1570,1571,1572,1573,1574,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1586,1588,1589,1590,1591,1592,1593,1594,1595,1596,1597,1598,1600,1601,1602,1603,1604,1605,1606,1607,1608,1609,1610,1612,1613,1614,1615,1616,1617,1618,1619,1620,1621,1622,1624,1625,1626,1627,1628,1629,1630,1631,1632,1633,1634,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1646,1648,1650,1651,1652,1653,1654,1655,1656,1657,1658,1659,1660,1662,1663,1664,1665,1666,1667,1668,1669,1670,1671,1672,1674,1675,1676,1677,1678,1679,1680,1681,1682,1683,1684,1686,1687,1688,1689,1690,1691,1692,1693,1694,1695,1696,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1710,1711,1712,1713,1714,1715,1716,1717,1718,1719,1720,1722,1723,1724,1725,1726,1727,1728,1729,1730,1731,1732,1734,1735,1736,1737,1738,1739,1740,1741,1742,1743,1744,1746,1747,1748,1749,1750,1751,1752,1753,1754,1755,1756,1758,1759,1760,1761,1762,1763,1764,1765,1766,1767,1768,1770,1772,1773,1775,1777,1779,1781,1783,1785,1787,1789,1791,1793,1795,1797,1799,1801,1803,1805,1807,1809,1811,1813,1815,1817,1819,1821,1823,1825,1827,1829,1831,1833,1835,1837,1839,1841,1843,1845,1847,1849,1851,1853,1855,1857,1859,1861,1862,1863,1864,1865,1866,1867,1868,1869,1870,1871,1872,1873,1874,1875,1876,1877,1878,1879,1880,1881,1882,1883,1884,1885,1886,1887,1888,1889,1890,1891,1892,1893,1894,1895,1896,1897,1898,1899,1900,1901,1902,1903,1904,1905,1906,1907,1908,1909,1910,1911,1912,1913,1914,1915,1916,1917,1918,1919,1920", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "66210,66296,66339,66382,66425,66468,66511,66554,66597,66640,66683,66765,66808,66851,66894,66937,66980,67023,67066,67109,67152,67195,67277,67320,67363,67406,67449,67492,67535,67578,67621,67664,67707,67789,67832,67875,67918,67961,68004,68047,68090,68133,68176,68219,68301,68344,68387,68430,68473,68516,68559,68602,68645,68688,68731,68813,68856,68899,68942,68985,69028,69071,69114,69157,69200,69243,69325,69368,69411,69454,69497,69540,69583,69626,69669,69712,69755,69837,69880,69923,69966,70009,70052,70095,70138,70181,70224,70267,70349,70392,70435,70478,70521,70564,70607,70650,70693,70736,70779,70861,70904,70947,70990,71033,71076,71119,71162,71205,71248,71291,71373,71451,71494,71537,71580,71623,71666,71709,71752,71795,71838,71881,71963,72006,72049,72092,72135,72178,72221,72264,72307,72350,72393,72475,72518,72561,72604,72647,72690,72733,72776,72819,72862,72905,72987,73030,73073,73116,73159,73202,73245,73288,73331,73374,73417,73499,73542,73585,73628,73671,73714,73757,73800,73843,73886,73929,74011,74054,74097,74140,74183,74226,74269,74312,74355,74398,74441,74523,74566,74609,74652,74695,74738,74781,74824,74867,74910,74953,75035,75078,75121,75164,75207,75250,75293,75336,75379,75422,75465,75547,75590,75633,75676,75719,75762,75805,75848,75891,75934,75977,76059,76102,76145,76188,76231,76274,76317,76360,76403,76446,76489,76571,76649,76692,76735,76778,76821,76864,76907,76950,76993,77036,77079,77161,77204,77247,77290,77333,77376,77419,77462,77505,77548,77591,77673,77716,77759,77802,77845,77888,77931,77974,78017,78060,78103,78185,78228,78271,78314,78357,78400,78443,78486,78529,78572,78615,78697,78740,78783,78826,78869,78912,78955,78998,79041,79084,79127,79209,79252,79295,79338,79381,79424,79467,79510,79553,79596,79639,79721,79764,79807,79850,79893,79936,79979,80022,80065,80108,80151,80233,80276,80319,80362,80405,80448,80491,80534,80577,80620,80663,80745,80788,80831,80874,80917,80960,81003,81046,81089,81132,81175,81257,81300,81343,81386,81429,81472,81515,81558,81601,81644,81687,81769,81847,81890,81933,81976,82019,82062,82105,82148,82191,82234,82277,82359,82402,82445,82488,82531,82574,82617,82660,82703,82746,82789,82871,82914,82957,83000,83043,83086,83129,83172,83215,83258,83301,83383,83426,83469,83512,83555,83598,83641,83684,83727,83770,83813,83895,83938,83981,84024,84067,84110,84153,84196,84239,84282,84325,84407,84450,84493,84536,84579,84622,84665,84708,84751,84794,84837,84919,84962,85005,85048,85091,85134,85177,85220,85263,85306,85349,85431,85474,85517,85560,85603,85646,85689,85732,85775,85818,85861,85943,85986,86029,86072,86115,86158,86201,86244,86287,86330,86373,86455,86498,86541,86584,86627,86670,86713,86756,86799,86842,86885,86967,87045,87088,87131,87174,87217,87260,87303,87346,87389,87432,87475,87557,87600,87643,87686,87729,87772,87815,87858,87901,87944,87987,88069,88112,88155,88198,88241,88284,88327,88370,88413,88456,88499,88581,88624,88667,88710,88753,88796,88839,88882,88925,88968,89011,89093,89136,89179,89222,89265,89308,89351,89394,89437,89480,89523,89605,89648,89691,89734,89777,89820,89863,89906,89949,89992,90035,90117,90160,90203,90246,90289,90332,90375,90418,90461,90504,90547,90629,90672,90715,90758,90801,90844,90887,90930,90973,91016,91059,91141,91184,91227,91270,91313,91356,91399,91442,91485,91528,91571,91653,91696,91739,91782,91825,91868,91911,91954,91997,92040,92083,92165,92243,92286,92368,92450,92532,92614,92696,92778,92860,92942,93024,93106,93184,93266,93348,93430,93512,93594,93676,93758,93840,93922,94004,94082,94164,94246,94328,94410,94492,94574,94656,94738,94820,94902,94980,95062,95144,95226,95308,95390,95472,95554,95636,95718,95800,95878,95925,95972,96019,96066,96113,96160,96207,96254,96301,96348,96393,96440,96487,96534,96581,96628,96675,96722,96769,96816,96863,96908,96955,97002,97049,97096,97143,97190,97237,97284,97331,97378,97423,97470,97517,97564,97611,97658,97705,97752,97799,97846,97893,97938,97985,98032,98079,98126,98173,98220,98267,98314,98361,98408,98453,98500,98545,98590,98635", "endColumns": "42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,38,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,38,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,38,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,38,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,42,42,42,42,42,42,42,42,42,42,40,38,42,40,40,40,40,40,40,40,40,40,40,38,40,40,40,40,40,40,40,40,40,40,38,40,40,40,40,40,40,40,40,40,40,38,40,40,40,40,40,40,40,40,40,40,38,46,46,46,46,46,46,46,46,46,46,44,46,46,46,46,46,46,46,46,46,46,44,46,46,46,46,46,46,46,46,46,46,44,46,46,46,46,46,46,46,46,46,46,44,46,46,46,46,46,46,46,46,46,46,44,46,44,44,44,44", "endOffsets": "66248,66334,66377,66420,66463,66506,66549,66592,66635,66678,66719,66803,66846,66889,66932,66975,67018,67061,67104,67147,67190,67231,67315,67358,67401,67444,67487,67530,67573,67616,67659,67702,67743,67827,67870,67913,67956,67999,68042,68085,68128,68171,68214,68255,68339,68382,68425,68468,68511,68554,68597,68640,68683,68726,68767,68851,68894,68937,68980,69023,69066,69109,69152,69195,69238,69279,69363,69406,69449,69492,69535,69578,69621,69664,69707,69750,69791,69875,69918,69961,70004,70047,70090,70133,70176,70219,70262,70303,70387,70430,70473,70516,70559,70602,70645,70688,70731,70774,70815,70899,70942,70985,71028,71071,71114,71157,71200,71243,71286,71327,71407,71489,71532,71575,71618,71661,71704,71747,71790,71833,71876,71917,72001,72044,72087,72130,72173,72216,72259,72302,72345,72388,72429,72513,72556,72599,72642,72685,72728,72771,72814,72857,72900,72941,73025,73068,73111,73154,73197,73240,73283,73326,73369,73412,73453,73537,73580,73623,73666,73709,73752,73795,73838,73881,73924,73965,74049,74092,74135,74178,74221,74264,74307,74350,74393,74436,74477,74561,74604,74647,74690,74733,74776,74819,74862,74905,74948,74989,75073,75116,75159,75202,75245,75288,75331,75374,75417,75460,75501,75585,75628,75671,75714,75757,75800,75843,75886,75929,75972,76013,76097,76140,76183,76226,76269,76312,76355,76398,76441,76484,76525,76605,76687,76730,76773,76816,76859,76902,76945,76988,77031,77074,77115,77199,77242,77285,77328,77371,77414,77457,77500,77543,77586,77627,77711,77754,77797,77840,77883,77926,77969,78012,78055,78098,78139,78223,78266,78309,78352,78395,78438,78481,78524,78567,78610,78651,78735,78778,78821,78864,78907,78950,78993,79036,79079,79122,79163,79247,79290,79333,79376,79419,79462,79505,79548,79591,79634,79675,79759,79802,79845,79888,79931,79974,80017,80060,80103,80146,80187,80271,80314,80357,80400,80443,80486,80529,80572,80615,80658,80699,80783,80826,80869,80912,80955,80998,81041,81084,81127,81170,81211,81295,81338,81381,81424,81467,81510,81553,81596,81639,81682,81723,81803,81885,81928,81971,82014,82057,82100,82143,82186,82229,82272,82313,82397,82440,82483,82526,82569,82612,82655,82698,82741,82784,82825,82909,82952,82995,83038,83081,83124,83167,83210,83253,83296,83337,83421,83464,83507,83550,83593,83636,83679,83722,83765,83808,83849,83933,83976,84019,84062,84105,84148,84191,84234,84277,84320,84361,84445,84488,84531,84574,84617,84660,84703,84746,84789,84832,84873,84957,85000,85043,85086,85129,85172,85215,85258,85301,85344,85385,85469,85512,85555,85598,85641,85684,85727,85770,85813,85856,85897,85981,86024,86067,86110,86153,86196,86239,86282,86325,86368,86409,86493,86536,86579,86622,86665,86708,86751,86794,86837,86880,86921,87001,87083,87126,87169,87212,87255,87298,87341,87384,87427,87470,87511,87595,87638,87681,87724,87767,87810,87853,87896,87939,87982,88023,88107,88150,88193,88236,88279,88322,88365,88408,88451,88494,88535,88619,88662,88705,88748,88791,88834,88877,88920,88963,89006,89047,89131,89174,89217,89260,89303,89346,89389,89432,89475,89518,89559,89643,89686,89729,89772,89815,89858,89901,89944,89987,90030,90071,90155,90198,90241,90284,90327,90370,90413,90456,90499,90542,90583,90667,90710,90753,90796,90839,90882,90925,90968,91011,91054,91095,91179,91222,91265,91308,91351,91394,91437,91480,91523,91566,91607,91691,91734,91777,91820,91863,91906,91949,91992,92035,92078,92119,92199,92281,92322,92404,92486,92568,92650,92732,92814,92896,92978,93060,93140,93220,93302,93384,93466,93548,93630,93712,93794,93876,93958,94038,94118,94200,94282,94364,94446,94528,94610,94692,94774,94856,94936,95016,95098,95180,95262,95344,95426,95508,95590,95672,95754,95834,95920,95967,96014,96061,96108,96155,96202,96249,96296,96343,96388,96435,96482,96529,96576,96623,96670,96717,96764,96811,96858,96903,96950,96997,97044,97091,97138,97185,97232,97279,97326,97373,97418,97465,97512,97559,97606,97653,97700,97747,97794,97841,97888,97933,97980,98027,98074,98121,98168,98215,98262,98309,98356,98403,98448,98495,98540,98585,98630,98675"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f1d8c3766b40fb978be1af5d4cc6d27e\\transformed\\navigation-fragment-2.8.9\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1"}, "to": {"startLines": "2766,2945,2965,2966", "startColumns": "4,4,4,4", "startOffsets": "162431,173810,174902,174961", "endColumns": "62,56,58,48", "endOffsets": "162489,173862,174956,175005"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5c5c72c6ff4a7863322da50648a25e99\\transformed\\browser-1.8.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "793,794,795,796,2006,2007,3265,3396,3397,3398", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "40059,40117,40183,40246,104353,104424,198240,211012,211079,211158", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "40112,40178,40241,40303,104419,104491,198303,211074,211153,211222"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\237635df39b25799c092d66a208ce67d\\transformed\\jetified-foundation-release\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "3790,3791", "startColumns": "4,4", "startOffsets": "249463,249519", "endColumns": "55,54", "endOffsets": "249514,249569"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\141e0148a23d0681a92a44c218f71e86\\transformed\\jetified-core-splashscreen-1.0.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "562,636,761,762,763,764,2771,2772,2773,2774,2775,2776,2777,3022,4323,4324,4325,7302,7304,8789,8798,8811", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "27996,31584,37757,37826,37898,37961,162675,162749,162825,162901,162978,163049,163118,177905,283477,283558,283650,493218,493327,588322,588782,589557", "endLines": "562,636,761,762,763,764,2771,2772,2773,2774,2775,2776,2777,3022,4323,4324,4325,7303,7305,8797,8810,8814", "endColumns": "59,58,68,71,62,71,73,75,75,76,70,68,70,67,80,91,92,12,12,12,12,12", "endOffsets": "28051,31638,37821,37893,37956,38028,162744,162820,162896,162973,163044,163113,163184,177968,283553,283645,283738,493322,493443,588777,589552,589825"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0b518e79f85ce070d76134f5e2085a96\\transformed\\jetified-customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "2929,2957", "startColumns": "4,4", "startOffsets": "172993,174508", "endColumns": "53,66", "endOffsets": "173042,174570"}}, {"source": "D:\\Duc\\AndroidStudioProjects\\TJ_BatteryOne\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "11571", "startColumns": "4", "startOffsets": "774172", "endLines": "11573", "endColumns": "12", "endOffsets": "774268"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b572512e02266e069f95737c22215ab9\\transformed\\jetified-media3-exoplayer-1.6.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "3366,3367,3368,3369,3370,3371,3372,3373,3374", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "208594,208664,208726,208791,208855,208932,208997,209087,209171", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "208659,208721,208786,208850,208927,208992,209082,209166,209235"}}, {"source": "D:\\Duc\\AndroidStudioProjects\\TJ_BatteryOne\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "3830,3857,7306,7333,7360,7365,7570,7575,7580,7606,7633,7659,7685,7690,7789,7794,7799,7968,8172,8174,8574,8602,8604,8608,11521,11526,11531,11536,11541,11546,11551,11556,11561,11566", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "251953,253347,493448,494849,496258,496442,507849,508033,508218,509549,510948,512284,513628,513818,520433,520623,520809,533254,545728,545832,572847,574314,574431,574622,772301,772488,772676,772863,773050,773237,773424,773611,773798,773985", "endLines": "3856,3883,7332,7359,7364,7368,7574,7579,7605,7632,7658,7684,7689,7694,7793,7798,7803,7972,8173,8178,8601,8603,8607,8613,11525,11530,11535,11540,11545,11550,11555,11560,11565,11570", "endColumns": "12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12", "endOffsets": "253342,254744,494844,496253,496437,496611,508028,508213,509544,510943,512279,513623,513813,514004,520618,520804,520988,533432,545827,546011,574309,574426,574617,575001,772483,772671,772858,773045,773232,773419,773606,773793,773980,774167"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\347d669cbb58eeb81531bf30e2f5697f\\transformed\\databinding-adapters-8.9.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "2954,2955,2988", "startColumns": "4,4,4", "startOffsets": "174365,174422,176147", "endColumns": "56,42,40", "endOffsets": "174417,174460,176183"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1eebb1a882efcc15d28c2876ceb6fba2\\transformed\\jetified-lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "3009", "startColumns": "4", "startOffsets": "177157", "endColumns": "42", "endOffsets": "177195"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5b80dcbf636dc26335bd1b8e4f16f918\\transformed\\material-1.12.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "24,37,53,57,58,59,60,61,65,66,67,68,69,71,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,151,152,182,184,185,186,187,188,189,190,191,192,193,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,275,277,278,286,287,288,289,290,291,292,294,444,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,524,525,526,561,592,593,601,602,603,604,605,606,607,608,609,610,614,631,632,633,635,645,647,648,658,659,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,691,692,693,694,695,696,697,702,703,715,716,726,729,770,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,928,929,930,931,932,933,934,935,936,937,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,964,965,966,967,968,969,970,971,972,973,974,975,976,977,978,979,980,981,982,983,984,985,986,987,988,989,990,991,992,993,994,995,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,1043,1044,1045,1046,1047,1048,1049,1050,1051,1052,1053,1054,1055,1056,1057,1058,1071,1072,1073,1074,1075,1076,1077,1078,1079,1080,1081,1082,1083,1084,1085,1086,1087,1088,1089,1090,1091,1092,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108,1109,1110,1111,1112,1113,1114,1115,1116,1117,1118,1119,1120,1121,1122,1123,1124,2002,2014,2026,2027,2028,2029,2030,2031,2032,2033,2034,2035,2036,2037,2038,2039,2040,2041,2042,2043,2044,2045,2046,2047,2048,2049,2050,2051,2052,2053,2054,2055,2056,2057,2058,2059,2060,2061,2062,2063,2064,2065,2066,2067,2068,2069,2070,2071,2072,2073,2128,2129,2130,2131,2132,2133,2134,2135,2136,2137,2138,2139,2140,2141,2142,2143,2144,2145,2146,2147,2148,2149,2150,2151,2152,2153,2154,2155,2156,2157,2158,2159,2160,2161,2162,2163,2164,2165,2166,2167,2168,2169,2170,2171,2172,2173,2174,2175,2176,2177,2178,2179,2180,2181,2182,2183,2184,2185,2186,2187,2188,2189,2190,2191,2192,2193,2194,2195,2196,2197,2198,2199,2200,2201,2202,2203,2204,2205,2206,2207,2208,2209,2210,2211,2212,2213,2214,2215,2216,2217,2218,2219,2220,2221,2222,2223,2224,2225,2226,2227,2228,2229,2230,2231,2232,2233,2234,2235,2236,2237,2238,2239,2240,2241,2242,2243,2244,2245,2246,2247,2248,2249,2250,2251,2252,2253,2254,2255,2256,2257,2258,2259,2260,2261,2262,2263,2264,2265,2266,2267,2268,2269,2270,2271,2272,2273,2274,2275,2276,2277,2278,2279,2280,2281,2282,2283,2284,2285,2286,2287,2288,2289,2290,2291,2292,2293,2294,2295,2296,2297,2298,2299,2300,2301,2302,2303,2304,2305,2306,2307,2308,2309,2310,2311,2312,2313,2314,2315,2316,2317,2318,2319,2320,2321,2322,2323,2324,2325,2326,2327,2328,2329,2330,2331,2332,2333,2334,2335,2336,2337,2338,2339,2340,2341,2342,2343,2344,2345,2346,2347,2348,2349,2350,2351,2352,2353,2354,2355,2356,2357,2358,2359,2360,2361,2362,2363,2364,2365,2366,2367,2368,2369,2370,2371,2372,2373,2374,2375,2376,2377,2378,2379,2380,2381,2382,2383,2384,2385,2386,2387,2388,2389,2390,2391,2392,2393,2394,2395,2396,2397,2398,2399,2400,2401,2402,2403,2404,2405,2406,2407,2408,2409,2410,2411,2412,2413,2414,2415,2416,2417,2418,2419,2420,2421,2422,2423,2424,2425,2426,2427,2428,2429,2430,2431,2432,2433,2434,2435,2436,2437,2438,2439,2440,2441,2442,2443,2444,2445,2446,2447,2448,2449,2450,2451,2452,2453,2454,2455,2456,2457,2458,2459,2460,2461,2462,2463,2464,2465,2466,2467,2468,2469,2470,2471,2472,2473,2474,2475,2476,2477,2478,2479,2480,2481,2482,2483,2484,2485,2486,2487,2488,2489,2490,2491,2492,2493,2494,2495,2496,2497,2498,2499,2500,2501,2502,2503,2504,2505,2506,2507,2508,2509,2510,2511,2512,2513,2514,2515,2516,2517,2518,2519,2520,2521,2522,2523,2524,2525,2526,2527,2528,2529,2530,2531,2532,2533,2534,2535,2536,2537,2538,2539,2540,2541,2542,2543,2544,2545,2546,2547,2548,2549,2550,2551,2552,2553,2554,2555,2556,2557,2558,2559,2560,2561,2562,2563,2564,2565,2566,2567,2568,2569,2570,2571,2572,2573,2574,2575,2576,2577,2578,2579,2580,2581,2582,2583,2584,2585,2586,2587,2588,2589,2590,2591,2592,2593,2594,2595,2596,2597,2598,2599,2600,2601,2602,2603,2604,2605,2606,2607,2608,2609,2610,2611,2612,2613,2614,2615,2616,2617,2618,2619,2620,2621,2622,2623,2624,2625,2626,2627,2628,2629,2630,2631,2632,2633,2634,2635,2636,2637,2638,2639,2640,2641,2642,2643,2644,2645,2646,2647,2648,2649,2650,2651,2652,2653,2654,2655,2656,2657,2658,2659,2660,2661,2662,2663,2664,2665,2666,2667,2668,2669,2670,2671,2672,2673,2674,2675,2676,2677,2678,2679,2680,2681,2682,2683,2684,2685,2686,2687,2688,2689,2690,2691,2692,2693,2694,2695,2696,2697,2698,2699,2700,2701,2702,2703,2704,2705,2706,2707,2708,2709,2710,2711,2712,2713,2714,2715,2716,2717,2718,2719,2720,2721,2722,2723,2724,2725,2726,2727,2728,2729,2730,2731,2732,2733,2734,2735,2736,2737,2738,2739,2740,2741,2742,2743,2744,2745,2746,2747,2748,2749,2750,2832,2833,2934,2935,2936,2938,2939,2940,2941,2942,2943,2946,2947,2948,2949,2950,2951,2961,2964,2967,2968,2989,2990,2991,2992,2993,2994,2995,3006,3017,3018,3023,3024,3028,3029,3030,3031,3032,3033,3034,3035,3036,3037,3038,3039,3040,3041,3042,3043,3044,3045,3046,3047,3048,3049,3050,3051,3052,3053,3054,3055,3056,3057,3059,3060,3061,3062,3063,3064,3065,3066,3067,3068,3069,3070,3071,3072,3073,3074,3075,3076,3077,3078,3079,3080,3081,3082,3083,3084,3085,3086,3087,3097,3164,3196,3197,3198,3199,3200,3201,3217,3218,3219,3237,3331,3336,3393,3394,3395,3424,3428,3437,3452,3453,3454,3455,3456,3457,3458,3459,3460,3461,3462,3463,3464,3465,3466,3467,3533,3534,3535,3536,3537,3538,3539,3540,3541,3542,3543,3544,3545,3546,3547,3548,3549,3550,3551,3552,3553,3554,3557,3558,3559,3560,3561,3562,3563,3564,3565,3566,3569,3572,3575,3576,3577,3578,3579,3580,3581,3582,3583,3584,3585,3586,3587,3588,3589,3590,3591,3592,3593,3594,3595,3596,3597,3598,3599,3600,3601,3602,3603,3604,3605,3606,3607,3608,3609,3610,3611,3612,3613,3614,3615,3616,3617,3618,3619,3620,3621,3622,3623,3624,3681,3682,3683,3684,3685,3740,3743,3744,3759,3760,3887,3891,3895,3899,3903,3904,3962,3970,3977,4147,4150,4160,4169,4178,4247,4248,4249,4250,4256,4257,4258,4259,4260,4261,4267,4268,4269,4270,4271,4276,4277,4281,4282,4288,4292,4293,4294,4295,4305,4306,4307,4311,4312,4318,4322,4395,4398,4399,4404,4405,4408,4409,4410,4411,4675,4682,4943,4949,5213,5220,5481,5487,5550,5632,5684,5766,5828,5910,5974,6026,6108,6116,6122,6133,6137,6141,6154,6929,6945,6952,6958,6975,6988,7008,7025,7034,7039,7046,7066,7079,7096,7102,7108,7115,7119,7125,7139,7142,7152,7153,7154,7202,7206,7210,7214,7215,7216,7219,7235,7242,7256,7301,7695,7701,7705,7709,7714,7721,7727,7728,7731,7735,7740,7753,7757,7762,7767,7772,7775,7778,7781,7785,7943,7944,7945,7946,8028,8029,8030,8031,8032,8033,8034,8035,8036,8037,8038,8039,8040,8041,8042,8043,8044,8045,8046,8050,8054,8058,8062,8066,8070,8074,8075,8076,8077,8078,8079,8080,8081,8085,8089,8090,8094,8095,8098,8102,8105,8108,8111,8115,8118,8121,8125,8129,8133,8137,8140,8141,8142,8143,8146,8150,8153,8156,8159,8162,8165,8168,8246,8249,8250,8253,8256,8257,8260,8261,8262,8266,8267,8272,8279,8286,8293,8300,8307,8314,8321,8328,8335,8344,8353,8362,8369,8378,8387,8390,8393,8394,8395,8396,8397,8398,8399,8400,8401,8402,8403,8404,8405,8409,8414,8419,8422,8423,8424,8425,8426,8434,8442,8443,8451,8455,8463,8471,8479,8487,8495,8496,8504,8512,8513,8516,8614,8616,8621,8623,8628,8632,8644,8645,8646,8647,8651,8655,8656,8660,8661,8662,8663,8664,8665,8666,8667,8668,8669,8670,8674,8675,8676,8677,8681,8682,8683,8684,8688,8692,8693,8697,8698,8699,8704,8705,8706,8707,8708,8709,8710,8711,8712,8713,8714,8715,8716,8717,8718,8719,8720,8721,8722,8723,8724,8728,8729,8730,8736,8737,8741,8743,8744,8749,8750,8751,8752,8753,8754,8758,8759,8760,8766,8767,8771,8773,8777,8781,8785,8827,8828,8829,8830,8833,8836,8839,8842,8845,8850,8854,8857,8858,8863,8867,8872,8878,8884,8889,8893,8898,8902,8906,8947,8948,8949,8950,8951,8955,8956,8957,8958,8962,8966,8970,8974,8978,8982,8986,8990,8996,8997,9038,9052,9057,9083,9090,9093,9104,9109,9112,9115,9170,9176,9177,9180,9183,9186,9189,9192,9195,9198,9202,9205,9206,9207,9215,9223,9226,9231,9236,9241,9246,9250,9254,9255,9263,9264,9265,9266,9267,9275,9280,9285,9286,9287,9288,9313,9319,9324,9327,9331,9334,9338,9348,9351,9356,9359,9363,9464,9472,9486,9499,9503,9518,9529,9532,9543,9548,9552,9587,9588,9589,9601,9609,9617,9625,9633,9653,9656,9683,9688,9708,9711,9714,9721,9734,9743,9746,9766,9776,9780,9784,9797,9801,9805,9809,9815,9819,9836,9844,9848,9852,9856,9859,9863,9867,9871,9881,9888,9895,9899,9925,9935,9960,9969,9989,9999,10003,10013,10038,10048,10051,10058,10065,10072,10073,10074,10075,10076,10083,10087,10093,10099,10100,10113,10114,10115,10118,10121,10124,10127,10130,10133,10136,10139,10142,10145,10148,10151,10154,10157,10160,10163,10166,10169,10172,10175,10178,10179,10187,10195,10196,10209,10219,10223,10228,10233,10237,10240,10244,10248,10251,10255,10258,10262,10267,10272,10275,10282,10286,10290,10299,10304,10309,10310,10314,10317,10321,10334,10339,10347,10351,10355,10372,10376,10381,10399,10406,10410,10440,10443,10446,10449,10452,10455,10458,10477,10483,10491,10498,10510,10518,10523,10531,10535,10553,10560,10576,10580,10588,10591,10596,10597,10598,10599,10603,10607,10611,10615,10650,10653,10657,10661,10695,10698,10702,10706,10715,10721,10724,10734,10738,10739,10746,10750,10757,10758,10759,10762,10767,10772,10773,10777,10792,10811,10815,10816,10828,10838,10839,10851,10856,10880,10883,10889,10892,10901,10909,10913,10916,10919,10922,10926,10929,10946,10950,10953,10968,10971,10979,10984,10991,10996,10997,11002,11003,11009,11015,11021,11053,11064,11081,11088,11092,11095,11108,11117,11121,11126,11130,11134,11138,11142,11146,11150,11154,11159,11162,11174,11179,11188,11191,11198,11199,11203,11212,11218,11222,11223,11227,11248,11254,11258,11262,11263,11281,11282,11283,11284,11285,11290,11293,11294,11300,11301,11313,11325,11332,11333,11338,11343,11344,11348,11362,11367,11373,11379,11385,11390,11396,11402,11403,11409,11424,11429,11438,11447,11450,11464,11469,11480,11484,11493,11502,11503,11510,16243,16244,16245,16246,16247,16248,16249,16250,16251,16252,16253,16254,16255,16256,16257,16258,16259,16260,16261,16262,16263,16264,16265,16266,16267,16268,16269,16270,16271,16272,16273,16274,16275,16276,16277,16278,16279,16280,16281,16282,16283,16284,16285,16286,16287,16288,16289,16290,16291,16292,16293,16294,16295,16296,16297,16298,16299,16300,16301,16302,16303,16304,16305,16306,16307,16308,16309,16310,16311,16312,16313,16314,16315,16316,16317,16318,16319,16320,16321,16322,16323,16324,16325,16326,16327,16328,16329,16330,16331,16332,16333,16334,16335,16336,16337,16338,16339,16340,16341,16342,16343,16344,16345,16346,16347,16348,16349,16350,16351,16352,16353,16354,16355,16356,16357,16358,16359,16360,16361,16362,16363,16364,16365,16366,16367,16368,16369,16370,16371,16372,16373,16374,16375,16376,16377,16378,16379,16380,16381,16382,16383,16384,16385,16386,16387,16388,16389,16390,16391,16392,16393,16394,16395,16396,16397,16398,16399,16400,16401,16402,16403,16404,16405,16406,16407,16408,16409,16410,16411,16412,16413,16414,16415,16416,16417,16418,16419,16420,16421,16422,16423,16424,16425,16426,16427,16428,16429,16430,16431,16432,16433,16434,16435,16436,16437,16438,16439,16440,16441,16442,16443,16444,16445,16446,16447,16448,16449,16450,16451,16452,16453,16454,16455,16456,16457,16458,16459,16460,16461,16462,16463,16464,16465,16466,16467,16468,16469,16470,16471,16472,16473,16474,16475,16476,16477,16478,16479,16480,16481,16482,16483,16484,16485,16486,16487,16488,16489,16490,16491,16492,16493,16494,16495,16496,16497,16498,16499,16500,16501,16502,16503,16504,16505,16506,16507,16508,16509,16510,16511,16512,16513,16514,16515,16516,16517,16518,16519,16520,16521,16522,16523,16524,16525,16526,16527,16528,16529,16530,16531,16532,16533,16534,16535,16536,16537,16538,16539,16540,16541,16542,16543,16544,16545,16546,16547,16548,16549,16550,16551,16552,16553,16554,16555,16556,16557,16558,16559,16560,16561,16562,16563,16564,16565,16566,16567,16568,16569,16570,16571,16572,16573,16574,16575,16576,16577,16578,16579,16580,16581,16582,16583,16584,16585,16586,16587,16588,16589,16590,16591,16592,16593,16594,16595,16596,16597,16598,16599,16600,16601,16602,16603,16604,16605,16606,16607,16608,16609,16610,16611,16612,16613,16614,16615,16616,16617", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "967,1467,2095,2293,2349,2409,2470,2535,2739,2789,2839,2892,2950,3049,3419,3467,3538,3610,3682,3755,3822,3871,3925,3962,4013,4073,4120,4176,4225,4283,4337,4398,4454,4505,4565,4621,4684,4733,4789,4845,4895,4954,5009,5071,5118,5172,5228,5280,5335,5389,5443,5497,5546,5604,5658,5715,5771,5818,5871,5927,5987,6050,6109,6171,6221,6275,6329,6377,6434,6487,7400,7454,8785,8896,8958,9014,9074,9127,9188,9267,9348,9420,9499,9634,9710,9788,9857,9933,10010,10081,10154,10230,10308,10377,10453,10530,10594,10665,13475,13571,13624,13958,14025,14078,14130,14180,14238,14303,14416,21357,21487,21553,21611,21680,21738,21807,21877,21950,22024,22092,22159,22229,22295,22368,22428,22504,22564,22624,22699,22767,22833,22901,22961,23020,23077,23143,23205,23262,23330,23403,23473,23535,23596,23664,23726,23796,23865,23921,23980,24042,24104,24171,24228,24289,24350,24411,24472,24528,24584,24640,24696,24754,24812,24870,24928,24985,25042,25099,25156,25215,25274,25332,25415,25498,25571,25625,25694,25750,25831,25912,25983,26318,26371,26429,27938,29256,29302,29765,29819,29889,29959,30024,30090,30155,30223,30292,30360,30490,31319,31378,31436,31534,31979,32075,32121,32569,32625,32719,32777,32835,32897,32960,33022,33081,33141,33206,33272,33337,33399,33461,33523,33585,33647,33709,33775,33842,33908,33971,34035,34098,34166,34227,34289,34351,34414,34478,34594,34658,34736,34795,34861,34941,35002,35271,35329,35820,35865,36332,36497,38349,42113,42187,42258,42324,42398,42467,42538,42611,42682,42750,42823,42899,42969,43047,43115,43181,43242,43311,43375,43441,43509,43575,43638,43706,43777,43842,43915,43978,44059,44123,44189,44259,44329,44399,44469,46068,46125,46183,46242,46302,46361,46420,46479,46538,46597,46656,46715,46774,46833,46892,46952,47013,47075,47136,47197,47258,47319,47380,47441,47501,47562,47623,47683,47744,47805,47866,47927,47988,48049,48110,48171,48232,48293,48354,48422,48491,48561,48630,48699,48768,48837,48906,48975,49044,49113,49182,49251,49311,49372,49434,49495,49556,49617,49678,49739,49800,49861,49922,49983,50044,50106,50169,50233,50296,50359,50422,50485,50548,50611,50674,50737,50800,50863,50924,50986,51049,51111,51173,51235,51297,51359,51421,51483,51545,51607,51669,51726,51812,51892,51982,52077,52169,52261,52351,52434,52527,52614,52711,52802,52903,52990,53093,53182,53281,53373,53473,53557,53651,53739,53837,53920,54011,54105,54204,54306,54404,54504,54591,54691,54777,54873,54961,55042,55133,55229,55322,55415,55506,55591,55685,55774,55872,55965,56067,56155,56259,56350,56450,56543,56644,56729,56824,56913,57012,57097,57189,57284,57384,57487,57586,57689,57778,57879,57966,58063,58151,58247,58339,58439,58529,58627,58712,58801,58890,58983,59070,59833,59899,59975,60044,60123,60196,60276,60356,60433,60501,60579,60655,60726,60807,60880,60963,61038,61123,61196,61277,61358,61432,61516,61586,61664,61734,61814,61892,61964,62046,62116,62193,62273,62358,62446,62530,62617,62691,62769,62847,62918,62999,63090,63173,63269,63367,63474,63539,63605,63658,63734,63800,63887,63963,104129,104812,105657,105711,105790,105868,105941,106006,106069,106135,106206,106277,106347,106409,106478,106544,106604,106671,106738,106794,106845,106898,106950,107004,107075,107138,107197,107259,107318,107391,107458,107528,107588,107651,107726,107798,107894,107965,108021,108092,108149,108206,108272,108336,108407,108464,108517,108580,108632,108690,112176,112245,112311,112370,112453,112512,112569,112636,112706,112780,112842,112911,112981,113080,113177,113276,113362,113448,113529,113604,113693,113784,113868,113927,113973,114039,114096,114163,114220,114302,114367,114433,114556,114640,114761,114826,114888,114986,115060,115143,115232,115296,115375,115449,115511,115607,115672,115731,115787,115843,115903,116010,116057,116117,116178,116242,116303,116363,116421,116464,116513,116565,116616,116668,116717,116766,116831,116897,116957,117018,117074,117133,117182,117230,117288,117345,117447,117504,117579,117627,117678,117740,117805,117857,117931,117994,118057,118125,118175,118237,118297,118354,118414,118463,118531,118637,118739,118808,118879,118935,118984,119084,119155,119265,119356,119438,119536,119592,119693,119803,119902,119965,120071,120148,120260,120387,120499,120626,120696,120810,120941,121038,121106,121224,121327,121445,121506,121580,121647,121752,121874,121948,122015,122125,122224,122297,122394,122516,122634,122752,122813,122935,123052,123120,123226,123328,123408,123479,123575,123642,123716,123790,123876,123966,124044,124121,124221,124292,124413,124534,124598,124723,124797,124921,125045,125112,125221,125349,125461,125540,125618,125719,125790,125912,126034,126099,126225,126337,126443,126511,126610,126714,126777,126843,126927,127040,127153,127271,127349,127421,127557,127693,127778,127918,128056,128194,128336,128418,128504,128581,128654,128763,128874,129002,129130,129262,129392,129522,129656,129745,129807,129903,129970,130087,130208,130305,130387,130474,130561,130692,130823,130958,131035,131112,131223,131337,131411,131520,131632,131699,131772,131837,131939,132035,132139,132207,132272,132366,132438,132548,132654,132727,132818,132920,133023,133118,133225,133330,133452,133574,133700,133759,133817,133941,134065,134193,134311,134429,134551,134637,134734,134868,135002,135082,135220,135352,135484,135620,135695,135771,135874,135948,136061,136142,136199,136260,136319,136379,136437,136498,136556,136606,136655,136722,136781,136840,136889,136960,137044,137114,137185,137265,137334,137397,137465,137531,137599,137664,137730,137807,137885,137991,138097,138193,138322,138411,138538,138604,138674,138760,138826,138909,138983,139081,139177,139273,139371,139480,139575,139664,139726,139786,139851,139908,139989,140043,140100,140197,140307,140368,140483,140604,140699,140791,140884,140940,140999,141048,141140,141189,141243,141297,141351,141405,141459,141514,141624,141734,141842,141952,142062,142172,142282,142390,142496,142600,142704,142808,142903,142998,143091,143184,143288,143394,143498,143602,143695,143788,143881,143974,144082,144188,144294,144400,144497,144592,144687,144782,144888,144994,145100,145206,145304,145399,145495,145592,145657,145761,145819,145883,145944,146006,146066,146131,146193,146261,146319,146382,146445,146512,146587,146660,146726,146778,146831,146883,146940,147024,147119,147204,147285,147365,147442,147521,147598,147672,147746,147817,147897,147969,148044,148109,148170,148230,148305,148379,148452,148522,148594,148664,148737,148801,148871,148917,148986,149038,149123,149206,149263,149329,149396,149462,149543,149618,149674,149727,149788,149846,149896,149945,149994,150043,150105,150157,150202,150283,150334,150388,150441,150495,150546,150595,150661,150712,150773,150834,150896,150946,150987,151064,151123,151182,151241,151302,151358,151414,151481,151542,151607,151662,151727,151796,151864,151942,152011,152071,152142,152216,152281,152353,152423,152490,152574,152643,152710,152780,152843,152910,152978,153061,153140,153230,153307,153375,153442,153520,153577,153634,153702,153768,153824,153884,153943,153997,154047,154097,154145,154207,154258,154331,154411,154491,154555,154622,154693,154751,154812,154878,154937,155004,155064,155124,155187,155255,155316,155383,155461,155531,155580,155637,155706,155767,155855,155943,156031,156119,156206,156293,156380,156467,156525,156599,156669,156725,156796,156861,156923,156998,157071,157161,157227,157293,157354,157418,157480,157538,157609,157692,157751,157822,157888,157953,158014,158073,158144,158210,158275,158358,158434,158509,158590,158650,158719,158789,158858,158913,158969,159025,159086,159144,159200,159259,159313,159368,159430,159487,159581,159650,159751,159802,159872,159935,159991,160049,160108,160162,160248,160332,160402,160471,160541,160656,160777,160844,160911,160986,161053,161112,161166,161220,161274,161327,161379,167774,167911,173230,173279,173329,173420,173468,173524,173582,173644,173699,173867,173938,174002,174061,174123,174189,174713,174858,175010,175055,176188,176239,176286,176331,176382,176433,176484,177021,177607,177673,177973,178036,178332,178389,178443,178498,178556,178611,178670,178726,178795,178864,178933,179003,179066,179129,179192,179255,179320,179385,179450,179515,179578,179642,179706,179770,179821,179899,179977,180048,180120,180193,180338,180404,180470,180538,180606,180672,180739,180813,180876,180933,180993,181058,181125,181190,181247,181308,181366,181470,181580,181689,181793,181871,181936,182003,182069,182139,182186,182238,182288,182982,187741,191433,191564,191748,191926,192164,192353,193538,193636,193751,195187,205894,206198,210609,210698,210855,213288,213577,214211,215576,215763,215859,215949,216045,216135,216301,216424,216547,216717,216823,216938,217053,217155,217261,217378,222108,222190,222363,222531,222679,222838,222993,223166,223283,223400,223568,223680,223794,223966,224142,224300,224433,224545,224691,224843,224975,225118,225370,225548,225684,225780,225916,226011,226178,226271,226363,226550,226706,226884,227048,227230,227547,227729,227911,228101,228333,228523,228700,228862,229019,229129,229312,229449,229653,229837,230021,230181,230339,230523,230750,230953,231124,231344,231566,231721,231921,232105,232208,232398,232539,232704,232875,233075,233279,233481,233646,233851,234050,234249,234446,234537,234686,234836,234920,235069,235214,235366,235507,235673,241039,241117,241418,241584,241739,245353,245511,245675,246887,247110,255023,255300,255572,255850,256095,256157,259764,260215,260671,271808,271956,272470,272907,273341,277681,277766,277887,277986,278391,278488,278605,278692,278815,278916,279322,279421,279540,279633,279740,280083,280190,280435,280556,280965,281213,281313,281418,281537,282046,282193,282312,282563,282696,283111,283365,288846,289093,289218,289626,289747,289975,290096,290229,290376,311098,311590,332061,332485,353252,353746,374262,374688,379529,384946,389037,394468,399210,404587,408571,412563,417954,418501,418934,419690,419920,420163,421330,469500,470404,470988,471461,472891,473635,474828,475882,476360,476653,477036,478551,479316,480459,480900,481341,481937,482211,482622,483638,483816,484569,484706,484797,486991,487257,487579,487789,487898,488017,488201,489319,489789,490540,493123,514009,514385,514613,514869,515128,515704,516058,516180,516319,516611,516871,517799,518085,518488,518890,519233,519445,519646,519859,520148,531595,531668,531755,531840,536692,536804,536910,537033,537165,537288,537418,537542,537675,537806,537931,538048,538168,538300,538428,538542,538660,538773,538894,539082,539269,539450,539633,539817,539982,540164,540284,540404,540512,540622,540734,540842,540952,541117,541283,541435,541600,541701,541821,541992,542153,542316,542477,542644,542763,542880,543060,543242,543423,543606,543761,543906,544028,544163,544326,544519,544645,544797,544939,545109,545265,545437,552488,552683,552775,552948,553110,553205,553374,553468,553557,553800,553889,554182,554598,555018,555439,555865,556282,556698,557115,557533,557947,558417,558890,559362,559773,560244,560716,560906,561112,561218,561326,561432,561544,561658,561770,561884,562000,562114,562222,562332,562440,562702,563081,563485,563632,563740,563850,563958,564072,564481,564895,565011,565429,565670,566100,566535,566945,567367,567777,567899,568308,568724,568846,569064,575006,575074,575418,575498,575854,576004,576546,576622,576734,576824,577086,577351,577459,577611,577719,577795,577907,577997,578099,578207,578315,578415,578523,578608,578774,578878,579006,579093,579260,579338,579452,579544,579808,580075,580185,580338,580448,580532,580921,581019,581127,581221,581351,581459,581581,581717,581825,581945,582079,582201,582329,582471,582597,582737,582863,582981,583113,583211,583321,583621,583733,583851,584315,584431,584734,584860,584956,585357,585467,585591,585729,585839,585961,586273,586397,586527,587003,587131,587446,587584,587746,587962,588118,590833,590901,590985,591089,591292,591481,591682,591875,592080,592393,592605,592771,592887,593133,593349,593662,594088,594550,594787,594939,595199,595343,595485,598717,598831,598951,599067,599161,599482,599581,599699,599800,600079,600364,600643,600925,601178,601437,601690,601946,602370,602446,605696,607051,607495,609349,609924,610132,611142,611522,611688,611829,616849,617275,617387,617522,617675,617872,618043,618226,618401,618588,618860,619018,619102,619206,619693,620249,620407,620626,620857,621080,621315,621537,621803,621941,622540,622654,622792,622904,623028,623599,624094,624640,624785,624878,624970,626897,627467,627765,627954,628160,628353,628563,629447,629592,629984,630142,630359,638415,638847,639722,640342,640539,641487,642252,642375,643148,643369,643569,645546,645646,645736,646422,647175,647940,648703,649478,650691,650856,652469,652790,653853,654063,654233,654803,655698,656331,656497,657983,658599,658835,659056,660014,660279,660544,660791,661205,661441,662726,663175,663362,663611,663853,664029,664270,664503,664728,665323,665798,666322,666583,667934,668409,669635,670105,671153,671605,671849,672306,673551,674034,674184,674739,675191,675591,675744,675889,676032,676102,676530,676818,677322,677831,677947,678849,678971,679083,679260,679526,679796,680062,680330,680586,680846,681102,681360,681612,681868,682120,682374,682606,682842,683094,683350,683602,683856,684088,684322,684434,685086,685541,685665,686757,687572,687768,688092,688481,688833,689074,689288,689587,689779,690094,690301,690647,690947,691348,691567,691980,692217,692587,693311,693666,693935,694075,694329,694473,694750,695742,696151,696783,697129,697497,698571,698934,699334,700842,701427,701745,704280,704474,704692,704918,705130,705329,705536,706740,707035,707592,707982,708614,709091,709336,709823,710069,711265,711662,712668,712890,713313,713504,713883,713971,714079,714187,714500,714825,715144,715475,718178,718366,718627,718876,721460,721652,721917,722170,722702,723110,723309,723893,724128,724252,724664,724878,725280,725383,725513,725688,725940,726136,726276,726470,727481,728550,728838,728968,729745,730402,730548,731254,731492,733032,733182,733599,733764,734450,734920,735116,735207,735291,735435,735669,735836,736764,737050,737210,737825,737984,738312,738539,739051,739413,739492,739831,739936,740301,740672,741033,742907,743536,744612,745036,745289,745441,746489,747226,747429,747675,747922,748140,748382,748703,748967,749272,749495,749806,749995,750710,750979,751473,751699,752139,752298,752582,753327,753692,753997,754155,754393,755712,756110,756338,756558,756700,757990,758096,758226,758364,758488,758776,758945,759045,759330,759444,760327,761082,761521,761645,761891,762084,762218,762409,763188,763406,763697,763976,764293,764515,764810,765093,765197,765538,766354,766670,767231,767737,767942,768728,769133,769794,769983,770534,771100,771220,771622,925275,925370,925463,925526,925608,925701,925794,925881,925979,926070,926161,926249,926333,926429,926529,926635,926738,926839,926943,927049,927148,927254,927356,927463,927572,927683,927814,927934,928050,928168,928267,928374,928490,928609,928737,928826,928921,928998,929087,929178,929271,929345,929442,929537,929635,929734,929838,929934,930036,930139,930239,930342,930427,930528,930626,930716,930811,930898,931004,931106,931200,931291,931385,931461,931553,931642,931745,931856,931939,932025,932120,932217,932313,932401,932502,932603,932706,932812,932910,933007,933102,933200,933303,933403,933506,933611,933729,933845,933940,934033,934118,934214,934308,934400,934483,934587,934692,934792,934893,934998,935098,935199,935298,935400,935494,935601,935703,935806,935899,935995,936097,936200,936296,936398,936501,936598,936701,936799,936903,937008,937105,937213,937327,937442,937550,937664,937779,937881,937986,938094,938204,938320,938437,938532,938629,938728,938833,938939,939038,939143,939249,939349,939455,939556,939663,939782,939881,939986,940088,940190,940290,940393,940488,940592,940677,940781,940885,940983,941087,941193,941291,941396,941494,941607,941701,941790,941879,941962,942053,942136,942234,942324,942420,942509,942603,942691,942787,942872,942980,943081,943182,943280,943386,943477,943576,943673,943771,943867,943960,944070,944168,944263,944373,944465,944565,944664,944751,944855,944960,945059,945166,945273,945372,945481,945573,945684,945795,945906,946010,946125,946241,946368,946488,946583,946678,946775,946874,946966,947065,947157,947256,947342,947436,947539,947635,947738,947834,947937,948034,948132,948235,948328,948418,948519,948602,948693,948778,948870,948973,949068,949164,949257,949351,949430,949537,949628,949727,949820,949923,950027,950128,950229,950333,950427,950531,950635,950748,950854,950960,951068,951185,951286,951394,951494,951597,951702,951809,951905,951984,952074,952158,952250,952323,952415,952504,952596,952681,952778,952871,952966,953065,953162,953253,953344,953436,953531,953638,953746,953848,953945,954042,954135,954222,954306,954403,954500,954593,954680,954771,954870,954969,955064,955153,955234,955333,955437,955534,955639,955736,955820,955919,956023,956120,956225,956322,956420,956521,956627,956726,956833,956932,957031,957122,957211,957300,957382,957475,957566,957677,957778,957878,957990,958103,958201,958309,958403,958503,958592,958684,958795,958905,959000,959116,959242,959368,959487,959615,959740,959865,959983,960110,960219,960328,960441,960564,960687,960803,960928,961025,961133,961255,961371,961487,961596,961684,961785,961874,961975,962062,962150,962247,962339,962445,962545,962621", "endLines": "24,37,53,57,58,59,60,61,65,66,67,68,69,71,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,151,152,182,184,185,186,187,188,189,190,191,192,193,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,275,277,278,286,287,288,289,290,291,292,294,444,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,519,524,525,526,561,592,593,601,602,603,604,605,606,607,608,609,613,614,631,632,633,635,645,647,648,658,659,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,691,692,693,694,695,696,697,702,703,715,716,726,729,770,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,928,929,930,931,932,933,934,935,936,937,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,964,965,966,967,968,969,970,971,972,973,974,975,976,977,978,979,980,981,982,983,984,985,986,987,988,989,990,991,992,993,994,995,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,1043,1044,1045,1046,1047,1048,1049,1050,1051,1052,1053,1054,1055,1056,1057,1058,1071,1072,1073,1074,1075,1076,1077,1078,1079,1080,1081,1082,1083,1084,1085,1086,1087,1088,1089,1090,1091,1092,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108,1109,1110,1111,1112,1113,1114,1115,1116,1117,1118,1119,1120,1121,1122,1123,1124,2002,2014,2026,2027,2028,2029,2030,2031,2032,2033,2034,2035,2036,2037,2038,2039,2040,2041,2042,2043,2044,2045,2046,2047,2048,2049,2050,2051,2052,2053,2054,2055,2056,2057,2058,2059,2060,2061,2062,2063,2064,2065,2066,2067,2068,2069,2070,2071,2072,2073,2128,2129,2130,2131,2132,2133,2134,2135,2136,2137,2138,2139,2140,2141,2142,2143,2144,2145,2146,2147,2148,2149,2150,2151,2152,2153,2154,2155,2156,2157,2158,2159,2160,2161,2162,2163,2164,2165,2166,2167,2168,2169,2170,2171,2172,2173,2174,2175,2176,2177,2178,2179,2180,2181,2182,2183,2184,2185,2186,2187,2188,2189,2190,2191,2192,2193,2194,2195,2196,2197,2198,2199,2200,2201,2202,2203,2204,2205,2206,2207,2208,2209,2210,2211,2212,2213,2214,2215,2216,2217,2218,2219,2220,2221,2222,2223,2224,2225,2226,2227,2228,2229,2230,2231,2232,2233,2234,2235,2236,2237,2238,2239,2240,2241,2242,2243,2244,2245,2246,2247,2248,2249,2250,2251,2252,2253,2254,2255,2256,2257,2258,2259,2260,2261,2262,2263,2264,2265,2266,2267,2268,2269,2270,2271,2272,2273,2274,2275,2276,2277,2278,2279,2280,2281,2282,2283,2284,2285,2286,2287,2288,2289,2290,2291,2292,2293,2294,2295,2296,2297,2298,2299,2300,2301,2302,2303,2304,2305,2306,2307,2308,2309,2310,2311,2312,2313,2314,2315,2316,2317,2318,2319,2320,2321,2322,2323,2324,2325,2326,2327,2328,2329,2330,2331,2332,2333,2334,2335,2336,2337,2338,2339,2340,2341,2342,2343,2344,2345,2346,2347,2348,2349,2350,2351,2352,2353,2354,2355,2356,2357,2358,2359,2360,2361,2362,2363,2364,2365,2366,2367,2368,2369,2370,2371,2372,2373,2374,2375,2376,2377,2378,2379,2380,2381,2382,2383,2384,2385,2386,2387,2388,2389,2390,2391,2392,2393,2394,2395,2396,2397,2398,2399,2400,2401,2402,2403,2404,2405,2406,2407,2408,2409,2410,2411,2412,2413,2414,2415,2416,2417,2418,2419,2420,2421,2422,2423,2424,2425,2426,2427,2428,2429,2430,2431,2432,2433,2434,2435,2436,2437,2438,2439,2440,2441,2442,2443,2444,2445,2446,2447,2448,2449,2450,2451,2452,2453,2454,2455,2456,2457,2458,2459,2460,2461,2462,2463,2464,2465,2466,2467,2468,2469,2470,2471,2472,2473,2474,2475,2476,2477,2478,2479,2480,2481,2482,2483,2484,2485,2486,2487,2488,2489,2490,2491,2492,2493,2494,2495,2496,2497,2498,2499,2500,2501,2502,2503,2504,2505,2506,2507,2508,2509,2510,2511,2512,2513,2514,2515,2516,2517,2518,2519,2520,2521,2522,2523,2524,2525,2526,2527,2528,2529,2530,2531,2532,2533,2534,2535,2536,2537,2538,2539,2540,2541,2542,2543,2544,2545,2546,2547,2548,2549,2550,2551,2552,2553,2554,2555,2556,2557,2558,2559,2560,2561,2562,2563,2564,2565,2566,2567,2568,2569,2570,2571,2572,2573,2574,2575,2576,2577,2578,2579,2580,2581,2582,2583,2584,2585,2586,2587,2588,2589,2590,2591,2592,2593,2594,2595,2596,2597,2598,2599,2600,2601,2602,2603,2604,2605,2606,2607,2608,2609,2610,2611,2612,2613,2614,2615,2616,2617,2618,2619,2620,2621,2622,2623,2624,2625,2626,2627,2628,2629,2630,2631,2632,2633,2634,2635,2636,2637,2638,2639,2640,2641,2642,2643,2644,2645,2646,2647,2648,2649,2650,2651,2652,2653,2654,2655,2656,2657,2658,2659,2660,2661,2662,2663,2664,2665,2666,2667,2668,2669,2670,2671,2672,2673,2674,2675,2676,2677,2678,2679,2680,2681,2682,2683,2684,2685,2686,2687,2688,2689,2690,2691,2692,2693,2694,2695,2696,2697,2698,2699,2700,2701,2702,2703,2704,2705,2706,2707,2708,2709,2710,2711,2712,2713,2714,2715,2716,2717,2718,2719,2720,2721,2722,2723,2724,2725,2726,2727,2728,2729,2730,2731,2732,2733,2734,2735,2736,2737,2738,2739,2740,2741,2742,2743,2744,2745,2746,2747,2748,2749,2750,2832,2833,2934,2935,2936,2938,2939,2940,2941,2942,2943,2946,2947,2948,2949,2950,2951,2961,2964,2967,2968,2989,2990,2991,2992,2993,2994,2995,3006,3017,3018,3023,3024,3028,3029,3030,3031,3032,3033,3034,3035,3036,3037,3038,3039,3040,3041,3042,3043,3044,3045,3046,3047,3048,3049,3050,3051,3052,3053,3054,3055,3056,3057,3059,3060,3061,3062,3063,3064,3065,3066,3067,3068,3069,3070,3071,3072,3073,3074,3075,3076,3077,3078,3079,3080,3081,3082,3083,3084,3085,3086,3087,3100,3164,3196,3197,3198,3199,3200,3201,3217,3218,3219,3237,3333,3336,3393,3394,3395,3424,3428,3437,3452,3453,3454,3455,3456,3457,3458,3459,3460,3461,3462,3463,3464,3465,3466,3467,3533,3534,3535,3536,3537,3538,3539,3540,3541,3542,3543,3544,3545,3546,3547,3548,3549,3550,3551,3552,3553,3554,3557,3558,3559,3560,3561,3562,3563,3564,3565,3568,3571,3574,3575,3576,3577,3578,3579,3580,3581,3582,3583,3584,3585,3586,3587,3588,3589,3590,3591,3592,3593,3594,3595,3596,3597,3598,3599,3600,3601,3602,3603,3604,3605,3606,3607,3608,3609,3610,3611,3612,3613,3614,3615,3616,3617,3618,3619,3620,3621,3622,3623,3624,3681,3682,3683,3684,3685,3742,3743,3744,3759,3760,3890,3894,3898,3902,3903,3907,3969,3976,3984,4149,4159,4168,4177,4186,4247,4248,4249,4255,4256,4257,4258,4259,4260,4266,4267,4268,4269,4270,4275,4276,4280,4281,4287,4291,4292,4293,4294,4304,4305,4306,4310,4311,4317,4321,4322,4397,4398,4403,4404,4407,4408,4409,4410,4674,4681,4942,4948,5212,5219,5480,5486,5549,5631,5683,5765,5827,5909,5973,6025,6107,6115,6121,6132,6136,6140,6153,6168,6944,6951,6957,6974,6987,7007,7024,7033,7038,7045,7065,7078,7095,7101,7107,7114,7118,7124,7138,7141,7151,7152,7153,7201,7205,7209,7213,7214,7215,7218,7234,7241,7255,7300,7301,7700,7704,7708,7713,7720,7726,7727,7730,7734,7739,7752,7756,7761,7766,7771,7774,7777,7780,7784,7788,7943,7944,7945,7946,8028,8029,8030,8031,8032,8033,8034,8035,8036,8037,8038,8039,8040,8041,8042,8043,8044,8045,8049,8053,8057,8061,8065,8069,8073,8074,8075,8076,8077,8078,8079,8080,8084,8088,8089,8093,8094,8097,8101,8104,8107,8110,8114,8117,8120,8124,8128,8132,8136,8139,8140,8141,8142,8145,8149,8152,8155,8158,8161,8164,8167,8171,8248,8249,8252,8255,8256,8259,8260,8261,8265,8266,8271,8278,8285,8292,8299,8306,8313,8320,8327,8334,8343,8352,8361,8368,8377,8386,8389,8392,8393,8394,8395,8396,8397,8398,8399,8400,8401,8402,8403,8404,8408,8413,8418,8421,8422,8423,8424,8425,8433,8441,8442,8450,8454,8462,8470,8478,8486,8494,8495,8503,8511,8512,8515,8518,8615,8620,8622,8627,8631,8635,8644,8645,8646,8650,8654,8655,8659,8660,8661,8662,8663,8664,8665,8666,8667,8668,8669,8673,8674,8675,8676,8680,8681,8682,8683,8687,8691,8692,8696,8697,8698,8703,8704,8705,8706,8707,8708,8709,8710,8711,8712,8713,8714,8715,8716,8717,8718,8719,8720,8721,8722,8723,8727,8728,8729,8735,8736,8740,8742,8743,8748,8749,8750,8751,8752,8753,8757,8758,8759,8765,8766,8770,8772,8776,8780,8784,8788,8827,8828,8829,8832,8835,8838,8841,8844,8849,8853,8856,8857,8862,8866,8871,8877,8883,8888,8892,8897,8901,8905,8946,8947,8948,8949,8950,8954,8955,8956,8957,8961,8965,8969,8973,8977,8981,8985,8989,8995,8996,9037,9051,9056,9082,9089,9092,9103,9108,9111,9114,9169,9175,9176,9179,9182,9185,9188,9191,9194,9197,9201,9204,9205,9206,9214,9222,9225,9230,9235,9240,9245,9249,9253,9254,9262,9263,9264,9265,9266,9274,9279,9284,9285,9286,9287,9312,9318,9323,9326,9330,9333,9337,9347,9350,9355,9358,9362,9366,9471,9485,9498,9502,9517,9528,9531,9542,9547,9551,9586,9587,9588,9600,9608,9616,9624,9632,9652,9655,9682,9687,9707,9710,9713,9720,9733,9742,9745,9765,9775,9779,9783,9796,9800,9804,9808,9814,9818,9835,9843,9847,9851,9855,9858,9862,9866,9870,9880,9887,9894,9898,9924,9934,9959,9968,9988,9998,10002,10012,10037,10047,10050,10057,10064,10071,10072,10073,10074,10075,10082,10086,10092,10098,10099,10112,10113,10114,10117,10120,10123,10126,10129,10132,10135,10138,10141,10144,10147,10150,10153,10156,10159,10162,10165,10168,10171,10174,10177,10178,10186,10194,10195,10208,10218,10222,10227,10232,10236,10239,10243,10247,10250,10254,10257,10261,10266,10271,10274,10281,10285,10289,10298,10303,10308,10309,10313,10316,10320,10333,10338,10346,10350,10354,10371,10375,10380,10398,10405,10409,10439,10442,10445,10448,10451,10454,10457,10476,10482,10490,10497,10509,10517,10522,10530,10534,10552,10559,10575,10579,10587,10590,10595,10596,10597,10598,10602,10606,10610,10614,10649,10652,10656,10660,10694,10697,10701,10705,10714,10720,10723,10733,10737,10738,10745,10749,10756,10757,10758,10761,10766,10771,10772,10776,10791,10810,10814,10815,10827,10837,10838,10850,10855,10879,10882,10888,10891,10900,10908,10912,10915,10918,10921,10925,10928,10945,10949,10952,10967,10970,10978,10983,10990,10995,10996,11001,11002,11008,11014,11020,11052,11063,11080,11087,11091,11094,11107,11116,11120,11125,11129,11133,11137,11141,11145,11149,11153,11158,11161,11173,11178,11187,11190,11197,11198,11202,11211,11217,11221,11222,11226,11247,11253,11257,11261,11262,11280,11281,11282,11283,11284,11289,11292,11293,11299,11300,11312,11324,11331,11332,11337,11342,11343,11347,11361,11366,11372,11378,11384,11389,11395,11401,11402,11408,11423,11428,11437,11446,11449,11463,11468,11479,11483,11492,11501,11502,11509,11517,16243,16244,16245,16246,16247,16248,16249,16250,16251,16252,16253,16254,16255,16256,16257,16258,16259,16260,16261,16262,16263,16264,16265,16266,16267,16268,16269,16270,16271,16272,16273,16274,16275,16276,16277,16278,16279,16280,16281,16282,16283,16284,16285,16286,16287,16288,16289,16290,16291,16292,16293,16294,16295,16296,16297,16298,16299,16300,16301,16302,16303,16304,16305,16306,16307,16308,16309,16310,16311,16312,16313,16314,16315,16316,16317,16318,16319,16320,16321,16322,16323,16324,16325,16326,16327,16328,16329,16330,16331,16332,16333,16334,16335,16336,16337,16338,16339,16340,16341,16342,16343,16344,16345,16346,16347,16348,16349,16350,16351,16352,16353,16354,16355,16356,16357,16358,16359,16360,16361,16362,16363,16364,16365,16366,16367,16368,16369,16370,16371,16372,16373,16374,16375,16376,16377,16378,16379,16380,16381,16382,16383,16384,16385,16386,16387,16388,16389,16390,16391,16392,16393,16394,16395,16396,16397,16398,16399,16400,16401,16402,16403,16404,16405,16406,16407,16408,16409,16410,16411,16412,16413,16414,16415,16416,16417,16418,16419,16420,16421,16422,16423,16424,16425,16426,16427,16428,16429,16430,16431,16432,16433,16434,16435,16436,16437,16438,16439,16440,16441,16442,16443,16444,16445,16446,16447,16448,16449,16450,16451,16452,16453,16454,16455,16456,16457,16458,16459,16460,16461,16462,16463,16464,16465,16466,16467,16468,16469,16470,16471,16472,16473,16474,16475,16476,16477,16478,16479,16480,16481,16482,16483,16484,16485,16486,16487,16488,16489,16490,16491,16492,16493,16494,16495,16496,16497,16498,16499,16500,16501,16502,16503,16504,16505,16506,16507,16508,16509,16510,16511,16512,16513,16514,16515,16516,16517,16518,16519,16520,16521,16522,16523,16524,16525,16526,16527,16528,16529,16530,16531,16532,16533,16534,16535,16536,16537,16538,16539,16540,16541,16542,16543,16544,16545,16546,16547,16548,16549,16550,16551,16552,16553,16554,16555,16556,16557,16558,16559,16560,16561,16562,16563,16564,16565,16566,16567,16568,16569,16570,16571,16572,16573,16574,16575,16576,16577,16578,16579,16580,16581,16582,16583,16584,16585,16586,16587,16588,16589,16590,16591,16592,16593,16594,16595,16596,16597,16598,16599,16600,16601,16602,16603,16604,16605,16606,16607,16608,16609,16610,16611,16612,16613,16614,16615,16616,16617", "endColumns": "55,48,54,55,59,60,64,54,49,49,52,57,47,68,47,70,71,71,72,66,48,53,36,50,59,46,55,48,57,53,60,55,50,59,55,62,48,55,55,49,58,54,61,46,53,55,51,54,53,53,53,48,57,53,56,55,46,52,55,59,62,58,61,49,53,53,47,56,52,55,53,55,62,61,55,59,52,60,78,80,71,78,79,75,77,68,75,76,70,72,75,77,68,75,76,63,70,71,50,52,54,66,52,51,49,57,64,47,50,66,65,57,68,57,68,69,72,73,67,66,69,65,72,59,75,59,59,74,67,65,67,59,58,56,65,61,56,67,72,69,61,60,67,61,69,68,55,58,61,61,66,56,60,60,60,60,55,55,55,55,57,57,57,57,56,56,56,56,58,58,57,82,82,72,53,68,55,80,80,70,9,52,57,57,57,45,59,53,69,69,64,65,64,67,68,67,9,52,58,57,51,49,51,45,49,55,46,57,57,61,62,61,58,59,64,65,64,61,61,61,61,61,61,65,66,65,62,63,62,67,60,61,61,62,63,62,63,77,58,65,79,60,52,57,50,44,60,63,58,61,73,70,65,73,68,70,72,70,67,72,75,69,77,67,65,60,68,63,65,67,65,62,67,70,64,72,62,80,63,65,69,69,69,69,66,56,57,58,59,58,58,58,58,58,58,58,58,58,58,59,60,61,60,60,60,60,60,60,59,60,60,59,60,60,60,60,60,60,60,60,60,60,60,67,68,69,68,68,68,68,68,68,68,68,68,68,59,60,61,60,60,60,60,60,60,60,60,60,60,61,62,63,62,62,62,62,62,62,62,62,62,62,60,61,62,61,61,61,61,61,61,61,61,61,61,56,85,79,89,94,91,91,89,82,92,86,96,90,100,86,102,88,98,91,99,83,93,87,97,82,90,93,98,101,97,99,86,99,85,95,87,80,90,95,92,92,90,84,93,88,97,92,101,87,103,90,99,92,100,84,94,88,98,84,91,94,99,102,98,102,88,100,86,96,87,95,91,99,89,97,84,88,88,92,86,90,65,75,68,78,72,79,79,76,67,77,75,70,80,72,82,74,84,72,80,80,73,83,69,77,69,79,77,71,81,69,76,79,84,87,83,86,73,77,77,70,80,90,82,95,97,106,64,65,52,75,65,86,75,75,64,54,53,78,77,72,64,62,65,70,70,69,61,68,65,59,66,66,55,50,52,51,53,70,62,58,61,58,72,66,69,59,62,74,71,95,70,55,70,56,56,65,63,70,56,52,62,51,57,66,68,65,58,82,58,56,66,69,73,61,68,69,98,96,98,85,85,80,74,88,90,83,58,45,65,56,66,56,81,64,65,122,83,120,64,61,97,73,82,88,63,78,73,61,95,64,58,55,55,59,106,46,59,60,63,60,59,57,42,48,51,50,51,48,48,64,65,59,60,55,58,48,47,57,56,101,56,74,47,50,61,64,51,73,62,62,67,49,61,59,56,59,48,67,105,101,68,70,55,48,99,70,109,90,81,97,55,100,109,98,62,105,76,111,126,111,126,69,113,130,96,67,117,102,117,60,73,66,104,121,73,66,109,98,72,96,121,117,117,60,121,116,67,105,101,79,70,95,66,73,73,85,89,77,76,99,70,120,120,63,124,73,123,123,66,108,127,111,78,77,100,70,121,121,64,125,111,105,67,98,103,62,65,83,112,112,117,77,71,135,135,84,139,137,137,141,81,85,76,72,108,110,127,127,131,129,129,133,88,61,95,66,116,120,96,81,86,86,130,130,134,76,76,110,113,73,108,111,66,72,64,101,95,103,67,64,93,71,109,105,72,90,101,102,94,106,104,121,121,125,58,57,123,123,127,117,117,121,85,96,133,133,79,137,131,131,135,74,75,102,73,112,80,56,60,58,59,57,60,57,49,48,66,58,58,48,70,83,69,70,79,68,62,67,65,67,64,65,76,77,105,105,95,128,88,126,65,69,85,65,82,73,97,95,95,97,108,94,88,61,59,64,56,80,53,56,96,109,60,114,120,94,91,92,55,58,48,91,48,53,53,53,53,53,54,109,109,107,109,109,109,109,107,105,103,103,103,94,94,92,92,103,105,103,103,92,92,92,92,107,105,105,105,96,94,94,94,105,105,105,105,97,94,95,96,64,103,57,63,60,61,59,64,61,67,57,62,62,66,74,72,65,51,52,51,56,83,94,84,80,79,76,78,76,73,73,70,79,71,74,64,60,59,74,73,72,69,71,69,72,63,69,45,68,51,84,82,56,65,66,65,80,74,55,52,60,57,49,48,48,48,61,51,44,80,50,53,52,53,50,48,65,50,60,60,61,49,40,76,58,58,58,60,55,55,66,60,64,54,64,68,67,77,68,59,70,73,64,71,69,66,83,68,66,69,62,66,67,82,78,89,76,67,66,77,56,56,67,65,55,59,58,53,49,49,47,61,50,72,79,79,63,66,70,57,60,65,58,66,59,59,62,67,60,66,77,69,48,56,68,60,87,87,87,87,86,86,86,86,57,73,69,55,70,64,61,74,72,89,65,65,60,63,61,57,70,82,58,70,65,64,60,58,70,65,64,82,75,74,80,59,68,69,68,54,55,55,60,57,55,58,53,54,61,56,93,68,100,50,69,62,55,57,58,53,85,83,69,68,69,114,120,66,66,74,66,58,53,53,53,52,51,73,136,139,48,49,49,47,55,57,61,54,57,70,63,58,61,65,65,42,43,44,42,50,46,44,50,50,50,50,47,65,61,62,71,56,53,54,57,54,58,55,68,68,68,69,62,62,62,62,64,64,64,64,62,63,63,63,50,77,77,70,71,72,71,65,65,67,67,65,66,73,62,56,59,64,66,64,56,60,57,103,109,108,103,77,64,66,65,69,46,51,49,56,12,149,130,183,177,237,188,168,97,114,84,78,11,64,88,156,156,152,153,58,186,95,89,95,89,165,122,122,169,105,114,114,101,105,116,114,81,172,167,147,158,154,172,116,116,167,111,113,171,175,157,132,111,145,151,131,142,121,177,135,95,135,94,166,92,91,186,11,11,11,181,316,181,181,189,231,189,176,161,156,109,182,136,203,183,183,159,157,183,226,202,170,219,221,154,199,183,102,189,140,164,170,199,203,201,164,204,198,198,196,90,148,149,83,148,144,151,140,165,160,77,300,165,154,101,11,163,185,222,124,10,10,10,10,61,10,10,10,10,10,10,10,10,10,84,120,98,10,96,116,86,122,100,10,98,118,92,106,10,106,10,120,10,10,99,104,118,10,146,118,10,132,10,10,111,10,124,10,120,10,120,132,146,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,136,90,10,10,10,10,108,118,10,10,10,10,10,94,10,10,10,10,10,10,121,10,10,10,10,10,10,10,10,10,10,10,10,10,72,86,84,98,111,105,122,131,122,129,123,132,130,124,116,119,131,127,113,117,112,120,10,10,10,10,10,10,10,119,119,107,109,111,107,109,10,10,151,10,100,10,10,10,10,10,10,10,10,10,10,10,10,10,144,121,134,10,10,10,10,10,10,10,10,10,10,91,10,10,94,10,93,88,10,88,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,105,107,105,111,113,111,113,115,113,107,109,107,10,10,10,10,107,109,107,113,10,10,115,10,10,10,10,10,10,10,121,10,10,121,10,10,10,10,10,10,10,10,75,111,89,10,10,107,10,107,75,111,89,101,107,107,99,107,84,10,103,127,86,10,77,113,91,10,10,109,10,109,83,10,97,107,93,129,107,121,135,107,119,133,121,127,141,125,139,125,117,131,97,109,10,111,117,10,115,10,10,95,10,109,123,137,109,121,10,123,129,10,127,10,10,10,10,10,10,67,83,103,10,10,10,10,10,10,10,10,115,10,10,10,10,10,10,10,10,10,10,10,113,119,115,93,10,98,117,100,10,10,10,10,10,10,10,10,10,75,10,10,10,10,10,10,10,10,10,10,10,10,111,10,10,10,10,10,10,10,10,10,83,103,10,10,10,10,10,10,10,10,10,137,10,113,137,111,123,10,10,10,144,92,91,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,99,89,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,152,144,142,69,10,10,10,10,115,10,121,111,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,111,10,10,123,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,139,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,87,107,107,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,123,10,10,10,102,129,10,10,10,139,10,10,10,10,129,10,10,145,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,78,10,104,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,158,10,10,10,10,157,10,10,10,10,10,141,10,105,129,137,123,10,10,99,10,113,10,10,10,123,10,10,133,10,10,10,10,10,10,10,10,10,103,10,10,10,10,10,10,10,10,10,10,10,10,119,10,10,94,92,62,81,92,92,86,97,90,90,87,83,95,99,105,102,100,103,105,98,105,101,106,108,110,130,119,115,117,98,106,115,118,127,88,94,76,88,90,92,73,96,94,97,98,103,95,101,102,99,102,84,100,97,89,94,86,105,101,93,90,93,75,91,88,102,110,82,85,94,96,95,87,100,100,102,105,97,96,94,97,102,99,102,104,117,115,94,92,84,95,93,91,82,103,104,99,100,104,99,100,98,101,93,106,101,102,92,95,101,102,95,101,102,96,102,97,103,104,96,107,113,114,107,113,114,101,104,107,109,115,116,94,96,98,104,105,98,104,105,99,105,100,106,118,98,104,101,101,99,102,94,103,84,103,103,97,103,105,97,104,97,112,93,88,88,82,90,82,97,89,95,88,93,87,95,84,107,100,100,97,105,90,98,96,97,95,92,109,97,94,109,91,99,98,86,103,104,98,106,106,98,108,91,110,110,110,103,114,115,126,119,94,94,96,98,91,98,91,98,85,93,102,95,102,95,102,96,97,102,92,89,100,82,90,84,91,102,94,95,92,93,78,106,90,98,92,102,103,100,100,103,93,103,103,112,105,105,107,116,100,107,99,102,104,106,95,78,89,83,91,72,91,88,91,84,96,92,94,98,96,90,90,91,94,106,107,101,96,96,92,86,83,96,96,92,86,90,98,98,94,88,80,98,103,96,104,96,83,98,103,96,104,96,97,100,105,98,106,98,98,90,88,88,81,92,90,110,100,99,111,112,97,107,93,99,88,91,110,109,94,115,125,125,118,127,124,124,117,126,108,108,112,122,122,115,124,96,107,121,115,115,108,87,100,88,100,86,87,96,91,105,99,75,76", "endOffsets": "1018,1511,2145,2344,2404,2465,2530,2585,2784,2834,2887,2945,2993,3113,3462,3533,3605,3677,3750,3817,3866,3920,3957,4008,4068,4115,4171,4220,4278,4332,4393,4449,4500,4560,4616,4679,4728,4784,4840,4890,4949,5004,5066,5113,5167,5223,5275,5330,5384,5438,5492,5541,5599,5653,5710,5766,5813,5866,5922,5982,6045,6104,6166,6216,6270,6324,6372,6429,6482,6538,7449,7505,8843,8953,9009,9069,9122,9183,9262,9343,9415,9494,9574,9705,9783,9852,9928,10005,10076,10149,10225,10303,10372,10448,10525,10589,10660,10732,13521,13619,13674,14020,14073,14125,14175,14233,14298,14346,14462,21419,21548,21606,21675,21733,21802,21872,21945,22019,22087,22154,22224,22290,22363,22423,22499,22559,22619,22694,22762,22828,22896,22956,23015,23072,23138,23200,23257,23325,23398,23468,23530,23591,23659,23721,23791,23860,23916,23975,24037,24099,24166,24223,24284,24345,24406,24467,24523,24579,24635,24691,24749,24807,24865,24923,24980,25037,25094,25151,25210,25269,25327,25410,25493,25566,25620,25689,25745,25826,25907,25978,26107,26366,26424,26482,27991,29297,29357,29814,29884,29954,30019,30085,30150,30218,30287,30355,30485,30538,31373,31431,31483,31579,32026,32116,32166,32620,32667,32772,32830,32892,32955,33017,33076,33136,33201,33267,33332,33394,33456,33518,33580,33642,33704,33770,33837,33903,33966,34030,34093,34161,34222,34284,34346,34409,34473,34536,34653,34731,34790,34856,34936,34997,35050,35324,35375,35860,35921,36391,36551,38406,42182,42253,42319,42393,42462,42533,42606,42677,42745,42818,42894,42964,43042,43110,43176,43237,43306,43370,43436,43504,43570,43633,43701,43772,43837,43910,43973,44054,44118,44184,44254,44324,44394,44464,44531,46120,46178,46237,46297,46356,46415,46474,46533,46592,46651,46710,46769,46828,46887,46947,47008,47070,47131,47192,47253,47314,47375,47436,47496,47557,47618,47678,47739,47800,47861,47922,47983,48044,48105,48166,48227,48288,48349,48417,48486,48556,48625,48694,48763,48832,48901,48970,49039,49108,49177,49246,49306,49367,49429,49490,49551,49612,49673,49734,49795,49856,49917,49978,50039,50101,50164,50228,50291,50354,50417,50480,50543,50606,50669,50732,50795,50858,50919,50981,51044,51106,51168,51230,51292,51354,51416,51478,51540,51602,51664,51721,51807,51887,51977,52072,52164,52256,52346,52429,52522,52609,52706,52797,52898,52985,53088,53177,53276,53368,53468,53552,53646,53734,53832,53915,54006,54100,54199,54301,54399,54499,54586,54686,54772,54868,54956,55037,55128,55224,55317,55410,55501,55586,55680,55769,55867,55960,56062,56150,56254,56345,56445,56538,56639,56724,56819,56908,57007,57092,57184,57279,57379,57482,57581,57684,57773,57874,57961,58058,58146,58242,58334,58434,58524,58622,58707,58796,58885,58978,59065,59156,59894,59970,60039,60118,60191,60271,60351,60428,60496,60574,60650,60721,60802,60875,60958,61033,61118,61191,61272,61353,61427,61511,61581,61659,61729,61809,61887,61959,62041,62111,62188,62268,62353,62441,62525,62612,62686,62764,62842,62913,62994,63085,63168,63264,63362,63469,63534,63600,63653,63729,63795,63882,63958,64034,104189,104862,105706,105785,105863,105936,106001,106064,106130,106201,106272,106342,106404,106473,106539,106599,106666,106733,106789,106840,106893,106945,106999,107070,107133,107192,107254,107313,107386,107453,107523,107583,107646,107721,107793,107889,107960,108016,108087,108144,108201,108267,108331,108402,108459,108512,108575,108627,108685,108752,112240,112306,112365,112448,112507,112564,112631,112701,112775,112837,112906,112976,113075,113172,113271,113357,113443,113524,113599,113688,113779,113863,113922,113968,114034,114091,114158,114215,114297,114362,114428,114551,114635,114756,114821,114883,114981,115055,115138,115227,115291,115370,115444,115506,115602,115667,115726,115782,115838,115898,116005,116052,116112,116173,116237,116298,116358,116416,116459,116508,116560,116611,116663,116712,116761,116826,116892,116952,117013,117069,117128,117177,117225,117283,117340,117442,117499,117574,117622,117673,117735,117800,117852,117926,117989,118052,118120,118170,118232,118292,118349,118409,118458,118526,118632,118734,118803,118874,118930,118979,119079,119150,119260,119351,119433,119531,119587,119688,119798,119897,119960,120066,120143,120255,120382,120494,120621,120691,120805,120936,121033,121101,121219,121322,121440,121501,121575,121642,121747,121869,121943,122010,122120,122219,122292,122389,122511,122629,122747,122808,122930,123047,123115,123221,123323,123403,123474,123570,123637,123711,123785,123871,123961,124039,124116,124216,124287,124408,124529,124593,124718,124792,124916,125040,125107,125216,125344,125456,125535,125613,125714,125785,125907,126029,126094,126220,126332,126438,126506,126605,126709,126772,126838,126922,127035,127148,127266,127344,127416,127552,127688,127773,127913,128051,128189,128331,128413,128499,128576,128649,128758,128869,128997,129125,129257,129387,129517,129651,129740,129802,129898,129965,130082,130203,130300,130382,130469,130556,130687,130818,130953,131030,131107,131218,131332,131406,131515,131627,131694,131767,131832,131934,132030,132134,132202,132267,132361,132433,132543,132649,132722,132813,132915,133018,133113,133220,133325,133447,133569,133695,133754,133812,133936,134060,134188,134306,134424,134546,134632,134729,134863,134997,135077,135215,135347,135479,135615,135690,135766,135869,135943,136056,136137,136194,136255,136314,136374,136432,136493,136551,136601,136650,136717,136776,136835,136884,136955,137039,137109,137180,137260,137329,137392,137460,137526,137594,137659,137725,137802,137880,137986,138092,138188,138317,138406,138533,138599,138669,138755,138821,138904,138978,139076,139172,139268,139366,139475,139570,139659,139721,139781,139846,139903,139984,140038,140095,140192,140302,140363,140478,140599,140694,140786,140879,140935,140994,141043,141135,141184,141238,141292,141346,141400,141454,141509,141619,141729,141837,141947,142057,142167,142277,142385,142491,142595,142699,142803,142898,142993,143086,143179,143283,143389,143493,143597,143690,143783,143876,143969,144077,144183,144289,144395,144492,144587,144682,144777,144883,144989,145095,145201,145299,145394,145490,145587,145652,145756,145814,145878,145939,146001,146061,146126,146188,146256,146314,146377,146440,146507,146582,146655,146721,146773,146826,146878,146935,147019,147114,147199,147280,147360,147437,147516,147593,147667,147741,147812,147892,147964,148039,148104,148165,148225,148300,148374,148447,148517,148589,148659,148732,148796,148866,148912,148981,149033,149118,149201,149258,149324,149391,149457,149538,149613,149669,149722,149783,149841,149891,149940,149989,150038,150100,150152,150197,150278,150329,150383,150436,150490,150541,150590,150656,150707,150768,150829,150891,150941,150982,151059,151118,151177,151236,151297,151353,151409,151476,151537,151602,151657,151722,151791,151859,151937,152006,152066,152137,152211,152276,152348,152418,152485,152569,152638,152705,152775,152838,152905,152973,153056,153135,153225,153302,153370,153437,153515,153572,153629,153697,153763,153819,153879,153938,153992,154042,154092,154140,154202,154253,154326,154406,154486,154550,154617,154688,154746,154807,154873,154932,154999,155059,155119,155182,155250,155311,155378,155456,155526,155575,155632,155701,155762,155850,155938,156026,156114,156201,156288,156375,156462,156520,156594,156664,156720,156791,156856,156918,156993,157066,157156,157222,157288,157349,157413,157475,157533,157604,157687,157746,157817,157883,157948,158009,158068,158139,158205,158270,158353,158429,158504,158585,158645,158714,158784,158853,158908,158964,159020,159081,159139,159195,159254,159308,159363,159425,159482,159576,159645,159746,159797,159867,159930,159986,160044,160103,160157,160243,160327,160397,160466,160536,160651,160772,160839,160906,160981,161048,161107,161161,161215,161269,161322,161374,161448,167906,168046,173274,173324,173374,173463,173519,173577,173639,173694,173752,173933,173997,174056,174118,174184,174250,174751,174897,175050,175093,176234,176281,176326,176377,176428,176479,176530,177064,177668,177730,178031,178103,178384,178438,178493,178551,178606,178665,178721,178790,178859,178928,178998,179061,179124,179187,179250,179315,179380,179445,179510,179573,179637,179701,179765,179816,179894,179972,180043,180115,180188,180260,180399,180465,180533,180601,180667,180734,180808,180871,180928,180988,181053,181120,181185,181242,181303,181361,181465,181575,181684,181788,181866,181931,181998,182064,182134,182181,182233,182283,182340,183297,187886,191559,191743,191921,192159,192348,192517,193631,193746,193831,195261,206049,206258,210693,210850,211007,213436,213726,214265,215758,215854,215944,216040,216130,216296,216419,216542,216712,216818,216933,217048,217150,217256,217373,217488,222185,222358,222526,222674,222833,222988,223161,223278,223395,223563,223675,223789,223961,224137,224295,224428,224540,224686,224838,224970,225113,225235,225543,225679,225775,225911,226006,226173,226266,226358,226545,226701,226879,227043,227225,227542,227724,227906,228096,228328,228518,228695,228857,229014,229124,229307,229444,229648,229832,230016,230176,230334,230518,230745,230948,231119,231339,231561,231716,231916,232100,232203,232393,232534,232699,232870,233070,233274,233476,233641,233846,234045,234244,234441,234532,234681,234831,234915,235064,235209,235361,235502,235668,235829,241112,241413,241579,241734,241836,245506,245670,245856,247105,247230,255295,255567,255845,256090,256152,256437,260210,260666,261175,271951,272465,272902,273336,273779,277761,277882,277981,278386,278483,278600,278687,278810,278911,279317,279416,279535,279628,279735,280078,280185,280430,280551,280960,281208,281308,281413,281532,282041,282188,282307,282558,282691,283106,283360,283472,289088,289213,289621,289742,289970,290091,290224,290371,311093,311585,332056,332480,353247,353741,374257,374683,379524,384941,389032,394463,399205,404582,408566,412558,417949,418496,418929,419685,419915,420158,421325,422254,470399,470983,471456,472886,473630,474823,475877,476355,476648,477031,478546,479311,480454,480895,481336,481932,482206,482617,483633,483811,484564,484701,484792,486986,487252,487574,487784,487893,488012,488196,489314,489784,490535,493118,493213,514380,514608,514864,515123,515699,516053,516175,516314,516606,516866,517794,518080,518483,518885,519228,519440,519641,519854,520143,520428,531663,531750,531835,531934,536799,536905,537028,537160,537283,537413,537537,537670,537801,537926,538043,538163,538295,538423,538537,538655,538768,538889,539077,539264,539445,539628,539812,539977,540159,540279,540399,540507,540617,540729,540837,540947,541112,541278,541430,541595,541696,541816,541987,542148,542311,542472,542639,542758,542875,543055,543237,543418,543601,543756,543901,544023,544158,544321,544514,544640,544792,544934,545104,545260,545432,545723,552678,552770,552943,553105,553200,553369,553463,553552,553795,553884,554177,554593,555013,555434,555860,556277,556693,557110,557528,557942,558412,558885,559357,559768,560239,560711,560901,561107,561213,561321,561427,561539,561653,561765,561879,561995,562109,562217,562327,562435,562697,563076,563480,563627,563735,563845,563953,564067,564476,564890,565006,565424,565665,566095,566530,566940,567362,567772,567894,568303,568719,568841,569059,569243,575069,575413,575493,575849,575999,576143,576617,576729,576819,577081,577346,577454,577606,577714,577790,577902,577992,578094,578202,578310,578410,578518,578603,578769,578873,579001,579088,579255,579333,579447,579539,579803,580070,580180,580333,580443,580527,580916,581014,581122,581216,581346,581454,581576,581712,581820,581940,582074,582196,582324,582466,582592,582732,582858,582976,583108,583206,583316,583616,583728,583846,584310,584426,584729,584855,584951,585352,585462,585586,585724,585834,585956,586268,586392,586522,586998,587126,587441,587579,587741,587957,588113,588317,590896,590980,591084,591287,591476,591677,591870,592075,592388,592600,592766,592882,593128,593344,593657,594083,594545,594782,594934,595194,595338,595480,598712,598826,598946,599062,599156,599477,599576,599694,599795,600074,600359,600638,600920,601173,601432,601685,601941,602365,602441,605691,607046,607490,609344,609919,610127,611137,611517,611683,611824,616844,617270,617382,617517,617670,617867,618038,618221,618396,618583,618855,619013,619097,619201,619688,620244,620402,620621,620852,621075,621310,621532,621798,621936,622535,622649,622787,622899,623023,623594,624089,624635,624780,624873,624965,626892,627462,627760,627949,628155,628348,628558,629442,629587,629979,630137,630354,630615,638842,639717,640337,640534,641482,642247,642370,643143,643364,643564,645541,645641,645731,646417,647170,647935,648698,649473,650686,650851,652464,652785,653848,654058,654228,654798,655693,656326,656492,657978,658594,658830,659051,660009,660274,660539,660786,661200,661436,662721,663170,663357,663606,663848,664024,664265,664498,664723,665318,665793,666317,666578,667929,668404,669630,670100,671148,671600,671844,672301,673546,674029,674179,674734,675186,675586,675739,675884,676027,676097,676525,676813,677317,677826,677942,678844,678966,679078,679255,679521,679791,680057,680325,680581,680841,681097,681355,681607,681863,682115,682369,682601,682837,683089,683345,683597,683851,684083,684317,684429,685081,685536,685660,686752,687567,687763,688087,688476,688828,689069,689283,689582,689774,690089,690296,690642,690942,691343,691562,691975,692212,692582,693306,693661,693930,694070,694324,694468,694745,695737,696146,696778,697124,697492,698566,698929,699329,700837,701422,701740,704275,704469,704687,704913,705125,705324,705531,706735,707030,707587,707977,708609,709086,709331,709818,710064,711260,711657,712663,712885,713308,713499,713878,713966,714074,714182,714495,714820,715139,715470,718173,718361,718622,718871,721455,721647,721912,722165,722697,723105,723304,723888,724123,724247,724659,724873,725275,725378,725508,725683,725935,726131,726271,726465,727476,728545,728833,728963,729740,730397,730543,731249,731487,733027,733177,733594,733759,734445,734915,735111,735202,735286,735430,735664,735831,736759,737045,737205,737820,737979,738307,738534,739046,739408,739487,739826,739931,740296,740667,741028,742902,743531,744607,745031,745284,745436,746484,747221,747424,747670,747917,748135,748377,748698,748962,749267,749490,749801,749990,750705,750974,751468,751694,752134,752293,752577,753322,753687,753992,754150,754388,755707,756105,756333,756553,756695,757985,758091,758221,758359,758483,758771,758940,759040,759325,759439,760322,761077,761516,761640,761886,762079,762213,762404,763183,763401,763692,763971,764288,764510,764805,765088,765192,765533,766349,766665,767226,767732,767937,768723,769128,769789,769978,770529,771095,771215,771617,772151,925365,925458,925521,925603,925696,925789,925876,925974,926065,926156,926244,926328,926424,926524,926630,926733,926834,926938,927044,927143,927249,927351,927458,927567,927678,927809,927929,928045,928163,928262,928369,928485,928604,928732,928821,928916,928993,929082,929173,929266,929340,929437,929532,929630,929729,929833,929929,930031,930134,930234,930337,930422,930523,930621,930711,930806,930893,930999,931101,931195,931286,931380,931456,931548,931637,931740,931851,931934,932020,932115,932212,932308,932396,932497,932598,932701,932807,932905,933002,933097,933195,933298,933398,933501,933606,933724,933840,933935,934028,934113,934209,934303,934395,934478,934582,934687,934787,934888,934993,935093,935194,935293,935395,935489,935596,935698,935801,935894,935990,936092,936195,936291,936393,936496,936593,936696,936794,936898,937003,937100,937208,937322,937437,937545,937659,937774,937876,937981,938089,938199,938315,938432,938527,938624,938723,938828,938934,939033,939138,939244,939344,939450,939551,939658,939777,939876,939981,940083,940185,940285,940388,940483,940587,940672,940776,940880,940978,941082,941188,941286,941391,941489,941602,941696,941785,941874,941957,942048,942131,942229,942319,942415,942504,942598,942686,942782,942867,942975,943076,943177,943275,943381,943472,943571,943668,943766,943862,943955,944065,944163,944258,944368,944460,944560,944659,944746,944850,944955,945054,945161,945268,945367,945476,945568,945679,945790,945901,946005,946120,946236,946363,946483,946578,946673,946770,946869,946961,947060,947152,947251,947337,947431,947534,947630,947733,947829,947932,948029,948127,948230,948323,948413,948514,948597,948688,948773,948865,948968,949063,949159,949252,949346,949425,949532,949623,949722,949815,949918,950022,950123,950224,950328,950422,950526,950630,950743,950849,950955,951063,951180,951281,951389,951489,951592,951697,951804,951900,951979,952069,952153,952245,952318,952410,952499,952591,952676,952773,952866,952961,953060,953157,953248,953339,953431,953526,953633,953741,953843,953940,954037,954130,954217,954301,954398,954495,954588,954675,954766,954865,954964,955059,955148,955229,955328,955432,955529,955634,955731,955815,955914,956018,956115,956220,956317,956415,956516,956622,956721,956828,956927,957026,957117,957206,957295,957377,957470,957561,957672,957773,957873,957985,958098,958196,958304,958398,958498,958587,958679,958790,958900,958995,959111,959237,959363,959482,959610,959735,959860,959978,960105,960214,960323,960436,960559,960682,960798,960923,961020,961128,961250,961366,961482,961591,961679,961780,961869,961970,962057,962145,962242,962334,962440,962540,962616,962693"}}, {"source": "D:\\Duc\\AndroidStudioProjects\\TJ_BatteryOne\\app\\src\\main\\res\\values\\attrs.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "13,14,54,133,134,135,270,271,272,273,529,599,690,728,760", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "484,524,2150,6543,6587,6631,13245,13290,13329,13376,26595,29648,34541,36445,37717", "endColumns": "39,48,39,43,43,40,44,38,46,46,39,65,52,51,39", "endOffsets": "519,568,2185,6582,6626,6667,13285,13324,13371,13418,26630,29709,34589,36492,37752"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\6718808f332794c6efacee4a0d5b3f88\\transformed\\jetified-lottie-6.6.2\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "445,2933", "startColumns": "4,4", "startOffsets": "21424,173183", "endColumns": "62,46", "endOffsets": "21482,173225"}}, {"source": "D:\\Duc\\AndroidStudioProjects\\TJ_BatteryOne\\app\\src\\main\\res\\values\\ic_launcher_background.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "883", "startColumns": "4", "startOffsets": "45804", "endColumns": "56", "endOffsets": "45856"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\470cb6f36c59533cd863c8252f38a327\\transformed\\jetified-dotsindicator-5.1.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "146,153,154,155,156,157,158,159,160,564,600,646", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7146,7510,7560,7604,7659,7706,7756,7806,7860,28108,29714,32031", "endColumns": "46,49,43,54,46,49,49,53,49,48,50,43", "endOffsets": "7188,7555,7599,7654,7701,7751,7801,7855,7905,28152,29760,32070"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\6ef5a5d4dd255734d1000e88cf2a8846\\transformed\\databinding-runtime-8.9.0\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2877", "startColumns": "4", "startOffsets": "170626", "endColumns": "40", "endOffsets": "170662"}}, {"source": "D:\\Duc\\AndroidStudioProjects\\TJ_BatteryOne\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "786,805,806,807,808,809,810,811,812,813,814,815,816,817,818,866,878,879,880,885,886,887,1125,1128,1129,1130,1139,1140,1141,1142,1143,1144,1155,1156,1157,1160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "39494,40803,40862,40923,40976,41031,41072,41114,41155,41196,41237,41278,41319,41360,41401,44816,45540,45579,45619,45914,45959,46005,64039,64216,64257,64296,64937,64989,65044,65091,65138,65185,65920,65965,66010,66170", "endColumns": "38,58,60,52,54,40,41,40,40,40,40,40,40,40,40,41,38,39,49,44,45,62,44,40,38,52,51,54,46,46,46,37,44,44,38,39", "endOffsets": "39528,40857,40918,40971,41026,41067,41109,41150,41191,41232,41273,41314,41355,41396,41437,44853,45574,45614,45664,45954,46000,46063,64079,64252,64291,64344,64984,65039,65086,65133,65180,65218,65960,66005,66044,66205"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d56ddc8f70c1b6c4f2dfff25a6818549\\transformed\\jetified-play-services-base-18.5.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "819,820,821,822,823,824,825,826,3243,3244,3245,3246,3247,3248,3249,3250,3252,3253,3254,3255,3256,3257,3258,3259,3260", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "41442,41532,41612,41702,41792,41872,41953,42033,195649,195754,195935,196060,196167,196347,196470,196586,196856,197044,197149,197330,197455,197630,197778,197841,197903", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78", "endOffsets": "41527,41607,41697,41787,41867,41948,42028,42108,195749,195930,196055,196162,196342,196465,196581,196684,197039,197144,197325,197450,197625,197773,197836,197898,197977"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f5658b4b85c4a4e32f0ba738fc8faf8b\\transformed\\jetified-ssp-android-1.1.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "1162,1173,1185,1197,1209,1221,1233,1245,1257,1269,1281,1283,1295,1307,1319,1331,1343,1355,1367,1379,1391,1403,1405,1417,1429,1441,1453,1465,1477,1489,1501,1513,1525,1527,1539,1551,1563,1575,1587,1599,1611,1623,1635,1647,1649,1661,1673,1685,1697,1709,1721,1733,1745,1757,1769,1771,1774,1776,1778,1780,1782,1784,1786,1788,1790,1792,1794,1796,1798,1800,1802,1804,1806,1808,1810,1812,1814,1816,1818,1820,1822,1824,1826,1828,1830,1832,1834,1836,1838,1840,1842,1844,1846,1848,1850,1852,1854,1856,1858,1860", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "66253,66724,67236,67748,68260,68772,69284,69796,70308,70820,71332,71412,71922,72434,72946,73458,73970,74482,74994,75506,76018,76530,76610,77120,77632,78144,78656,79168,79680,80192,80704,81216,81728,81808,82318,82830,83342,83854,84366,84878,85390,85902,86414,86926,87006,87516,88028,88540,89052,89564,90076,90588,91100,91612,92124,92204,92327,92409,92491,92573,92655,92737,92819,92901,92983,93065,93145,93225,93307,93389,93471,93553,93635,93717,93799,93881,93963,94043,94123,94205,94287,94369,94451,94533,94615,94697,94779,94861,94941,95021,95103,95185,95267,95349,95431,95513,95595,95677,95759,95839", "endColumns": "42,40,40,40,40,40,40,40,40,40,40,38,40,40,40,40,40,40,40,40,40,40,38,40,40,40,40,40,40,40,40,40,40,38,40,40,40,40,40,40,40,40,40,40,38,40,40,40,40,40,40,40,40,40,40,38,40,40,40,40,40,40,40,40,40,40,38,40,40,40,40,40,40,40,40,40,40,38,40,40,40,40,40,40,40,40,40,40,38,40,40,40,40,40,40,40,40,40,40,38", "endOffsets": "66291,66760,67272,67784,68296,68808,69320,69832,70344,70856,71368,71446,71958,72470,72982,73494,74006,74518,75030,75542,76054,76566,76644,77156,77668,78180,78692,79204,79716,80228,80740,81252,81764,81842,82354,82866,83378,83890,84402,84914,85426,85938,86450,86962,87040,87552,88064,88576,89088,89600,90112,90624,91136,91648,92160,92238,92363,92445,92527,92609,92691,92773,92855,92937,93019,93101,93179,93261,93343,93425,93507,93589,93671,93753,93835,93917,93999,94077,94159,94241,94323,94405,94487,94569,94651,94733,94815,94897,94975,95057,95139,95221,95303,95385,95467,95549,95631,95713,95795,95873"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\705b667089ad425e9a6a8d447e2e4b19\\transformed\\jetified-ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2925", "startColumns": "4", "startOffsets": "172787", "endColumns": "65", "endOffsets": "172848"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4c7b48e4d1f473a8b802d70e4aff0147\\transformed\\jetified-savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "3011", "startColumns": "4", "startOffsets": "177260", "endColumns": "53", "endOffsets": "177309"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\17ade856dd8011987dee0a638e332f55\\transformed\\jetified-lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "3012", "startColumns": "4", "startOffsets": "177314", "endColumns": "49", "endOffsets": "177359"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2821057fb80181e87f67e0a41ee8161d\\transformed\\jetified-customactivityoncrash-2.4.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2022,2023,2024,3276,3277,3278,3279,3280,3281,3282,3283,3284", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105355,105435,105513,200278,200363,200456,200569,200660,200766,200868,200967,201119", "endColumns": "79,77,91,84,92,112,90,105,101,98,151,88", "endOffsets": "105430,105508,105600,200358,200451,200564,200655,200761,200863,200962,201114,201203"}}, {"source": "D:\\Duc\\AndroidStudioProjects\\TJ_BatteryOne\\app\\build\\generated\\res\\processDebugGoogleServices\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "3404,3405,3406,3407,3408,3710", "startColumns": "4,4,4,4,4,4", "startOffsets": "211672,211753,211857,211965,212085,243454", "endColumns": "80,103,107,119,120,89", "endOffsets": "211748,211852,211960,212080,212201,243539"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8007b3eee33a0787235755b3a0af0b83\\transformed\\constraintlayout-2.2.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "15,22,25,43,44,52,55,56,64,70,72,73,74,75,76,136,137,138,139,142,147,149,150,161,171,181,210,211,216,217,222,227,228,229,234,235,240,241,246,247,248,254,255,256,261,267,274,295,296,302,303,304,305,308,311,314,315,318,321,322,323,324,325,328,331,332,333,334,340,345,348,351,352,353,358,359,360,363,366,367,370,373,376,379,380,381,384,387,388,393,394,400,405,408,411,412,413,414,415,416,417,418,419,420,421,422,438,520,521,522,523,530,537,545,547,548,551,560,565,573,574,634,660,698,699,704,705,717,718,719,725,732,738,743,744,745,746,747,756,2937,3007", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "573,857,1023,1691,1752,2043,2190,2240,2691,2998,3118,3173,3233,3298,3357,6672,6724,6785,6847,7013,7193,7299,7349,7910,8317,8740,10737,10796,10993,11050,11245,11426,11480,11537,11729,11787,11983,12039,12233,12290,12341,12563,12615,12670,12860,13076,13423,14467,14523,14729,14790,14850,14920,15053,15184,15312,15380,15509,15635,15697,15760,15828,15895,16018,16143,16210,16275,16340,16629,16810,16931,17052,17118,17185,17395,17464,17530,17655,17781,17848,17974,18101,18226,18353,18409,18474,18600,18723,18788,18996,19063,19351,19531,19651,19771,19836,19898,19960,20024,20086,20145,20205,20266,20327,20386,20446,21106,26112,26163,26212,26260,26635,26927,27235,27331,27391,27497,27884,28157,28492,28546,31488,32672,35055,35106,35380,35432,35926,35985,36039,36277,36655,36857,37042,37088,37143,37188,37232,37580,173379,177069", "endLines": "21,22,29,43,51,52,55,56,64,70,72,73,74,75,76,136,137,138,139,145,147,149,150,170,178,181,210,215,216,221,226,227,228,233,234,239,240,245,246,247,253,254,255,260,266,267,274,295,301,302,303,304,307,310,313,314,317,320,321,322,323,324,327,330,331,332,333,339,344,347,350,351,352,357,358,359,362,365,366,369,372,375,378,379,380,383,386,387,392,393,399,404,407,410,411,412,413,414,415,416,417,418,419,420,421,437,443,520,521,522,523,536,544,545,547,550,555,560,572,573,574,634,660,698,699,704,713,717,718,724,725,737,741,743,744,745,746,755,759,2937,3007", "endColumns": "11,55,11,60,11,51,49,52,47,50,54,59,64,58,61,51,60,61,45,11,51,49,50,11,11,44,58,11,56,11,11,53,56,11,57,11,55,11,56,50,11,51,54,11,11,49,51,55,11,60,59,69,11,11,11,67,11,11,61,62,67,66,11,11,66,64,64,11,11,11,11,65,66,11,68,65,11,11,66,11,11,11,11,55,64,11,11,64,11,66,11,11,11,11,64,61,61,63,61,58,59,60,60,58,59,11,11,50,48,47,57,11,11,46,59,11,11,53,11,53,55,45,46,50,58,51,11,58,53,11,54,11,11,45,54,44,43,11,11,40,44", "endOffsets": "852,908,1204,1747,2038,2090,2235,2288,2734,3044,3168,3228,3293,3352,3414,6719,6780,6842,6888,7141,7240,7344,7395,8312,8624,8780,10791,10988,11045,11240,11421,11475,11532,11724,11782,11978,12034,12228,12285,12336,12558,12610,12665,12855,13071,13121,13470,14518,14724,14785,14845,14915,15048,15179,15307,15375,15504,15630,15692,15755,15823,15890,16013,16138,16205,16270,16335,16624,16805,16926,17047,17113,17180,17390,17459,17525,17650,17776,17843,17969,18096,18221,18348,18404,18469,18595,18718,18783,18991,19058,19346,19526,19646,19766,19831,19893,19955,20019,20081,20140,20200,20261,20322,20381,20441,21101,21352,26158,26207,26255,26313,26922,27230,27277,27386,27492,27672,27933,28487,28541,28597,31529,32714,35101,35160,35427,35757,35980,36034,36272,36327,36852,36991,37083,37138,37183,37227,37575,37712,173415,177109"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0f5d2fdc4a4434be9fcd65932e42964a\\transformed\\jetified-activity-1.10.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "2960,3010", "startColumns": "4,4", "startOffsets": "174671,177200", "endColumns": "41,59", "endOffsets": "174708,177255"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4961d4f566368a726dd9f06399253e9d\\transformed\\fragment-1.6.2\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "2921,2969,3013", "startColumns": "4,4,4", "startOffsets": "172585,175098,177364", "endColumns": "56,64,63", "endOffsets": "172637,175158,177423"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f691554f471de61b2961ded671aeec4e\\transformed\\drawerlayout-1.1.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "180,183,2025", "startColumns": "4,4,4", "startOffsets": "8684,8848,105605", "endColumns": "55,47,51", "endOffsets": "8735,8891,105652"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a57f610ef3585645bfe3fcbe98d165f8\\transformed\\jetified-core-viewtree-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "3008", "startColumns": "4", "startOffsets": "177114", "endColumns": "42", "endOffsets": "177152"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\6df7bf1ddef91663e5063bb6ebc3431d\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "3156", "startColumns": "4", "startOffsets": "186803", "endColumns": "82", "endOffsets": "186881"}}, {"source": "D:\\Duc\\AndroidStudioProjects\\TJ_BatteryOne\\app\\src\\main\\res\\values\\dimens.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2003,2004,2005,2008,2009,2010,2114,2122,2123,2124,2767,2768,2769,2770", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "104194,104245,104300,104496,104546,104591,111172,111826,111868,111911,162494,162539,162585,162629", "endColumns": "50,54,52,49,44,50,42,41,42,41,44,45,43,45", "endOffsets": "104240,104295,104348,104541,104586,104637,111210,111863,111906,111948,162534,162580,162624,162670"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1bc3ac1a7cb7052d837b8364241b66b3\\transformed\\work-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1"}, "to": {"startLines": "767,768,769,771", "startColumns": "4,4,4,4", "startOffsets": "38150,38215,38285,38411", "endColumns": "64,69,63,60", "endOffsets": "38210,38280,38344,38467"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\179e6486bd57a16ea175623aa423e7ed\\transformed\\jetified-material3-release\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "3058,3468,3469,3470,3471,3472,3473,3474,3475,3476,3477,3480,3481,3482,3483,3484,3485,3486,3487,3488,3489,3490,3493,3494,3495,3496,3497,3498,3499,3500,3501,3502,3503,3504,3505,3506,3507,3508,3509,3510,3511,3512,3513,3514,3515,3516,3517,3518,3519,3520,3521,3522,3523,3524,3525,3526,3527,3528,7384,7394", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "180265,217493,217581,217667,217748,217832,217901,217966,218049,218155,218241,218361,218415,218484,218545,218614,218703,218798,218872,218969,219062,219160,219309,219400,219488,219584,219682,219746,219814,219901,219995,220062,220134,220206,220307,220416,220492,220561,220609,220675,220739,220813,220870,220927,220999,221049,221103,221174,221245,221315,221384,221442,221518,221589,221663,221749,221799,221869,497263,497978", "endLines": "3058,3468,3469,3470,3471,3472,3473,3474,3475,3476,3479,3480,3481,3482,3483,3484,3485,3486,3487,3488,3489,3492,3493,3494,3495,3496,3497,3498,3499,3500,3501,3502,3503,3504,3505,3506,3507,3508,3509,3510,3511,3512,3513,3514,3515,3516,3517,3518,3519,3520,3521,3522,3523,3524,3525,3526,3527,3528,7393,7396", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "180333,217576,217662,217743,217827,217896,217961,218044,218150,218236,218356,218410,218479,218540,218609,218698,218793,218867,218964,219057,219155,219304,219395,219483,219579,219677,219741,219809,219896,219990,220057,220129,220201,220302,220411,220487,220556,220604,220670,220734,220808,220865,220922,220994,221044,221098,221169,221240,221310,221379,221437,221513,221584,221658,221744,221794,221864,221929,497973,498126"}}]}]}