package com.tqhit.battery.one.features.stats.charge.presentation

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tqhit.battery.one.features.stats.charge.data.StatsChargeSession
import com.tqhit.battery.one.features.stats.charge.data.StatsChargeStatus
import com.tqhit.battery.one.features.stats.charge.domain.CalculateSimpleChargeEstimateUseCase
import com.tqhit.battery.one.features.stats.charge.repository.StatsChargeRepository
import com.tqhit.battery.one.repository.AppRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * UI State data class for the StatsChargeFragment.
 */
data class StatsChargeUiState(
    val isLoading: Boolean = true,
    val status: StatsChargeStatus? = null,
    val session: StatsChargeSession? = null,
    val timeToFullMillis: Long = 0L,
    val timeToTargetMillis: Long = 0L,
    val targetPercentage: Int = 80,
    val powerWatts: Double = 0.0
)

/**
 * ViewModel for the StatsChargeFragment.
 * Manages UI state by combining data from StatsChargeRepository and calculating estimates.
 */
@HiltViewModel
class StatsChargeViewModel @Inject constructor(
    private val statsChargeRepository: StatsChargeRepository,
    private val calculateSimpleChargeEstimateUseCase: CalculateSimpleChargeEstimateUseCase,
    private val appRepository: AppRepository
) : ViewModel() {
    
    companion object {
        private const val TAG = "StatsChargeViewModel"
    }
    
    // Private mutable UI state
    private val _uiState = MutableStateFlow(StatsChargeUiState())
    
    // Public read-only UI state
    val uiState: StateFlow<StatsChargeUiState> = _uiState.asStateFlow()
    
    init {
        // Combine flows from repository and app settings
        viewModelScope.launch {
            combine(
                statsChargeRepository.statsChargeStatusFlow,
                statsChargeRepository.activeChargeSessionFlow,
                appRepository.chargeAlarmPercentFlow
            ) { status, session, targetPercent ->
                updateUiState(status, session, targetPercent)
            }.collect { /* Collection handled in updateUiState */ }
        }
    }
    
    /**
     * Updates the UI state with new data and calculates estimates.
     */
    private suspend fun updateUiState(
        status: StatsChargeStatus,
        session: StatsChargeSession?,
        targetPercentage: Int
    ) {
        try {
            // Get battery capacity from app repository
            val batteryCapacityMah = appRepository.getBatteryCapacity()
            
            // Calculate time estimates
            val timeToFull = calculateSimpleChargeEstimateUseCase.calculateTimeToFull(
                currentStatus = status,
                activeSession = session,
                batteryCapacityMah = batteryCapacityMah
            )
            
            val timeToTarget = calculateSimpleChargeEstimateUseCase.calculateTimeToTarget(
                currentStatus = status,
                activeSession = session,
                batteryCapacityMah = batteryCapacityMah,
                targetPercentage = targetPercentage
            )

            // Calculate power
            val powerWatts = calculatePower(status.voltageMillivolts, status.currentMicroAmperes)

            // Update UI state
            val newUiState = StatsChargeUiState(
                isLoading = false,
                status = status,
                session = session,
                timeToFullMillis = timeToFull,
                timeToTargetMillis = timeToTarget,
                targetPercentage = targetPercentage,
                powerWatts = powerWatts
            )
            
            _uiState.value = newUiState
            
            Log.d(TAG, "UI state updated - " +
                "percentage=${status.percentage}%, " +
                "charging=${status.isCharging}, " +
                "sessionActive=${session?.isActive}, " +
                "timeToFull=${timeToFull}ms, " +
                "timeToTarget=${timeToTarget}ms, " +
                "targetPercent=${targetPercentage}%, " +
                "power=${powerWatts}W")
                
        } catch (e: Exception) {
            Log.e(TAG, "Error updating UI state", e)
            
            // Set error state
            _uiState.value = StatsChargeUiState(
                isLoading = false,
                status = status,
                session = session,
                timeToFullMillis = 0L,
                timeToTargetMillis = 0L,
                targetPercentage = targetPercentage,
                powerWatts = 0.0
            )
        }
    }
    
    /**
     * Sets the target charge percentage and saves it to preferences.
     *
     * @param percentage The target percentage (1-100)
     */
    fun setTargetChargePercentage(percentage: Int) {
        viewModelScope.launch {
            try {
                val clampedPercentage = percentage.coerceIn(1, 100)
                appRepository.setChargeAlarmPercent(clampedPercentage)
                
                Log.d(TAG, "Target charge percentage set to: ${clampedPercentage}%")
            } catch (e: Exception) {
                Log.e(TAG, "Failed to set target charge percentage", e)
            }
        }
    }
    
    /**
     * Resets the current charge session.
     */
    fun resetChargeSession() {
        viewModelScope.launch {
            try {
                statsChargeRepository.resetCurrentSession()
                Log.d(TAG, "Charge session reset")
            } catch (e: Exception) {
                Log.e(TAG, "Failed to reset charge session", e)
            }
        }
    }
    
    /**
     * Gets the current battery status synchronously.
     *
     * @return Current StatsChargeStatus or null
     */
    fun getCurrentStatus(): StatsChargeStatus? {
        return _uiState.value.status
    }
    
    /**
     * Gets the current active session synchronously.
     *
     * @return Current StatsChargeSession or null
     */
    fun getCurrentSession(): StatsChargeSession? {
        return _uiState.value.session
    }
    
    /**
     * Checks if currently charging.
     *
     * @return true if currently charging, false otherwise
     */
    fun isCharging(): Boolean {
        return _uiState.value.status?.isCharging ?: false
    }
    
    /**
     * Formats time in milliseconds to human-readable string.
     *
     * @param timeMillis Time in milliseconds
     * @return Formatted time string (e.g., "2h 30m", "45m", "< 1m")
     */
    fun formatTime(timeMillis: Long): String {
        if (timeMillis <= 0) return "N/A"
        
        val totalMinutes = (timeMillis / (60 * 1000)).toInt()
        
        return when {
            totalMinutes < 1 -> "< 1m"
            totalMinutes < 60 -> "${totalMinutes}m"
            else -> {
                val hours = totalMinutes / 60
                val minutes = totalMinutes % 60
                if (minutes == 0) {
                    "${hours}h"
                } else {
                    "${hours}h ${minutes}m"
                }
            }
        }
    }
    
    /**
     * Formats current in microamperes to human-readable string.
     *
     * @param currentMicroAmperes Current in microamperes
     * @return Formatted current string (e.g., "1.5A", "500mA")
     */
    fun formatCurrent(currentMicroAmperes: Long): String {
        val absCurrentMa = kotlin.math.abs(currentMicroAmperes) / 1000.0
        
        return when {
            absCurrentMa >= 1000 -> String.format("%.1fA", absCurrentMa / 1000.0)
            absCurrentMa >= 1 -> String.format("%.0fmA", absCurrentMa)
            else -> "0mA"
        }
    }
    
    /**
     * Formats voltage in millivolts to human-readable string.
     *
     * @param voltageMillivolts Voltage in millivolts
     * @return Formatted voltage string (e.g., "4.2V")
     */
    fun formatVoltage(voltageMillivolts: Int): String {
        val voltageVolts = voltageMillivolts / 1000.0
        return String.format("%.1fV", voltageVolts)
    }
    
    /**
     * Formats temperature in Celsius to human-readable string.
     *
     * @param temperatureCelsius Temperature in Celsius
     * @return Formatted temperature string (e.g., "25.5°C")
     */
    fun formatTemperature(temperatureCelsius: Float): String {
        return String.format("%.1f°C", temperatureCelsius)
    }

    /**
     * Calculates power in watts from voltage and current.
     * Formula: Power = (Voltage × Current) / 1,000,000
     * Converts from millivolts and microamperes to watts.
     *
     * @param voltageMillivolts Voltage in millivolts
     * @param currentMicroAmperes Current in microamperes
     * @return Power in watts
     */
    fun calculatePower(voltageMillivolts: Int, currentMicroAmperes: Long): Double {
        val voltageVolts = voltageMillivolts / 1000.0
        val currentAmperes = currentMicroAmperes / 1000000.0
        val powerWatts = voltageVolts * currentAmperes

        Log.v(TAG, "Power calculation - " +
            "voltage=${voltageVolts}V, " +
            "current=${currentAmperes}A, " +
            "power=${powerWatts}W")

        return powerWatts
    }

    /**
     * Formats power in watts to human-readable string.
     *
     * @param powerWatts Power in watts
     * @return Formatted power string (e.g., "1.5W", "500mW")
     */
    fun formatPower(powerWatts: Double): String {
        val absPowerWatts = kotlin.math.abs(powerWatts)

        return when {
            absPowerWatts >= 1.0 -> String.format("%.1fW", absPowerWatts)
            absPowerWatts >= 0.001 -> String.format("%.0fmW", absPowerWatts * 1000)
            else -> "0W"
        }
    }
}
