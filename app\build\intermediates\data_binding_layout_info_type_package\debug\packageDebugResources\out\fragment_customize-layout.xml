<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_customize" modulePackage="com.tqhit.battery.one" filePath="app\src\main\res\layout\fragment_customize.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/fragment_customize_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="384" endOffset="53"/></Target><Target id="@+id/previewContainer" view="FrameLayout"><Expressions/><location startLine="47" startOffset="20" endLine="68" endOffset="33"/></Target><Target id="@+id/livePreviewView" view="com.tqhit.battery.one.features.emoji.presentation.customize.view.LivePreviewView"><Expressions/><location startLine="54" startOffset="24" endLine="58" endOffset="61"/></Target><Target id="@+id/previewLoadingIndicator" view="ProgressBar"><Expressions/><location startLine="61" startOffset="24" endLine="66" endOffset="55"/></Target><Target id="@+id/previewBatterySlider" view="com.google.android.material.slider.Slider"><Expressions/><location startLine="84" startOffset="24" endLine="92" endOffset="50"/></Target><Target id="@+id/previewBatteryText" view="TextView"><Expressions/><location startLine="94" startOffset="24" endLine="102" endOffset="54"/></Target><Target id="@+id/batteryOptionsRecyclerView" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="143" startOffset="20" endLine="149" endOffset="70"/></Target><Target id="@+id/emojiOptionsRecyclerView" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="160" startOffset="20" endLine="165" endOffset="68"/></Target><Target id="@+id/showEmojiSwitch" view="com.google.android.material.materialswitch.MaterialSwitch"><Expressions/><location startLine="210" startOffset="24" endLine="214" endOffset="52"/></Target><Target id="@+id/showPercentageSwitch" view="com.google.android.material.materialswitch.MaterialSwitch"><Expressions/><location startLine="233" startOffset="24" endLine="237" endOffset="52"/></Target><Target id="@+id/emojiSizeSlider" view="com.google.android.material.slider.Slider"><Expressions/><location startLine="249" startOffset="20" endLine="257" endOffset="77"/></Target><Target id="@+id/percentageFontSizeSlider" view="com.google.android.material.slider.Slider"><Expressions/><location startLine="267" startOffset="20" endLine="275" endOffset="77"/></Target><Target id="@+id/colorPickerButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="292" startOffset="24" endLine="298" endOffset="67"/></Target><Target id="@+id/resetButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="320" startOffset="8" endLine="328" endOffset="45"/></Target><Target id="@+id/saveButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="330" startOffset="8" endLine="337" endOffset="42"/></Target><Target id="@+id/applyButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="339" startOffset="8" endLine="346" endOffset="43"/></Target><Target id="@+id/loadingOverlay" view="FrameLayout"><Expressions/><location startLine="351" startOffset="4" endLine="382" endOffset="17"/></Target><Target id="@+id/loadingText" view="TextView"><Expressions/><location startLine="372" startOffset="12" endLine="378" endOffset="58"/></Target></Targets></Layout>