<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/colorSurface"
    tools:context=".features.emoji.presentation.customize.CustomizeFragment">

    <!-- Main Content -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="@dimen/spacing_medium">

            <!-- Live Preview Section -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/spacing_large"
                app:cardCornerRadius="@dimen/card_corner_radius"
                app:cardElevation="@dimen/card_elevation"
                app:strokeColor="?attr/colorOutline"
                app:strokeWidth="1dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="@dimen/spacing_medium">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/live_preview"
                        android:textAppearance="?attr/textAppearanceHeadlineSmall"
                        android:textColor="?attr/colorOnSurface"
                        android:layout_marginBottom="@dimen/spacing_medium" />

                    <!-- Live Preview Container -->
                    <FrameLayout
                        android:id="@+id/previewContainer"
                        android:layout_width="match_parent"
                        android:layout_height="120dp"
                        android:background="@drawable/preview_background"
                        android:layout_marginBottom="@dimen/spacing_medium">

                        <com.tqhit.battery.one.features.emoji.presentation.customize.view.LivePreviewView
                            android:id="@+id/livePreviewView"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_gravity="center" />

                        <!-- Preview Loading Indicator -->
                        <ProgressBar
                            android:id="@+id/previewLoadingIndicator"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:visibility="gone" />

                    </FrameLayout>

                    <!-- Preview Controls -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/preview_battery_level"
                            android:textAppearance="?attr/textAppearanceBodyMedium"
                            android:layout_marginEnd="@dimen/spacing_medium" />

                        <com.google.android.material.slider.Slider
                            android:id="@+id/previewBatterySlider"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:valueFrom="0"
                            android:valueTo="100"
                            android:value="50"
                            android:stepSize="1" />

                        <TextView
                            android:id="@+id/previewBatteryText"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="50%"
                            android:textAppearance="?attr/textAppearanceBodyMedium"
                            android:layout_marginStart="@dimen/spacing_medium"
                            android:minWidth="40dp"
                            android:gravity="center" />

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Style Selection Section -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/spacing_large"
                app:cardCornerRadius="@dimen/card_corner_radius"
                app:cardElevation="@dimen/card_elevation"
                app:strokeColor="?attr/colorOutline"
                app:strokeWidth="1dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="@dimen/spacing_medium">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/style_selection"
                        android:textAppearance="?attr/textAppearanceHeadlineSmall"
                        android:textColor="?attr/colorOnSurface"
                        android:layout_marginBottom="@dimen/spacing_medium" />

                    <!-- Battery Container Selection -->
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/battery_container"
                        android:textAppearance="?attr/textAppearanceBodyLarge"
                        android:textColor="?attr/colorOnSurface"
                        android:layout_marginBottom="@dimen/spacing_small" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/batteryOptionsRecyclerView"
                        android:layout_width="match_parent"
                        android:layout_height="80dp"
                        android:layout_marginBottom="@dimen/spacing_medium"
                        android:orientation="horizontal"
                        tools:listitem="@layout/item_battery_option" />

                    <!-- Emoji Character Selection -->
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/emoji_character"
                        android:textAppearance="?attr/textAppearanceBodyLarge"
                        android:textColor="?attr/colorOnSurface"
                        android:layout_marginBottom="@dimen/spacing_small" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/emojiOptionsRecyclerView"
                        android:layout_width="match_parent"
                        android:layout_height="80dp"
                        android:orientation="horizontal"
                        tools:listitem="@layout/item_emoji_option" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Customization Options Section -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/spacing_large"
                app:cardCornerRadius="@dimen/card_corner_radius"
                app:cardElevation="@dimen/card_elevation"
                app:strokeColor="?attr/colorOutline"
                app:strokeWidth="1dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="@dimen/spacing_medium">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/customization_options"
                        android:textAppearance="?attr/textAppearanceHeadlineSmall"
                        android:textColor="?attr/colorOnSurface"
                        android:layout_marginBottom="@dimen/spacing_medium" />

                    <!-- Show Emoji Toggle -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="@dimen/spacing_medium">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/show_emoji"
                            android:textAppearance="?attr/textAppearanceBodyLarge" />

                        <com.google.android.material.materialswitch.MaterialSwitch
                            android:id="@+id/showEmojiSwitch"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:checked="true" />

                    </LinearLayout>

                    <!-- Show Percentage Toggle -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="@dimen/spacing_medium">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/show_percentage"
                            android:textAppearance="?attr/textAppearanceBodyLarge" />

                        <com.google.android.material.materialswitch.MaterialSwitch
                            android:id="@+id/showPercentageSwitch"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:checked="true" />

                    </LinearLayout>

                    <!-- Emoji Size Slider -->
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/emoji_size"
                        android:textAppearance="?attr/textAppearanceBodyLarge"
                        android:layout_marginBottom="@dimen/spacing_small" />

                    <com.google.android.material.slider.Slider
                        android:id="@+id/emojiSizeSlider"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:valueFrom="0.5"
                        android:valueTo="2.0"
                        android:value="1.0"
                        android:stepSize="0.1"
                        android:layout_marginBottom="@dimen/spacing_medium" />

                    <!-- Percentage Font Size Slider -->
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/percentage_font_size"
                        android:textAppearance="?attr/textAppearanceBodyLarge"
                        android:layout_marginBottom="@dimen/spacing_small" />

                    <com.google.android.material.slider.Slider
                        android:id="@+id/percentageFontSizeSlider"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:valueFrom="5"
                        android:valueTo="40"
                        android:value="14"
                        android:stepSize="1"
                        android:layout_marginBottom="@dimen/spacing_medium" />

                    <!-- Percentage Color Picker -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="@dimen/spacing_medium">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/percentage_color"
                            android:textAppearance="?attr/textAppearanceBodyLarge" />

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/colorPickerButton"
                            style="@style/Widget.Material3.Button.OutlinedButton"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/choose_color"
                            app:icon="@drawable/ic_color_palette" />

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- Action Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:orientation="horizontal"
        android:padding="@dimen/spacing_medium"
        android:background="?attr/colorSurface"
        android:elevation="@dimen/card_elevation">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/resetButton"
            style="@style/Widget.Material3.Button.OutlinedButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginEnd="@dimen/spacing_small"
            android:text="@string/reset"
            app:icon="@drawable/ic_refresh" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/saveButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="@dimen/spacing_small"
            android:text="@string/save"
            app:icon="@drawable/ic_save" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/applyButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="@dimen/spacing_small"
            android:text="@string/apply"
            app:icon="@drawable/ic_check" />

    </LinearLayout>

    <!-- Loading Overlay -->
    <FrameLayout
        android:id="@+id/loadingOverlay"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/loading_overlay_background"
        android:visibility="gone"
        android:clickable="true"
        android:focusable="true">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:orientation="vertical"
            android:gravity="center">

            <ProgressBar
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/spacing_medium" />

            <TextView
                android:id="@+id/loadingText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/loading"
                android:textAppearance="?attr/textAppearanceBodyLarge"
                android:textColor="?attr/colorOnSurface" />

        </LinearLayout>

    </FrameLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
