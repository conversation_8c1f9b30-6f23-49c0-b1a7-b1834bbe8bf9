package com.tqhit.battery.one.features.emoji.presentation.customize

import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfig
import com.tqhit.battery.one.features.emoji.domain.model.CustomizationConfig
import com.tqhit.battery.one.features.emoji.domain.model.FeatureStatus
import com.tqhit.battery.one.features.emoji.domain.model.UserCustomization
import com.tqhit.battery.one.features.emoji.domain.use_case.EnrichedUserCustomization
import com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase
import com.tqhit.battery.one.features.emoji.domain.use_case.ResetCustomizationUseCase
import com.tqhit.battery.one.features.emoji.domain.use_case.SaveCustomizationUseCase
import com.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus
import com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.advanceUntilIdle
import kotlinx.coroutines.test.resetMain
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.test.setMain
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Rule
import org.junit.Test

/**
 * Unit tests for CustomizeViewModel.
 * 
 * Tests the MVI pattern implementation including state management,
 * event handling, and integration with use cases and battery provider.
 */
@ExperimentalCoroutinesApi
class CustomizeViewModelTest {
    
    @get:Rule
    val instantTaskExecutorRule = InstantTaskExecutorRule()
    
    private val testDispatcher = StandardTestDispatcher()
    
    private lateinit var viewModel: CustomizeViewModel
    private lateinit var mockLoadCustomizationUseCase: LoadCustomizationUseCase
    private lateinit var mockSaveCustomizationUseCase: SaveCustomizationUseCase
    private lateinit var mockResetCustomizationUseCase: ResetCustomizationUseCase
    private lateinit var mockCoreBatteryStatsProvider: CoreBatteryStatsProvider
    
    // Test data
    private val testStyleConfig = BatteryStyleConfig(
        showEmoji = true,
        showPercentage = true,
        percentageFontSizeDp = 14,
        emojiSizeScale = 1.0f,
        percentageColor = 0xFFFFFFFF.toInt()
    )
    
    private val testBatteryStyle = BatteryStyle(
        id = "test_style_1",
        name = "Test Style",
        category = BatteryStyleCategory.CUTE,
        batteryImageUrl = "https://example.com/battery.png",
        emojiImageUrl = "https://example.com/emoji.png",
        isPremium = false,
        isPopular = true,
        defaultConfig = testStyleConfig
    )
    
    private val testCustomizationConfig = CustomizationConfig(
        selectedStyleId = "test_style_1",
        selectedBatteryImageUrl = "https://example.com/battery.png",
        selectedEmojiImageUrl = "https://example.com/emoji.png",
        styleConfig = testStyleConfig,
        isFeatureEnabled = false
    )
    
    private val testUserCustomization = UserCustomization(
        customizationConfig = testCustomizationConfig,
        hasAccessibilityPermission = true,
        hasOverlayPermission = true,
        isAccessibilityServiceEnabled = true
    )
    
    private val testEnrichedCustomization = EnrichedUserCustomization(
        userCustomization = testUserCustomization,
        selectedStyle = testBatteryStyle,
        isSelectedStyleAvailable = true,
        featureStatus = FeatureStatus.ACTIVE,
        availableStylesCount = 1
    )
    
    private val testBatteryStatus = CoreBatteryStatus(
        batteryLevel = 75,
        isCharging = false,
        chargingRate = 0.0,
        dischargingRate = -0.5,
        estimatedTimeToFull = 0L,
        estimatedTimeToEmpty = 7200000L, // 2 hours
        temperature = 25.0,
        voltage = 4.2,
        current = -500.0,
        capacity = 3000,
        cycleCount = 100,
        health = "Good",
        technology = "Li-ion",
        isPresent = true,
        timestamp = System.currentTimeMillis()
    )
    
    @Before
    fun setUp() {
        Dispatchers.setMain(testDispatcher)
        
        mockLoadCustomizationUseCase = mockk()
        mockSaveCustomizationUseCase = mockk()
        mockResetCustomizationUseCase = mockk()
        mockCoreBatteryStatsProvider = mockk()
        
        // Setup default mock behaviors
        every { mockLoadCustomizationUseCase.userCustomizationFlow } returns flowOf(testEnrichedCustomization)
        every { mockCoreBatteryStatsProvider.coreBatteryStatusFlow } returns flowOf(testBatteryStatus)
        coEvery { mockLoadCustomizationUseCase.getCurrentEnrichedUserCustomization() } returns testEnrichedCustomization
        
        viewModel = CustomizeViewModel(
            loadCustomizationUseCase = mockLoadCustomizationUseCase,
            saveCustomizationUseCase = mockSaveCustomizationUseCase,
            resetCustomizationUseCase = mockResetCustomizationUseCase,
            coreBatteryStatsProvider = mockCoreBatteryStatsProvider
        )
    }
    
    @After
    fun tearDown() {
        Dispatchers.resetMain()
    }
    
    @Test
    fun `initial state should be loading`() = runTest {
        // When
        val initialState = viewModel.uiState.value
        
        // Then
        assertTrue("Should start in loading state", initialState.isLoading)
        assertTrue("Should start loading styles", initialState.isLoadingStyles)
        assertEquals("Should have default preview level", 50, initialState.previewBatteryLevel)
        assertTrue("Should show live preview by default", initialState.showLivePreview)
    }
    
    @Test
    fun `handleEvent OnScreenEnter should load initial data`() = runTest {
        // When
        viewModel.handleEvent(CustomizeEvent.OnScreenEnter)
        advanceUntilIdle()
        
        // Then
        val state = viewModel.uiState.value
        assertFalse("Should finish loading", state.isLoading)
        assertFalse("Should finish loading styles", state.isLoadingStyles)
        assertEquals("Should have enriched customization", testEnrichedCustomization, state.enrichedCustomization)
    }
    
    @Test
    fun `handleEvent ToggleEmojiVisibility should update style config`() = runTest {
        // Given
        viewModel.handleEvent(CustomizeEvent.OnScreenEnter)
        advanceUntilIdle()
        
        // When
        viewModel.handleEvent(CustomizeEvent.ToggleEmojiVisibility(false))
        
        // Then
        val state = viewModel.uiState.value
        assertFalse("Should update emoji visibility", state.editingConfig.showEmoji)
        assertTrue("Should mark as modified", state.isConfigModified)
        assertTrue("Should have unsaved changes", state.hasUnsavedChanges)
    }
    
    @Test
    fun `handleEvent TogglePercentageVisibility should update style config`() = runTest {
        // Given
        viewModel.handleEvent(CustomizeEvent.OnScreenEnter)
        advanceUntilIdle()
        
        // When
        viewModel.handleEvent(CustomizeEvent.TogglePercentageVisibility(false))
        
        // Then
        val state = viewModel.uiState.value
        assertFalse("Should update percentage visibility", state.editingConfig.showPercentage)
        assertTrue("Should mark as modified", state.isConfigModified)
    }
    
    @Test
    fun `handleEvent ChangePercentageFontSize should clamp values`() = runTest {
        // Given
        viewModel.handleEvent(CustomizeEvent.OnScreenEnter)
        advanceUntilIdle()
        
        // When
        viewModel.handleEvent(CustomizeEvent.ChangePercentageFontSize(50)) // Above max
        
        // Then
        val state = viewModel.uiState.value
        assertEquals("Should clamp to maximum", 40, state.editingConfig.percentageFontSizeDp)
        
        // When
        viewModel.handleEvent(CustomizeEvent.ChangePercentageFontSize(0)) // Below min
        
        // Then
        val updatedState = viewModel.uiState.value
        assertEquals("Should clamp to minimum", 5, updatedState.editingConfig.percentageFontSizeDp)
    }
    
    @Test
    fun `handleEvent ChangeEmojiSize should clamp values`() = runTest {
        // Given
        viewModel.handleEvent(CustomizeEvent.OnScreenEnter)
        advanceUntilIdle()
        
        // When
        viewModel.handleEvent(CustomizeEvent.ChangeEmojiSize(3.0f)) // Above max
        
        // Then
        val state = viewModel.uiState.value
        assertEquals("Should clamp to maximum", 2.0f, state.editingConfig.emojiSizeScale, 0.01f)
        
        // When
        viewModel.handleEvent(CustomizeEvent.ChangeEmojiSize(0.1f)) // Below min
        
        // Then
        val updatedState = viewModel.uiState.value
        assertEquals("Should clamp to minimum", 0.5f, updatedState.editingConfig.emojiSizeScale, 0.01f)
    }
    
    @Test
    fun `handleEvent ChangePercentageColor should update color`() = runTest {
        // Given
        viewModel.handleEvent(CustomizeEvent.OnScreenEnter)
        advanceUntilIdle()
        val newColor = 0xFF00FF00.toInt() // Green
        
        // When
        viewModel.handleEvent(CustomizeEvent.ChangePercentageColor(newColor))
        
        // Then
        val state = viewModel.uiState.value
        assertEquals("Should update color", newColor, state.editingConfig.percentageColor)
        assertTrue("Should mark as modified", state.isConfigModified)
    }
    
    @Test
    fun `handleEvent SaveConfiguration should save current config`() = runTest {
        // Given
        coEvery { mockSaveCustomizationUseCase.saveCustomizationConfig(any()) } returns Result.success(Unit)
        viewModel.handleEvent(CustomizeEvent.OnScreenEnter)
        advanceUntilIdle()
        viewModel.handleEvent(CustomizeEvent.ToggleEmojiVisibility(false)) // Make a change
        
        // When
        viewModel.handleEvent(CustomizeEvent.SaveConfiguration)
        advanceUntilIdle()
        
        // Then
        coVerify { mockSaveCustomizationUseCase.saveCustomizationConfig(any()) }
        val state = viewModel.uiState.value
        assertFalse("Should clear unsaved changes", state.hasUnsavedChanges)
        assertFalse("Should clear modified flag", state.isConfigModified)
    }
    
    @Test
    fun `handleEvent ApplyAndEnable should save and enable feature`() = runTest {
        // Given
        coEvery { mockSaveCustomizationUseCase.saveCustomizationConfig(any()) } returns Result.success(Unit)
        coEvery { mockSaveCustomizationUseCase.setFeatureEnabled(true) } returns Result.success(Unit)
        viewModel.handleEvent(CustomizeEvent.OnScreenEnter)
        advanceUntilIdle()
        
        // When
        viewModel.handleEvent(CustomizeEvent.ApplyAndEnable)
        advanceUntilIdle()
        
        // Then
        coVerify { mockSaveCustomizationUseCase.saveCustomizationConfig(any()) }
        coVerify { mockSaveCustomizationUseCase.setFeatureEnabled(true) }
    }
    
    @Test
    fun `handleEvent ResetToDefaults should show confirmation first`() = runTest {
        // Given
        viewModel.handleEvent(CustomizeEvent.OnScreenEnter)
        advanceUntilIdle()
        
        // When
        viewModel.handleEvent(CustomizeEvent.ResetToDefaults)
        
        // Then
        // Should not call reset immediately, requires confirmation
        coVerify(exactly = 0) { mockResetCustomizationUseCase.resetCustomizationConfig() }
    }
    
    @Test
    fun `handleEvent ConfirmReset should reset configuration`() = runTest {
        // Given
        coEvery { mockResetCustomizationUseCase.resetCustomizationConfig() } returns Result.success(Unit)
        viewModel.handleEvent(CustomizeEvent.OnScreenEnter)
        advanceUntilIdle()
        
        // When
        viewModel.handleEvent(CustomizeEvent.ConfirmReset)
        advanceUntilIdle()
        
        // Then
        coVerify { mockResetCustomizationUseCase.resetCustomizationConfig() }
    }
    
    @Test
    fun `handleEvent SelectBatteryContainer should update selection`() = runTest {
        // Given
        viewModel.handleEvent(CustomizeEvent.OnScreenEnter)
        advanceUntilIdle()
        
        // When
        viewModel.handleEvent(CustomizeEvent.SelectBatteryContainer(testBatteryStyle))
        
        // Then
        val state = viewModel.uiState.value
        assertEquals("Should update battery style ID", testBatteryStyle.id, state.selectedBatteryStyleId)
        assertTrue("Should have unsaved changes", state.hasUnsavedChanges)
    }
    
    @Test
    fun `handleEvent SelectEmojiCharacter should update selection`() = runTest {
        // Given
        viewModel.handleEvent(CustomizeEvent.OnScreenEnter)
        advanceUntilIdle()
        
        // When
        viewModel.handleEvent(CustomizeEvent.SelectEmojiCharacter(testBatteryStyle))
        
        // Then
        val state = viewModel.uiState.value
        assertEquals("Should update emoji style ID", testBatteryStyle.id, state.selectedEmojiStyleId)
        assertTrue("Should have unsaved changes", state.hasUnsavedChanges)
    }
    
    @Test
    fun `handleEvent ChangePreviewBatteryLevel should update preview level`() = runTest {
        // Given
        viewModel.handleEvent(CustomizeEvent.OnScreenEnter)
        advanceUntilIdle()
        
        // When
        viewModel.handleEvent(CustomizeEvent.ChangePreviewBatteryLevel(80))
        
        // Then
        val state = viewModel.uiState.value
        assertEquals("Should update preview battery level", 80, state.previewBatteryLevel)
    }
    
    @Test
    fun `handleEvent ChangePreviewBatteryLevel should clamp values`() = runTest {
        // Given
        viewModel.handleEvent(CustomizeEvent.OnScreenEnter)
        advanceUntilIdle()
        
        // When
        viewModel.handleEvent(CustomizeEvent.ChangePreviewBatteryLevel(150)) // Above max
        
        // Then
        val state = viewModel.uiState.value
        assertEquals("Should clamp to maximum", 100, state.previewBatteryLevel)
        
        // When
        viewModel.handleEvent(CustomizeEvent.ChangePreviewBatteryLevel(-10)) // Below min
        
        // Then
        val updatedState = viewModel.uiState.value
        assertEquals("Should clamp to minimum", 0, updatedState.previewBatteryLevel)
    }
    
    @Test
    fun `handleEvent OpenColorPicker should show color picker`() = runTest {
        // Given
        viewModel.handleEvent(CustomizeEvent.OnScreenEnter)
        advanceUntilIdle()
        
        // When
        viewModel.handleEvent(CustomizeEvent.OpenColorPicker)
        
        // Then
        val state = viewModel.uiState.value
        assertTrue("Should show color picker", state.showColorPicker)
    }
    
    @Test
    fun `handleEvent CloseColorPicker should hide color picker`() = runTest {
        // Given
        viewModel.handleEvent(CustomizeEvent.OnScreenEnter)
        advanceUntilIdle()
        viewModel.handleEvent(CustomizeEvent.OpenColorPicker)
        
        // When
        viewModel.handleEvent(CustomizeEvent.CloseColorPicker)
        
        // Then
        val state = viewModel.uiState.value
        assertFalse("Should hide color picker", state.showColorPicker)
    }
    
    @Test
    fun `handleEvent DismissError should clear errors`() = runTest {
        // Given
        viewModel.handleEvent(CustomizeEvent.OnScreenEnter)
        advanceUntilIdle()
        // Simulate an error state
        coEvery { mockSaveCustomizationUseCase.saveCustomizationConfig(any()) } returns Result.failure(RuntimeException("Test error"))
        viewModel.handleEvent(CustomizeEvent.SaveConfiguration)
        advanceUntilIdle()
        
        // When
        viewModel.handleEvent(CustomizeEvent.DismissError)
        
        // Then
        val state = viewModel.uiState.value
        assertNull("Should clear error message", state.errorMessage)
        assertTrue("Should clear validation errors", state.validationErrors.isEmpty())
    }
    
    @Test
    fun `battery level changes should update preview when live preview enabled`() = runTest {
        // Given
        viewModel.handleEvent(CustomizeEvent.OnScreenEnter)
        advanceUntilIdle()
        
        // When
        viewModel.handleEvent(CustomizeEvent.BatteryLevelChanged(85, false))
        
        // Then
        val state = viewModel.uiState.value
        assertEquals("Should update preview battery level", 85, state.previewBatteryLevel)
    }
    
    @Test
    fun `save configuration failure should show error`() = runTest {
        // Given
        val errorMessage = "Save failed"
        coEvery { mockSaveCustomizationUseCase.saveCustomizationConfig(any()) } returns Result.failure(RuntimeException(errorMessage))
        viewModel.handleEvent(CustomizeEvent.OnScreenEnter)
        advanceUntilIdle()
        viewModel.handleEvent(CustomizeEvent.ToggleEmojiVisibility(false)) // Make a change
        
        // When
        viewModel.handleEvent(CustomizeEvent.SaveConfiguration)
        advanceUntilIdle()
        
        // Then
        val state = viewModel.uiState.value
        assertNotNull("Should show error message", state.errorMessage)
        assertTrue("Should contain error details", state.errorMessage!!.contains("Save failed"))
    }
}
