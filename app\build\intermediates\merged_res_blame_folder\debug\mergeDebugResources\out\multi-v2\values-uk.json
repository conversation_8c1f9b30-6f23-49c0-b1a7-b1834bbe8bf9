{"logs": [{"outputFile": "com.tqhit.battery.one.app-mergeDebugResources-99:/values-uk/values-uk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5b80dcbf636dc26335bd1b8e4f16f918\\transformed\\material-1.12.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,374,452,530,618,726,817,913,1029,1112,1184,1251,1342,1408,1471,1559,1621,1688,1746,1817,1876,1930,2044,2104,2167,2221,2294,2413,2499,2575,2666,2747,2830,2969,3054,3141,3274,3362,3440,3497,3548,3614,3686,3762,3833,3916,3989,4066,4148,4222,4331,4421,4500,4591,4687,4761,4842,4937,4991,5073,5139,5226,5312,5374,5438,5501,5574,5681,5791,5889,5995,6056,6111,6193,6278,6354", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "endColumns": "12,77,77,87,107,90,95,115,82,71,66,90,65,62,87,61,66,57,70,58,53,113,59,62,53,72,118,85,75,90,80,82,138,84,86,132,87,77,56,50,65,71,75,70,82,72,76,81,73,108,89,78,90,95,73,80,94,53,81,65,86,85,61,63,62,72,106,109,97,105,60,54,81,84,75,76", "endOffsets": "369,447,525,613,721,812,908,1024,1107,1179,1246,1337,1403,1466,1554,1616,1683,1741,1812,1871,1925,2039,2099,2162,2216,2289,2408,2494,2570,2661,2742,2825,2964,3049,3136,3269,3357,3435,3492,3543,3609,3681,3757,3828,3911,3984,4061,4143,4217,4326,4416,4495,4586,4682,4756,4837,4932,4986,5068,5134,5221,5307,5369,5433,5496,5569,5676,5786,5884,5990,6051,6106,6188,6273,6349,6426"}, "to": {"startLines": "23,112,113,114,115,116,132,133,147,215,217,269,293,301,373,374,375,376,377,378,379,380,381,382,383,384,385,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,469,503,504,515", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1051,9248,9326,9404,9492,9600,10942,11038,12137,20479,20644,24669,26582,27148,34662,34750,34812,34879,34937,35008,35067,35121,35235,35295,35358,35412,35485,35739,35825,35901,35992,36073,36156,36295,36380,36467,36600,36688,36766,36823,36874,36940,37012,37088,37159,37242,37315,37392,37474,37548,37657,37747,37826,37917,38013,38087,38168,38263,38317,38399,38465,38552,38638,38700,38764,38827,38900,39007,39117,39215,39321,39382,42640,45010,45095,46013", "endLines": "28,112,113,114,115,116,132,133,147,215,217,269,293,301,373,374,375,376,377,378,379,380,381,382,383,384,385,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,469,503,504,515", "endColumns": "12,77,77,87,107,90,95,115,82,71,66,90,65,62,87,61,66,57,70,58,53,113,59,62,53,72,118,85,75,90,80,82,138,84,86,132,87,77,56,50,65,71,75,70,82,72,76,81,73,108,89,78,90,95,73,80,94,53,81,65,86,85,61,63,62,72,106,109,97,105,60,54,81,84,75,76", "endOffsets": "1320,9321,9399,9487,9595,9686,11033,11149,12215,20546,20706,24755,26643,27206,34745,34807,34874,34932,35003,35062,35116,35230,35290,35353,35407,35480,35599,35820,35896,35987,36068,36151,36290,36375,36462,36595,36683,36761,36818,36869,36935,37007,37083,37154,37237,37310,37387,37469,37543,37652,37742,37821,37912,38008,38082,38163,38258,38312,38394,38460,38547,38633,38695,38759,38822,38895,39002,39112,39210,39316,39377,39432,42717,45090,45166,46085"}}, {"source": "D:\\Duc\\AndroidStudioProjects\\TJ_BatteryOne\\app\\src\\main\\res\\values-uk\\strings.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,237,18,19,20,21,22,23,24,25,26,27,28,29,264,30,31,32,33,34,35,36,240,250,253,255,37,2,263,243,261,38,39,40,41,42,43,44,45,115,116,114,113,117,46,47,48,49,50,51,52,53,54,55,56,238,57,58,242,59,60,61,62,63,64,65,66,67,68,248,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,119,120,258,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,259,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,241,168,169,170,171,172,173,174,175,176,177,178,246,245,244,252,256,179,180,181,182,183,184,254,185,186,187,239,188,260,189,190,191,192,193,194,195,196,247,197,198,257,199,200,201,202,203,251,204,205,206,207,208,209,210,211,212,213,214,215,216,262,217,218,219,220,221,222,223,224,225,226,249,227,228,229,230,231,232,233,234,235,236", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "126,188,242,295,347,398,451,502,552,611,681,734,786,838,889,20800,942,1021,1078,1128,1178,1230,1307,1376,1456,1534,1605,1667,23300,1724,1764,1930,1989,2088,2131,2198,20991,21797,22233,22365,2286,57,23251,21187,23124,2327,2376,2415,2499,2560,2639,3035,3436,11721,11788,11075,10991,11853,3480,3555,3633,3835,4122,4194,4251,4292,4356,4432,4481,20877,4550,4620,21111,4666,4743,4785,4864,5025,5100,5194,5274,5337,5429,21672,5494,5551,5607,5671,5737,5786,5859,5949,6002,6312,6370,6429,6943,7472,7539,7589,7728,7789,8058,8111,8537,8599,8722,8873,9011,9074,9120,9166,9245,9343,9429,9483,9618,9718,9775,9839,9905,10190,10444,10620,10779,10860,11940,12010,22921,12065,12108,12202,12271,12330,12539,12587,12660,12720,12776,12911,12970,13096,13308,13348,13412,13450,13488,13524,13560,13630,13671,13729,13778,13846,22980,13917,13984,14043,14086,14128,14208,14574,14629,14705,14868,14937,15006,15044,15080,15116,15154,15208,15272,15345,15400,15479,15619,21039,15654,15711,15764,15850,15922,15997,16101,16213,16283,16322,16397,21490,21318,21234,22128,22687,16621,16661,16713,16764,16827,16903,22284,16952,17037,17087,20946,17133,23049,17264,17356,17450,17509,17577,17759,17834,17891,21599,17949,18004,22732,18064,18145,18242,18319,18370,22057,18443,18523,18565,18621,18669,18789,19123,19196,19246,19307,19380,19438,19491,23194,19531,19595,19784,19875,19942,20014,20069,20148,20218,20252,21740,20307,20371,20416,20454,20507,20560,20625,20682,20719,20755", "endColumns": "60,52,51,50,49,51,49,48,57,68,51,50,50,49,51,75,77,55,48,48,50,75,67,78,76,69,60,55,64,38,164,57,97,41,65,86,46,258,49,320,39,67,47,45,68,47,37,82,59,77,394,399,42,65,63,644,82,83,73,76,200,285,70,55,39,62,74,47,67,67,68,44,74,75,40,77,159,73,92,78,61,90,63,66,55,54,62,64,47,71,88,51,308,56,57,512,527,65,48,137,59,267,51,424,60,121,149,136,61,44,44,77,96,84,52,133,98,55,62,64,283,252,174,157,79,84,68,53,57,41,92,67,57,207,46,71,58,54,133,57,124,210,38,62,36,36,34,34,68,39,56,47,66,69,67,65,57,41,40,78,364,53,74,161,67,67,36,34,34,36,52,62,71,53,77,138,33,70,55,51,84,70,73,102,110,68,37,73,222,107,170,82,103,43,38,50,49,61,74,47,79,83,48,44,43,129,73,90,92,57,66,180,73,55,56,71,53,58,187,79,95,75,49,71,69,78,40,54,46,118,332,71,48,59,71,56,51,38,55,62,187,89,65,70,53,77,68,32,53,55,62,43,36,51,51,63,55,35,34,43", "endOffsets": "182,236,289,341,392,445,496,546,605,675,728,780,832,883,936,20871,1015,1072,1122,1172,1224,1301,1370,1450,1528,1599,1661,1718,23360,1758,1924,1983,2082,2125,2192,2280,21033,22051,22278,22681,2321,120,23294,21228,23188,2370,2409,2493,2554,2633,3029,3430,3474,11782,11847,11715,11069,11932,3549,3627,3829,4116,4188,4245,4286,4350,4426,4475,4544,20940,4614,4660,21181,4737,4779,4858,5019,5094,5188,5268,5331,5423,5488,21734,5545,5601,5665,5731,5780,5853,5943,5996,6306,6364,6423,6937,7466,7533,7583,7722,7783,8052,8105,8531,8593,8716,8867,9005,9068,9114,9160,9239,9337,9423,9477,9612,9712,9769,9833,9899,10184,10438,10614,10773,10854,10940,12004,12059,22974,12102,12196,12265,12324,12533,12581,12654,12714,12770,12905,12964,13090,13302,13342,13406,13444,13482,13518,13554,13624,13665,13723,13772,13840,13911,23043,13978,14037,14080,14122,14202,14568,14623,14699,14862,14931,15000,15038,15074,15110,15148,15202,15266,15339,15394,15473,15613,15648,21105,15705,15758,15844,15916,15991,16095,16207,16277,16316,16391,16615,21593,21484,21312,22227,22726,16655,16707,16758,16821,16897,16946,22359,17031,17081,17127,20985,17258,23118,17350,17444,17503,17571,17753,17828,17885,17943,21666,17998,18058,22915,18139,18236,18313,18364,18437,22122,18517,18559,18615,18663,18783,19117,19190,19240,19301,19374,19432,19485,19525,23245,19589,19778,19869,19936,20008,20063,20142,20212,20246,20301,21791,20365,20410,20448,20501,20554,20619,20676,20713,20749,20794"}, "to": {"startLines": "29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,47,48,51,52,63,64,66,67,68,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,117,118,119,120,121,129,130,131,134,135,136,137,138,139,140,141,142,143,144,145,146,168,169,170,171,173,174,175,176,177,178,179,180,181,182,183,184,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,210,211,212,213,214,216,268,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,294,295,297,299,300,302,303,304,305,306,307,308,309,310,311,369,370,371,372,386,387,433,440,441,442,443,444,446,447,448,449,450,451,452,453,462,463,464,465,466,467,468,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,487,488,489,490,491,492,500,501,505,507,508,509,510,511,512,513,514,516,517,518,519,520,521,522,526,527,528,531,533,534,535,536,537,540,541,542,543,544,545,546,547,548,549,551,552,553,554,555,556,557,558", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1325,1386,1439,1491,1542,1592,1644,1694,1743,1801,1870,1922,1973,2024,2074,2126,2413,2491,2770,2819,3806,3857,4039,4107,4186,4538,4608,4669,4725,4790,4829,4994,5052,5150,5192,5258,5345,5392,5651,5701,6022,6062,6130,6178,6224,6293,6341,6379,6462,6522,6600,6995,7395,7438,7504,7568,8213,8296,8380,8454,8531,8732,9018,9089,9145,9185,9691,9766,9814,9882,9950,10746,10791,10866,11154,11195,11273,11433,11507,11600,11679,11741,11832,11896,11963,12019,12074,14623,14688,14736,14808,15007,15059,15368,15425,15483,15996,16524,16590,16639,16777,16837,17105,17360,17785,17846,17968,18118,18255,18317,18362,18407,18485,18582,18667,18720,18854,18953,19009,19072,19137,19421,19674,19849,20007,20171,20256,20325,20379,20437,20551,24601,25095,25153,25361,25408,25480,25539,25594,25728,25786,25911,26122,26161,26224,26261,26298,26333,26368,26437,26477,26534,26648,26715,26867,27024,27090,27211,27253,27294,27373,27738,27792,27867,28029,28097,28165,34502,34537,34572,34609,35604,35667,39437,39980,40058,40197,40231,40302,40440,40492,40577,40648,40722,40825,40936,41005,41833,41907,42130,42238,42409,42492,42596,42722,42761,42812,42862,42924,42999,43047,43127,43211,43260,43305,43349,43479,43553,43644,43910,43968,44035,44216,44290,44346,44802,44874,45171,45302,45490,45570,45666,45742,45792,45864,45934,46090,46131,46186,46233,46352,46685,46757,47061,47121,47193,47400,47575,47614,47670,47733,47921,48208,48274,48345,48399,48477,48546,48579,48633,48689,48752,48856,48893,48945,48997,49061,49117,49153,49188", "endColumns": "60,52,51,50,49,51,49,48,57,68,51,50,50,49,51,75,77,55,48,48,50,75,67,78,76,69,60,55,64,38,164,57,97,41,65,86,46,258,49,320,39,67,47,45,68,47,37,82,59,77,394,399,42,65,63,644,82,83,73,76,200,285,70,55,39,62,74,47,67,67,68,44,74,75,40,77,159,73,92,78,61,90,63,66,55,54,62,64,47,71,88,51,308,56,57,512,527,65,48,137,59,267,51,424,60,121,149,136,61,44,44,77,96,84,52,133,98,55,62,64,283,252,174,157,79,84,68,53,57,41,92,67,57,207,46,71,58,54,133,57,124,210,38,62,36,36,34,34,68,39,56,47,66,69,67,65,57,41,40,78,364,53,74,161,67,67,36,34,34,36,52,62,71,53,77,138,33,70,55,51,84,70,73,102,110,68,37,73,222,107,170,82,103,43,38,50,49,61,74,47,79,83,48,44,43,129,73,90,92,57,66,180,73,55,56,71,53,58,187,79,95,75,49,71,69,78,40,54,46,118,332,71,48,59,71,56,51,38,55,62,187,89,65,70,53,77,68,32,53,55,62,43,36,51,51,63,55,35,34,43", "endOffsets": "1381,1434,1486,1537,1587,1639,1689,1738,1796,1865,1917,1968,2019,2069,2121,2197,2486,2542,2814,2863,3852,3928,4102,4181,4258,4603,4664,4720,4785,4824,4989,5047,5145,5187,5253,5340,5387,5646,5696,6017,6057,6125,6173,6219,6288,6336,6374,6457,6517,6595,6990,7390,7433,7499,7563,8208,8291,8375,8449,8526,8727,9013,9084,9140,9180,9243,9761,9809,9877,9945,10014,10786,10861,10937,11190,11268,11428,11502,11595,11674,11736,11827,11891,11958,12014,12069,12132,14683,14731,14803,14892,15054,15363,15420,15478,15991,16519,16585,16634,16772,16832,17100,17152,17780,17841,17963,18113,18250,18312,18357,18402,18480,18577,18662,18715,18849,18948,19004,19067,19132,19416,19669,19844,20002,20082,20251,20320,20374,20432,20474,20639,24664,25148,25356,25403,25475,25534,25589,25723,25781,25906,26117,26156,26219,26256,26293,26328,26363,26432,26472,26529,26577,26710,26780,26930,27085,27143,27248,27289,27368,27733,27787,27862,28024,28092,28160,28197,34532,34567,34604,34657,35662,35734,39486,40053,40192,40226,40297,40353,40487,40572,40643,40717,40820,40931,41000,41038,41902,42125,42233,42404,42487,42591,42635,42756,42807,42857,42919,42994,43042,43122,43206,43255,43300,43344,43474,43548,43639,43732,43963,44030,44211,44285,44341,44398,44869,44923,45225,45485,45565,45661,45737,45787,45859,45929,46008,46126,46181,46228,46347,46680,46752,46801,47116,47188,47245,47447,47609,47665,47728,47916,48006,48269,48340,48394,48472,48541,48574,48628,48684,48747,48791,48888,48940,48992,49056,49112,49148,49183,49227"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\42f95d9fa807b14415e836fc15872a54\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-uk\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "141", "endOffsets": "336"}, "to": {"startLines": "158", "startColumns": "4", "startOffsets": "13408", "endColumns": "145", "endOffsets": "13549"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\179e6486bd57a16ea175623aa423e7ed\\transformed\\jetified-material3-release\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,289,407,525,624,719,831,969,1085,1232,1316,1416,1509,1605,1721,1845,1950,2091,2228,2363,2552,2679,2803,2932,3053,3147,3248,3374,3504,3602,3707,3816,3961,4112,4220,4320,4395,4490,4586,4705,4791,4878,4977,5057,5143,5242,5346,5441,5541,5630,5737,5833,5936,6054,6134,6249", "endColumns": "117,115,117,117,98,94,111,137,115,146,83,99,92,95,115,123,104,140,136,134,188,126,123,128,120,93,100,125,129,97,104,108,144,150,107,99,74,94,95,118,85,86,98,79,85,98,103,94,99,88,106,95,102,117,79,114,105", "endOffsets": "168,284,402,520,619,714,826,964,1080,1227,1311,1411,1504,1600,1716,1840,1945,2086,2223,2358,2547,2674,2798,2927,3048,3142,3243,3369,3499,3597,3702,3811,3956,4107,4215,4315,4390,4485,4581,4700,4786,4873,4972,5052,5138,5237,5341,5436,5536,5625,5732,5828,5931,6049,6129,6244,6350"}, "to": {"startLines": "312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "28202,28320,28436,28554,28672,28771,28866,28978,29116,29232,29379,29463,29563,29656,29752,29868,29992,30097,30238,30375,30510,30699,30826,30950,31079,31200,31294,31395,31521,31651,31749,31854,31963,32108,32259,32367,32467,32542,32637,32733,32852,32938,33025,33124,33204,33290,33389,33493,33588,33688,33777,33884,33980,34083,34201,34281,34396", "endColumns": "117,115,117,117,98,94,111,137,115,146,83,99,92,95,115,123,104,140,136,134,188,126,123,128,120,93,100,125,129,97,104,108,144,150,107,99,74,94,95,118,85,86,98,79,85,98,103,94,99,88,106,95,102,117,79,114,105", "endOffsets": "28315,28431,28549,28667,28766,28861,28973,29111,29227,29374,29458,29558,29651,29747,29863,29987,30092,30233,30370,30505,30694,30821,30945,31074,31195,31289,31390,31516,31646,31744,31849,31958,32103,32254,32362,32462,32537,32632,32728,32847,32933,33020,33119,33199,33285,33384,33488,33583,33683,33772,33879,33975,34078,34196,34276,34391,34497"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f908cdc45776521b403beeef1508641c\\transformed\\core-1.16.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,358,459,564,669,782", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "150,252,353,454,559,664,777,878"}, "to": {"startLines": "122,123,124,125,126,127,128,525", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "10019,10119,10221,10322,10423,10528,10633,46960", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "10114,10216,10317,10418,10523,10628,10741,47056"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\237635df39b25799c092d66a208ce67d\\transformed\\jetified-foundation-release\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,152", "endColumns": "96,99", "endOffsets": "147,247"}, "to": {"startLines": "538,539", "startColumns": "4,4", "startOffsets": "48011,48108", "endColumns": "96,99", "endOffsets": "48103,48203"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8bd7feaae90e869538df51f29dd16595\\transformed\\jetified-media3-ui-1.6.1\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,626,956,1040,1122,1205,1305,1404,1489,1552,1650,1749,1820,1889,1955,2023,2149,2274,2411,2488,2570,2645,2733,2828,2921,2989,3074,3127,3187,3235,3296,3363,3431,3495,3562,3627,3687,3753,3805,3866,3951,4036,4091", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63", "endColumns": "17,12,12,83,81,82,99,98,84,62,97,98,70,68,65,67,125,124,136,76,81,74,87,94,92,67,84,52,59,47,60,66,67,63,66,64,59,65,51,60,84,84,54,66", "endOffsets": "282,621,951,1035,1117,1200,1300,1399,1484,1547,1645,1744,1815,1884,1950,2018,2144,2269,2406,2483,2565,2640,2728,2823,2916,2984,3069,3122,3182,3230,3291,3358,3426,3490,3557,3622,3682,3748,3800,3861,3946,4031,4086,4153"}, "to": {"startLines": "2,11,17,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,382,721,20711,20795,20877,20960,21060,21159,21244,21307,21405,21504,21575,21644,21710,21778,21904,22029,22166,22243,22325,22400,22488,22583,22676,22744,23517,23570,23630,23678,23739,23806,23874,23938,24005,24070,24130,24196,24248,24309,24394,24479,24534", "endLines": "10,16,22,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267", "endColumns": "17,12,12,83,81,82,99,98,84,62,97,98,70,68,65,67,125,124,136,76,81,74,87,94,92,67,84,52,59,47,60,66,67,63,66,64,59,65,51,60,84,84,54,66", "endOffsets": "377,716,1046,20790,20872,20955,21055,21154,21239,21302,21400,21499,21570,21639,21705,21773,21899,22024,22161,22238,22320,22395,22483,22578,22671,22739,22824,23565,23625,23673,23734,23801,23869,23933,24000,24065,24125,24191,24243,24304,24389,24474,24529,24596"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\93d3043f0a8b9466a00a736e170a6ddc\\transformed\\appcompat-1.7.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,6,7,10,11,12,13,14,15,16,17,18,19,22,26,27,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,510,615,898,989,1082,1177,1271,1371,1464,1559,1654,1745,2041,2459,2564,2834", "endColumns": "108,101,104,117,90,92,94,93,99,92,94,94,90,90,105,104,169,81", "endOffsets": "209,311,610,728,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,2142,2559,2729,2911"}, "to": {"startLines": "45,46,49,50,53,54,55,56,57,58,59,60,61,62,65,69,70,502", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2202,2311,2547,2652,2868,2959,3052,3147,3241,3341,3434,3529,3624,3715,3933,4263,4368,44928", "endColumns": "108,101,104,117,90,92,94,93,99,92,94,94,90,90,105,104,169,81", "endOffsets": "2306,2408,2647,2765,2954,3047,3142,3236,3336,3429,3524,3619,3710,3801,4034,4363,4533,45005"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d56ddc8f70c1b6c4f2dfff25a6818549\\transformed\\jetified-play-services-base-18.5.0\\res\\values-uk\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,456,579,685,835,960,1071,1172,1336,1438,1596,1717,1860,1998,2064,2121", "endColumns": "103,158,122,105,149,124,110,100,163,101,157,120,142,137,65,56,83", "endOffsets": "296,455,578,684,834,959,1070,1171,1335,1437,1595,1716,1859,1997,2063,2120,2204"}, "to": {"startLines": "150,151,152,153,154,155,156,157,159,160,161,162,163,164,165,166,167", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12397,12505,12668,12795,12905,13059,13188,13303,13554,13722,13828,13990,14115,14262,14404,14474,14535", "endColumns": "107,162,126,109,153,128,114,104,167,105,161,124,146,141,69,60,87", "endOffsets": "12500,12663,12790,12900,13054,13183,13298,13403,13717,13823,13985,14110,14257,14399,14469,14530,14618"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9e0a763189144907fb0197c2b097244b\\transformed\\jetified-ui-release\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,384,485,569,651,740,828,910,995,1083,1231,1308,1385,1465,1535", "endColumns": "92,83,101,100,83,81,88,87,81,84,87,71,76,76,79,69,122", "endOffsets": "193,277,379,480,564,646,735,823,905,990,1078,1150,1303,1380,1460,1530,1653"}, "to": {"startLines": "148,149,185,186,209,296,298,439,445,485,486,506,523,524,529,530,532", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12220,12313,17157,17259,20087,26785,26935,39892,40358,43737,43822,45230,46806,46883,47250,47330,47452", "endColumns": "92,83,101,100,83,81,88,87,81,84,87,71,76,76,79,69,122", "endOffsets": "12308,12392,17254,17355,20166,26862,27019,39975,40435,43817,43905,45297,46878,46955,47325,47395,47570"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b572512e02266e069f95737c22215ab9\\transformed\\jetified-media3-exoplayer-1.6.1\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,194,262,333,413,486,579,668", "endColumns": "73,64,67,70,79,72,92,88,74", "endOffsets": "124,189,257,328,408,481,574,663,738"}, "to": {"startLines": "242,243,244,245,246,247,248,249,250", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "22829,22903,22968,23036,23107,23187,23260,23353,23442", "endColumns": "73,64,67,70,79,72,92,88,74", "endOffsets": "22898,22963,23031,23102,23182,23255,23348,23437,23512"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5c5c72c6ff4a7863322da50648a25e99\\transformed\\browser-1.8.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,272,392", "endColumns": "109,106,119,107", "endOffsets": "160,267,387,495"}, "to": {"startLines": "172,270,271,272", "startColumns": "4,4,4,4", "startOffsets": "14897,24760,24867,24987", "endColumns": "109,106,119,107", "endOffsets": "15002,24862,24982,25090"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5444be4bc77930bd89cfbb9f2224d8e4\\transformed\\navigation-ui-2.8.9\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,163", "endColumns": "107,119", "endOffsets": "158,278"}, "to": {"startLines": "437,438", "startColumns": "4,4", "startOffsets": "39664,39772", "endColumns": "107,119", "endOffsets": "39767,39887"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\237e0b5db534c615c4317f1b214e3e7f\\transformed\\jetified-play-services-ads-24.2.0\\res\\values-uk\\values.xml", "from": {"startLines": "4,5,6,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,250,300,596,660,805,919,1044,1094,1153,1259,1354,1401,1480,1516,1553,1605,1679,1725", "endColumns": "50,49,59,63,144,113,124,49,58,105,94,46,78,35,36,51,73,45,55", "endOffsets": "249,299,359,659,804,918,1043,1093,1152,1258,1353,1400,1479,1515,1552,1604,1678,1724,1780"}, "to": {"startLines": "434,435,436,454,455,456,457,458,459,460,461,493,494,495,496,497,498,499,550", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "39491,39546,39600,41043,41111,41260,41378,41507,41561,41624,41734,44403,44454,44537,44577,44618,44674,44752,48796", "endColumns": "54,53,63,67,148,117,128,53,62,109,98,50,82,39,40,55,77,49,59", "endOffsets": "39541,39595,39659,41106,41255,41373,41502,41556,41619,41729,41828,44449,44532,44572,44613,44669,44747,44797,48851"}}]}]}