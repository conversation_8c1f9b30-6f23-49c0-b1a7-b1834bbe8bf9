package com.tqhit.battery.one.dialog.theme

import android.app.Activity
import android.graphics.Color
import android.view.ViewGroup
import android.view.WindowManager
import androidx.core.graphics.drawable.toDrawable
import com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog
import com.tqhit.battery.one.R
import com.tqhit.battery.one.databinding.DialogSelectThemeBinding
import com.tqhit.battery.one.manager.theme.ThemeManager

class SelectThemeDialog(
    private val activity: Activity,
) : AdLibBaseDialog<DialogSelectThemeBinding>(activity) {
    override val binding by lazy { DialogSelectThemeBinding.inflate(layoutInflater) }

    override fun initWindow() {
        window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        window?.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
        val layoutParams = WindowManager.LayoutParams()
        layoutParams.copyFrom(window?.attributes)
        window?.attributes = layoutParams

        setCanceledOnTouchOutside(false)
    }

    override fun setupListener() {
        super.setupListener()
        setupThemeButtons()
        setupCloseButton()
        updateSelectedThemeUI()
    }

    private fun setupThemeButtons() {
        // Auto theme
        binding.auto.setOnClickListener {
            saveTheme("AutoTheme")
        }

        // Light theme
        binding.lightTheme.setOnClickListener {
            saveTheme("LightTheme")
        }

        // Dark theme
        binding.darkTheme.setOnClickListener {
            saveTheme("BlackTheme")
        }

        // AMOLED theme
        binding.amoledTheme.setOnClickListener {
            saveTheme("AmoledTheme")
        }

        // Grey theme
        binding.greyTheme.setOnClickListener {
            saveTheme("GreyTheme")
        }

        // Light inverted theme
        binding.lightThemeInverted.setOnClickListener {
            saveTheme("LightThemeInverted")
        }

        // Dark inverted theme
        binding.darkThemeInverted.setOnClickListener {
            saveTheme("BlackThemeInverted")
        }

        // AMOLED inverted theme
        binding.amoledThemeInverted.setOnClickListener {
            saveTheme("AmoledThemeInverted")
        }

        // Grey inverted theme
        binding.greyThemeInverted.setOnClickListener {
            saveTheme("GreyThemeInverted")
        }
    }

    private fun setupCloseButton() {
        binding.exitTheme.setOnClickListener {
            dismiss()
        }
    }

    private fun updateSelectedThemeUI() {
        val currentTheme = ThemeManager.getSelectedTheme()

        // Set backgrounds and selection state based on current theme
        binding.auto.apply {
            isSelected = currentTheme == "AutoTheme"
            setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_line_up else R.drawable.grey_block_line_up)
        }

        binding.lightTheme.apply {
            isSelected = currentTheme == "LightTheme"
            setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
        }

        binding.darkTheme.apply {
            isSelected = currentTheme == "BlackTheme"
            setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
        }

        binding.amoledTheme.apply {
            isSelected = currentTheme == "AmoledTheme"
            setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
        }

        binding.greyTheme.apply {
            isSelected = currentTheme == "GreyTheme"
            setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
        }

        binding.lightThemeInverted.apply {
            isSelected = currentTheme == "LightThemeInverted"
            setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
        }

        binding.darkThemeInverted.apply {
            isSelected = currentTheme == "BlackThemeInverted"
            setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
        }

        binding.amoledThemeInverted.apply {
            isSelected = currentTheme == "AmoledThemeInverted"
            setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
        }

        binding.greyThemeInverted.apply {
            isSelected = currentTheme == "GreyThemeInverted"
            setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_line_down else R.drawable.grey_block_line_down)
        }
    }

    private fun saveTheme(themeName: String) {
        ThemeManager.saveTheme(themeName)
        ThemeManager.applyTheme(activity)
        dismiss()
        activity.recreate()
    }
}