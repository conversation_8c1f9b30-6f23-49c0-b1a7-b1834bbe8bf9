{"logs": [{"outputFile": "com.tqhit.battery.one.app-mergeDebugResources-99:/values-pt/values-pt.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5444be4bc77930bd89cfbb9f2224d8e4\\transformed\\navigation-ui-2.8.9\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,119", "endOffsets": "162,282"}, "to": {"startLines": "335,336", "startColumns": "4,4", "startOffsets": "31741,31853", "endColumns": "111,119", "endOffsets": "31848,31968"}}, {"source": "D:\\Duc\\AndroidStudioProjects\\TJ_BatteryOne\\app\\src\\main\\res\\values-pt\\strings.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,237,18,19,20,21,22,23,24,25,26,27,28,29,265,30,31,32,33,34,35,36,241,251,254,256,37,2,264,244,262,38,39,40,41,42,43,44,45,115,116,114,113,117,46,47,48,49,50,51,52,53,54,55,56,239,57,58,243,59,60,61,62,63,64,65,66,67,68,249,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,119,120,259,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,260,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,242,168,169,170,171,172,173,174,175,176,177,178,247,246,245,253,257,179,180,181,182,183,184,255,185,186,187,240,188,261,189,190,191,192,193,194,195,196,248,197,198,258,199,200,201,202,203,252,204,205,206,207,208,209,210,211,212,213,214,215,216,263,217,218,219,220,221,222,223,224,225,226,250,227,228,229,230,231,232,233,234,235,236", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "134,194,243,292,341,390,442,491,540,595,663,713,761,812,860,22014,912,990,1050,1105,1156,1212,1290,1363,1442,1521,1592,1656,24639,1709,1748,1907,1964,2055,2098,2160,22278,23126,23582,23699,2251,57,24593,22489,24473,2295,2342,2387,2478,2538,2621,3028,3430,12457,12523,11728,11641,12587,3474,3549,3625,3835,4157,4236,4290,4332,4393,4464,4519,22163,4589,4659,22408,4704,4786,4831,4914,5079,5156,5262,5348,5418,5512,23000,5586,5642,5699,5765,5820,5867,5937,6038,6089,6457,6513,6571,7185,7796,7870,7924,8071,8142,8434,8488,8986,9053,9194,9346,9508,9576,9625,9673,9749,9861,9954,10005,10161,10266,10323,10388,10455,10769,11043,11253,11413,11512,12672,12740,24276,12795,12835,12929,12998,13056,13283,13335,13417,13482,13548,13693,13756,13895,14115,14155,14214,14252,14290,14326,14362,14440,14482,14536,14590,14659,24335,14733,14801,14854,14899,14940,15015,15464,15519,15595,15763,15839,15915,15952,15987,16028,16071,16128,16191,16260,16319,16398,16550,22326,16586,16647,16701,16795,16866,16941,17047,17166,17245,17290,17371,22806,22623,22532,23481,24034,17667,17707,17762,17813,17882,17964,23632,18013,18102,18152,22233,18196,24400,18316,18414,18514,18565,18635,18844,18926,18986,22921,19047,19102,24078,19164,19249,19349,19433,19485,23413,19575,19651,19694,19752,19799,19916,20276,20358,20405,20471,20552,20608,20661,24534,20701,20773,20990,21082,21145,21215,21268,21339,21407,21446,23073,21507,21570,21616,21658,21714,21771,21846,21896,21933,21969", "endColumns": "58,47,47,47,47,50,47,47,53,66,48,46,49,46,50,88,76,58,53,49,54,76,71,77,77,69,62,51,70,37,157,55,89,41,60,89,46,285,48,333,42,75,44,41,59,45,43,89,58,81,405,400,42,64,62,727,85,81,73,74,208,320,77,52,40,59,69,53,68,68,68,43,79,80,43,81,163,75,104,84,68,92,72,71,54,55,64,53,45,68,99,49,366,54,56,612,609,72,52,145,69,290,52,496,65,139,150,160,66,47,46,74,110,91,49,154,103,55,63,65,312,272,208,158,97,82,66,53,57,38,92,67,56,225,50,80,63,64,143,61,137,218,38,57,36,36,34,34,76,40,52,52,67,72,63,66,51,43,39,73,447,53,74,166,74,74,35,33,39,41,55,61,67,57,77,150,34,80,59,52,92,69,73,104,117,77,43,79,294,113,181,89,99,42,38,53,49,67,80,47,65,87,48,42,43,118,71,96,98,49,68,207,80,58,59,77,53,60,196,83,98,82,50,88,66,74,41,56,45,115,358,80,45,64,79,54,51,38,57,70,215,90,61,68,51,69,66,37,59,51,61,44,40,54,55,73,48,35,34,43", "endOffsets": "188,237,286,335,384,436,485,534,589,657,707,755,806,854,906,22098,984,1044,1099,1150,1206,1284,1357,1436,1515,1586,1650,1703,24705,1742,1901,1958,2049,2092,2154,2245,22320,23407,23626,24028,2289,128,24633,22526,24528,2336,2381,2472,2532,2615,3022,3424,3468,12517,12581,12451,11722,12664,3543,3619,3829,4151,4230,4284,4326,4387,4458,4513,4583,22227,4653,4698,22483,4780,4825,4908,5073,5150,5256,5342,5412,5506,5580,23067,5636,5693,5759,5814,5861,5931,6032,6083,6451,6507,6565,7179,7790,7864,7918,8065,8136,8428,8482,8980,9047,9188,9340,9502,9570,9619,9667,9743,9855,9948,9999,10155,10260,10317,10382,10449,10763,11037,11247,11407,11506,11590,12734,12789,24329,12829,12923,12992,13050,13277,13329,13411,13476,13542,13687,13750,13889,14109,14149,14208,14246,14284,14320,14356,14434,14476,14530,14584,14653,14727,24394,14795,14848,14893,14934,15009,15458,15513,15589,15757,15833,15909,15946,15981,16022,16065,16122,16185,16254,16313,16392,16544,16580,22402,16641,16695,16789,16860,16935,17041,17160,17239,17284,17365,17661,22915,22800,22617,23576,24072,17701,17756,17807,17876,17958,18007,23693,18096,18146,18190,22272,18310,24467,18408,18508,18559,18629,18838,18920,18980,19041,22994,19096,19158,24270,19243,19343,19427,19479,19569,23475,19645,19688,19746,19793,19910,20270,20352,20399,20465,20546,20602,20655,20695,24587,20767,20984,21076,21139,21209,21262,21333,21401,21440,21501,23120,21564,21610,21652,21708,21765,21840,21890,21927,21963,22008"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,37,38,41,42,53,54,56,57,58,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,132,133,134,135,137,138,139,140,141,142,143,144,145,146,147,148,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,174,175,176,177,178,179,230,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,257,259,260,261,262,263,264,265,266,267,268,269,270,328,329,330,331,332,333,334,338,339,340,341,342,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,376,377,378,379,380,381,382,383,385,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,405,406,407,410,412,413,414,415,416,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "700,759,807,855,903,951,1002,1050,1098,1152,1219,1268,1315,1365,1412,1463,1778,1855,2134,2188,3176,3231,3419,3491,3569,3919,3989,4052,4104,4175,4213,4371,4427,4517,4559,4620,4710,4757,5043,5092,5426,5469,5545,5590,5632,5692,5738,5782,5872,5931,6013,6419,6820,6863,6928,6991,7719,7805,7887,7961,8036,8245,8566,8644,8697,8738,8798,8868,8922,8991,9060,9867,9911,9991,10072,10116,10198,10362,10438,10543,10628,10697,10790,10863,10935,10990,11046,11292,11346,11392,11461,11676,11726,12093,12148,12205,12818,13428,13501,13554,13700,13770,14061,14310,14807,14873,15013,15164,15325,15392,15440,15487,15562,15673,15765,15815,15970,16074,16130,16194,16260,16573,16846,17055,17214,17398,17481,17548,17602,17660,17699,21576,21961,22018,22244,22295,22376,22440,22505,22649,22711,22849,23068,23107,23165,23202,23239,23274,23309,23386,23427,23480,23533,23601,23757,23918,23985,24037,24081,24121,24195,24643,24697,24772,24939,25014,25089,31381,31415,31455,31497,31553,31615,31683,32064,32142,32293,32328,32409,32556,32609,32702,32772,32846,32951,33069,33147,33191,33271,33566,33680,33862,33952,34052,34095,34134,34188,34238,34306,34387,34435,34501,34589,34638,34681,34725,34844,34916,35013,35287,35337,35406,35614,35695,35754,35814,35892,36032,36169,36366,36450,36549,36632,36683,36772,36839,36914,36956,37013,37059,37175,37534,37615,37916,37981,38061,38259,38424,38463,38521,38592,38808,39067,39129,39198,39250,39320,39387,39425,39485,39537,39599,39644,39685,39740,39796,39870,39919,39955,39990", "endColumns": "58,47,47,47,47,50,47,47,53,66,48,46,49,46,50,88,76,58,53,49,54,76,71,77,77,69,62,51,70,37,157,55,89,41,60,89,46,285,48,333,42,75,44,41,59,45,43,89,58,81,405,400,42,64,62,727,85,81,73,74,208,320,77,52,40,59,69,53,68,68,68,43,79,80,43,81,163,75,104,84,68,92,72,71,54,55,64,53,45,68,99,49,366,54,56,612,609,72,52,145,69,290,52,496,65,139,150,160,66,47,46,74,110,91,49,154,103,55,63,65,312,272,208,158,97,82,66,53,57,38,92,67,56,225,50,80,63,64,143,61,137,218,38,57,36,36,34,34,76,40,52,52,67,72,63,66,51,43,39,73,447,53,74,166,74,74,35,33,39,41,55,61,67,57,77,150,34,80,59,52,92,69,73,104,117,77,43,79,294,113,181,89,99,42,38,53,49,67,80,47,65,87,48,42,43,118,71,96,98,49,68,207,80,58,59,77,53,60,196,83,98,82,50,88,66,74,41,56,45,115,358,80,45,64,79,54,51,38,57,70,215,90,61,68,51,69,66,37,59,51,61,44,40,54,55,73,48,35,34,43", "endOffsets": "754,802,850,898,946,997,1045,1093,1147,1214,1263,1310,1360,1407,1458,1547,1850,1909,2183,2233,3226,3303,3486,3564,3642,3984,4047,4099,4170,4208,4366,4422,4512,4554,4615,4705,4752,5038,5087,5421,5464,5540,5585,5627,5687,5733,5777,5867,5926,6008,6414,6815,6858,6923,6986,7714,7800,7882,7956,8031,8240,8561,8639,8692,8733,8793,8863,8917,8986,9055,9124,9906,9986,10067,10111,10193,10357,10433,10538,10623,10692,10785,10858,10930,10985,11041,11106,11341,11387,11456,11556,11721,12088,12143,12200,12813,13423,13496,13549,13695,13765,14056,14109,14802,14868,15008,15159,15320,15387,15435,15482,15557,15668,15760,15810,15965,16069,16125,16189,16255,16568,16841,17050,17209,17307,17476,17543,17597,17655,17694,17787,21639,22013,22239,22290,22371,22435,22500,22644,22706,22844,23063,23102,23160,23197,23234,23269,23304,23381,23422,23475,23528,23596,23669,23816,23980,24032,24076,24116,24190,24638,24692,24767,24934,25009,25084,25120,31410,31450,31492,31548,31610,31678,31736,32137,32288,32323,32404,32464,32604,32697,32767,32841,32946,33064,33142,33186,33266,33561,33675,33857,33947,34047,34090,34129,34183,34233,34301,34382,34430,34496,34584,34633,34676,34720,34839,34911,35008,35107,35332,35401,35609,35690,35749,35809,35887,35941,36088,36361,36445,36544,36627,36678,36767,36834,36909,36951,37008,37054,37170,37529,37610,37656,37976,38056,38111,38306,38458,38516,38587,38803,38894,39124,39193,39245,39315,39382,39420,39480,39532,39594,39639,39680,39735,39791,39865,39914,39950,39985,40029"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8bd7feaae90e869538df51f29dd16595\\transformed\\jetified-media3-ui-1.6.1\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,471,650,731,814,887,986,1082,1156,1222,1318,1413,1479,1548,1615,1686,1804,1921,2042,2109,2195,2271,2345,2443,2543,2607,2671,2724,2782,2830,2891,2956,3018,3084,3154,3218,3279,3345,3398,3462,3540,3618,3677", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,80,82,72,98,95,73,65,95,94,65,68,66,70,117,116,120,66,85,75,73,97,99,63,63,52,57,47,60,64,61,65,69,63,60,65,52,63,77,77,58,70", "endOffsets": "280,466,645,726,809,882,981,1077,1151,1217,1313,1408,1474,1543,1610,1681,1799,1916,2037,2104,2190,2266,2340,2438,2538,2602,2666,2719,2777,2825,2886,2951,3013,3079,3149,3213,3274,3340,3393,3457,3535,3613,3672,3743"}, "to": {"startLines": "2,11,15,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,521,17792,17873,17956,18029,18128,18224,18298,18364,18460,18555,18621,18690,18757,18828,18946,19063,19184,19251,19337,19413,19487,19585,19685,19749,20499,20552,20610,20658,20719,20784,20846,20912,20982,21046,21107,21173,21226,21290,21368,21446,21505", "endLines": "10,14,18,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229", "endColumns": "17,12,12,80,82,72,98,95,73,65,95,94,65,68,66,70,117,116,120,66,85,75,73,97,99,63,63,52,57,47,60,64,61,65,69,63,60,65,52,63,77,77,58,70", "endOffsets": "330,516,695,17868,17951,18024,18123,18219,18293,18359,18455,18550,18616,18685,18752,18823,18941,19058,19179,19246,19332,19408,19482,19580,19680,19744,19808,20547,20605,20653,20714,20779,20841,20907,20977,21041,21102,21168,21221,21285,21363,21441,21500,21571"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\93d3043f0a8b9466a00a736e170a6ddc\\transformed\\appcompat-1.7.0\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,6,7,10,11,12,13,14,15,16,17,18,19,22,26,27,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,527,628,912,1003,1096,1191,1285,1385,1478,1573,1668,1759,2042,2471,2581,2843", "endColumns": "119,105,100,118,90,92,94,93,99,92,94,94,90,90,110,109,161,85", "endOffsets": "220,326,623,742,998,1091,1186,1280,1380,1473,1568,1663,1754,1845,2148,2576,2738,2924"}, "to": {"startLines": "35,36,39,40,43,44,45,46,47,48,49,50,51,52,55,59,60,384", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1552,1672,1914,2015,2238,2329,2422,2517,2611,2711,2804,2899,2994,3085,3308,3647,3757,35946", "endColumns": "119,105,100,118,90,92,94,93,99,92,94,94,90,90,110,109,161,85", "endOffsets": "1667,1773,2010,2129,2324,2417,2512,2606,2706,2799,2894,2989,3080,3171,3414,3752,3914,36027"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9e0a763189144907fb0197c2b097244b\\transformed\\jetified-ui-release\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,286,383,482,568,651,748,839,926,1011,1101,1253,1332,1407,1483,1550", "endColumns": "94,85,96,98,85,82,96,90,86,84,89,75,78,74,75,66,112", "endOffsets": "195,281,378,477,563,646,743,834,921,1006,1096,1172,1327,1402,1478,1545,1658"}, "to": {"startLines": "130,131,149,150,173,256,258,337,343,374,375,386,402,403,408,409,411", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "11111,11206,14114,14211,17312,23674,23821,31973,32469,35112,35197,36093,37661,37740,38116,38192,38311", "endColumns": "94,85,96,98,85,82,96,90,86,84,89,75,78,74,75,66,112", "endOffsets": "11201,11287,14206,14305,17393,23752,23913,32059,32551,35192,35282,36164,37735,37810,38187,38254,38419"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b572512e02266e069f95737c22215ab9\\transformed\\jetified-media3-exoplayer-1.6.1\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,195,267,333,410,477,578,671", "endColumns": "69,69,71,65,76,66,100,92,69", "endOffsets": "120,190,262,328,405,472,573,666,736"}, "to": {"startLines": "204,205,206,207,208,209,210,211,212", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "19813,19883,19953,20025,20091,20168,20235,20336,20429", "endColumns": "69,69,71,65,76,66,100,92,69", "endOffsets": "19878,19948,20020,20086,20163,20230,20331,20424,20494"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5c5c72c6ff4a7863322da50648a25e99\\transformed\\browser-1.8.0\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,170,269,381", "endColumns": "114,98,111,105", "endOffsets": "165,264,376,482"}, "to": {"startLines": "136,231,232,233", "startColumns": "4,4,4,4", "startOffsets": "11561,21644,21743,21855", "endColumns": "114,98,111,105", "endOffsets": "11671,21738,21850,21956"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f908cdc45776521b403beeef1508641c\\transformed\\core-1.16.0\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,563,673,793", "endColumns": "96,101,98,99,109,109,119,100", "endOffsets": "147,249,348,448,558,668,788,889"}, "to": {"startLines": "107,108,109,110,111,112,113,404", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "9129,9226,9328,9427,9527,9637,9747,37815", "endColumns": "96,101,98,99,109,109,119,100", "endOffsets": "9221,9323,9422,9522,9632,9742,9862,37911"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\237635df39b25799c092d66a208ce67d\\transformed\\jetified-foundation-release\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,138", "endColumns": "82,84", "endOffsets": "133,218"}, "to": {"startLines": "417,418", "startColumns": "4,4", "startOffsets": "38899,38982", "endColumns": "82,84", "endOffsets": "38977,39062"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\179e6486bd57a16ea175623aa423e7ed\\transformed\\jetified-material3-release\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,295,411,527,629,726,840,974,1092,1244,1328,1429,1524,1624,1739,1869,1975,2114,2250,2381,2547,2674,2794,2918,3038,3134,3231,3351,3467,3567,3678,3787,3927,4072,4182,4285,4371,4465,4557,4673,4763,4852,4953,5033,5117,5218,5324,5416,5515,5603,5715,5816,5920,6039,6119,6219", "endColumns": "118,120,115,115,101,96,113,133,117,151,83,100,94,99,114,129,105,138,135,130,165,126,119,123,119,95,96,119,115,99,110,108,139,144,109,102,85,93,91,115,89,88,100,79,83,100,105,91,98,87,111,100,103,118,79,99,91", "endOffsets": "169,290,406,522,624,721,835,969,1087,1239,1323,1424,1519,1619,1734,1864,1970,2109,2245,2376,2542,2669,2789,2913,3033,3129,3226,3346,3462,3562,3673,3782,3922,4067,4177,4280,4366,4460,4552,4668,4758,4847,4948,5028,5112,5213,5319,5411,5510,5598,5710,5811,5915,6034,6114,6214,6306"}, "to": {"startLines": "271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "25125,25244,25365,25481,25597,25699,25796,25910,26044,26162,26314,26398,26499,26594,26694,26809,26939,27045,27184,27320,27451,27617,27744,27864,27988,28108,28204,28301,28421,28537,28637,28748,28857,28997,29142,29252,29355,29441,29535,29627,29743,29833,29922,30023,30103,30187,30288,30394,30486,30585,30673,30785,30886,30990,31109,31189,31289", "endColumns": "118,120,115,115,101,96,113,133,117,151,83,100,94,99,114,129,105,138,135,130,165,126,119,123,119,95,96,119,115,99,110,108,139,144,109,102,85,93,91,115,89,88,100,79,83,100,105,91,98,87,111,100,103,118,79,99,91", "endOffsets": "25239,25360,25476,25592,25694,25791,25905,26039,26157,26309,26393,26494,26589,26689,26804,26934,27040,27179,27315,27446,27612,27739,27859,27983,28103,28199,28296,28416,28532,28632,28743,28852,28992,29137,29247,29350,29436,29530,29622,29738,29828,29917,30018,30098,30182,30283,30389,30481,30580,30668,30780,30881,30985,31104,31184,31284,31376"}}]}]}