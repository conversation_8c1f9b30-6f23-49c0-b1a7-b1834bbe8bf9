-- Merging decision tree log ---
manifest
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:2:1-129:12
MERGED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:2:1-129:12
INJECTED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\debug\AndroidManifest.xml:2:1-12:12
INJECTED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\debug\AndroidManifest.xml:2:1-12:12
INJECTED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\debug\AndroidManifest.xml:2:1-12:12
MERGED from [com.github.tqhit:AdLib:1.2.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a649bc6d741f1561035089684f0b179\transformed\jetified-AdLib-1.2.7\AndroidManifest.xml:2:1-35:12
MERGED from [androidx.databinding:databinding-adapters:8.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\347d669cbb58eeb81531bf30e2f5697f\transformed\databinding-adapters-8.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:databinding-ktx:8.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0e72972b73ca7fdb0f985eb53e342193\transformed\jetified-databinding-ktx-8.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:databinding-runtime:8.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6ef5a5d4dd255734d1000e88cf2a8846\transformed\databinding-runtime-8.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:viewbinding:8.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aebc70e832ba5c630ed1fbf4e8f04c28\transformed\jetified-viewbinding-8.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-common:2.8.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\34a6842c1b80269d992caf2930f259ba\transformed\navigation-common-2.8.9\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.8.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3bc518547c6882b7477e5ff3d29ba9a5\transformed\navigation-common-ktx-2.8.9\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime:2.8.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd1152147d00b2d5b451be6d0860a83e\transformed\navigation-runtime-2.8.9\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.8.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\095b6daf6420d67df184b7df8284dd89\transformed\navigation-runtime-ktx-2.8.9\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-fragment:2.8.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f1d8c3766b40fb978be1af5d4cc6d27e\transformed\navigation-fragment-2.8.9\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-fragment-ktx:2.8.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\936a9a9c6dfba698d349b963a4c50ed3\transformed\navigation-fragment-ktx-2.8.9\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui-ktx:2.8.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\565e79c53e4c0da6e1a45023e199972e\transformed\navigation-ui-ktx-2.8.9\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui:2.8.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5444be4bc77930bd89cfbb9f2224d8e4\transformed\navigation-ui-2.8.9\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b80dcbf636dc26335bd1b8e4f16f918\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8007b3eee33a0787235755b3a0af0b83\transformed\constraintlayout-2.2.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.airbnb.android:lottie:6.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6718808f332794c6efacee4a0d5b3f88\transformed\jetified-lottie-6.6.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.tbuonomo:dotsindicator:5.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\470cb6f36c59533cd863c8252f38a327\transformed\jetified-dotsindicator-5.1.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd63341832ff3d1f0c3fa653850d92ee\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [cat.ereza:customactivityoncrash:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2821057fb80181e87f67e0a41ee8161d\transformed\jetified-customactivityoncrash-2.4.0\AndroidManifest.xml:2:1-21:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\93d3043f0a8b9466a00a736e170a6ddc\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.dagger:hilt-android:2.56.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\256cc587ecfd6b868fb806253ccf395f\transformed\jetified-hilt-android-2.56.1\AndroidManifest.xml:16:1-19:12
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66d600e9b1169a1e725381a5921a9d13\transformed\jetified-glide-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.media3:media3-exoplayer:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b572512e02266e069f95737c22215ab9\transformed\jetified-media3-exoplayer-1.6.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-extractor:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6764720ad3b6d5917bb536f6780d684\transformed\jetified-media3-extractor-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-container:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62b13c141d1c4550e2db4310f8c3b278\transformed\jetified-media3-container-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-datasource:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\248d533135995d3d18235d674c56134a\transformed\jetified-media3-datasource-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-decoder:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f09d7a6e8098c8249bd4a767f03ac7a\transformed\jetified-media3-decoder-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-database:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25371daf998da8915f3701ff0d432c19\transformed\jetified-media3-database-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-common:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64b6bd64f7e89e8f48e748d8da411f43\transformed\jetified-media3-common-1.6.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-ui:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8bd7feaae90e869538df51f29dd16595\transformed\jetified-media3-ui-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ae9e6fb794aab6f23f82d9d2a805731\transformed\recyclerview-1.3.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\01e2300b6b710552831be921ec408c74\transformed\jetified-viewpager2-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-ads:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\237e0b5db534c615c4317f1b214e3e7f\transformed\jetified-play-services-ads-24.2.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.firebase:firebase-analytics:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\724e7f8814b0a904b84a7da61a2fff9f\transformed\jetified-firebase-analytics-22.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\430c56e4d5b03e6b74c8f45faf04d96d\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:17:1-46:12
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\382e0d6a1af511ca59a11019348ab80b\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:17:1-39:12
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b034c97d14d08666bf0faf4a610bc04f\transformed\jetified-play-services-measurement-sdk-22.4.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0bd9b80e9a416301d2239cd96524ca3a\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:17:1-32:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85e07b7dad8a0af8147f5311c4247a4d\transformed\jetified-play-services-ads-identifier-18.2.0\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:17:1-115:12
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d861824a24f836c544f1ecb2711be738\transformed\jetified-play-services-appset-16.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-crashlytics:19.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7827a62f7a18bcb72aaa3ce93f2a42ed\transformed\jetified-firebase-crashlytics-19.4.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\647741b0d36db01281989d0b55b41c1e\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:17:1-38:12
MERGED from [com.google.firebase:firebase-sessions:2.0.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d72e4ec5a5740afe66ba66375b2f440d\transformed\jetified-firebase-sessions-2.0.8\AndroidManifest.xml:15:1-35:12
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d824c942e0670731cb8b0e5390339c22\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf6d9f69f61c325a990b48b77dab7869\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\42c987edc6bac2d568a7305ae38ce788\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d56ddc8f70c1b6c4f2dfff25a6818549\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-installations-interop:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bbb40e47a1eb48e983fe6e7d96d2ed8\transformed\jetified-firebase-installations-interop-17.2.0\AndroidManifest.xml:15:1-19:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8634365bf1152e1cd9a053656cde2744\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\179e6486bd57a16ea175623aa423e7ed\transformed\jetified-material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.animation:animation-core-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\685979bdf4e338bede8f597be25f6f85\transformed\jetified-animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c88f426dac06daea6f403b33dc30a779\transformed\jetified-material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b251c0e3af947a65d7a94a909760549\transformed\jetified-animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1a21fbc400d5caf540ad887213f399b9\transformed\jetified-foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\237635df39b25799c092d66a208ce67d\transformed\jetified-foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4739a37df38d6d3167e025d3e02f2d4f\transformed\jetified-ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf3f14f61f654ca87e875fb842d85041\transformed\jetified-ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4168971c306c258c6df514e5fb75e24\transformed\jetified-ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fcad03f6139abb74df47ac2c38f7150d\transformed\jetified-ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80b9575061b7113086c965852a660d37\transformed\jetified-ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\705b667089ad425e9a6a8d447e2e4b19\transformed\jetified-ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2957fefc5cbf3ca73909d951bd6622e2\transformed\jetified-fragment-ktx-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\755cc2112db16a4945b8616bf2764b3c\transformed\jetified-activity-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\202151605bf9046134e71d49e3bcfab2\transformed\jetified-activity-compose-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2735aacb78950a7aea7cb796e4e87e7\transformed\jetified-material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e0a763189144907fb0197c2b097244b\transformed\jetified-ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\04e6ea113b6b9423e1db617895bd977a\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\683a094184685dee7824268984a48693\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60a813200b74c1dc84d254b8806ebd17\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30188ea85dd819e5cb58ff0ed544e055\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80b277d63f31e9b131f3b2a80303bb42\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\adf431448d9952a7bfc1730a2449c941\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a2bba29280d1d9d4ef13247e6c9abae\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f691554f471de61b2961ded671aeec4e\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9bed8d4710c38689abca92b67b225ab3\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b565ac0ad8434f83b6988fa74d8800c\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91dab5885f5560bd65be1e52d9b695ca\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5c5c72c6ff4a7863322da50648a25e99\transformed\browser-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.webkit:webkit:1.11.0-alpha02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\439a2d203198d843a8d888fae84bd2a9\transformed\webkit-1.11.0-alpha02\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a3aa6ec5b50d44e4805132f5d9e3f646\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d4b898ed3dd7fc494935f4d2e801126\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69fa008bb70ecc0d8e73621c41f47f2d\transformed\jetified-window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac42dc8b85a532399756bb632f272af9\transformed\jetified-autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2189c570813bc0f2cb30b4758a1420b2\transformed\jetified-graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f908cdc45776521b403beeef1508641c\transformed\core-1.16.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2a3f12bf6b40e2cfca4058cca8bbcc6\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4c7b48e4d1f473a8b802d70e4aff0147\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6f685b0d0b4ae316a91fcc6f10da548\transformed\jetified-lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\14ecb582312534e9963d84c222812c5b\transformed\jetified-lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\880ebab98f3493eda4af72fec141de1e\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17ade856dd8011987dee0a638e332f55\transformed\jetified-lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\34ff98ecf4c4bcf721c91b81eabab462\transformed\jetified-lifecycle-runtime-ktx-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3d8e112f24c7225462503b588bb0055c\transformed\jetified-lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1eebb1a882efcc15d28c2876ceb6fba2\transformed\jetified-lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-service:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ac2ac01d0e2d9d1be275a5f8b039215\transformed\jetified-lifecycle-service-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\204396a0a01b39d67dab179cc3d5cc84\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54c5ab35e7f232bd0706882e7fefe677\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25d2fd9aae62b446b90a4fd8f9d80408\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e86056dcfc48e830b6da0ede09de9e34\transformed\jetified-lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d380ebedd25fb23edf1949b73a1e0c6\transformed\jetified-runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e745399e7d774c8df31e1e97961041eb\transformed\jetified-runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-core-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f4a03e79b2114de7f0a0d332731f16a\transformed\jetified-datastore-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee5719f91e687c59ebc143c8aca22017\transformed\jetified-datastore-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-preferences:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\849e152feb07c9011174f589048f9022\transformed\jetified-datastore-preferences-1.1.1\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9db713bd46ed01b74ada965cab7a6b31\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb561ccecad2e9fd2bf98a76522c9f4d\transformed\jetified-ads-adservices-java-1.1.0-beta11\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe8b59d760258b64cc2e08caaf757d3\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:17:1-28:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a0502e998f317dfcb658a352de2cd3\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:2:1-5:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98e15cc48088d4cf51d7c42a5f31d80a\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:17:1-30:12
MERGED from [com.google.android.gms:play-services-measurement-base:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39008844c0169f05f0b3eade5d4064db\transformed\jetified-play-services-measurement-base-22.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d23f72c165873084e0061094322da6f\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ee7eee8c149506e91953117e879e05\transformed\jetified-firebase-measurement-connector-20.0.1\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.ump:user-messaging-platform:3.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a71a40e4160df8002d49c156025423f\transformed\jetified-user-messaging-platform-3.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\42f95d9fa807b14415e836fc15872a54\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4961d4f566368a726dd9f06399253e9d\transformed\fragment-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f5d2fdc4a4434be9fcd65932e42964a\transformed\jetified-activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b518e79f85ce070d76134f5e2085a96\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c691a52d16d17081fdd44e90e0363686\transformed\jetified-core-ktx-1.16.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-splashscreen:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\141e0148a23d0681a92a44c218f71e86\transformed\jetified-core-splashscreen-1.0.1\AndroidManifest.xml:17:1-24:12
MERGED from [me.tankery.lib:circularSeekBar:1.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19df98120f1e878bbd36ae400e97f28b\transformed\jetified-circularSeekBar-1.4.2\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.firebase:firebase-config-interop:16.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0310dfde44a4b562a000032c1077936f\transformed\jetified-firebase-config-interop-16.0.1\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\167f60ddd189c968da5456a2b3b7711e\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:15:1-31:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29198f9de8a02a6dbf7704c477a13803\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:15:1-35:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\465a46469bddbafb86fb7613265bae0a\transformed\jetified-firebase-encoders-json-18.0.1\AndroidManifest.xml:15:1-22:12
MERGED from [com.squareup.picasso:picasso:2.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3db596f3ce001dc72c702061cbd417cf\transformed\jetified-picasso-2.8\AndroidManifest.xml:2:1-14:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a57f610ef3585645bfe3fcbe98d165f8\transformed\jetified-core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.shimmer:shimmer:0.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24c3083ed1841653be2c5c5238c002f8\transformed\jetified-shimmer-0.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.PhilJay:MPAndroidChart:v3.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8705dbe80330cf51059e4cf6958823c4\transformed\jetified-MPAndroidChart-v3.1.0\AndroidManifest.xml:2:1-20:12
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\71af04e3c58b6554ca900891431220d8\transformed\jetified-BlurView-version-2.0.6\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8eab8a838f0f81f776d8ffbd732492d7\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2689e40363c086e26e0a6a841af9a39c\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea1b0c1bbaf4c424ba812ea50539ea72\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\045adccf1e2e01c8fc427fe22c44c3d2\transformed\jetified-gifdecoder-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb1c28a2ff7b145ef909defefb7fc232\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6df7bf1ddef91663e5063bb6ebc3431d\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\599087b95bbc93489c0632601e242d3f\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a46481580e219c1c955066d96ba2fcb\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e74877bcb1d449cece0d3989e1586b35\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ed936dc0283901104a2185c7c048406\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fa1b4c3d3ee8d16b849865caadfb65e\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:15:1-39:12
MERGED from [com.google.android.datatransport:transport-api:3.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b81264e61541a2f55d07e34832d3dbb\transformed\jetified-transport-api-3.2.0\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\74a0504aa17c55099e2abc4daad546c1\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a0fc76e31ea58355bd9f993ec0ff2e7\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3de44db5490c663c2bdc1080a507338c\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4344bc802b6747759d9e037220bf761a\transformed\room-runtime-2.2.5\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4c32fa8fbc4a31b3c30757b8a268c5c\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4b15df558aa555d447bb1d5194849b28\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20312724fa5f8c3feaaaa8068655b512\transformed\sqlite-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc692a120143169d749d7ac0b630b233\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.intuit.sdp:sdp-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc35ffdefd2abfa5aac2f445dfc58ee4\transformed\jetified-sdp-android-1.1.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.intuit.ssp:ssp-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5658b4b85c4a4e32f0ba738fc8faf8b\transformed\jetified-ssp-android-1.1.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.adjust.sdk:adjust-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\efac1c2d606ea39a514ba9d42b351722\transformed\jetified-adjust-android-5.1.0\AndroidManifest.xml:2:1-17:12
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59a9191e92c16f81b31d9fba59b2bce2\transformed\jetified-installreferrer-2.2\AndroidManifest.xml:2:1-13:12
MERGED from [com.google.dagger:dagger-lint-aar:2.56.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3ac84cee72251c573666a65182890d95\transformed\jetified-dagger-lint-aar-2.56.1\AndroidManifest.xml:16:1-19:12
MERGED from [com.adjust.signature:adjust-android-signature:3.35.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39c1655bdeadb572f2d5f5e9acd549ae\transformed\jetified-adjust-android-signature-3.35.2\AndroidManifest.xml:2:1-7:12
	package
		INJECTED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\debug\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:5:5-67
MERGED from [com.github.tqhit:AdLib:1.2.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a649bc6d741f1561035089684f0b179\transformed\jetified-AdLib-1.2.7\AndroidManifest.xml:30:5-67
MERGED from [com.github.tqhit:AdLib:1.2.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a649bc6d741f1561035089684f0b179\transformed\jetified-AdLib-1.2.7\AndroidManifest.xml:30:5-67
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\430c56e4d5b03e6b74c8f45faf04d96d\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\430c56e4d5b03e6b74c8f45faf04d96d\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\382e0d6a1af511ca59a11019348ab80b\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\382e0d6a1af511ca59a11019348ab80b\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0bd9b80e9a416301d2239cd96524ca3a\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0bd9b80e9a416301d2239cd96524ca3a\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.firebase:firebase-crashlytics:19.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7827a62f7a18bcb72aaa3ce93f2a42ed\transformed\jetified-firebase-crashlytics-19.4.0\AndroidManifest.xml:7:5-67
MERGED from [com.google.firebase:firebase-crashlytics:19.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7827a62f7a18bcb72aaa3ce93f2a42ed\transformed\jetified-firebase-crashlytics-19.4.0\AndroidManifest.xml:7:5-67
MERGED from [com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\647741b0d36db01281989d0b55b41c1e\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\647741b0d36db01281989d0b55b41c1e\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d824c942e0670731cb8b0e5390339c22\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d824c942e0670731cb8b0e5390339c22\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98e15cc48088d4cf51d7c42a5f31d80a\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98e15cc48088d4cf51d7c42a5f31d80a\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29198f9de8a02a6dbf7704c477a13803\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29198f9de8a02a6dbf7704c477a13803\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:23:5-67
MERGED from [com.adjust.sdk:adjust-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\efac1c2d606ea39a514ba9d42b351722\transformed\jetified-adjust-android-5.1.0\AndroidManifest.xml:7:5-67
MERGED from [com.adjust.sdk:adjust-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\efac1c2d606ea39a514ba9d42b351722\transformed\jetified-adjust-android-5.1.0\AndroidManifest.xml:7:5-67
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:5:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:6:5-79
MERGED from [com.github.tqhit:AdLib:1.2.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a649bc6d741f1561035089684f0b179\transformed\jetified-AdLib-1.2.7\AndroidManifest.xml:31:5-79
MERGED from [com.github.tqhit:AdLib:1.2.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a649bc6d741f1561035089684f0b179\transformed\jetified-AdLib-1.2.7\AndroidManifest.xml:31:5-79
MERGED from [androidx.media3:media3-exoplayer:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b572512e02266e069f95737c22215ab9\transformed\jetified-media3-exoplayer-1.6.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b572512e02266e069f95737c22215ab9\transformed\jetified-media3-exoplayer-1.6.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64b6bd64f7e89e8f48e748d8da411f43\transformed\jetified-media3-common-1.6.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64b6bd64f7e89e8f48e748d8da411f43\transformed\jetified-media3-common-1.6.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\430c56e4d5b03e6b74c8f45faf04d96d\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\430c56e4d5b03e6b74c8f45faf04d96d\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\382e0d6a1af511ca59a11019348ab80b\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\382e0d6a1af511ca59a11019348ab80b\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0bd9b80e9a416301d2239cd96524ca3a\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0bd9b80e9a416301d2239cd96524ca3a\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\647741b0d36db01281989d0b55b41c1e\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\647741b0d36db01281989d0b55b41c1e\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d824c942e0670731cb8b0e5390339c22\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d824c942e0670731cb8b0e5390339c22\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:26:5-79
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98e15cc48088d4cf51d7c42a5f31d80a\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98e15cc48088d4cf51d7c42a5f31d80a\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29198f9de8a02a6dbf7704c477a13803\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29198f9de8a02a6dbf7704c477a13803\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fa1b4c3d3ee8d16b849865caadfb65e\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:20:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fa1b4c3d3ee8d16b849865caadfb65e\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:20:5-79
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:6:22-76
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:7:5-77
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:7:22-74
uses-permission#android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:8:5-95
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:8:22-92
uses-permission#android.permission.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:9:5-101
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:9:22-99
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:10:5-76
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:5-77
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:5-77
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:10:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_SPECIAL_USE
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:11:5-88
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:11:22-86
uses-permission#android.permission.VIBRATE
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:12:5-65
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:12:22-63
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:13:5-78
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:13:22-75
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:14:5-81
REJECTED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:30:5-32:31
REJECTED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:27:5-81
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:14:22-78
uses-permission#android.permission.PACKAGE_USAGE_STATS
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:15:5-16:47
	tools:ignore
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:16:9-44
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:15:22-75
permission#com.tqhit.battery.one.permission.FINISH_OVERLAY
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:18:5-20:47
	android:protectionLevel
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:20:9-44
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:19:9-71
application
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:22:5-127:19
MERGED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:22:5-127:19
MERGED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:22:5-127:19
INJECTED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\debug\AndroidManifest.xml:5:5-10:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b80dcbf636dc26335bd1b8e4f16f918\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b80dcbf636dc26335bd1b8e4f16f918\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8007b3eee33a0787235755b3a0af0b83\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8007b3eee33a0787235755b3a0af0b83\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [com.airbnb.android:lottie:6.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6718808f332794c6efacee4a0d5b3f88\transformed\jetified-lottie-6.6.2\AndroidManifest.xml:7:5-20
MERGED from [com.airbnb.android:lottie:6.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6718808f332794c6efacee4a0d5b3f88\transformed\jetified-lottie-6.6.2\AndroidManifest.xml:7:5-20
MERGED from [com.tbuonomo:dotsindicator:5.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\470cb6f36c59533cd863c8252f38a327\transformed\jetified-dotsindicator-5.1.0\AndroidManifest.xml:7:5-47
MERGED from [com.tbuonomo:dotsindicator:5.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\470cb6f36c59533cd863c8252f38a327\transformed\jetified-dotsindicator-5.1.0\AndroidManifest.xml:7:5-47
MERGED from [cat.ereza:customactivityoncrash:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2821057fb80181e87f67e0a41ee8161d\transformed\jetified-customactivityoncrash-2.4.0\AndroidManifest.xml:9:5-19:19
MERGED from [cat.ereza:customactivityoncrash:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2821057fb80181e87f67e0a41ee8161d\transformed\jetified-customactivityoncrash-2.4.0\AndroidManifest.xml:9:5-19:19
MERGED from [com.google.firebase:firebase-analytics:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\724e7f8814b0a904b84a7da61a2fff9f\transformed\jetified-firebase-analytics-22.4.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-analytics:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\724e7f8814b0a904b84a7da61a2fff9f\transformed\jetified-firebase-analytics-22.4.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\430c56e4d5b03e6b74c8f45faf04d96d\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\430c56e4d5b03e6b74c8f45faf04d96d\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\382e0d6a1af511ca59a11019348ab80b\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:29:5-37:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\382e0d6a1af511ca59a11019348ab80b\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:29:5-37:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b034c97d14d08666bf0faf4a610bc04f\transformed\jetified-play-services-measurement-sdk-22.4.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b034c97d14d08666bf0faf4a610bc04f\transformed\jetified-play-services-measurement-sdk-22.4.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0bd9b80e9a416301d2239cd96524ca3a\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0bd9b80e9a416301d2239cd96524ca3a\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-ads-identifier:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85e07b7dad8a0af8147f5311c4247a4d\transformed\jetified-play-services-ads-identifier-18.2.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85e07b7dad8a0af8147f5311c4247a4d\transformed\jetified-play-services-ads-identifier-18.2.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:70:5-113:19
MERGED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:70:5-113:19
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d861824a24f836c544f1ecb2711be738\transformed\jetified-play-services-appset-16.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d861824a24f836c544f1ecb2711be738\transformed\jetified-play-services-appset-16.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-crashlytics:19.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7827a62f7a18bcb72aaa3ce93f2a42ed\transformed\jetified-firebase-crashlytics-19.4.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-crashlytics:19.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7827a62f7a18bcb72aaa3ce93f2a42ed\transformed\jetified-firebase-crashlytics-19.4.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\647741b0d36db01281989d0b55b41c1e\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:25:5-36:19
MERGED from [com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\647741b0d36db01281989d0b55b41c1e\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:25:5-36:19
MERGED from [com.google.firebase:firebase-sessions:2.0.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d72e4ec5a5740afe66ba66375b2f440d\transformed\jetified-firebase-sessions-2.0.8\AndroidManifest.xml:21:5-33:19
MERGED from [com.google.firebase:firebase-sessions:2.0.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d72e4ec5a5740afe66ba66375b2f440d\transformed\jetified-firebase-sessions-2.0.8\AndroidManifest.xml:21:5-33:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d824c942e0670731cb8b0e5390339c22\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d824c942e0670731cb8b0e5390339c22\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf6d9f69f61c325a990b48b77dab7869\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf6d9f69f61c325a990b48b77dab7869\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\42c987edc6bac2d568a7305ae38ce788\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\42c987edc6bac2d568a7305ae38ce788\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d56ddc8f70c1b6c4f2dfff25a6818549\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d56ddc8f70c1b6c4f2dfff25a6818549\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\04e6ea113b6b9423e1db617895bd977a\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\04e6ea113b6b9423e1db617895bd977a\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80b277d63f31e9b131f3b2a80303bb42\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80b277d63f31e9b131f3b2a80303bb42\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:30:5-143:19
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:30:5-143:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69fa008bb70ecc0d8e73621c41f47f2d\transformed\jetified-window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69fa008bb70ecc0d8e73621c41f47f2d\transformed\jetified-window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f908cdc45776521b403beeef1508641c\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f908cdc45776521b403beeef1508641c\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25d2fd9aae62b446b90a4fd8f9d80408\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25d2fd9aae62b446b90a4fd8f9d80408\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe8b59d760258b64cc2e08caaf757d3\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe8b59d760258b64cc2e08caaf757d3\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:22:5-26:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a0502e998f317dfcb658a352de2cd3\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a0502e998f317dfcb658a352de2cd3\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39008844c0169f05f0b3eade5d4064db\transformed\jetified-play-services-measurement-base-22.4.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39008844c0169f05f0b3eade5d4064db\transformed\jetified-play-services-measurement-base-22.4.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d23f72c165873084e0061094322da6f\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:10:5-16:19
MERGED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d23f72c165873084e0061094322da6f\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:10:5-16:19
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ee7eee8c149506e91953117e879e05\transformed\jetified-firebase-measurement-connector-20.0.1\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ee7eee8c149506e91953117e879e05\transformed\jetified-firebase-measurement-connector-20.0.1\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\42f95d9fa807b14415e836fc15872a54\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\42f95d9fa807b14415e836fc15872a54\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:20:5-24:19
MERGED from [me.tankery.lib:circularSeekBar:1.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19df98120f1e878bbd36ae400e97f28b\transformed\jetified-circularSeekBar-1.4.2\AndroidManifest.xml:9:5-20
MERGED from [me.tankery.lib:circularSeekBar:1.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19df98120f1e878bbd36ae400e97f28b\transformed\jetified-circularSeekBar-1.4.2\AndroidManifest.xml:9:5-20
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\167f60ddd189c968da5456a2b3b7711e\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\167f60ddd189c968da5456a2b3b7711e\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29198f9de8a02a6dbf7704c477a13803\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:25:5-33:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29198f9de8a02a6dbf7704c477a13803\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:25:5-33:19
MERGED from [com.squareup.picasso:picasso:2.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3db596f3ce001dc72c702061cbd417cf\transformed\jetified-picasso-2.8\AndroidManifest.xml:7:5-12:19
MERGED from [com.squareup.picasso:picasso:2.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3db596f3ce001dc72c702061cbd417cf\transformed\jetified-picasso-2.8\AndroidManifest.xml:7:5-12:19
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\71af04e3c58b6554ca900891431220d8\transformed\jetified-BlurView-version-2.0.6\AndroidManifest.xml:9:5-20
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\71af04e3c58b6554ca900891431220d8\transformed\jetified-BlurView-version-2.0.6\AndroidManifest.xml:9:5-20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea1b0c1bbaf4c424ba812ea50539ea72\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea1b0c1bbaf4c424ba812ea50539ea72\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6df7bf1ddef91663e5063bb6ebc3431d\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6df7bf1ddef91663e5063bb6ebc3431d\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e74877bcb1d449cece0d3989e1586b35\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e74877bcb1d449cece0d3989e1586b35\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fa1b4c3d3ee8d16b849865caadfb65e\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:23:5-37:19
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fa1b4c3d3ee8d16b849865caadfb65e\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:23:5-37:19
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4344bc802b6747759d9e037220bf761a\transformed\room-runtime-2.2.5\AndroidManifest.xml:24:5-29:19
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4344bc802b6747759d9e037220bf761a\transformed\room-runtime-2.2.5\AndroidManifest.xml:24:5-29:19
MERGED from [com.adjust.sdk:adjust-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\efac1c2d606ea39a514ba9d42b351722\transformed\jetified-adjust-android-5.1.0\AndroidManifest.xml:10:5-15:19
MERGED from [com.adjust.sdk:adjust-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\efac1c2d606ea39a514ba9d42b351722\transformed\jetified-adjust-android-5.1.0\AndroidManifest.xml:10:5-15:19
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59a9191e92c16f81b31d9fba59b2bce2\transformed\jetified-installreferrer-2.2\AndroidManifest.xml:11:5-20
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59a9191e92c16f81b31d9fba59b2bce2\transformed\jetified-installreferrer-2.2\AndroidManifest.xml:11:5-20
	android:extractNativeLibs
		INJECTED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\debug\AndroidManifest.xml
	android:screenOrientation
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:30:9-45
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:30:9-45
	android:appComponentFactory
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f908cdc45776521b403beeef1508641c\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:31:9-35
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:31:9-35
	android:label
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:28:9-41
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:28:9-41
	android:fullBackupContent
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:26:9-54
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:26:9-54
	android:roundIcon
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:29:9-54
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:29:9-54
	tools:targetApi
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:33:9-29
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:33:9-29
	android:icon
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:27:9-43
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:27:9-43
	android:allowBackup
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:24:9-35
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:24:9-35
	android:theme
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:32:9-48
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:32:9-48
	android:dataExtractionRules
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:25:9-65
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:25:9-65
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:23:9-64
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:23:9-64
meta-data#com.google.android.gms.ads.APPLICATION_ID
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:35:9-37:70
	android:value
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:37:13-67
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:36:13-69
activity#com.tqhit.battery.one.activity.splash.SplashActivity
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:39:9-47:20
	android:exported
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:41:13-36
	android:theme
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:42:13-59
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:40:13-80
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:43:13-46:29
action#android.intent.action.MAIN
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:44:17-69
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:44:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:45:17-77
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:45:27-74
activity#com.tqhit.battery.one.activity.starting.StartingActivity
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:49:9-53:54
	android:screenOrientation
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:52:13-49
	android:exported
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:51:13-37
	android:theme
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:53:13-52
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:50:13-84
activity#com.tqhit.battery.one.activity.main.MainActivity
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:55:9-59:51
	android:screenOrientation
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:59:13-49
	android:launchMode
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:58:13-44
	android:exported
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:57:13-36
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:56:13-76
activity#com.tqhit.battery.one.activity.animation.AnimationActivity
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:61:9-65:51
	android:screenOrientation
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:65:13-49
	android:launchMode
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:64:13-44
	android:configChanges
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:63:13-59
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:62:13-86
activity#com.tqhit.battery.one.activity.overlay.ChargingOverlayActivity
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:67:9-73:51
	android:screenOrientation
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:73:13-49
	android:launchMode
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:72:13-44
	android:exported
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:69:13-37
	android:configChanges
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:71:13-59
	android:theme
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:70:13-63
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:68:13-90
activity#com.tqhit.battery.one.activity.password.EnterPasswordActivity
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:75:9-81:51
	android:screenOrientation
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:81:13-49
	android:launchMode
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:80:13-44
	android:exported
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:77:13-37
	android:configChanges
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:79:13-59
	android:theme
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:78:13-63
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:76:13-89
activity#com.tqhit.battery.one.features.new_discharge.presentation.TestNewDischargeActivity
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:83:9-94:20
	android:screenOrientation
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:87:13-49
	android:exported
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:85:13-36
	android:theme
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:86:13-51
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:84:13-110
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:test_discharge+data:scheme:battery
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:88:13-93:29
action#android.intent.action.VIEW
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:89:17-69
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:89:25-66
category#android.intent.category.DEFAULT
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:90:17-76
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:90:27-73
category#android.intent.category.BROWSABLE
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:91:17-78
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:91:27-75
data
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:92:17-80
	android:host
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:92:48-77
	android:scheme
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:92:23-47
service#com.tqhit.battery.one.service.BatteryMonitorService
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:100:9-104:59
	android:enabled
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:102:13-35
	android:exported
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:103:13-37
	android:foregroundServiceType
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:104:13-55
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:101:13-79
service#com.tqhit.battery.one.service.ChargingOverlayService
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:105:9-109:59
	android:enabled
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:107:13-35
	android:exported
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:108:13-37
	android:foregroundServiceType
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:109:13-55
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:106:13-80
service#com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerService
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:112:9-116:59
	android:enabled
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:114:13-35
	android:exported
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:115:13-37
	android:foregroundServiceType
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:116:13-55
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:113:13-112
service#com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:117:9-121:59
	android:enabled
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:119:13-35
	android:exported
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:120:13-37
	android:foregroundServiceType
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:121:13-55
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:118:13-108
service#com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationService
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:122:9-126:59
	android:enabled
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:124:13-35
	android:exported
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:125:13-37
	android:foregroundServiceType
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:126:13-55
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\main\AndroidManifest.xml:123:13-112
activity#com.tqhit.battery.one.activity.debug.DebugActivity
ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\debug\AndroidManifest.xml:7:9-9:35
	tools:node
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\debug\AndroidManifest.xml:9:13-32
	android:name
		ADDED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\debug\AndroidManifest.xml:8:13-78
uses-sdk
INJECTED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\debug\AndroidManifest.xml
INJECTED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\debug\AndroidManifest.xml
MERGED from [com.github.tqhit:AdLib:1.2.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a649bc6d741f1561035089684f0b179\transformed\jetified-AdLib-1.2.7\AndroidManifest.xml:28:5-44
MERGED from [com.github.tqhit:AdLib:1.2.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a649bc6d741f1561035089684f0b179\transformed\jetified-AdLib-1.2.7\AndroidManifest.xml:28:5-44
MERGED from [androidx.databinding:databinding-adapters:8.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\347d669cbb58eeb81531bf30e2f5697f\transformed\databinding-adapters-8.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-adapters:8.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\347d669cbb58eeb81531bf30e2f5697f\transformed\databinding-adapters-8.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-ktx:8.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0e72972b73ca7fdb0f985eb53e342193\transformed\jetified-databinding-ktx-8.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-ktx:8.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0e72972b73ca7fdb0f985eb53e342193\transformed\jetified-databinding-ktx-8.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-runtime:8.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6ef5a5d4dd255734d1000e88cf2a8846\transformed\databinding-runtime-8.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-runtime:8.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6ef5a5d4dd255734d1000e88cf2a8846\transformed\databinding-runtime-8.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aebc70e832ba5c630ed1fbf4e8f04c28\transformed\jetified-viewbinding-8.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aebc70e832ba5c630ed1fbf4e8f04c28\transformed\jetified-viewbinding-8.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common:2.8.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\34a6842c1b80269d992caf2930f259ba\transformed\navigation-common-2.8.9\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.8.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\34a6842c1b80269d992caf2930f259ba\transformed\navigation-common-2.8.9\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.8.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3bc518547c6882b7477e5ff3d29ba9a5\transformed\navigation-common-ktx-2.8.9\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.8.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3bc518547c6882b7477e5ff3d29ba9a5\transformed\navigation-common-ktx-2.8.9\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime:2.8.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd1152147d00b2d5b451be6d0860a83e\transformed\navigation-runtime-2.8.9\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.8.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd1152147d00b2d5b451be6d0860a83e\transformed\navigation-runtime-2.8.9\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.8.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\095b6daf6420d67df184b7df8284dd89\transformed\navigation-runtime-ktx-2.8.9\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.8.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\095b6daf6420d67df184b7df8284dd89\transformed\navigation-runtime-ktx-2.8.9\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment:2.8.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f1d8c3766b40fb978be1af5d4cc6d27e\transformed\navigation-fragment-2.8.9\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.8.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f1d8c3766b40fb978be1af5d4cc6d27e\transformed\navigation-fragment-2.8.9\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.8.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\936a9a9c6dfba698d349b963a4c50ed3\transformed\navigation-fragment-ktx-2.8.9\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.8.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\936a9a9c6dfba698d349b963a4c50ed3\transformed\navigation-fragment-ktx-2.8.9\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.8.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\565e79c53e4c0da6e1a45023e199972e\transformed\navigation-ui-ktx-2.8.9\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.8.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\565e79c53e4c0da6e1a45023e199972e\transformed\navigation-ui-ktx-2.8.9\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui:2.8.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5444be4bc77930bd89cfbb9f2224d8e4\transformed\navigation-ui-2.8.9\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.8.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5444be4bc77930bd89cfbb9f2224d8e4\transformed\navigation-ui-2.8.9\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b80dcbf636dc26335bd1b8e4f16f918\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b80dcbf636dc26335bd1b8e4f16f918\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8007b3eee33a0787235755b3a0af0b83\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8007b3eee33a0787235755b3a0af0b83\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [com.airbnb.android:lottie:6.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6718808f332794c6efacee4a0d5b3f88\transformed\jetified-lottie-6.6.2\AndroidManifest.xml:5:5-44
MERGED from [com.airbnb.android:lottie:6.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6718808f332794c6efacee4a0d5b3f88\transformed\jetified-lottie-6.6.2\AndroidManifest.xml:5:5-44
MERGED from [com.tbuonomo:dotsindicator:5.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\470cb6f36c59533cd863c8252f38a327\transformed\jetified-dotsindicator-5.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.tbuonomo:dotsindicator:5.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\470cb6f36c59533cd863c8252f38a327\transformed\jetified-dotsindicator-5.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd63341832ff3d1f0c3fa653850d92ee\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd63341832ff3d1f0c3fa653850d92ee\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [cat.ereza:customactivityoncrash:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2821057fb80181e87f67e0a41ee8161d\transformed\jetified-customactivityoncrash-2.4.0\AndroidManifest.xml:5:5-7:41
MERGED from [cat.ereza:customactivityoncrash:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2821057fb80181e87f67e0a41ee8161d\transformed\jetified-customactivityoncrash-2.4.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\93d3043f0a8b9466a00a736e170a6ddc\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\93d3043f0a8b9466a00a736e170a6ddc\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.dagger:hilt-android:2.56.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\256cc587ecfd6b868fb806253ccf395f\transformed\jetified-hilt-android-2.56.1\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:hilt-android:2.56.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\256cc587ecfd6b868fb806253ccf395f\transformed\jetified-hilt-android-2.56.1\AndroidManifest.xml:18:3-42
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66d600e9b1169a1e725381a5921a9d13\transformed\jetified-glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66d600e9b1169a1e725381a5921a9d13\transformed\jetified-glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.media3:media3-exoplayer:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b572512e02266e069f95737c22215ab9\transformed\jetified-media3-exoplayer-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b572512e02266e069f95737c22215ab9\transformed\jetified-media3-exoplayer-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6764720ad3b6d5917bb536f6780d684\transformed\jetified-media3-extractor-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6764720ad3b6d5917bb536f6780d684\transformed\jetified-media3-extractor-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62b13c141d1c4550e2db4310f8c3b278\transformed\jetified-media3-container-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62b13c141d1c4550e2db4310f8c3b278\transformed\jetified-media3-container-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\248d533135995d3d18235d674c56134a\transformed\jetified-media3-datasource-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\248d533135995d3d18235d674c56134a\transformed\jetified-media3-datasource-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f09d7a6e8098c8249bd4a767f03ac7a\transformed\jetified-media3-decoder-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f09d7a6e8098c8249bd4a767f03ac7a\transformed\jetified-media3-decoder-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25371daf998da8915f3701ff0d432c19\transformed\jetified-media3-database-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25371daf998da8915f3701ff0d432c19\transformed\jetified-media3-database-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64b6bd64f7e89e8f48e748d8da411f43\transformed\jetified-media3-common-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64b6bd64f7e89e8f48e748d8da411f43\transformed\jetified-media3-common-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8bd7feaae90e869538df51f29dd16595\transformed\jetified-media3-ui-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8bd7feaae90e869538df51f29dd16595\transformed\jetified-media3-ui-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ae9e6fb794aab6f23f82d9d2a805731\transformed\recyclerview-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ae9e6fb794aab6f23f82d9d2a805731\transformed\recyclerview-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\01e2300b6b710552831be921ec408c74\transformed\jetified-viewpager2-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\01e2300b6b710552831be921ec408c74\transformed\jetified-viewpager2-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-ads:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\237e0b5db534c615c4317f1b214e3e7f\transformed\jetified-play-services-ads-24.2.0\AndroidManifest.xml:21:5-23:52
MERGED from [com.google.android.gms:play-services-ads:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\237e0b5db534c615c4317f1b214e3e7f\transformed\jetified-play-services-ads-24.2.0\AndroidManifest.xml:21:5-23:52
MERGED from [com.google.firebase:firebase-analytics:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\724e7f8814b0a904b84a7da61a2fff9f\transformed\jetified-firebase-analytics-22.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\724e7f8814b0a904b84a7da61a2fff9f\transformed\jetified-firebase-analytics-22.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\430c56e4d5b03e6b74c8f45faf04d96d\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\430c56e4d5b03e6b74c8f45faf04d96d\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\382e0d6a1af511ca59a11019348ab80b\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\382e0d6a1af511ca59a11019348ab80b\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b034c97d14d08666bf0faf4a610bc04f\transformed\jetified-play-services-measurement-sdk-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b034c97d14d08666bf0faf4a610bc04f\transformed\jetified-play-services-measurement-sdk-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0bd9b80e9a416301d2239cd96524ca3a\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0bd9b80e9a416301d2239cd96524ca3a\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85e07b7dad8a0af8147f5311c4247a4d\transformed\jetified-play-services-ads-identifier-18.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85e07b7dad8a0af8147f5311c4247a4d\transformed\jetified-play-services-ads-identifier-18.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d861824a24f836c544f1ecb2711be738\transformed\jetified-play-services-appset-16.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d861824a24f836c544f1ecb2711be738\transformed\jetified-play-services-appset-16.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-crashlytics:19.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7827a62f7a18bcb72aaa3ce93f2a42ed\transformed\jetified-firebase-crashlytics-19.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-crashlytics:19.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7827a62f7a18bcb72aaa3ce93f2a42ed\transformed\jetified-firebase-crashlytics-19.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\647741b0d36db01281989d0b55b41c1e\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\647741b0d36db01281989d0b55b41c1e\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-sessions:2.0.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d72e4ec5a5740afe66ba66375b2f440d\transformed\jetified-firebase-sessions-2.0.8\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-sessions:2.0.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d72e4ec5a5740afe66ba66375b2f440d\transformed\jetified-firebase-sessions-2.0.8\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d824c942e0670731cb8b0e5390339c22\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d824c942e0670731cb8b0e5390339c22\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf6d9f69f61c325a990b48b77dab7869\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf6d9f69f61c325a990b48b77dab7869\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\42c987edc6bac2d568a7305ae38ce788\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\42c987edc6bac2d568a7305ae38ce788\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d56ddc8f70c1b6c4f2dfff25a6818549\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d56ddc8f70c1b6c4f2dfff25a6818549\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bbb40e47a1eb48e983fe6e7d96d2ed8\transformed\jetified-firebase-installations-interop-17.2.0\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bbb40e47a1eb48e983fe6e7d96d2ed8\transformed\jetified-firebase-installations-interop-17.2.0\AndroidManifest.xml:17:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8634365bf1152e1cd9a053656cde2744\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8634365bf1152e1cd9a053656cde2744\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\179e6486bd57a16ea175623aa423e7ed\transformed\jetified-material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\179e6486bd57a16ea175623aa423e7ed\transformed\jetified-material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\685979bdf4e338bede8f597be25f6f85\transformed\jetified-animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\685979bdf4e338bede8f597be25f6f85\transformed\jetified-animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c88f426dac06daea6f403b33dc30a779\transformed\jetified-material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c88f426dac06daea6f403b33dc30a779\transformed\jetified-material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b251c0e3af947a65d7a94a909760549\transformed\jetified-animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b251c0e3af947a65d7a94a909760549\transformed\jetified-animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1a21fbc400d5caf540ad887213f399b9\transformed\jetified-foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1a21fbc400d5caf540ad887213f399b9\transformed\jetified-foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\237635df39b25799c092d66a208ce67d\transformed\jetified-foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\237635df39b25799c092d66a208ce67d\transformed\jetified-foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4739a37df38d6d3167e025d3e02f2d4f\transformed\jetified-ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4739a37df38d6d3167e025d3e02f2d4f\transformed\jetified-ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf3f14f61f654ca87e875fb842d85041\transformed\jetified-ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf3f14f61f654ca87e875fb842d85041\transformed\jetified-ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4168971c306c258c6df514e5fb75e24\transformed\jetified-ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4168971c306c258c6df514e5fb75e24\transformed\jetified-ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fcad03f6139abb74df47ac2c38f7150d\transformed\jetified-ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fcad03f6139abb74df47ac2c38f7150d\transformed\jetified-ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80b9575061b7113086c965852a660d37\transformed\jetified-ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80b9575061b7113086c965852a660d37\transformed\jetified-ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\705b667089ad425e9a6a8d447e2e4b19\transformed\jetified-ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\705b667089ad425e9a6a8d447e2e4b19\transformed\jetified-ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2957fefc5cbf3ca73909d951bd6622e2\transformed\jetified-fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2957fefc5cbf3ca73909d951bd6622e2\transformed\jetified-fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\755cc2112db16a4945b8616bf2764b3c\transformed\jetified-activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\755cc2112db16a4945b8616bf2764b3c\transformed\jetified-activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\202151605bf9046134e71d49e3bcfab2\transformed\jetified-activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\202151605bf9046134e71d49e3bcfab2\transformed\jetified-activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2735aacb78950a7aea7cb796e4e87e7\transformed\jetified-material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2735aacb78950a7aea7cb796e4e87e7\transformed\jetified-material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e0a763189144907fb0197c2b097244b\transformed\jetified-ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e0a763189144907fb0197c2b097244b\transformed\jetified-ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\04e6ea113b6b9423e1db617895bd977a\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\04e6ea113b6b9423e1db617895bd977a\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\683a094184685dee7824268984a48693\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\683a094184685dee7824268984a48693\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60a813200b74c1dc84d254b8806ebd17\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60a813200b74c1dc84d254b8806ebd17\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30188ea85dd819e5cb58ff0ed544e055\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30188ea85dd819e5cb58ff0ed544e055\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80b277d63f31e9b131f3b2a80303bb42\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80b277d63f31e9b131f3b2a80303bb42\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\adf431448d9952a7bfc1730a2449c941\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\adf431448d9952a7bfc1730a2449c941\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a2bba29280d1d9d4ef13247e6c9abae\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a2bba29280d1d9d4ef13247e6c9abae\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f691554f471de61b2961ded671aeec4e\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f691554f471de61b2961ded671aeec4e\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9bed8d4710c38689abca92b67b225ab3\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9bed8d4710c38689abca92b67b225ab3\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b565ac0ad8434f83b6988fa74d8800c\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b565ac0ad8434f83b6988fa74d8800c\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91dab5885f5560bd65be1e52d9b695ca\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91dab5885f5560bd65be1e52d9b695ca\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5c5c72c6ff4a7863322da50648a25e99\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5c5c72c6ff4a7863322da50648a25e99\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.11.0-alpha02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\439a2d203198d843a8d888fae84bd2a9\transformed\webkit-1.11.0-alpha02\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.11.0-alpha02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\439a2d203198d843a8d888fae84bd2a9\transformed\webkit-1.11.0-alpha02\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a3aa6ec5b50d44e4805132f5d9e3f646\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a3aa6ec5b50d44e4805132f5d9e3f646\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d4b898ed3dd7fc494935f4d2e801126\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d4b898ed3dd7fc494935f4d2e801126\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69fa008bb70ecc0d8e73621c41f47f2d\transformed\jetified-window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69fa008bb70ecc0d8e73621c41f47f2d\transformed\jetified-window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac42dc8b85a532399756bb632f272af9\transformed\jetified-autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac42dc8b85a532399756bb632f272af9\transformed\jetified-autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2189c570813bc0f2cb30b4758a1420b2\transformed\jetified-graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2189c570813bc0f2cb30b4758a1420b2\transformed\jetified-graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f908cdc45776521b403beeef1508641c\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f908cdc45776521b403beeef1508641c\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2a3f12bf6b40e2cfca4058cca8bbcc6\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2a3f12bf6b40e2cfca4058cca8bbcc6\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4c7b48e4d1f473a8b802d70e4aff0147\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4c7b48e4d1f473a8b802d70e4aff0147\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6f685b0d0b4ae316a91fcc6f10da548\transformed\jetified-lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6f685b0d0b4ae316a91fcc6f10da548\transformed\jetified-lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\14ecb582312534e9963d84c222812c5b\transformed\jetified-lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\14ecb582312534e9963d84c222812c5b\transformed\jetified-lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\880ebab98f3493eda4af72fec141de1e\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\880ebab98f3493eda4af72fec141de1e\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17ade856dd8011987dee0a638e332f55\transformed\jetified-lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17ade856dd8011987dee0a638e332f55\transformed\jetified-lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\34ff98ecf4c4bcf721c91b81eabab462\transformed\jetified-lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\34ff98ecf4c4bcf721c91b81eabab462\transformed\jetified-lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3d8e112f24c7225462503b588bb0055c\transformed\jetified-lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3d8e112f24c7225462503b588bb0055c\transformed\jetified-lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1eebb1a882efcc15d28c2876ceb6fba2\transformed\jetified-lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1eebb1a882efcc15d28c2876ceb6fba2\transformed\jetified-lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ac2ac01d0e2d9d1be275a5f8b039215\transformed\jetified-lifecycle-service-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ac2ac01d0e2d9d1be275a5f8b039215\transformed\jetified-lifecycle-service-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\204396a0a01b39d67dab179cc3d5cc84\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\204396a0a01b39d67dab179cc3d5cc84\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54c5ab35e7f232bd0706882e7fefe677\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54c5ab35e7f232bd0706882e7fefe677\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25d2fd9aae62b446b90a4fd8f9d80408\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25d2fd9aae62b446b90a4fd8f9d80408\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e86056dcfc48e830b6da0ede09de9e34\transformed\jetified-lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e86056dcfc48e830b6da0ede09de9e34\transformed\jetified-lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d380ebedd25fb23edf1949b73a1e0c6\transformed\jetified-runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d380ebedd25fb23edf1949b73a1e0c6\transformed\jetified-runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e745399e7d774c8df31e1e97961041eb\transformed\jetified-runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e745399e7d774c8df31e1e97961041eb\transformed\jetified-runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f4a03e79b2114de7f0a0d332731f16a\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f4a03e79b2114de7f0a0d332731f16a\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee5719f91e687c59ebc143c8aca22017\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee5719f91e687c59ebc143c8aca22017\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\849e152feb07c9011174f589048f9022\transformed\jetified-datastore-preferences-1.1.1\AndroidManifest.xml:4:5-43
MERGED from [androidx.datastore:datastore-preferences:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\849e152feb07c9011174f589048f9022\transformed\jetified-datastore-preferences-1.1.1\AndroidManifest.xml:4:5-43
MERGED from [androidx.datastore:datastore-preferences-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9db713bd46ed01b74ada965cab7a6b31\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9db713bd46ed01b74ada965cab7a6b31\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb561ccecad2e9fd2bf98a76522c9f4d\transformed\jetified-ads-adservices-java-1.1.0-beta11\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb561ccecad2e9fd2bf98a76522c9f4d\transformed\jetified-ads-adservices-java-1.1.0-beta11\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe8b59d760258b64cc2e08caaf757d3\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe8b59d760258b64cc2e08caaf757d3\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a0502e998f317dfcb658a352de2cd3\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a0502e998f317dfcb658a352de2cd3\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98e15cc48088d4cf51d7c42a5f31d80a\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98e15cc48088d4cf51d7c42a5f31d80a\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39008844c0169f05f0b3eade5d4064db\transformed\jetified-play-services-measurement-base-22.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39008844c0169f05f0b3eade5d4064db\transformed\jetified-play-services-measurement-base-22.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d23f72c165873084e0061094322da6f\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d23f72c165873084e0061094322da6f\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ee7eee8c149506e91953117e879e05\transformed\jetified-firebase-measurement-connector-20.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ee7eee8c149506e91953117e879e05\transformed\jetified-firebase-measurement-connector-20.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.ump:user-messaging-platform:3.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a71a40e4160df8002d49c156025423f\transformed\jetified-user-messaging-platform-3.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.ump:user-messaging-platform:3.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a71a40e4160df8002d49c156025423f\transformed\jetified-user-messaging-platform-3.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\42f95d9fa807b14415e836fc15872a54\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\42f95d9fa807b14415e836fc15872a54\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4961d4f566368a726dd9f06399253e9d\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4961d4f566368a726dd9f06399253e9d\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f5d2fdc4a4434be9fcd65932e42964a\transformed\jetified-activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0f5d2fdc4a4434be9fcd65932e42964a\transformed\jetified-activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b518e79f85ce070d76134f5e2085a96\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b518e79f85ce070d76134f5e2085a96\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c691a52d16d17081fdd44e90e0363686\transformed\jetified-core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c691a52d16d17081fdd44e90e0363686\transformed\jetified-core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-splashscreen:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\141e0148a23d0681a92a44c218f71e86\transformed\jetified-core-splashscreen-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-splashscreen:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\141e0148a23d0681a92a44c218f71e86\transformed\jetified-core-splashscreen-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [me.tankery.lib:circularSeekBar:1.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19df98120f1e878bbd36ae400e97f28b\transformed\jetified-circularSeekBar-1.4.2\AndroidManifest.xml:5:5-7:41
MERGED from [me.tankery.lib:circularSeekBar:1.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19df98120f1e878bbd36ae400e97f28b\transformed\jetified-circularSeekBar-1.4.2\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-config-interop:16.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0310dfde44a4b562a000032c1077936f\transformed\jetified-firebase-config-interop-16.0.1\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-config-interop:16.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0310dfde44a4b562a000032c1077936f\transformed\jetified-firebase-config-interop-16.0.1\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\167f60ddd189c968da5456a2b3b7711e\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\167f60ddd189c968da5456a2b3b7711e\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29198f9de8a02a6dbf7704c477a13803\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29198f9de8a02a6dbf7704c477a13803\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-encoders-json:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\465a46469bddbafb86fb7613265bae0a\transformed\jetified-firebase-encoders-json-18.0.1\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\465a46469bddbafb86fb7613265bae0a\transformed\jetified-firebase-encoders-json-18.0.1\AndroidManifest.xml:18:5-20:41
MERGED from [com.squareup.picasso:picasso:2.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3db596f3ce001dc72c702061cbd417cf\transformed\jetified-picasso-2.8\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.picasso:picasso:2.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3db596f3ce001dc72c702061cbd417cf\transformed\jetified-picasso-2.8\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a57f610ef3585645bfe3fcbe98d165f8\transformed\jetified-core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a57f610ef3585645bfe3fcbe98d165f8\transformed\jetified-core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.shimmer:shimmer:0.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24c3083ed1841653be2c5c5238c002f8\transformed\jetified-shimmer-0.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.shimmer:shimmer:0.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24c3083ed1841653be2c5c5238c002f8\transformed\jetified-shimmer-0.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.github.PhilJay:MPAndroidChart:v3.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8705dbe80330cf51059e4cf6958823c4\transformed\jetified-MPAndroidChart-v3.1.0\AndroidManifest.xml:16:5-18:41
MERGED from [com.github.PhilJay:MPAndroidChart:v3.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8705dbe80330cf51059e4cf6958823c4\transformed\jetified-MPAndroidChart-v3.1.0\AndroidManifest.xml:16:5-18:41
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\71af04e3c58b6554ca900891431220d8\transformed\jetified-BlurView-version-2.0.6\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\71af04e3c58b6554ca900891431220d8\transformed\jetified-BlurView-version-2.0.6\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8eab8a838f0f81f776d8ffbd732492d7\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8eab8a838f0f81f776d8ffbd732492d7\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2689e40363c086e26e0a6a841af9a39c\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2689e40363c086e26e0a6a841af9a39c\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea1b0c1bbaf4c424ba812ea50539ea72\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea1b0c1bbaf4c424ba812ea50539ea72\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\045adccf1e2e01c8fc427fe22c44c3d2\transformed\jetified-gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\045adccf1e2e01c8fc427fe22c44c3d2\transformed\jetified-gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb1c28a2ff7b145ef909defefb7fc232\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb1c28a2ff7b145ef909defefb7fc232\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6df7bf1ddef91663e5063bb6ebc3431d\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6df7bf1ddef91663e5063bb6ebc3431d\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\599087b95bbc93489c0632601e242d3f\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\599087b95bbc93489c0632601e242d3f\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a46481580e219c1c955066d96ba2fcb\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a46481580e219c1c955066d96ba2fcb\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e74877bcb1d449cece0d3989e1586b35\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e74877bcb1d449cece0d3989e1586b35\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ed936dc0283901104a2185c7c048406\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ed936dc0283901104a2185c7c048406\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fa1b4c3d3ee8d16b849865caadfb65e\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fa1b4c3d3ee8d16b849865caadfb65e\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b81264e61541a2f55d07e34832d3dbb\transformed\jetified-transport-api-3.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b81264e61541a2f55d07e34832d3dbb\transformed\jetified-transport-api-3.2.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\74a0504aa17c55099e2abc4daad546c1\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\74a0504aa17c55099e2abc4daad546c1\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a0fc76e31ea58355bd9f993ec0ff2e7\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a0fc76e31ea58355bd9f993ec0ff2e7\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3de44db5490c663c2bdc1080a507338c\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3de44db5490c663c2bdc1080a507338c\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4344bc802b6747759d9e037220bf761a\transformed\room-runtime-2.2.5\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4344bc802b6747759d9e037220bf761a\transformed\room-runtime-2.2.5\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4c32fa8fbc4a31b3c30757b8a268c5c\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4c32fa8fbc4a31b3c30757b8a268c5c\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4b15df558aa555d447bb1d5194849b28\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4b15df558aa555d447bb1d5194849b28\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20312724fa5f8c3feaaaa8068655b512\transformed\sqlite-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20312724fa5f8c3feaaaa8068655b512\transformed\sqlite-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc692a120143169d749d7ac0b630b233\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc692a120143169d749d7ac0b630b233\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [com.intuit.sdp:sdp-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc35ffdefd2abfa5aac2f445dfc58ee4\transformed\jetified-sdp-android-1.1.1\AndroidManifest.xml:7:5-44
MERGED from [com.intuit.sdp:sdp-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc35ffdefd2abfa5aac2f445dfc58ee4\transformed\jetified-sdp-android-1.1.1\AndroidManifest.xml:7:5-44
MERGED from [com.intuit.ssp:ssp-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5658b4b85c4a4e32f0ba738fc8faf8b\transformed\jetified-ssp-android-1.1.1\AndroidManifest.xml:7:5-44
MERGED from [com.intuit.ssp:ssp-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5658b4b85c4a4e32f0ba738fc8faf8b\transformed\jetified-ssp-android-1.1.1\AndroidManifest.xml:7:5-44
MERGED from [com.adjust.sdk:adjust-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\efac1c2d606ea39a514ba9d42b351722\transformed\jetified-adjust-android-5.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.adjust.sdk:adjust-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\efac1c2d606ea39a514ba9d42b351722\transformed\jetified-adjust-android-5.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59a9191e92c16f81b31d9fba59b2bce2\transformed\jetified-installreferrer-2.2\AndroidManifest.xml:5:5-7:41
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59a9191e92c16f81b31d9fba59b2bce2\transformed\jetified-installreferrer-2.2\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.dagger:dagger-lint-aar:2.56.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3ac84cee72251c573666a65182890d95\transformed\jetified-dagger-lint-aar-2.56.1\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:dagger-lint-aar:2.56.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3ac84cee72251c573666a65182890d95\transformed\jetified-dagger-lint-aar-2.56.1\AndroidManifest.xml:18:3-42
MERGED from [com.adjust.signature:adjust-android-signature:3.35.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39c1655bdeadb572f2d5f5e9acd549ae\transformed\jetified-adjust-android-signature-3.35.2\AndroidManifest.xml:5:5-44
MERGED from [com.adjust.signature:adjust-android-signature:3.35.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39c1655bdeadb572f2d5f5e9acd549ae\transformed\jetified-adjust-android-signature-3.35.2\AndroidManifest.xml:5:5-44
	tools:overrideLibrary
		ADDED from [com.google.android.gms:play-services-ads:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\237e0b5db534c615c4317f1b214e3e7f\transformed\jetified-play-services-ads-24.2.0\AndroidManifest.xml:23:9-49
	android:targetSdkVersion
		INJECTED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\Duc\AndroidStudioProjects\TJ_BatteryOne\app\src\debug\AndroidManifest.xml
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from [com.github.tqhit:AdLib:1.2.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a649bc6d741f1561035089684f0b179\transformed\jetified-AdLib-1.2.7\AndroidManifest.xml:32:5-76
	android:name
		ADDED from [com.github.tqhit:AdLib:1.2.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a649bc6d741f1561035089684f0b179\transformed\jetified-AdLib-1.2.7\AndroidManifest.xml:32:22-73
uses-permission#android.permission.WAKE_LOCK
ADDED from [com.github.tqhit:AdLib:1.2.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a649bc6d741f1561035089684f0b179\transformed\jetified-AdLib-1.2.7\AndroidManifest.xml:33:5-68
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\430c56e4d5b03e6b74c8f45faf04d96d\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\430c56e4d5b03e6b74c8f45faf04d96d\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\382e0d6a1af511ca59a11019348ab80b\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\382e0d6a1af511ca59a11019348ab80b\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0bd9b80e9a416301d2239cd96524ca3a\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0bd9b80e9a416301d2239cd96524ca3a\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:25:5-68
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:25:5-68
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98e15cc48088d4cf51d7c42a5f31d80a\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98e15cc48088d4cf51d7c42a5f31d80a\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:25:5-68
	android:name
		ADDED from [com.github.tqhit:AdLib:1.2.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a649bc6d741f1561035089684f0b179\transformed\jetified-AdLib-1.2.7\AndroidManifest.xml:33:22-65
activity#cat.ereza.customactivityoncrash.activity.DefaultErrorActivity
ADDED from [cat.ereza:customactivityoncrash:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2821057fb80181e87f67e0a41ee8161d\transformed\jetified-customactivityoncrash-2.4.0\AndroidManifest.xml:10:9-12:49
	android:process
		ADDED from [cat.ereza:customactivityoncrash:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2821057fb80181e87f67e0a41ee8161d\transformed\jetified-customactivityoncrash-2.4.0\AndroidManifest.xml:12:13-46
	android:name
		ADDED from [cat.ereza:customactivityoncrash:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2821057fb80181e87f67e0a41ee8161d\transformed\jetified-customactivityoncrash-2.4.0\AndroidManifest.xml:11:13-89
provider#cat.ereza.customactivityoncrash.provider.CaocInitProvider
ADDED from [cat.ereza:customactivityoncrash:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2821057fb80181e87f67e0a41ee8161d\transformed\jetified-customactivityoncrash-2.4.0\AndroidManifest.xml:14:9-18:39
	android:authorities
		ADDED from [cat.ereza:customactivityoncrash:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2821057fb80181e87f67e0a41ee8161d\transformed\jetified-customactivityoncrash-2.4.0\AndroidManifest.xml:16:13-85
	android:exported
		ADDED from [cat.ereza:customactivityoncrash:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2821057fb80181e87f67e0a41ee8161d\transformed\jetified-customactivityoncrash-2.4.0\AndroidManifest.xml:17:13-37
	android:initOrder
		ADDED from [cat.ereza:customactivityoncrash:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2821057fb80181e87f67e0a41ee8161d\transformed\jetified-customactivityoncrash-2.4.0\AndroidManifest.xml:18:13-36
	android:name
		ADDED from [cat.ereza:customactivityoncrash:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2821057fb80181e87f67e0a41ee8161d\transformed\jetified-customactivityoncrash-2.4.0\AndroidManifest.xml:15:13-85
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\430c56e4d5b03e6b74c8f45faf04d96d\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0bd9b80e9a416301d2239cd96524ca3a\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0bd9b80e9a416301d2239cd96524ca3a\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:26:5-110
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59a9191e92c16f81b31d9fba59b2bce2\transformed\jetified-installreferrer-2.2\AndroidManifest.xml:9:5-110
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59a9191e92c16f81b31d9fba59b2bce2\transformed\jetified-installreferrer-2.2\AndroidManifest.xml:9:5-110
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\430c56e4d5b03e6b74c8f45faf04d96d\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:26:22-107
receiver#com.google.android.gms.measurement.AppMeasurementReceiver
ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\430c56e4d5b03e6b74c8f45faf04d96d\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:29:9-33:20
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\430c56e4d5b03e6b74c8f45faf04d96d\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:31:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\430c56e4d5b03e6b74c8f45faf04d96d\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\430c56e4d5b03e6b74c8f45faf04d96d\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:30:13-85
service#com.google.android.gms.measurement.AppMeasurementService
ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\430c56e4d5b03e6b74c8f45faf04d96d\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:35:9-38:40
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\430c56e4d5b03e6b74c8f45faf04d96d\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\430c56e4d5b03e6b74c8f45faf04d96d\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:38:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\430c56e4d5b03e6b74c8f45faf04d96d\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:36:13-84
service#com.google.android.gms.measurement.AppMeasurementJobService
ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\430c56e4d5b03e6b74c8f45faf04d96d\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:39:9-43:72
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\430c56e4d5b03e6b74c8f45faf04d96d\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:41:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\430c56e4d5b03e6b74c8f45faf04d96d\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:42:13-37
	android:permission
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\430c56e4d5b03e6b74c8f45faf04d96d\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:43:13-69
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\430c56e4d5b03e6b74c8f45faf04d96d\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:40:13-87
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\382e0d6a1af511ca59a11019348ab80b\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0bd9b80e9a416301d2239cd96524ca3a\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0bd9b80e9a416301d2239cd96524ca3a\transformed\jetified-play-services-measurement-impl-22.4.0\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85e07b7dad8a0af8147f5311c4247a4d\transformed\jetified-play-services-ads-identifier-18.2.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85e07b7dad8a0af8147f5311c4247a4d\transformed\jetified-play-services-ads-identifier-18.2.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98e15cc48088d4cf51d7c42a5f31d80a\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98e15cc48088d4cf51d7c42a5f31d80a\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:26:5-79
MERGED from [com.adjust.sdk:adjust-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\efac1c2d606ea39a514ba9d42b351722\transformed\jetified-adjust-android-5.1.0\AndroidManifest.xml:8:5-79
MERGED from [com.adjust.sdk:adjust-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\efac1c2d606ea39a514ba9d42b351722\transformed\jetified-adjust-android-5.1.0\AndroidManifest.xml:8:5-79
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\382e0d6a1af511ca59a11019348ab80b\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:25:22-76
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\382e0d6a1af511ca59a11019348ab80b\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:26:5-88
MERGED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:28:5-88
MERGED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:28:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98e15cc48088d4cf51d7c42a5f31d80a\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:27:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98e15cc48088d4cf51d7c42a5f31d80a\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:27:5-88
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\382e0d6a1af511ca59a11019348ab80b\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:26:22-85
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\382e0d6a1af511ca59a11019348ab80b\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98e15cc48088d4cf51d7c42a5f31d80a\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:28:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98e15cc48088d4cf51d7c42a5f31d80a\transformed\jetified-play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:28:5-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\382e0d6a1af511ca59a11019348ab80b\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:27:22-79
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\382e0d6a1af511ca59a11019348ab80b\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:30:9-36:19
MERGED from [com.google.firebase:firebase-crashlytics:19.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7827a62f7a18bcb72aaa3ce93f2a42ed\transformed\jetified-firebase-crashlytics-19.4.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-crashlytics:19.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7827a62f7a18bcb72aaa3ce93f2a42ed\transformed\jetified-firebase-crashlytics-19.4.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\647741b0d36db01281989d0b55b41c1e\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:26:9-35:19
MERGED from [com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\647741b0d36db01281989d0b55b41c1e\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:26:9-35:19
MERGED from [com.google.firebase:firebase-sessions:2.0.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d72e4ec5a5740afe66ba66375b2f440d\transformed\jetified-firebase-sessions-2.0.8\AndroidManifest.xml:26:9-32:19
MERGED from [com.google.firebase:firebase-sessions:2.0.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d72e4ec5a5740afe66ba66375b2f440d\transformed\jetified-firebase-sessions-2.0.8\AndroidManifest.xml:26:9-32:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d824c942e0670731cb8b0e5390339c22\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d824c942e0670731cb8b0e5390339c22\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf6d9f69f61c325a990b48b77dab7869\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf6d9f69f61c325a990b48b77dab7869\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\42c987edc6bac2d568a7305ae38ce788\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\42c987edc6bac2d568a7305ae38ce788\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d23f72c165873084e0061094322da6f\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:11:9-15:19
MERGED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d23f72c165873084e0061094322da6f\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:11:9-15:19
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\167f60ddd189c968da5456a2b3b7711e\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:22:9-28:19
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\167f60ddd189c968da5456a2b3b7711e\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:22:9-28:19
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\382e0d6a1af511ca59a11019348ab80b\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:32:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\42c987edc6bac2d568a7305ae38ce788\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\42c987edc6bac2d568a7305ae38ce788\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\382e0d6a1af511ca59a11019348ab80b\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:31:13-84
meta-data#com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar
ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\382e0d6a1af511ca59a11019348ab80b\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:33:13-35:85
	android:value
		ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\382e0d6a1af511ca59a11019348ab80b\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:35:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\382e0d6a1af511ca59a11019348ab80b\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:34:17-139
uses-permission#android.permission.ACCESS_ADSERVICES_TOPICS
ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:29:5-83
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:29:22-80
queries
ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:35:5-68:15
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:scheme:https
ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:38:9-44:18
intent#action:name:android.support.customtabs.action.CustomTabsService
ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:47:9-49:18
action#android.support.customtabs.action.CustomTabsService
ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:48:13-90
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:48:21-87
intent#action:name:android.intent.action.INSERT+data:mimeType:vnd.android.cursor.dir/event
ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:52:9-56:18
action#android.intent.action.INSERT
ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:53:13-67
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:53:21-64
intent#action:name:android.intent.action.VIEW+data:scheme:sms
ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:57:9-61:18
intent#action:name:android.intent.action.DIAL+data:path:tel:
ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:62:9-66:18
action#android.intent.action.DIAL
ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:63:13-65
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:63:21-62
activity#com.google.android.gms.ads.AdActivity
ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:73:9-78:43
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:76:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:78:13-40
	android:configChanges
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:75:13-122
	android:theme
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:77:13-61
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:74:13-65
provider#com.google.android.gms.ads.MobileAdsInitProvider
ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:80:9-85:43
	android:authorities
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:82:13-73
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:83:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:85:13-40
	android:initOrder
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:84:13-36
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:81:13-76
service#com.google.android.gms.ads.AdService
ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:87:9-91:43
	android:enabled
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:89:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:90:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:91:13-40
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:88:13-64
activity#com.google.android.gms.ads.OutOfContextTestingActivity
ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:93:9-97:43
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:96:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:97:13-40
	android:configChanges
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:95:13-122
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:94:13-82
activity#com.google.android.gms.ads.NotificationHandlerActivity
ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:98:9-105:43
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:100:13-46
	android:launchMode
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:102:13-44
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:101:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:105:13-40
	android:theme
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:104:13-72
	android:taskAffinity
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:103:13-36
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:99:13-82
meta-data#com.google.android.gms.ads.flag.OPTIMIZE_AD_LOADING
ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:107:9-109:36
	android:value
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:109:13-33
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:108:13-79
meta-data#com.google.android.gms.ads.flag.OPTIMIZE_INITIALIZATION
ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:110:9-112:36
	android:value
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:112:13-33
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a26d25b3618e3c60f978a7b1cb6a100\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:111:13-83
meta-data#com.google.firebase.components:com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar
ADDED from [com.google.firebase:firebase-crashlytics:19.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7827a62f7a18bcb72aaa3ce93f2a42ed\transformed\jetified-firebase-crashlytics-19.4.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-crashlytics:19.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7827a62f7a18bcb72aaa3ce93f2a42ed\transformed\jetified-firebase-crashlytics-19.4.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-crashlytics:19.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7827a62f7a18bcb72aaa3ce93f2a42ed\transformed\jetified-firebase-crashlytics-19.4.0\AndroidManifest.xml:16:17-126
meta-data#com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar
ADDED from [com.google.firebase:firebase-crashlytics:19.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7827a62f7a18bcb72aaa3ce93f2a42ed\transformed\jetified-firebase-crashlytics-19.4.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-crashlytics:19.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7827a62f7a18bcb72aaa3ce93f2a42ed\transformed\jetified-firebase-crashlytics-19.4.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-crashlytics:19.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7827a62f7a18bcb72aaa3ce93f2a42ed\transformed\jetified-firebase-crashlytics-19.4.0\AndroidManifest.xml:19:17-115
meta-data#com.google.firebase.components:com.google.firebase.remoteconfig.FirebaseRemoteConfigKtxRegistrar
ADDED from [com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\647741b0d36db01281989d0b55b41c1e\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:29:13-31:85
	android:value
		ADDED from [com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\647741b0d36db01281989d0b55b41c1e\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:31:17-82
	android:name
		ADDED from [com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\647741b0d36db01281989d0b55b41c1e\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:30:17-128
meta-data#com.google.firebase.components:com.google.firebase.remoteconfig.RemoteConfigRegistrar
ADDED from [com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\647741b0d36db01281989d0b55b41c1e\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:32:13-34:85
	android:value
		ADDED from [com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\647741b0d36db01281989d0b55b41c1e\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:34:17-82
	android:name
		ADDED from [com.google.firebase:firebase-config:22.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\647741b0d36db01281989d0b55b41c1e\transformed\jetified-firebase-config-22.1.0\AndroidManifest.xml:33:17-117
service#com.google.firebase.sessions.SessionLifecycleService
ADDED from [com.google.firebase:firebase-sessions:2.0.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d72e4ec5a5740afe66ba66375b2f440d\transformed\jetified-firebase-sessions-2.0.8\AndroidManifest.xml:22:9-25:40
	android:enabled
		ADDED from [com.google.firebase:firebase-sessions:2.0.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d72e4ec5a5740afe66ba66375b2f440d\transformed\jetified-firebase-sessions-2.0.8\AndroidManifest.xml:24:13-35
	android:exported
		ADDED from [com.google.firebase:firebase-sessions:2.0.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d72e4ec5a5740afe66ba66375b2f440d\transformed\jetified-firebase-sessions-2.0.8\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [com.google.firebase:firebase-sessions:2.0.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d72e4ec5a5740afe66ba66375b2f440d\transformed\jetified-firebase-sessions-2.0.8\AndroidManifest.xml:23:13-80
meta-data#com.google.firebase.components:com.google.firebase.sessions.FirebaseSessionsRegistrar
ADDED from [com.google.firebase:firebase-sessions:2.0.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d72e4ec5a5740afe66ba66375b2f440d\transformed\jetified-firebase-sessions-2.0.8\AndroidManifest.xml:29:13-31:85
	android:value
		ADDED from [com.google.firebase:firebase-sessions:2.0.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d72e4ec5a5740afe66ba66375b2f440d\transformed\jetified-firebase-sessions-2.0.8\AndroidManifest.xml:31:17-82
	android:name
		ADDED from [com.google.firebase:firebase-sessions:2.0.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d72e4ec5a5740afe66ba66375b2f440d\transformed\jetified-firebase-sessions-2.0.8\AndroidManifest.xml:30:17-117
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d824c942e0670731cb8b0e5390339c22\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d824c942e0670731cb8b0e5390339c22\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d824c942e0670731cb8b0e5390339c22\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d824c942e0670731cb8b0e5390339c22\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d824c942e0670731cb8b0e5390339c22\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d824c942e0670731cb8b0e5390339c22\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf6d9f69f61c325a990b48b77dab7869\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf6d9f69f61c325a990b48b77dab7869\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf6d9f69f61c325a990b48b77dab7869\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\42c987edc6bac2d568a7305ae38ce788\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\42c987edc6bac2d568a7305ae38ce788\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\42c987edc6bac2d568a7305ae38ce788\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\42c987edc6bac2d568a7305ae38ce788\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\42c987edc6bac2d568a7305ae38ce788\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\42c987edc6bac2d568a7305ae38ce788\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\42c987edc6bac2d568a7305ae38ce788\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\42c987edc6bac2d568a7305ae38ce788\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\42c987edc6bac2d568a7305ae38ce788\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d56ddc8f70c1b6c4f2dfff25a6818549\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d56ddc8f70c1b6c4f2dfff25a6818549\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d56ddc8f70c1b6c4f2dfff25a6818549\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d56ddc8f70c1b6c4f2dfff25a6818549\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\04e6ea113b6b9423e1db617895bd977a\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:31:9-39:20
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:31:9-39:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25d2fd9aae62b446b90a4fd8f9d80408\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25d2fd9aae62b446b90a4fd8f9d80408\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea1b0c1bbaf4c424ba812ea50539ea72\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea1b0c1bbaf4c424ba812ea50539ea72\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6df7bf1ddef91663e5063bb6ebc3431d\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6df7bf1ddef91663e5063bb6ebc3431d\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\04e6ea113b6b9423e1db617895bd977a\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\04e6ea113b6b9423e1db617895bd977a\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\04e6ea113b6b9423e1db617895bd977a\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\04e6ea113b6b9423e1db617895bd977a\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\04e6ea113b6b9423e1db617895bd977a\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\04e6ea113b6b9423e1db617895bd977a\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\04e6ea113b6b9423e1db617895bd977a\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:36:13-38:52
	android:value
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:38:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:37:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:41:9-46:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:44:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:45:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:46:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:43:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:42:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:47:9-53:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:50:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:51:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:52:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:53:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:49:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:48:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:54:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:57:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:58:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:56:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:55:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:116:13-120:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:25-76
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bc3ac1a7cb7052d837b8364241b66b3\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:25-85
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69fa008bb70ecc0d8e73621c41f47f2d\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69fa008bb70ecc0d8e73621c41f47f2d\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69fa008bb70ecc0d8e73621c41f47f2d\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69fa008bb70ecc0d8e73621c41f47f2d\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69fa008bb70ecc0d8e73621c41f47f2d\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69fa008bb70ecc0d8e73621c41f47f2d\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f908cdc45776521b403beeef1508641c\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f908cdc45776521b403beeef1508641c\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f908cdc45776521b403beeef1508641c\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
permission#com.fc.p.tj.charginganimation.batterycharging.chargeeffect.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f908cdc45776521b403beeef1508641c\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f908cdc45776521b403beeef1508641c\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f908cdc45776521b403beeef1508641c\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f908cdc45776521b403beeef1508641c\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f908cdc45776521b403beeef1508641c\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
uses-permission#com.fc.p.tj.charginganimation.batterycharging.chargeeffect.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f908cdc45776521b403beeef1508641c\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f908cdc45776521b403beeef1508641c\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25d2fd9aae62b446b90a4fd8f9d80408\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25d2fd9aae62b446b90a4fd8f9d80408\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25d2fd9aae62b446b90a4fd8f9d80408\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
uses-library#android.ext.adservices
ADDED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe8b59d760258b64cc2e08caaf757d3\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe8b59d760258b64cc2e08caaf757d3\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dbe8b59d760258b64cc2e08caaf757d3\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:24:13-50
meta-data#com.google.firebase.components:com.google.firebase.abt.component.AbtRegistrar
ADDED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d23f72c165873084e0061094322da6f\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d23f72c165873084e0061094322da6f\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d23f72c165873084e0061094322da6f\transformed\jetified-firebase-abt-21.1.1\AndroidManifest.xml:13:17-109
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\42f95d9fa807b14415e836fc15872a54\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\42f95d9fa807b14415e836fc15872a54\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\42f95d9fa807b14415e836fc15872a54\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:22:13-58
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\167f60ddd189c968da5456a2b3b7711e\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\167f60ddd189c968da5456a2b3b7711e\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\167f60ddd189c968da5456a2b3b7711e\transformed\jetified-firebase-datatransport-19.0.0\AndroidManifest.xml:26:17-115
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29198f9de8a02a6dbf7704c477a13803\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:26:9-32:19
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fa1b4c3d3ee8d16b849865caadfb65e\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:34:9-36:40
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fa1b4c3d3ee8d16b849865caadfb65e\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:34:9-36:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29198f9de8a02a6dbf7704c477a13803\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29198f9de8a02a6dbf7704c477a13803\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:27:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29198f9de8a02a6dbf7704c477a13803\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:29:13-31:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29198f9de8a02a6dbf7704c477a13803\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:31:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29198f9de8a02a6dbf7704c477a13803\transformed\jetified-transport-backend-cct-3.3.0\AndroidManifest.xml:30:17-94
provider#com.squareup.picasso.PicassoProvider
ADDED from [com.squareup.picasso:picasso:2.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3db596f3ce001dc72c702061cbd417cf\transformed\jetified-picasso-2.8\AndroidManifest.xml:8:9-11:40
	android:authorities
		ADDED from [com.squareup.picasso:picasso:2.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3db596f3ce001dc72c702061cbd417cf\transformed\jetified-picasso-2.8\AndroidManifest.xml:10:13-72
	android:exported
		ADDED from [com.squareup.picasso:picasso:2.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3db596f3ce001dc72c702061cbd417cf\transformed\jetified-picasso-2.8\AndroidManifest.xml:11:13-37
	android:name
		ADDED from [com.squareup.picasso:picasso:2.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3db596f3ce001dc72c702061cbd417cf\transformed\jetified-picasso-2.8\AndroidManifest.xml:9:13-64
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea1b0c1bbaf4c424ba812ea50539ea72\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea1b0c1bbaf4c424ba812ea50539ea72\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea1b0c1bbaf4c424ba812ea50539ea72\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea1b0c1bbaf4c424ba812ea50539ea72\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea1b0c1bbaf4c424ba812ea50539ea72\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea1b0c1bbaf4c424ba812ea50539ea72\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea1b0c1bbaf4c424ba812ea50539ea72\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea1b0c1bbaf4c424ba812ea50539ea72\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea1b0c1bbaf4c424ba812ea50539ea72\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea1b0c1bbaf4c424ba812ea50539ea72\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea1b0c1bbaf4c424ba812ea50539ea72\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea1b0c1bbaf4c424ba812ea50539ea72\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea1b0c1bbaf4c424ba812ea50539ea72\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea1b0c1bbaf4c424ba812ea50539ea72\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea1b0c1bbaf4c424ba812ea50539ea72\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea1b0c1bbaf4c424ba812ea50539ea72\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea1b0c1bbaf4c424ba812ea50539ea72\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea1b0c1bbaf4c424ba812ea50539ea72\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea1b0c1bbaf4c424ba812ea50539ea72\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea1b0c1bbaf4c424ba812ea50539ea72\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea1b0c1bbaf4c424ba812ea50539ea72\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fa1b4c3d3ee8d16b849865caadfb65e\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:24:9-28:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fa1b4c3d3ee8d16b849865caadfb65e\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:26:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fa1b4c3d3ee8d16b849865caadfb65e\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:27:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fa1b4c3d3ee8d16b849865caadfb65e\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:25:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fa1b4c3d3ee8d16b849865caadfb65e\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:30:9-32:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fa1b4c3d3ee8d16b849865caadfb65e\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fa1b4c3d3ee8d16b849865caadfb65e\transformed\jetified-transport-runtime-3.3.0\AndroidManifest.xml:31:13-132
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4344bc802b6747759d9e037220bf761a\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
	android:exported
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4344bc802b6747759d9e037220bf761a\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4344bc802b6747759d9e037220bf761a\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4344bc802b6747759d9e037220bf761a\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
provider#com.adjust.sdk.SystemLifecycleContentProvider
ADDED from [com.adjust.sdk:adjust-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\efac1c2d606ea39a514ba9d42b351722\transformed\jetified-adjust-android-5.1.0\AndroidManifest.xml:11:9-14:40
	android:authorities
		ADDED from [com.adjust.sdk:adjust-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\efac1c2d606ea39a514ba9d42b351722\transformed\jetified-adjust-android-5.1.0\AndroidManifest.xml:13:13-77
	android:exported
		ADDED from [com.adjust.sdk:adjust-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\efac1c2d606ea39a514ba9d42b351722\transformed\jetified-adjust-android-5.1.0\AndroidManifest.xml:14:13-37
	android:name
		ADDED from [com.adjust.sdk:adjust-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\efac1c2d606ea39a514ba9d42b351722\transformed\jetified-adjust-android-5.1.0\AndroidManifest.xml:12:13-73
