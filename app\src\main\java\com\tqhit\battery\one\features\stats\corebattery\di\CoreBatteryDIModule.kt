package com.tqhit.battery.one.features.stats.corebattery.di

import com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider
import com.tqhit.battery.one.features.stats.corebattery.domain.DefaultCoreBatteryStatsProvider
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Hilt module for providing dependencies for the core battery module.
 * This module binds the CoreBatteryStatsProvider interface to its default implementation.
 */
@Module
@InstallIn(SingletonComponent::class)
abstract class CoreBatteryDIModule {
    
    /**
     * Binds the CoreBatteryStatsProvider interface to DefaultCoreBatteryStatsProvider.
     * This ensures that whenever CoreBatteryStatsProvider is injected,
     * the DefaultCoreBatteryStatsProvider implementation will be provided.
     *
     * @param defaultCoreBatteryStatsProvider The default implementation
     * @return The CoreBatteryStatsProvider interface
     */
    @Binds
    @Singleton
    abstract fun bindCoreBatteryStatsProvider(
        defaultCoreBatteryStatsProvider: DefaultCoreBatteryStatsProvider
    ): CoreBatteryStatsProvider
}
