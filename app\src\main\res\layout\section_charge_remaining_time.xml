<?xml version="1.0" encoding="utf-8"?>
<!-- 
 * Remaining charging time section
 * This section contains:
 * - Section title
 * - Time to target percentage
 * - Time to full (100%)
 */
-->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/charge_remaining_time_root"
    android:orientation="vertical"
    android:background="@drawable/white_block"
    android:padding="8dp"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="9dp"
    android:layout_marginEnd="9dp"
    android:layout_marginBottom="14dp">

    <!-- Section Title -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/remaining_charging_time"
        android:textColor="?attr/black"
        android:textSize="19sp"
        android:layout_marginBottom="8dp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="?android:attr/listDivider"
        android:layout_marginBottom="8dp" />

    <!-- Time to target percentage -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="6dp">
        <TextView
            android:id="@+id/label_time_to_target"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/time_to_target_percent"
            android:textColor="?attr/black"
            android:textSize="14sp" />
        <TextView
            android:id="@+id/val_time_to_target"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="-"
            android:textColor="?attr/colorr"
            android:textSize="14sp" />
    </LinearLayout>

    <!-- Time to full (100%) -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/time_to_full"
            android:textColor="?attr/black"
            android:textSize="14sp" />
        <TextView
            android:id="@+id/val_time_to_full"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="-"
            android:textColor="?attr/colorr"
            android:textSize="14sp" />
    </LinearLayout>
</LinearLayout>