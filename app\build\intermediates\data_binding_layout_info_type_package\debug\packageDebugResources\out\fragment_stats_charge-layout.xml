<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_stats_charge" modulePackage="com.tqhit.battery.one" filePath="app\src\main\res\layout\fragment_stats_charge.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.core.widget.NestedScrollView" rootNodeViewId="@+id/stats_charge_scroll_view"><Targets><Target id="@+id/stats_charge_scroll_view" tag="layout/fragment_stats_charge_0" view="androidx.core.widget.NestedScrollView"><Expressions/><location startLine="6" startOffset="0" endLine="492" endOffset="39"/></Target><Target id="@+id/stats_charge_main_display_root" view="LinearLayout"><Expressions/><location startLine="20" startOffset="8" endLine="169" endOffset="22"/></Target><Target id="@+id/percent_layout" view="RelativeLayout"><Expressions/><location startLine="29" startOffset="12" endLine="67" endOffset="28"/></Target><Target id="@+id/percent_inner_layout" view="RelativeLayout"><Expressions/><location startLine="39" startOffset="16" endLine="66" endOffset="32"/></Target><Target id="@+id/tv_percentage" view="TextView"><Expressions/><location startLine="44" startOffset="20" endLine="51" endOffset="52"/></Target><Target id="@+id/charge_prog_bar_percent" view="com.google.android.material.progressindicator.CircularProgressIndicator"><Expressions/><location startLine="52" startOffset="20" endLine="65" endOffset="49"/></Target><Target id="@+id/time_estimates_layout" view="LinearLayout"><Expressions/><location startLine="70" startOffset="12" endLine="168" endOffset="26"/></Target><Target id="@+id/full_charge_block" view="LinearLayout"><Expressions/><location startLine="82" startOffset="16" endLine="108" endOffset="30"/></Target><Target id="@+id/tv_time_to_full" view="TextView"><Expressions/><location startLine="100" startOffset="20" endLine="107" endOffset="52"/></Target><Target id="@+id/target_charge_block" view="LinearLayout"><Expressions/><location startLine="111" startOffset="16" endLine="138" endOffset="30"/></Target><Target id="@+id/tv_time_to_target" view="TextView"><Expressions/><location startLine="130" startOffset="20" endLine="137" endOffset="52"/></Target><Target id="@+id/charging_status_block" view="LinearLayout"><Expressions/><location startLine="141" startOffset="16" endLine="167" endOffset="30"/></Target><Target id="@+id/tv_charging_status" view="TextView"><Expressions/><location startLine="159" startOffset="20" endLine="166" endOffset="52"/></Target><Target id="@+id/target_percentage_root" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="172" startOffset="8" endLine="265" endOffset="59"/></Target><Target id="@+id/target_percentage_title" view="TextView"><Expressions/><location startLine="187" startOffset="12" endLine="196" endOffset="59"/></Target><Target id="@+id/divider_target" view="View"><Expressions/><location startLine="198" startOffset="12" endLine="206" endOffset="84"/></Target><Target id="@+id/battery_alarm_btn" view="TextView"><Expressions/><location startLine="209" startOffset="12" endLine="227" endOffset="75"/></Target><Target id="@+id/target_percent_label" view="TextView"><Expressions/><location startLine="230" startOffset="12" endLine="239" endOffset="78"/></Target><Target id="@+id/tv_target_percentage" view="TextView"><Expressions/><location startLine="241" startOffset="12" endLine="252" endOffset="78"/></Target><Target id="@+id/seekbar_target" view="SeekBar"><Expressions/><location startLine="255" startOffset="12" endLine="264" endOffset="81"/></Target><Target id="@+id/charge_current_session_root" view="LinearLayout"><Expressions/><location startLine="268" startOffset="8" endLine="484" endOffset="22"/></Target><Target id="@+id/tv_session_start_time" view="TextView"><Expressions/><location startLine="314" startOffset="16" endLine="320" endOffset="45"/></Target><Target id="@+id/tv_session_duration" view="TextView"><Expressions/><location startLine="336" startOffset="16" endLine="342" endOffset="45"/></Target><Target id="@+id/tv_session_percentage_charged" view="TextView"><Expressions/><location startLine="358" startOffset="16" endLine="364" endOffset="45"/></Target><Target id="@+id/tv_session_total_charge_mah" view="TextView"><Expressions/><location startLine="380" startOffset="16" endLine="386" endOffset="45"/></Target><Target id="@+id/tv_current" view="TextView"><Expressions/><location startLine="402" startOffset="16" endLine="408" endOffset="45"/></Target><Target id="@+id/tv_voltage" view="TextView"><Expressions/><location startLine="424" startOffset="16" endLine="430" endOffset="45"/></Target><Target id="@+id/tv_temperature" view="TextView"><Expressions/><location startLine="446" startOffset="16" endLine="452" endOffset="45"/></Target><Target id="@+id/tv_power" view="TextView"><Expressions/><location startLine="467" startOffset="16" endLine="473" endOffset="45"/></Target><Target id="@+id/btn_reset_session" view="Button"><Expressions/><location startLine="477" startOffset="12" endLine="483" endOffset="45"/></Target></Targets></Layout>