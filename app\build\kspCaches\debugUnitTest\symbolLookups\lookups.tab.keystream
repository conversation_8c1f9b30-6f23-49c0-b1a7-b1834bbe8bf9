  Context android.content  SharedPreferences android.content  Editor !android.content.SharedPreferences  PackageManager android.content.pm  AssetManager android.content.res  PowerManager 
android.os  LifecycleOwner androidx.lifecycle  Gson com.google.gson  FirebaseRemoteConfigHelper com.tqhit.adlib.sdk.firebase  CustomizationDataStore 3com.tqhit.battery.one.features.emoji.data.datastore  BatteryStyleRepositoryImpl 4com.tqhit.battery.one.features.emoji.data.repository  CustomizationRepositoryImpl 4com.tqhit.battery.one.features.emoji.data.repository  OptIn 4com.tqhit.battery.one.features.emoji.data.repository  AssetManager Scom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest  BatteryStyleRepositoryImpl Scom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest  Context Scom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest  FirebaseRemoteConfigHelper Scom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest  Gson Scom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest  CustomizationDataStore Tcom.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest  CustomizationRepositoryImpl Tcom.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest  Gson Tcom.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest  BatteryStyle 1com.tqhit.battery.one.features.emoji.domain.model  BatteryStyleRepository 6com.tqhit.battery.one.features.emoji.domain.repository  CustomizationRepository 6com.tqhit.battery.one.features.emoji.domain.repository  GetBatteryStylesUseCase 4com.tqhit.battery.one.features.emoji.domain.use_case  LoadCustomizationUseCase 4com.tqhit.battery.one.features.emoji.domain.use_case  ResetCustomizationUseCase 4com.tqhit.battery.one.features.emoji.domain.use_case  SaveCustomizationUseCase 4com.tqhit.battery.one.features.emoji.domain.use_case  BatteryStyleRepository Pcom.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCaseTest  GetBatteryStylesUseCase Pcom.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCaseTest  BatteryStyleRepository Qcom.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest  CustomizationRepository Qcom.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest  LoadCustomizationUseCase Qcom.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest  BatteryStyleRepository Qcom.tqhit.battery.one.features.emoji.domain.use_case.SaveCustomizationUseCaseTest  CustomizationRepository Qcom.tqhit.battery.one.features.emoji.domain.use_case.SaveCustomizationUseCaseTest  SaveCustomizationUseCase Qcom.tqhit.battery.one.features.emoji.domain.use_case.SaveCustomizationUseCaseTest  CustomizeViewModel ;com.tqhit.battery.one.features.emoji.presentation.customize  CoreBatteryStatsProvider Rcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModelTest  CustomizeViewModel Rcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModelTest  LoadCustomizationUseCase Rcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModelTest  ResetCustomizationUseCase Rcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModelTest  Rule Rcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModelTest  SaveCustomizationUseCase Rcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModelTest  BatteryGalleryViewModel 9com.tqhit.battery.one.features.emoji.presentation.gallery  OptIn 9com.tqhit.battery.one.features.emoji.presentation.gallery  BatteryGalleryViewModel Ucom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest  CoreBatteryStatsProvider Ucom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest  GetBatteryStylesUseCase Ucom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest  Rule Ucom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest  BatteryStyleAdapter Acom.tqhit.battery.one.features.emoji.presentation.gallery.adapter  String Acom.tqhit.battery.one.features.emoji.presentation.gallery.adapter  Unit Acom.tqhit.battery.one.features.emoji.presentation.gallery.adapter  BatteryStyle Ycom.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest  BatteryStyleAdapter Ycom.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest  Context Ycom.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest  String Ycom.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest  Unit Ycom.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest  OptIn $com.tqhit.battery.one.features.stats  Context >com.tqhit.battery.one.features.stats.UnifiedBatteryServiceTest  CoreBatteryStatsProvider >com.tqhit.battery.one.features.stats.UnifiedBatteryServiceTest  'UnifiedBatteryNotificationServiceHelper >com.tqhit.battery.one.features.stats.UnifiedBatteryServiceTest  
AppRepository Tcom.tqhit.battery.one.features.stats.charge.StatsChargeViewModelPowerCalculationTest  $CalculateSimpleChargeEstimateUseCase Tcom.tqhit.battery.one.features.stats.charge.StatsChargeViewModelPowerCalculationTest  StatsChargeRepository Tcom.tqhit.battery.one.features.stats.charge.StatsChargeViewModelPowerCalculationTest  StatsChargeViewModel Tcom.tqhit.battery.one.features.stats.charge.StatsChargeViewModelPowerCalculationTest  $CalculateSimpleChargeEstimateUseCase 2com.tqhit.battery.one.features.stats.charge.domain  StatsChargeViewModel 8com.tqhit.battery.one.features.stats.charge.presentation  StatsChargeRepository 6com.tqhit.battery.one.features.stats.charge.repository  CoreBatteryStatsProvider 7com.tqhit.battery.one.features.stats.corebattery.domain  ScreenStateTimeTracker Jcom.tqhit.battery.one.features.stats.discharge.ScreenTimeGapValidationTest  CurrentSessionCache 4com.tqhit.battery.one.features.stats.discharge.cache  DischargeRatesCache 4com.tqhit.battery.one.features.stats.discharge.cache  ScreenStateReceiver 9com.tqhit.battery.one.features.stats.discharge.datasource  AppLifecycleManager 5com.tqhit.battery.one.features.stats.discharge.domain  DischargeCalculator 5com.tqhit.battery.one.features.stats.discharge.domain  DischargeRateCalculator 5com.tqhit.battery.one.features.stats.discharge.domain  FullSessionReEstimator 5com.tqhit.battery.one.features.stats.discharge.domain  GapEstimationCalculator 5com.tqhit.battery.one.features.stats.discharge.domain  OptIn 5com.tqhit.battery.one.features.stats.discharge.domain  ScreenStateTimeTracker 5com.tqhit.battery.one.features.stats.discharge.domain  ScreenTimeCalculator 5com.tqhit.battery.one.features.stats.discharge.domain  ScreenTimeValidationService 5com.tqhit.battery.one.features.stats.discharge.domain  SessionManager 5com.tqhit.battery.one.features.stats.discharge.domain  SessionMetricsCalculator 5com.tqhit.battery.one.features.stats.discharge.domain  
TimeConverter 5com.tqhit.battery.one.features.stats.discharge.domain  AppLifecycleManager Mcom.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManagerTest  LifecycleOwner Mcom.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManagerTest  DischargeCalculator Mcom.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculatorTest  ScreenStateTimeTracker Scom.tqhit.battery.one.features.stats.discharge.domain.EnhancedScreenTimeTrackerTest  DischargeRateCalculator Pcom.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimatorTest  DischargeRatesCache Pcom.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimatorTest  FullSessionReEstimator Pcom.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimatorTest  ScreenTimeCalculator Pcom.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimatorTest  
TimeConverter Pcom.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimatorTest  AppLifecycleManager Ucom.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationServiceTest  ScreenTimeValidationService Ucom.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationServiceTest  
TimeConverter Gcom.tqhit.battery.one.features.stats.discharge.domain.TimeConverterTest  FullSessionReEstimator :com.tqhit.battery.one.features.stats.discharge.integration  GapEstimationCalculator :com.tqhit.battery.one.features.stats.discharge.integration  OptIn :com.tqhit.battery.one.features.stats.discharge.integration  SessionManager :com.tqhit.battery.one.features.stats.discharge.integration  SessionMetricsCalculator :com.tqhit.battery.one.features.stats.discharge.integration  Context ^com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeEstimationIntegrationTest  CurrentSessionCache ^com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeEstimationIntegrationTest  DischargeSessionRepository ^com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeEstimationIntegrationTest  FullSessionReEstimator ^com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeEstimationIntegrationTest  GapEstimationCalculator ^com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeEstimationIntegrationTest  PowerManager ^com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeEstimationIntegrationTest  ScreenStateReceiver ^com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeEstimationIntegrationTest  SessionManager ^com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeEstimationIntegrationTest  SessionMetricsCalculator ^com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeEstimationIntegrationTest  AppLifecycleManager \com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeUiUpdateIntegrationTest  ScreenTimeValidationService \com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeUiUpdateIntegrationTest  DischargeSessionRepository 9com.tqhit.battery.one.features.stats.discharge.repository  FullSessionReEstimator 9com.tqhit.battery.one.features.stats.discharge.repository  GapEstimationCalculator 9com.tqhit.battery.one.features.stats.discharge.repository  OptIn 9com.tqhit.battery.one.features.stats.discharge.repository  SessionManager 9com.tqhit.battery.one.features.stats.discharge.repository  SessionMetricsCalculator 9com.tqhit.battery.one.features.stats.discharge.repository  Context Xcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepositoryTest  CurrentSessionCache Xcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepositoryTest  DischargeSessionRepository Xcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepositoryTest  FullSessionReEstimator Xcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepositoryTest  GapEstimationCalculator Xcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepositoryTest  PowerManager Xcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepositoryTest  ScreenStateReceiver Xcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepositoryTest  SessionManager Xcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepositoryTest  SessionMetricsCalculator Xcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepositoryTest  CalculateBatteryHealthUseCase 2com.tqhit.battery.one.features.stats.health.domain  CalculateBatteryHealthUseCase Tcom.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCaseTest  'UnifiedBatteryNotificationServiceHelper 2com.tqhit.battery.one.features.stats.notifications  
AppRepository  com.tqhit.battery.one.repository  Context ;com.tqhit.battery.one.utils.BackgroundPermissionManagerTest  PackageManager ;com.tqhit.battery.one.utils.BackgroundPermissionManagerTest  PowerManager ;com.tqhit.battery.one.utils.BackgroundPermissionManagerTest  SharedPreferences ;com.tqhit.battery.one.utils.BackgroundPermissionManagerTest  FullSessionReEstimator io.mockk  GapEstimationCalculator io.mockk  SessionManager io.mockk  SessionMetricsCalculator io.mockk  OptIn kotlin  String kotlin  Unit kotlin  ExperimentalCoroutinesApi kotlinx.coroutines  Assert 	org.junit  Rule 	org.junit  FullSessionReEstimator org.junit.Assert  GapEstimationCalculator org.junit.Assert  SessionManager org.junit.Assert  SessionMetricsCalculator org.junit.Assert  RunWith org.junit.runner  Config org.robolectric.annotation                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       