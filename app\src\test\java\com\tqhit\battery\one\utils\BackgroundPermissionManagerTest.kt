package com.tqhit.battery.one.utils

import android.content.Context
import android.content.SharedPreferences
import android.content.pm.PackageManager
import android.os.Build
import android.os.PowerManager
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

/**
 * Unit tests for BackgroundPermissionManager
 * Tests the core permission checking and request logic
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [Build.VERSION_CODES.M, Build.VERSION_CODES.TIRAMISU])
class BackgroundPermissionManagerTest {
    
    private lateinit var mockContext: Context
    private lateinit var mockPowerManager: PowerManager
    private lateinit var mockPackageManager: PackageManager
    private lateinit var mockSharedPreferences: SharedPreferences
    private lateinit var mockEditor: SharedPreferences.Editor
    
    @Before
    fun setUp() {
        mockContext = mockk(relaxed = true)
        mockPowerManager = mockk(relaxed = true)
        mockPackageManager = mockk(relaxed = true)
        mockSharedPreferences = mockk(relaxed = true)
        mockEditor = mockk(relaxed = true)

        every { mockContext.getSystemService(Context.POWER_SERVICE) } returns mockPowerManager
        every { mockContext.packageManager } returns mockPackageManager
        every { mockContext.packageName } returns "com.tqhit.battery.one"
        every { mockContext.getSharedPreferences(any(), any()) } returns mockSharedPreferences
        every { mockSharedPreferences.edit() } returns mockEditor
        every { mockEditor.putLong(any(), any()) } returns mockEditor
        every { mockEditor.putBoolean(any(), any()) } returns mockEditor
        every { mockEditor.remove(any()) } returns mockEditor
        every { mockEditor.apply() } returns Unit

        mockkStatic(Build.VERSION::class)
    }
    
    @After
    fun tearDown() {
        unmockkStatic(Build.VERSION::class)
    }
    
    @Test
    fun `isIgnoringBatteryOptimizations returns true when permission granted on Android M+`() {
        // Given
        every { Build.VERSION.SDK_INT } returns Build.VERSION_CODES.M
        every { mockPowerManager.isIgnoringBatteryOptimizations("com.tqhit.battery.one") } returns true
        
        // When
        val result = BackgroundPermissionManager.isIgnoringBatteryOptimizations(mockContext)
        
        // Then
        assertTrue("Should return true when battery optimization is ignored", result)
    }
    
    @Test
    fun `isIgnoringBatteryOptimizations returns false when permission not granted on Android M+`() {
        // Given
        every { Build.VERSION.SDK_INT } returns Build.VERSION_CODES.M
        every { mockPowerManager.isIgnoringBatteryOptimizations("com.tqhit.battery.one") } returns false
        
        // When
        val result = BackgroundPermissionManager.isIgnoringBatteryOptimizations(mockContext)
        
        // Then
        assertFalse("Should return false when battery optimization is not ignored", result)
    }
    
    @Test
    fun `isIgnoringBatteryOptimizations returns true for Android versions below M`() {
        // Given
        every { Build.VERSION.SDK_INT } returns Build.VERSION_CODES.LOLLIPOP
        
        // When
        val result = BackgroundPermissionManager.isIgnoringBatteryOptimizations(mockContext)
        
        // Then
        assertTrue("Should return true for Android versions below M", result)
    }
    
    @Test
    fun `shouldShowBackgroundPermissionDialog returns true when permission not granted`() {
        // Given
        every { Build.VERSION.SDK_INT } returns Build.VERSION_CODES.M
        every { mockPowerManager.isIgnoringBatteryOptimizations("com.tqhit.battery.one") } returns false
        
        // When
        val result = BackgroundPermissionManager.shouldShowBackgroundPermissionDialog(mockContext)
        
        // Then
        assertTrue("Should show dialog when permission not granted", result)
    }
    
    @Test
    fun `shouldShowBackgroundPermissionDialog returns false when permission granted`() {
        // Given
        every { Build.VERSION.SDK_INT } returns Build.VERSION_CODES.M
        every { mockPowerManager.isIgnoringBatteryOptimizations("com.tqhit.battery.one") } returns true
        
        // When
        val result = BackgroundPermissionManager.shouldShowBackgroundPermissionDialog(mockContext)
        
        // Then
        assertFalse("Should not show dialog when permission already granted", result)
    }
    
    @Test
    fun `shouldShowBackgroundPermissionDialog returns false for Android versions below M`() {
        // Given
        every { Build.VERSION.SDK_INT } returns Build.VERSION_CODES.LOLLIPOP

        // When
        val result = BackgroundPermissionManager.shouldShowBackgroundPermissionDialog(mockContext)

        // Then
        assertFalse("Should not show dialog for Android versions below M", result)
    }

    // Rate Limiting Tests

    @Test
    fun `isInCooldownPeriod returns false when no dismissal recorded`() {
        // Given
        every { mockSharedPreferences.getLong("last_dismissal_time", 0L) } returns 0L

        // When
        val result = BackgroundPermissionManager.isInCooldownPeriod(mockContext)

        // Then
        assertFalse("Should not be in cooldown when no dismissal recorded", result)
    }

    @Test
    fun `isInCooldownPeriod returns true when within 30 minute window`() {
        // Given
        val currentTime = System.currentTimeMillis()
        val dismissalTime = currentTime - (15 * 60 * 1000L) // 15 minutes ago
        every { mockSharedPreferences.getLong("last_dismissal_time", 0L) } returns dismissalTime

        // When
        val result = BackgroundPermissionManager.isInCooldownPeriod(mockContext)

        // Then
        assertTrue("Should be in cooldown when within 30 minute window", result)
    }

    @Test
    fun `isInCooldownPeriod returns false when beyond 30 minute window`() {
        // Given
        val currentTime = System.currentTimeMillis()
        val dismissalTime = currentTime - (35 * 60 * 1000L) // 35 minutes ago
        every { mockSharedPreferences.getLong("last_dismissal_time", 0L) } returns dismissalTime

        // When
        val result = BackgroundPermissionManager.isInCooldownPeriod(mockContext)

        // Then
        assertFalse("Should not be in cooldown when beyond 30 minute window", result)
    }

    @Test
    fun `recordDialogDismissal stores current timestamp`() {
        // When
        BackgroundPermissionManager.recordDialogDismissal(mockContext)

        // Then
        verify { mockEditor.putLong("last_dismissal_time", any()) }
        verify { mockEditor.apply() }
    }

    @Test
    fun `getRemainingCooldownTime returns correct remaining time`() {
        // Given
        val currentTime = System.currentTimeMillis()
        val dismissalTime = currentTime - (10 * 60 * 1000L) // 10 minutes ago
        every { mockSharedPreferences.getLong("last_dismissal_time", 0L) } returns dismissalTime

        // When
        val remainingTime = BackgroundPermissionManager.getRemainingCooldownTime(mockContext)

        // Then
        val expectedRemaining = 20 * 60 * 1000L // 20 minutes remaining
        val tolerance = 1000L // 1 second tolerance for test execution time
        assertTrue("Remaining time should be approximately 20 minutes",
            Math.abs(remainingTime - expectedRemaining) < tolerance)
    }

    @Test
    fun `getRemainingCooldownTime returns zero when no dismissal recorded`() {
        // Given
        every { mockSharedPreferences.getLong("last_dismissal_time", 0L) } returns 0L

        // When
        val remainingTime = BackgroundPermissionManager.getRemainingCooldownTime(mockContext)

        // Then
        assertEquals("Should return zero when no dismissal recorded", 0L, remainingTime)
    }

    @Test
    fun `shouldShowBackgroundPermissionDialog respects cooldown period`() {
        // Given
        every { Build.VERSION.SDK_INT } returns Build.VERSION_CODES.M
        every { mockPowerManager.isIgnoringBatteryOptimizations("com.tqhit.battery.one") } returns false
        every { mockSharedPreferences.getBoolean("last_permission_status", false) } returns false

        val currentTime = System.currentTimeMillis()
        val dismissalTime = currentTime - (15 * 60 * 1000L) // 15 minutes ago (within cooldown)
        every { mockSharedPreferences.getLong("last_dismissal_time", 0L) } returns dismissalTime

        // When
        val result = BackgroundPermissionManager.shouldShowBackgroundPermissionDialog(mockContext)

        // Then
        assertFalse("Should not show dialog when in cooldown period", result)
    }

    @Test
    fun `shouldShowBackgroundPermissionDialog shows dialog after cooldown expires`() {
        // Given
        every { Build.VERSION.SDK_INT } returns Build.VERSION_CODES.M
        every { mockPowerManager.isIgnoringBatteryOptimizations("com.tqhit.battery.one") } returns false
        every { mockSharedPreferences.getBoolean("last_permission_status", false) } returns false

        val currentTime = System.currentTimeMillis()
        val dismissalTime = currentTime - (35 * 60 * 1000L) // 35 minutes ago (beyond cooldown)
        every { mockSharedPreferences.getLong("last_dismissal_time", 0L) } returns dismissalTime

        // When
        val result = BackgroundPermissionManager.shouldShowBackgroundPermissionDialog(mockContext)

        // Then
        assertTrue("Should show dialog when cooldown period has expired", result)
    }
}
