package com.tqhit.battery.one.activity.password

import android.app.NotificationManager
import android.app.PendingIntent
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.media.MediaPlayer
import android.os.Handler
import android.os.Looper
import android.view.KeyEvent
import android.widget.Toast
import androidx.core.app.NotificationCompat
import androidx.core.app.TaskStackBuilder
import com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity
import com.tqhit.battery.one.R
import com.tqhit.battery.one.databinding.ActivityEnterPasswordBinding
import com.tqhit.battery.one.repository.AppRepository
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class EnterPasswordActivity : AdLibBaseActivity<ActivityEnterPasswordBinding>() {
    override val binding by lazy { ActivityEnterPasswordBinding.inflate(layoutInflater) }
    @Inject lateinit var appRepository: AppRepository

    private var mediaPlayer: MediaPlayer? = null
    private var handler: Handler? = null
    private var playRingtoneRunnable: Runnable? = null
    private var stopRingtoneRunnable: Runnable? = null
    private var isUnlocked = false

    override fun setupData() {
        super.setupData()
        setupNotificationChannel()
        setupPasswordInput()
        startAntiThiefFlow()
    }

    private fun setupPasswordInput() {
        binding.confirmChangeCapacity.setOnClickListener {
            checkPassword()
        }
        binding.textInputEdit.setOnEditorActionListener { v, actionId, event ->
            if (event != null && event.keyCode == KeyEvent.KEYCODE_ENTER) {
                checkPassword()
                true
            } else {
                false
            }
        }
    }

    private fun checkPassword() {
        val input = binding.textInputEdit.text?.toString() ?: ""
        val correct = appRepository.getAntiThiefPassword()
        if (input == correct) {
            isUnlocked = true
            appRepository.setAntiThiefAlertActive(false)
            stopRingtone()
            cancelNotification()
            finish()
        } else {
            Toast.makeText(this, getString(R.string.incorrect_password), Toast.LENGTH_SHORT).show()
        }
    }

    private fun startAntiThiefFlow() {
        showNotification()
        playRingtone()
        handler = Handler(Looper.getMainLooper())
        // Stop ringtone after 1 minute
        stopRingtoneRunnable = Runnable { stopRingtone() }
        handler?.postDelayed(stopRingtoneRunnable!!, 60000)
    }

    private fun playRingtone() {
        if (appRepository.isAntiThiefSoundEnabled()) {
            mediaPlayer = MediaPlayer.create(this, R.raw.ringtone)
            mediaPlayer?.isLooping = true
            mediaPlayer?.start()
        }
    }

    private fun stopRingtone() {
        mediaPlayer?.stop()
        mediaPlayer?.release()
        mediaPlayer = null
    }

    private fun showNotification() {
        val intent = Intent(this, EnterPasswordActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        val pendingIntent = TaskStackBuilder.create(this).run {
            addNextIntentWithParentStack(intent)
            getPendingIntent(0, PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE)
        }
        val notification = NotificationCompat.Builder(this, "anti_thief_channel")
            .setSmallIcon(R.mipmap.ic_launcher)
            .setContentTitle(getString(R.string.anti_thief))
            .setContentText(getString(R.string.enter_password))
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setAutoCancel(true)
            .setContentIntent(pendingIntent)
            .build()
        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(ANTI_THIEF_NOTIFICATION_ID, notification)
    }

    private fun cancelNotification() {
        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.cancel(ANTI_THIEF_NOTIFICATION_ID)
    }

    private fun setupNotificationChannel() {
        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            val channel = android.app.NotificationChannel(
                "anti_thief_channel",
                getString(R.string.anti_thief),
                NotificationManager.IMPORTANCE_HIGH
            )
            notificationManager.createNotificationChannel(channel)
        }
    }

    override fun onResume() {
        super.onResume()
        // If unlocked, finish immediately
        if (isUnlocked) finish()
    }

    override fun onDestroy() {
        super.onDestroy()
        handler?.removeCallbacksAndMessages(null)
        stopRingtone()
    }

    override fun onBackPressed() {
        // Prevent back if not unlocked
        if (!isUnlocked) {
            moveTaskToBack(true)
        } else {
            super.onBackPressed()
        }
    }

    companion object {
        private const val ANTI_THIEF_NOTIFICATION_ID = 1001
    }
}