package com.tqhit.battery.one.features.stats.discharge.domain

import androidx.lifecycle.LifecycleOwner
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.every
import androidx.lifecycle.ProcessLifecycleOwner
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.runTest
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test

/**
 * Unit tests for AppLifecycleManager
 */
@ExperimentalCoroutinesApi
class AppLifecycleManagerTest {
    
    private lateinit var appLifecycleManager: AppLifecycleManager
    private lateinit var mockOwner: LifecycleOwner
    
    @Before
    fun setUp() {
        // Mock ProcessLifecycleOwner to prevent actual lifecycle registration
        mockkStatic(ProcessLifecycleOwner::class)
        val mockProcessLifecycleOwner = mockk<ProcessLifecycleOwner>(relaxed = true)
        every { ProcessLifecycleOwner.get() } returns mockProcessLifecycleOwner

        appLifecycleManager = AppLifecycleManager()
        mockOwner = mockk()
    }
    
    @Test
    fun `initial state is FOREGROUND with fragment inactive`() = runTest {
        // Assert
        assertEquals(AppState.FOREGROUND, appLifecycleManager.appState.value)
        assertFalse(appLifecycleManager.isDischargeFragmentActive.value)
    }
    
    @Test
    fun `onStart sets app state to FOREGROUND`() = runTest {
        // Arrange - first set to background
        appLifecycleManager.onStop(mockOwner)
        assertEquals(AppState.BACKGROUND, appLifecycleManager.appState.value)
        
        // Act
        appLifecycleManager.onStart(mockOwner)
        
        // Assert
        assertEquals(AppState.FOREGROUND, appLifecycleManager.appState.value)
    }
    
    @Test
    fun `onStop sets app state to BACKGROUND`() = runTest {
        // Arrange - ensure we start in foreground
        assertEquals(AppState.FOREGROUND, appLifecycleManager.appState.value)
        
        // Act
        appLifecycleManager.onStop(mockOwner)
        
        // Assert
        assertEquals(AppState.BACKGROUND, appLifecycleManager.appState.value)
    }
    
    @Test
    fun `setDischargeFragmentActive updates fragment state`() = runTest {
        // Arrange
        assertFalse(appLifecycleManager.isDischargeFragmentActive.value)
        
        // Act
        appLifecycleManager.setDischargeFragmentActive(true)
        
        // Assert
        assertTrue(appLifecycleManager.isDischargeFragmentActive.value)
        
        // Act again
        appLifecycleManager.setDischargeFragmentActive(false)
        
        // Assert
        assertFalse(appLifecycleManager.isDischargeFragmentActive.value)
    }
    
    @Test
    fun `shouldTriggerUiUpdate returns true when app in foreground and fragment inactive`() = runTest {
        // Arrange
        appLifecycleManager.onStart(mockOwner) // Ensure foreground
        appLifecycleManager.setDischargeFragmentActive(false) // Fragment inactive
        
        // Act
        val result = appLifecycleManager.shouldTriggerUiUpdate()
        
        // Assert
        assertTrue(result)
    }
    
    @Test
    fun `shouldTriggerUiUpdate returns false when app in background`() = runTest {
        // Arrange
        appLifecycleManager.onStop(mockOwner) // Set to background
        appLifecycleManager.setDischargeFragmentActive(false) // Fragment inactive
        
        // Act
        val result = appLifecycleManager.shouldTriggerUiUpdate()
        
        // Assert
        assertFalse(result)
    }
    
    @Test
    fun `shouldTriggerUiUpdate returns true when recently resumed regardless of fragment state`() = runTest {
        // Arrange
        appLifecycleManager.onStop(mockOwner) // Go to background
        appLifecycleManager.setDischargeFragmentActive(true) // Fragment active
        
        // Act - come back to foreground (simulates recent resume)
        appLifecycleManager.onStart(mockOwner)
        val result = appLifecycleManager.shouldTriggerUiUpdate()
        
        // Assert
        assertTrue(result) // Should be true even though fragment is active due to recent resume
    }
    
    @Test
    fun `shouldTriggerUiUpdate returns false when app foreground and fragment active and not recently resumed`() = runTest {
        // Arrange
        appLifecycleManager.onStart(mockOwner) // Ensure foreground
        appLifecycleManager.setDischargeFragmentActive(true) // Fragment active
        
        // Simulate time passing (more than 5 seconds)
        Thread.sleep(100) // Small delay to simulate time passing
        
        // Act
        val result = appLifecycleManager.shouldTriggerUiUpdate()
        
        // Assert
        assertFalse(result) // Should be false when fragment is active and not recently resumed
    }
    
    @Test
    fun `getCurrentStateInfo returns correct state information`() = runTest {
        // Arrange
        appLifecycleManager.onStart(mockOwner) // Foreground
        appLifecycleManager.setDischargeFragmentActive(true) // Active
        
        // Act
        val stateInfo = appLifecycleManager.getCurrentStateInfo()
        
        // Assert
        assertEquals("App: FOREGROUND, Fragment: ACTIVE", stateInfo)
        
        // Change states
        appLifecycleManager.onStop(mockOwner) // Background
        appLifecycleManager.setDischargeFragmentActive(false) // Inactive
        
        // Act
        val newStateInfo = appLifecycleManager.getCurrentStateInfo()
        
        // Assert
        assertEquals("App: BACKGROUND, Fragment: INACTIVE", newStateInfo)
    }
    
    @Test
    fun `app state transitions work correctly`() = runTest {
        // Start in foreground
        assertEquals(AppState.FOREGROUND, appLifecycleManager.appState.value)
        
        // Go to background
        appLifecycleManager.onStop(mockOwner)
        assertEquals(AppState.BACKGROUND, appLifecycleManager.appState.value)
        
        // Return to foreground
        appLifecycleManager.onStart(mockOwner)
        assertEquals(AppState.FOREGROUND, appLifecycleManager.appState.value)
        
        // Multiple transitions
        appLifecycleManager.onStop(mockOwner)
        assertEquals(AppState.BACKGROUND, appLifecycleManager.appState.value)
        
        appLifecycleManager.onStart(mockOwner)
        assertEquals(AppState.FOREGROUND, appLifecycleManager.appState.value)
    }
    
    @Test
    fun `fragment state transitions work correctly`() = runTest {
        // Start inactive
        assertFalse(appLifecycleManager.isDischargeFragmentActive.value)
        
        // Activate
        appLifecycleManager.setDischargeFragmentActive(true)
        assertTrue(appLifecycleManager.isDischargeFragmentActive.value)
        
        // Deactivate
        appLifecycleManager.setDischargeFragmentActive(false)
        assertFalse(appLifecycleManager.isDischargeFragmentActive.value)
        
        // Multiple transitions
        appLifecycleManager.setDischargeFragmentActive(true)
        assertTrue(appLifecycleManager.isDischargeFragmentActive.value)
        
        appLifecycleManager.setDischargeFragmentActive(false)
        assertFalse(appLifecycleManager.isDischargeFragmentActive.value)
    }
}
