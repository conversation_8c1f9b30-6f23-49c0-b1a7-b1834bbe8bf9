package com.tqhit.battery.one.features.emoji.domain.use_case;

import com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class ResetCustomizationUseCase_Factory implements Factory<ResetCustomizationUseCase> {
  private final Provider<CustomizationRepository> customizationRepositoryProvider;

  public ResetCustomizationUseCase_Factory(
      Provider<CustomizationRepository> customizationRepositoryProvider) {
    this.customizationRepositoryProvider = customizationRepositoryProvider;
  }

  @Override
  public ResetCustomizationUseCase get() {
    return newInstance(customizationRepositoryProvider.get());
  }

  public static ResetCustomizationUseCase_Factory create(
      Provider<CustomizationRepository> customizationRepositoryProvider) {
    return new ResetCustomizationUseCase_Factory(customizationRepositoryProvider);
  }

  public static ResetCustomizationUseCase newInstance(
      CustomizationRepository customizationRepository) {
    return new ResetCustomizationUseCase(customizationRepository);
  }
}
