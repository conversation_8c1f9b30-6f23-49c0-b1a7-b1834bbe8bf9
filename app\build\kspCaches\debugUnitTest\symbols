{"src\\test\\java\\com\\tqhit\\battery\\one\\features\\emoji\\presentation\\gallery\\BatteryGalleryViewModelTest.kt": ["ToggleShowOnlyPremium event filters premium styles:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest", "RefreshData event triggers refresh:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest", "instantTaskExecutorRule:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest", "tearDown:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest", "<init>:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest", "ToggleShowOnlyFree event filters free styles:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest", "ClearAllFilters event resets all filters:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest", "error handling works correctly:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest", "initial state is correct:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest", "FilterByCategory event filters styles correctly:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest", "DismissError event clears error message:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest", "BatteryGalleryViewModelTest:com.tqhit.battery.one.features.emoji.presentation.gallery", "setUp:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest", "LoadInitialData event loads styles:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest", "SearchStyles event filters by search query:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest", "multiple filter combinations work correctly:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest", "SelectStyle event updates selected style:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest", "battery state changes are handled correctly:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest", "ToggleShowOnlyPopular event filters popular styles:com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest"], "src\\test\\java\\com\\tqhit\\battery\\one\\features\\emoji\\domain\\use_case\\GetBatteryStylesUseCaseTest.kt": ["getPopularStyles returns popular styles:com.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCaseTest", "setUp:com.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCaseTest", "hasCachedData returns false on exception:com.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCaseTest", "getLoadingStateFlow returns repository loading flow:com.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCaseTest", "getPremiumStyles returns premium styles:com.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCaseTest", "refreshStyles returns success from repository:com.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCaseTest", "getAllStylesFlow returns repository flow:com.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCaseTest", "getAllStyles with force refresh calls repository with correct parameter:com.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCaseTest", "getAllStyles returns empty list on exception:com.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCaseTest", "refreshStyles returns false on exception:com.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCaseTest", "GetBatteryStylesUseCaseTest:com.tqhit.battery.one.features.emoji.domain.use_case", "getStylesByCategory returns filtered styles:com.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCaseTest", "searchStyles returns matching styles:com.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCaseTest", "getCurrentStyles returns empty list on exception:com.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCaseTest", "getAllStyles returns styles from repository:com.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCaseTest", "hasCachedData returns repository result:com.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCaseTest", "getFreeStyles returns free styles:com.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCaseTest", "getStylesByCategory returns empty list on exception:com.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCaseTest", "getStyleById returns correct style:com.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCaseTest", "getCurrentStyles returns cached styles:com.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCaseTest", "getStyleById returns null when not found:com.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCaseTest", "<init>:com.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCaseTest"], "src\\test\\java\\com\\tqhit\\battery\\one\\features\\emoji\\domain\\model\\BatteryStyleConfigTest.kt": ["test validated clamps emoji scale to maximum:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfigTest", "test isValid returns false for font size above maximum:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfigTest", "test validated clamps font size to minimum:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfigTest", "<init>:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfigTest", "test custom battery style config creation:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfigTest", "test isValid returns true for minimum valid values:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfigTest", "test validated preserves valid values:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfigTest", "test default battery style config creation:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfigTest", "test isValid returns true for maximum valid values:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfigTest", "test isValid returns false for font size below minimum:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfigTest", "test validated clamps emoji scale to minimum:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfigTest", "test validated clamps font size to maximum:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfigTest", "test validated clamps both values when both are invalid:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfigTest", "test createDefault returns valid default config:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfigTest", "BatteryStyleConfigTest:com.tqhit.battery.one.features.emoji.domain.model", "test isValid returns false for emoji scale above maximum:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfigTest", "test isValid returns false for emoji scale below minimum:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfigTest", "test isValid returns true for valid config:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfigTest"], "src\\test\\java\\com\\tqhit\\battery\\one\\ExampleUnitTest.kt": ["ExampleUnitTest:com.tqhit.battery.one", "addition_isCorrect:com.tqhit.battery.one.ExampleUnitTest", "<init>:com.tqhit.battery.one.ExampleUnitTest"], "src\\test\\java\\com\\tqhit\\battery\\one\\features\\stats\\UnifiedBatteryServiceTest.kt": ["UnifiedBatteryServiceTest:com.tqhit.battery.one.features.stats", "test CoreBatteryStatsProvider getCurrentStatus returns latest status:com.tqhit.battery.one.features.stats.UnifiedBatteryServiceTest", "<init>:com.tqhit.battery.one.features.stats.UnifiedBatteryServiceTest", "test CoreBatteryStatsProvider provides consistent data:com.tqhit.battery.one.features.stats.UnifiedBatteryServiceTest", "test CoreBatteryStatsProvider handles charging state changes:com.tqhit.battery.one.features.stats.UnifiedBatteryServiceTest", "test service helper is properly initialized:com.tqhit.battery.one.features.stats.UnifiedBatteryServiceTest", "setup:com.tqhit.battery.one.features.stats.UnifiedBatteryServiceTest", "test CoreBatteryStatsProvider handles null initial state:com.tqhit.battery.one.features.stats.UnifiedBatteryServiceTest", "test battery percentage boundaries:com.tqhit.battery.one.features.stats.UnifiedBatteryServiceTest"], "src\\test\\java\\com\\tqhit\\battery\\one\\features\\emoji\\domain\\model\\BatteryStyleCategoryTest.kt": ["<init>:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest", "test findByDisplayName with case insensitive match:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest", "BatteryStyleCategoryTest:com.tqhit.battery.one.features.emoji.domain.model", "test getAllSorted returns categories in sort order:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest", "test fromString with display name fallback:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest", "test fromString with blank string returns default:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest", "test fromString with invalid value returns default:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest", "test HEART category properties:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest", "test all categories have unique sort orders:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest", "test CHARACTER category properties:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest", "test HOT category properties:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest", "test fromString with null returns default:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest", "test isFeatured returns true only for HOT category:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest", "test all categories have required properties:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest", "test getDefault returns CHARACTER:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest", "test getMainFilterCategories returns expected categories:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest", "test fromString with empty string returns default:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest", "test getDisplayText returns formatted text:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest", "test fromString with lowercase enum name:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest", "test findByDisplayName with exact match:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest", "test fromString with valid enum name:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest", "test findByDisplayName with non-matching name returns null:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest"], "src\\test\\java\\com\\tqhit\\battery\\one\\features\\emoji\\data\\repository\\CustomizationRepositoryImplTest.kt": ["userPreferencesFlow should expose DataStore flow:com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest", "CustomizationRepositoryImplTest:com.tqhit.battery.one.features.emoji.data.repository", "<init>:com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest", "customizationConfigFlow should expose DataStore flow:com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest", "validateAndFixConfiguration should detect and fix issues:com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest", "resetToDefaults should reset everything when not preserving preferences:com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest", "recordStyleUsage should update usage history:com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest", "validateAndFixConfiguration should pass for valid configuration:com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest", "getCurrentCustomizationConfig should return current config:com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest", "importCustomizationData should fail for invalid data format:com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest", "saveUserPreferences should save preferences:com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest", "saveCustomizationConfig should validate and save config:com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest", "resetToDefaults should reset with preserved preferences:com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest", "clearAllData should clear DataStore:com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest", "getCurrentUserCustomization should return current customization:com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest", "saveCustomizationConfig should handle DataStore errors:com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest", "getCurrentCustomizationConfig should return default on error:com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest", "saveUserCustomization should validate and save customization:com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest", "updatePermissionStates should update permissions:com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest", "exportCustomizationData should serialize current data:com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest", "importCustomizationData should validate and import data:com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest", "userCustomizationFlow should expose DataStore flow:com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest", "setFeatureEnabled should update feature state:com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest", "importCustomizationData should fail for invalid version:com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest", "setUp:com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImplTest"], "src\\test\\java\\com\\tqhit\\battery\\one\\features\\stats\\health\\domain\\CalculateBatteryHealthUseCaseTest.kt": ["isFullChargeCycle with valid cycle returns true:com.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCaseTest", "calculateCumulativeHealth with 1000 sessions returns 0 percent (clamped):com.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCaseTest", "isFullChargeCycle with invalid start percentage returns false:com.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCaseTest", "calculateEffectiveCapacity with 0 percent health returns 0 capacity:com.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCaseTest", "calculateEffectiveCapacity with 80 percent health returns 80 percent of capacity:com.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCaseTest", "calculateCumulativeHealth with 250 sessions returns 60 percent:com.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCaseTest", "calculateCumulativeHealth with zero sessions returns 100 percent:com.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCaseTest", "<init>:com.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCaseTest", "isFullChargeCycle with invalid end percentage returns false:com.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCaseTest", "estimateRemainingLifespan with low health returns zero:com.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCaseTest", "CalculateBatteryHealthUseCaseTest:com.tqhit.battery.one.features.stats.health.domain", "calculateSingularHealth always returns 0 percent (no data):com.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCaseTest", "calculateHealthStatus with singular mode returns zero health:com.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCaseTest", "setUp:com.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCaseTest", "estimateRemainingLifespan with valid parameters returns reasonable estimate:com.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCaseTest", "calculateCumulativeHealth with 500 sessions returns 20 percent:com.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCaseTest", "calculateEffectiveCapacity with 100 percent health returns full capacity:com.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCaseTest", "calculateDegradationRate with valid parameters returns correct rate:com.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCaseTest", "calculateDegradationRate with zero sessions returns zero:com.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCaseTest", "calculateHealthStatus with cumulative mode returns correct health status:com.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCaseTest"], "src\\test\\java\\com\\tqhit\\battery\\one\\features\\emoji\\data\\repository\\BatteryStyleRepositoryImplTest.kt": ["test isLoadingFlow emits correct loading states:com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest", "test getAllStyles fetches from remote config successfully:com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest", "test getPremiumStyles filters correctly:com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest", "test searchStyles filters by name correctly:com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest", "test getStyleById returns correct style:com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest", "test getFreeStyles filters correctly:com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest", "setUp:com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest", "test getAllStyles falls back to local JSON when remote config fails:com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest", "test getAllStyles returns empty list when both remote and local fail:com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest", "test getPopularStyles filters correctly:com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest", "test getStylesByCategory filters correctly:com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest", "<init>:com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest", "test clearCache clears all cached data:com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest", "test getCurrentStyles returns cached data:com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest", "test batteryStylesFlow emits updated data:com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest", "BatteryStyleRepositoryImplTest:com.tqhit.battery.one.features.emoji.data.repository", "test hasCachedData returns correct state:com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest"], "src\\test\\java\\com\\tqhit\\battery\\one\\features\\emoji\\presentation\\customize\\CustomizeViewModelTest.kt": ["handleEvent TogglePercentageVisibility should update style config:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModelTest", "battery level changes should update preview when live preview enabled:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModelTest", "handleEvent ApplyAndEnable should save and enable feature:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModelTest", "instantTaskExecutorRule:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModelTest", "handleEvent OnScreenEnter should load initial data:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModelTest", "handleEvent OpenColorPicker should show color picker:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModelTest", "<init>:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModelTest", "handleEvent ChangePercentageFontSize should clamp values:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModelTest", "handleEvent SelectBatteryContainer should update selection:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModelTest", "handleEvent ChangePreviewBatteryLevel should clamp values:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModelTest", "save configuration failure should show error:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModelTest", "handleEvent ConfirmReset should reset configuration:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModelTest", "initial state should be loading:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModelTest", "CustomizeViewModelTest:com.tqhit.battery.one.features.emoji.presentation.customize", "handleEvent ChangePercentageColor should update color:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModelTest", "handleEvent CloseColorPicker should hide color picker:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModelTest", "handleEvent ChangePreviewBatteryLevel should update preview level:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModelTest", "handleEvent DismissError should clear errors:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModelTest", "handleEvent ChangeEmojiSize should clamp values:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModelTest", "handleEvent SaveConfiguration should save current config:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModelTest", "handleEvent SelectEmojiCharacter should update selection:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModelTest", "tearDown:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModelTest", "handleEvent ToggleEmojiVisibility should update style config:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModelTest", "handleEvent ResetToDefaults should show confirmation first:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModelTest", "setUp:com.tqhit.battery.one.features.emoji.presentation.customize.CustomizeViewModelTest"], "src\\test\\java\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\domain\\ScreenTimeValidationServiceTest.kt": ["validateScreenTimes applies proportional scaling for significant excess:com.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationServiceTest", "setUp:com.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationServiceTest", "shouldTriggerUiUpdate delegates to AppLifecycleManager:com.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationServiceTest", "ScreenTimeValidationServiceTest:com.tqhit.battery.one.features.stats.discharge.domain", "validateScreenTimes handles zero screen OFF time correctly:com.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationServiceTest", "validateScreenTimes corrects OFF time when gap exceeds tolerance:com.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationServiceTest", "getAppStateInfo delegates to AppLifecycleManager:com.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationServiceTest", "validateScreenTimes returns NoValidationNeeded when session is null:com.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationServiceTest", "validateScreenTimes returns Valid when times are within acceptable range:com.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationServiceTest", "<init>:com.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationServiceTest", "validateScreenTimes returns NoValidationNeeded when session is inactive:com.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationServiceTest", "validateScreenTimes forces correction when shouldForceCorrection is true:com.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationServiceTest"], "src\\test\\java\\com\\tqhit\\battery\\one\\features\\emoji\\Phase0IntegrationTest.kt": ["Phase 0 - emoji module structure is properly created:com.tqhit.battery.one.features.emoji.Phase0IntegrationTest", "Phase 0 - module follows stats architecture pattern:com.tqhit.battery.one.features.emoji.Phase0IntegrationTest", "Phase0IntegrationTest:com.tqhit.battery.one.features.emoji", "Phase 0 - module has correct Hilt annotations:com.tqhit.battery.one.features.emoji.Phase0IntegrationTest", "Phase 0 - directory structure verification:com.tqhit.battery.one.features.emoji.Phase0IntegrationTest", "<init>:com.tqhit.battery.one.features.emoji.Phase0IntegrationTest"], "src\\test\\java\\com\\tqhit\\battery\\one\\features\\emoji\\presentation\\gallery\\adapter\\BatteryStyleAdapterTest.kt": ["action button is visible for premium styles:com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest", "style long click triggers callback:com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest", "action button is hidden for free styles:com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest", "BatteryStyleAdapterTest:com.tqhit.battery.one.features.emoji.presentation.gallery.adapter", "view holder binds style data correctly:com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest", "adapter handles empty list correctly:com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest", "<init>:com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest", "adapter returns correct item at position:com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest", "premium badge is visible for premium styles:com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest", "adapter creates correct number of view holders:com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest", "style click triggers callback:com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest", "category text includes premium status:com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest", "setUp:com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest", "adapter handles list updates correctly:com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest", "premium badge is hidden for free styles:com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest", "popular badge is visible for popular styles:com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest", "popular badge is hidden for non-popular styles:com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest", "premium unlock click triggers callback:com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest"], "src\\test\\java\\com\\tqhit\\battery\\one\\features\\emoji\\domain\\model\\BatteryStyleTest.kt": ["test matchesSearch with matching name:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleTest", "test matchesSearch with non-matching query:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleTest", "test matchesSearch with empty query returns true:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleTest", "test getPreviewId generates unique identifier:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleTest", "test createDefault returns valid default style:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleTest", "<init>:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleTest", "test isValid returns false for empty emoji image URL:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleTest", "test isValid returns false for empty battery image URL:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleTest", "test isValid returns true for valid style:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleTest", "test battery style with default values:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleTest", "test valid battery style creation:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleTest", "BatteryStyleTest:com.tqhit.battery.one.features.emoji.domain.model", "test matchesSearch with matching category:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleTest", "test isValid returns false for empty id:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleTest", "test isValid returns false for blank name:com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleTest"], "src\\test\\java\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\domain\\TimeConverterTest.kt": ["hoursToMillis converts correctly:com.tqhit.battery.one.features.stats.discharge.domain.TimeConverterTest", "formatMillisToHoursMinutesSeconds returns 0s for zero or negative input:com.tqhit.battery.one.features.stats.discharge.domain.TimeConverterTest", "setUp:com.tqhit.battery.one.features.stats.discharge.domain.TimeConverterTest", "formatMillisToHoursMinutesSeconds formats minutes and seconds only:com.tqhit.battery.one.features.stats.discharge.domain.TimeConverterTest", "TimeConverterTest:com.tqhit.battery.one.features.stats.discharge.domain", "formatMillisToHoursMinutesSeconds formats seconds only:com.tqhit.battery.one.features.stats.discharge.domain.TimeConverterTest", "formatMillisToMinutes formats correctly:com.tqhit.battery.one.features.stats.discharge.domain.TimeConverterTest", "formatMillisToHoursMinutes formats hours and minutes correctly:com.tqhit.battery.one.features.stats.discharge.domain.TimeConverterTest", "formatMillisToHoursMinutesSeconds formats full time correctly:com.tqhit.battery.one.features.stats.discharge.domain.TimeConverterTest", "millisToHours converts correctly:com.tqhit.battery.one.features.stats.discharge.domain.TimeConverterTest", "formatMillisToHoursMinutes returns 0m for zero or negative input:com.tqhit.battery.one.features.stats.discharge.domain.TimeConverterTest", "formatMillisToHoursMinutes formats minutes only when less than hour:com.tqhit.battery.one.features.stats.discharge.domain.TimeConverterTest", "<init>:com.tqhit.battery.one.features.stats.discharge.domain.TimeConverterTest"], "src\\test\\java\\com\\tqhit\\battery\\one\\features\\stats\\charge\\PowerCalculationManualTest.kt": ["testSessionStatisticsCalculation:com.tqhit.battery.one.features.stats.charge.PowerCalculationManualTest", "testPowerCalculationFormula:com.tqhit.battery.one.features.stats.charge.PowerCalculationManualTest", "<init>:com.tqhit.battery.one.features.stats.charge.PowerCalculationManualTest", "PowerCalculationManualTest:com.tqhit.battery.one.features.stats.charge"], "src\\test\\java\\com\\tqhit\\battery\\one\\features\\stats\\charge\\StatsChargeViewModelPowerCalculationTest.kt": ["test calculatePower with zero current:com.tqhit.battery.one.features.stats.charge.StatsChargeViewModelPowerCalculationTest", "StatsChargeViewModelPowerCalculationTest:com.tqhit.battery.one.features.stats.charge", "test calculatePower with negative current (discharging):com.tqhit.battery.one.features.stats.charge.StatsChargeViewModelPowerCalculationTest", "test formatPower with watts:com.tqhit.battery.one.features.stats.charge.StatsChargeViewModelPowerCalculationTest", "test calculatePower with low power values:com.tqhit.battery.one.features.stats.charge.StatsChargeViewModelPowerCalculationTest", "test calculatePower with typical charging values:com.tqhit.battery.one.features.stats.charge.StatsChargeViewModelPowerCalculationTest", "test formatPower with milliwatts:com.tqhit.battery.one.features.stats.charge.StatsChargeViewModelPowerCalculationTest", "<init>:com.tqhit.battery.one.features.stats.charge.StatsChargeViewModelPowerCalculationTest", "test formatPower with zero power:com.tqhit.battery.one.features.stats.charge.StatsChargeViewModelPowerCalculationTest", "setup:com.tqhit.battery.one.features.stats.charge.StatsChargeViewModelPowerCalculationTest", "test formatPower with very small power:com.tqhit.battery.one.features.stats.charge.StatsChargeViewModelPowerCalculationTest", "test formatPower with negative power:com.tqhit.battery.one.features.stats.charge.StatsChargeViewModelPowerCalculationTest"], "src\\test\\java\\com\\tqhit\\battery\\one\\features\\stats\\charge\\CalculateSimpleChargeEstimateUseCaseTest.kt": ["test estimate when above target returns zero:com.tqhit.battery.one.features.stats.charge.CalculateSimpleChargeEstimateUseCaseTest", "test estimate with default rate when no session:com.tqhit.battery.one.features.stats.charge.CalculateSimpleChargeEstimateUseCaseTest", "test estimate with very fast session rate is capped:com.tqhit.battery.one.features.stats.charge.CalculateSimpleChargeEstimateUseCaseTest", "<init>:com.tqhit.battery.one.features.stats.charge.CalculateSimpleChargeEstimateUseCaseTest", "test estimate ignores session if no percentage charged:com.tqhit.battery.one.features.stats.charge.CalculateSimpleChargeEstimateUseCaseTest", "test estimate when not charging returns zero:com.tqhit.battery.one.features.stats.charge.CalculateSimpleChargeEstimateUseCaseTest", "test estimate with session-based rate:com.tqhit.battery.one.features.stats.charge.CalculateSimpleChargeEstimateUseCaseTest", "test calculateTimeToTarget method:com.tqhit.battery.one.features.stats.charge.CalculateSimpleChargeEstimateUseCaseTest", "CalculateSimpleChargeEstimateUseCaseTest:com.tqhit.battery.one.features.stats.charge", "test estimate when already at target returns zero:com.tqhit.battery.one.features.stats.charge.CalculateSimpleChargeEstimateUseCaseTest", "test estimate ignores session if too short:com.tqhit.battery.one.features.stats.charge.CalculateSimpleChargeEstimateUseCaseTest", "test calculateTimeToFull method:com.tqhit.battery.one.features.stats.charge.CalculateSimpleChargeEstimateUseCaseTest"], "src\\test\\java\\com\\tqhit\\battery\\one\\features\\emoji\\domain\\use_case\\SaveCustomizationUseCaseTest.kt": ["saveCustomizationConfig should fail for invalid configuration:com.tqhit.battery.one.features.emoji.domain.use_case.SaveCustomizationUseCaseTest", "saveCustomizationFromStyle should fail for invalid style:com.tqhit.battery.one.features.emoji.domain.use_case.SaveCustomizationUseCaseTest", "setUp:com.tqhit.battery.one.features.emoji.domain.use_case.SaveCustomizationUseCaseTest", "saveCustomizationFromStyle should create and save configuration from style:com.tqhit.battery.one.features.emoji.domain.use_case.SaveCustomizationUseCaseTest", "saveCustomizationConfig should fail when selected style not found:com.tqhit.battery.one.features.emoji.domain.use_case.SaveCustomizationUseCaseTest", "setFeatureEnabled should handle repository errors:com.tqhit.battery.one.features.emoji.domain.use_case.SaveCustomizationUseCaseTest", "saveCustomizationFromStyle should allow premium styles during development:com.tqhit.battery.one.features.emoji.domain.use_case.SaveCustomizationUseCaseTest", "setFeatureEnabled should update feature enablement state:com.tqhit.battery.one.features.emoji.domain.use_case.SaveCustomizationUseCaseTest", "updateStyleConfig should update configuration with new style settings:com.tqhit.battery.one.features.emoji.domain.use_case.SaveCustomizationUseCaseTest", "<init>:com.tqhit.battery.one.features.emoji.domain.use_case.SaveCustomizationUseCaseTest", "SaveCustomizationUseCaseTest:com.tqhit.battery.one.features.emoji.domain.use_case", "updateStyleConfig should fail for invalid style configuration:com.tqhit.battery.one.features.emoji.domain.use_case.SaveCustomizationUseCaseTest", "saveCustomizationConfig should handle repository save errors:com.tqhit.battery.one.features.emoji.domain.use_case.SaveCustomizationUseCaseTest", "saveCustomizationConfig should save valid configuration successfully:com.tqhit.battery.one.features.emoji.domain.use_case.SaveCustomizationUseCaseTest"], "src\\test\\java\\com\\tqhit\\battery\\one\\utils\\BackgroundPermissionManagerTest.kt": ["isIgnoringBatteryOptimizations returns true for Android versions below M:com.tqhit.battery.one.utils.BackgroundPermissionManagerTest", "recordDialogDismissal stores current timestamp:com.tqhit.battery.one.utils.BackgroundPermissionManagerTest", "shouldShowBackgroundPermissionDialog respects cooldown period:com.tqhit.battery.one.utils.BackgroundPermissionManagerTest", "getRemainingCooldownTime returns correct remaining time:com.tqhit.battery.one.utils.BackgroundPermissionManagerTest", "shouldShowBackgroundPermissionDialog shows dialog after cooldown expires:com.tqhit.battery.one.utils.BackgroundPermissionManagerTest", "isInCooldownPeriod returns false when no dismissal recorded:com.tqhit.battery.one.utils.BackgroundPermissionManagerTest", "isInCooldownPeriod returns false when beyond 30 minute window:com.tqhit.battery.one.utils.BackgroundPermissionManagerTest", "getRemainingCooldownTime returns zero when no dismissal recorded:com.tqhit.battery.one.utils.BackgroundPermissionManagerTest", "BackgroundPermissionManagerTest:com.tqhit.battery.one.utils", "setUp:com.tqhit.battery.one.utils.BackgroundPermissionManagerTest", "shouldShowBackgroundPermissionDialog returns true when permission not granted:com.tqhit.battery.one.utils.BackgroundPermissionManagerTest", "isIgnoringBatteryOptimizations returns true when permission granted on Android M+:com.tqhit.battery.one.utils.BackgroundPermissionManagerTest", "<init>:com.tqhit.battery.one.utils.BackgroundPermissionManagerTest", "isInCooldownPeriod returns true when within 30 minute window:com.tqhit.battery.one.utils.BackgroundPermissionManagerTest", "tearDown:com.tqhit.battery.one.utils.BackgroundPermissionManagerTest", "isIgnoringBatteryOptimizations returns false when permission not granted on Android M+:com.tqhit.battery.one.utils.BackgroundPermissionManagerTest", "shouldShowBackgroundPermissionDialog returns false for Android versions below M:com.tqhit.battery.one.utils.BackgroundPermissionManagerTest", "shouldShowBackgroundPermissionDialog returns false when permission granted:com.tqhit.battery.one.utils.BackgroundPermissionManagerTest"], "src\\test\\java\\com\\tqhit\\battery\\one\\features\\stats\\charge\\StatsChargeSessionStatisticsTest.kt": ["test session with zero statistics:com.tqhit.battery.one.features.stats.charge.StatsChargeSessionStatisticsTest", "test session ending preserves statistics:com.tqhit.battery.one.features.stats.charge.StatsChargeSessionStatisticsTest", "test session duration calculation with statistics:com.tqhit.battery.one.features.stats.charge.StatsChargeSessionStatisticsTest", "test session creation with default statistics:com.tqhit.battery.one.features.stats.charge.StatsChargeSessionStatisticsTest", "<init>:com.tqhit.battery.one.features.stats.charge.StatsChargeSessionStatisticsTest", "StatsChargeSessionStatisticsTest:com.tqhit.battery.one.features.stats.charge", "test session with totalChargePercentage and totalChargeMah:com.tqhit.battery.one.features.stats.charge.StatsChargeSessionStatisticsTest", "test session copy with updated statistics:com.tqhit.battery.one.features.stats.charge.StatsChargeSessionStatisticsTest", "test session percentage charged calculation with statistics:com.tqhit.battery.one.features.stats.charge.StatsChargeSessionStatisticsTest", "test ended session percentage charged with statistics:com.tqhit.battery.one.features.stats.charge.StatsChargeSessionStatisticsTest"], "src\\test\\java\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\integration\\ScreenTimeEstimationIntegrationTest.kt": ["setUp:com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeEstimationIntegrationTest", "test screen time tracking continues correctly after restart:com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeEstimationIntegrationTest", "ScreenTimeEstimationIntegrationTest:com.tqhit.battery.one.features.stats.discharge.integration", "test screen time estimation consistency after app restart with screen state change:com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeEstimationIntegrationTest", "test screen time estimation with no cached session:com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeEstimationIntegrationTest", "<init>:com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeEstimationIntegrationTest"], "src\\test\\java\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\domain\\EnhancedScreenTimeTrackerTest.kt": ["test gap estimation prevents time going backwards:com.tqhit.battery.one.features.stats.discharge.domain.EnhancedScreenTimeTrackerTest", "test fallback to original logic when cache invalid:com.tqhit.battery.one.features.stats.discharge.domain.EnhancedScreenTimeTrackerTest", "test gap estimation fallbacks for short sessions:com.tqhit.battery.one.features.stats.discharge.domain.EnhancedScreenTimeTrackerTest", "test fixed issue - only active state updates:com.tqhit.battery.one.features.stats.discharge.domain.EnhancedScreenTimeTrackerTest", "test gap estimation prevents backward time jumps:com.tqhit.battery.one.features.stats.discharge.domain.EnhancedScreenTimeTrackerTest", "test simplified approach - OFF time equals total minus ON time:com.tqhit.battery.one.features.stats.discharge.domain.EnhancedScreenTimeTrackerTest", "test reset clears gap estimation state:com.tqhit.battery.one.features.stats.discharge.domain.EnhancedScreenTimeTrackerTest", "test force set screen state updates correctly:com.tqhit.battery.one.features.stats.discharge.domain.EnhancedScreenTimeTrackerTest", "EnhancedScreenTimeTrackerTest:com.tqhit.battery.one.features.stats.discharge.domain", "test robust timer logic with screen OFF state:com.tqhit.battery.one.features.stats.discharge.domain.EnhancedScreenTimeTrackerTest", "setUp:com.tqhit.battery.one.features.stats.discharge.domain.EnhancedScreenTimeTrackerTest", "test simplified gap estimation functionality:com.tqhit.battery.one.features.stats.discharge.domain.EnhancedScreenTimeTrackerTest", "test robust timer logic with screen ON state:com.tqhit.battery.one.features.stats.discharge.domain.EnhancedScreenTimeTrackerTest", "<init>:com.tqhit.battery.one.features.stats.discharge.domain.EnhancedScreenTimeTrackerTest", "test force set screen state updates timestamps:com.tqhit.battery.one.features.stats.discharge.domain.EnhancedScreenTimeTrackerTest", "test multiple increments with gap estimation:com.tqhit.battery.one.features.stats.discharge.domain.EnhancedScreenTimeTrackerTest", "test screen state change during gap estimation operation:com.tqhit.battery.one.features.stats.discharge.domain.EnhancedScreenTimeTrackerTest"], "src\\test\\java\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\repository\\DischargeSessionRepositoryTest.kt": ["test gap estimation on app restart:com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepositoryTest", "test screen time increment without session:com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepositoryTest", "test session restoration with inactive session:com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepositoryTest", "setUp:com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepositoryTest", "DischargeSessionRepositoryTest:com.tqhit.battery.one.features.stats.discharge.repository", "test session restoration with active session:com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepositoryTest", "<init>:com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepositoryTest", "test new session creation when no cached session:com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepositoryTest", "test screen time UI tracking initialization:com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepositoryTest"], "src\\test\\java\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\integration\\ScreenTimeUiUpdateIntegrationTest.kt": ["validation during discharge start ensures proper screen time calculation:com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeUiUpdateIntegrationTest", "<init>:com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeUiUpdateIntegrationTest", "no ui update when fragment active and not recently resumed:com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeUiUpdateIntegrationTest", "validation ensures screen time sum does not exceed total discharge time:com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeUiUpdateIntegrationTest", "setUp:com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeUiUpdateIntegrationTest", "ui update triggered when app resumes regardless of fragment state:com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeUiUpdateIntegrationTest", "ScreenTimeUiUpdateIntegrationTest:com.tqhit.battery.one.features.stats.discharge.integration", "proportional scaling applied for significant time excess:com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeUiUpdateIntegrationTest", "complete flow - app minimized then resumed with screen OFF time gap:com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeUiUpdateIntegrationTest"], "src\\test\\java\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\integration\\ScreenTimeConstraintTest.kt": ["test inactive session duration calculation:com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeConstraintTest", "<init>:com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeConstraintTest", "ScreenTimeConstraintTest:com.tqhit.battery.one.features.stats.discharge.integration", "test session duration calculation with real-time updates:com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeConstraintTest", "test constraint validation in UI updater:com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeConstraintTest", "test constraint enforcement with proportional scaling:com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeConstraintTest"], "src\\test\\java\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\integration\\ScreenTimeOscillationTest.kt": ["test session duration buffer prevents constraint violations:com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeOscillationTest", "test proportional scaling maintains ratios:com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeOscillationTest", "<init>:com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeOscillationTest", "test inactive session duration is fixed:com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeOscillationTest", "ScreenTimeOscillationTest:com.tqhit.battery.one.features.stats.discharge.integration", "test constraint enforcement cooldown logic:com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeOscillationTest", "test constraint enforcement frequency limits:com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeOscillationTest", "test gentle scaling vs aggressive scaling:com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeOscillationTest"], "src\\test\\java\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\domain\\AppLifecycleManagerTest.kt": ["fragment state transitions work correctly:com.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManagerTest", "app state transitions work correctly:com.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManagerTest", "setUp:com.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManagerTest", "initial state is FOREGROUND with fragment inactive:com.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManagerTest", "AppLifecycleManagerTest:com.tqhit.battery.one.features.stats.discharge.domain", "getCurrentStateInfo returns correct state information:com.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManagerTest", "shouldTriggerUiUpdate returns false when app in background:com.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManagerTest", "<init>:com.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManagerTest", "onStart sets app state to FOREGROUND:com.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManagerTest", "shouldTriggerUiUpdate returns false when app foreground and fragment active and not recently resumed:com.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManagerTest", "shouldTriggerUiUpdate returns true when recently resumed regardless of fragment state:com.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManagerTest", "onStop sets app state to BACKGROUND:com.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManagerTest", "setDischargeFragmentActive updates fragment state:com.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManagerTest", "shouldTriggerUiUpdate returns true when app in foreground and fragment inactive:com.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManagerTest"], "src\\test\\java\\com\\tqhit\\battery\\one\\features\\navigation\\NavigationStateTest.kt": ["NavigationStateChangeTest:com.tqhit.battery.one.features.navigation", "createDischargingState returns correct state:com.tqhit.battery.one.features.navigation.NavigationStateTest", "all menu items constant contains expected items:com.tqhit.battery.one.features.navigation.NavigationStateTest", "createDischargingState with no transition returns correct state:com.tqhit.battery.one.features.navigation.NavigationStateTest", "NavigationStateChange can have null previous state:com.tqhit.battery.one.features.navigation.NavigationStateChangeTest", "NavigationStateTest:com.tqhit.battery.one.features.navigation", "StateChangeReasonTest:com.tqhit.battery.one.features.navigation", "StateChangeReason enum contains expected values:com.tqhit.battery.one.features.navigation.StateChangeReasonTest", "getFragmentClass returns correct fragment classes:com.tqhit.battery.one.features.navigation.NavigationStateTest", "createFragment returns default for unknown fragment id:com.tqhit.battery.one.features.navigation.NavigationStateTest", "<init>:com.tqhit.battery.one.features.navigation.StateChangeReasonTest", "always visible items constant contains expected items:com.tqhit.battery.one.features.navigation.NavigationStateTest", "<init>:com.tqhit.battery.one.features.navigation.NavigationStateTest", "getFragmentClass returns default for unknown fragment id:com.tqhit.battery.one.features.navigation.NavigationStateTest", "<init>:com.tqhit.battery.one.features.navigation.NavigationStateChangeTest", "createChargingState with no transition returns correct state:com.tqhit.battery.one.features.navigation.NavigationStateTest", "createDefaultState returns charging state without transition:com.tqhit.battery.one.features.navigation.NavigationStateTest", "always visible items are included in both states:com.tqhit.battery.one.features.navigation.NavigationStateTest", "createFragment returns correct fragment instances:com.tqhit.battery.one.features.navigation.NavigationStateTest", "NavigationStateChange holds correct data:com.tqhit.battery.one.features.navigation.NavigationStateChangeTest", "createChargingState returns correct state:com.tqhit.battery.one.features.navigation.NavigationStateTest"], "src\\test\\java\\com\\tqhit\\battery\\one\\features\\emoji\\domain\\use_case\\LoadCustomizationUseCaseTest.kt": ["userCustomizationFlow should handle empty style list:com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest", "getCurrentCustomizationConfig should return current configuration:com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest", "getCurrentEnrichedUserCustomization should return enriched data:com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest", "validateCurrentConfiguration should handle validation errors:com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest", "setUp:com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest", "isFeatureReady should return true when all conditions met:com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest", "getCurrentSelectedStyle should return selected style when available:com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest", "LoadCustomizationUseCaseTest:com.tqhit.battery.one.features.emoji.domain.use_case", "isFeatureReadyFlow should emit correct readiness state:com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest", "getCurrentSelectedStyle should return null when style not found:com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest", "userCustomizationFlow should emit enriched customization data:com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest", "getCurrentUserCustomization should return current user customization:com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest", "getCurrentSelectedStyle should return null when no style selected:com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest", "validateCurrentConfiguration should return empty list for valid configuration:com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest", "getFeatureStatus should return correct status:com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest", "getFeatureStatus should return permissions required when missing permissions:com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest", "isFeatureReady should return false when permissions missing:com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest", "featureStatusFlow should emit correct status changes:com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest", "getCurrentCustomizationConfig should return default on error:com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest", "userCustomizationFlow should handle missing selected style:com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest", "validateCurrentConfiguration should return issues for invalid configuration:com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest", "<init>:com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCaseTest"], "src\\test\\java\\com\\tqhit\\battery\\one\\features\\emoji\\di\\EmojiBatteryDIModuleTest.kt": ["EmojiBatteryDIModuleTest:com.tqhit.battery.one.features.emoji.di", "module class is properly configured:com.tqhit.battery.one.features.emoji.di.EmojiBatteryDIModuleTest", "<init>:com.tqhit.battery.one.features.emoji.di.EmojiBatteryDIModuleTest", "module class exists and is accessible:com.tqhit.battery.one.features.emoji.di.EmojiBatteryDIModuleTest", "setUp:com.tqhit.battery.one.features.emoji.di.EmojiBatteryDIModuleTest", "module follows abstract class pattern:com.tqhit.battery.one.features.emoji.di.EmojiBatteryDIModuleTest"], "src\\test\\java\\com\\tqhit\\battery\\one\\features\\stats\\health\\data\\HealthStatusTest.kt": ["createCalculated with high sessions clamps health to zero:com.tqhit.battery.one.features.stats.health.data.HealthStatusTest", "getDegradationPercentage returns correct value:com.tqhit.battery.one.features.stats.health.data.HealthStatusTest", "health calculation mode enum has correct descriptions:com.tqhit.battery.one.features.stats.health.data.HealthStatusTest", "createDefault returns valid default health status:com.tqhit.battery.one.features.stats.health.data.HealthStatusTest", "isValid returns false for effective capacity greater than design capacity:com.tqhit.battery.one.features.stats.health.data.HealthStatusTest", "isValid returns true for valid health status:com.tqhit.battery.one.features.stats.health.data.HealthStatusTest", "HealthStatusTest:com.tqhit.battery.one.features.stats.health.data", "timestamp is set correctly on creation:com.tqhit.battery.one.features.stats.health.data.HealthStatusTest", "edge case zero health percentage is valid:com.tqhit.battery.one.features.stats.health.data.HealthStatusTest", "getCapacityLossMah returns correct value:com.tqhit.battery.one.features.stats.health.data.HealthStatusTest", "createCalculated with singular mode returns zero health:com.tqhit.battery.one.features.stats.health.data.HealthStatusTest", "isValid returns false for negative sessions:com.tqhit.battery.one.features.stats.health.data.HealthStatusTest", "isValid returns false for zero design capacity:com.tqhit.battery.one.features.stats.health.data.HealthStatusTest", "edge case perfect health is valid:com.tqhit.battery.one.features.stats.health.data.HealthStatusTest", "isValid returns false for invalid health percentage:com.tqhit.battery.one.features.stats.health.data.HealthStatusTest", "createCalculated with cumulative mode returns correct health status:com.tqhit.battery.one.features.stats.health.data.HealthStatusTest", "health calculation mode enum has correct display names:com.tqhit.battery.one.features.stats.health.data.HealthStatusTest", "<init>:com.tqhit.battery.one.features.stats.health.data.HealthStatusTest"], "src\\test\\java\\com\\tqhit\\battery\\one\\features\\stats\\corebattery\\CoreBatteryStatusTest.kt": ["test CoreBatteryStatus data class equality:com.tqhit.battery.one.features.stats.corebattery.CoreBatteryStatusTest", "test CoreBatteryStatus creation with boundary values:com.tqhit.battery.one.features.stats.corebattery.CoreBatteryStatusTest", "test CoreBatteryStatus data class inequality:com.tqhit.battery.one.features.stats.corebattery.CoreBatteryStatusTest", "CoreBatteryStatusTest:com.tqhit.battery.one.features.stats.corebattery", "test CoreBatteryStatus toString contains key information:com.tqhit.battery.one.features.stats.corebattery.CoreBatteryStatusTest", "test CoreBatteryStatus createDefault method:com.tqhit.battery.one.features.stats.corebattery.CoreBatteryStatusTest", "<init>:com.tqhit.battery.one.features.stats.corebattery.CoreBatteryStatusTest", "test CoreBatteryStatus creation with valid data:com.tqhit.battery.one.features.stats.corebattery.CoreBatteryStatusTest"], "src\\test\\java\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\domain\\DischargeCalculatorTest.kt": ["shouldSkipUpdate returns false for percentage change:com.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculatorTest", "shouldSkipUpdate returns true for short time and same percentage:com.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculatorTest", "estimateTimeRemainingMillis returns zero for zero discharge rate:com.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculatorTest", "DischargeCalculatorTest:com.tqhit.battery.one.features.stats.discharge.domain", "calculateMixedDischargeRate returns correct weighted average:com.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculatorTest", "setUp:com.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculatorTest", "calculateCurrentCapacityMah returns correct capacity:com.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculatorTest", "estimateTimeRemainingMillis returns correct time for valid inputs:com.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculatorTest", "estimateTimeRemainingMillis returns zero for zero capacity:com.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculatorTest", "shouldSkipUpdate returns false for sufficient time elapsed:com.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculatorTest", "<init>:com.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculatorTest"], "src\\test\\java\\com\\tqhit\\battery\\one\\features\\stats\\charge\\StatsChargeSessionTest.kt": ["test getPercentageCharged handles negative values for active session:com.tqhit.battery.one.features.stats.charge.StatsChargeSessionTest", "test active session creation:com.tqhit.battery.one.features.stats.charge.StatsChargeSessionTest", "test percentage charged for completed session:com.tqhit.battery.one.features.stats.charge.StatsChargeSessionTest", "test duration calculation for completed session:com.tqhit.battery.one.features.stats.charge.StatsChargeSessionTest", "test completed session creation:com.tqhit.battery.one.features.stats.charge.StatsChargeSessionTest", "StatsChargeSessionTest:com.tqhit.battery.one.features.stats.charge", "test getPercentageCharged with current percentage for completed session:com.tqhit.battery.one.features.stats.charge.StatsChargeSessionTest", "<init>:com.tqhit.battery.one.features.stats.charge.StatsChargeSessionTest", "test percentage charged for active session returns zero:com.tqhit.battery.one.features.stats.charge.StatsChargeSessionTest", "test percentage charged handles negative values:com.tqhit.battery.one.features.stats.charge.StatsChargeSessionTest", "test data class equality:com.tqhit.battery.one.features.stats.charge.StatsChargeSessionTest", "test getPercentageCharged with current percentage for active session:com.tqhit.battery.one.features.stats.charge.StatsChargeSessionTest", "test endSession factory method:com.tqhit.battery.one.features.stats.charge.StatsChargeSessionTest", "test createNew factory method:com.tqhit.battery.one.features.stats.charge.StatsChargeSessionTest", "test duration calculation for active session:com.tqhit.battery.one.features.stats.charge.StatsChargeSessionTest", "test data class inequality:com.tqhit.battery.one.features.stats.charge.StatsChargeSessionTest"], "src\\test\\java\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\ScreenTimeGapValidationTest.kt": ["test gap correction when exceeding 60 second tolerance:com.tqhit.battery.one.features.stats.discharge.ScreenTimeGapValidationTest", "test force set screen OFF time functionality:com.tqhit.battery.one.features.stats.discharge.ScreenTimeGapValidationTest", "setup:com.tqhit.battery.one.features.stats.discharge.ScreenTimeGapValidationTest", "test edge case - very short session:com.tqhit.battery.one.features.stats.discharge.ScreenTimeGapValidationTest", "ScreenTimeGapValidationTest:com.tqhit.battery.one.features.stats.discharge", "test simplified calculation during screen OFF periods:com.tqhit.battery.one.features.stats.discharge.ScreenTimeGapValidationTest", "test mathematical constraint validation:com.tqhit.battery.one.features.stats.discharge.ScreenTimeGapValidationTest", "test gap validation within 60 second tolerance:com.tqhit.battery.one.features.stats.discharge.ScreenTimeGapValidationTest", "<init>:com.tqhit.battery.one.features.stats.discharge.ScreenTimeGapValidationTest"], "src\\test\\java\\com\\tqhit\\battery\\one\\features\\stats\\health\\repository\\HealthChartDataTest.kt": ["battery percentage entries have valid ranges:com.tqhit.battery.one.features.stats.health.repository.HealthChartDataTest", "daily wear data has reasonable values:com.tqhit.battery.one.features.stats.health.repository.HealthChartDataTest", "temperature entries have realistic ranges:com.tqhit.battery.one.features.stats.health.repository.HealthChartDataTest", "getMaxBatteryPercentage handles empty data:com.tqhit.battery.one.features.stats.health.repository.HealthChartDataTest", "sample data shows realistic battery discharge pattern:com.tqhit.battery.one.features.stats.health.repository.HealthChartDataTest", "chart data validation works correctly:com.tqhit.battery.one.features.stats.health.repository.HealthChartDataTest", "chart data entries are properly ordered:com.tqhit.battery.one.features.stats.health.repository.HealthChartDataTest", "temperature correlates with battery usage patterns:com.tqhit.battery.one.features.stats.health.repository.HealthChartDataTest", "getMaxBatteryPercentage returns correct values:com.tqhit.battery.one.features.stats.health.repository.HealthChartDataTest", "createSample generates valid chart data for all time ranges:com.tqhit.battery.one.features.stats.health.repository.HealthChartDataTest", "createEmpty generates valid empty chart data:com.tqhit.battery.one.features.stats.health.repository.HealthChartDataTest", "<init>:com.tqhit.battery.one.features.stats.health.repository.HealthChartDataTest", "HealthChartDataTest:com.tqhit.battery.one.features.stats.health.repository"], "src\\test\\java\\com\\tqhit\\battery\\one\\features\\stats\\discharge\\domain\\FullSessionReEstimatorTest.kt": ["<init>:com.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimatorTest", "test re-estimation with fallback rates when learned rates are invalid:com.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimatorTest", "test re-estimation with very short session duration:com.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimatorTest", "setUp:com.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimatorTest", "test re-estimation with learned rates:com.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimatorTest", "test re-estimation with battery percentage increase:com.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimatorTest", "FullSessionReEstimatorTest:com.tqhit.battery.one.features.stats.discharge.domain"]}