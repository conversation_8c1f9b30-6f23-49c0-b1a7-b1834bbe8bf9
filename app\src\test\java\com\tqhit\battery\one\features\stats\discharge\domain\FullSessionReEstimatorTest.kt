package com.tqhit.battery.one.features.stats.discharge.domain

import com.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus
import com.tqhit.battery.one.features.stats.discharge.cache.DischargeRatesCache
import com.tqhit.battery.one.features.stats.discharge.data.DischargeSessionData
import io.mockk.*
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.junit.Assert.*

/**
 * Tests for FullSessionReEstimator focusing on screen time estimation after app restart
 */
@OptIn(ExperimentalCoroutinesApi::class)
class FullSessionReEstimatorTest {

    private lateinit var reEstimator: FullSessionReEstimator
    private lateinit var mockScreenTimeCalculator: ScreenTimeCalculator
    private lateinit var mockDischargeRatesCache: DischargeRatesCache
    private lateinit var mockTimeConverter: TimeConverter
    private lateinit var mockDischargeRateCalculator: DischargeRateCalculator

    @Before
    fun setUp() {
        mockScreenTimeCalculator = mockk()
        mockDischargeRatesCache = mockk()
        mockTimeConverter = mockk()
        mockDischargeRateCalculator = mockk()

        reEstimator = FullSessionReEstimator(
            screenTimeCalculator = mockScreenTimeCalculator,
            dischargeRatesCache = mockDischargeRatesCache,
            timeConverter = mockTimeConverter,
            dischargeRateCalculator = mockDischargeRateCalculator
        )
    }

    @Test
    fun `test re-estimation with learned rates`() = runTest {
        // Given: A session that started 2 hours ago with 10% battery drop
        val sessionStartTime = System.currentTimeMillis() - 7200000L // 2 hours ago
        val cachedSession = createTestSession(
            startTime = sessionStartTime,
            startPercentage = 90,
            currentPercentage = 85 // Cached shows 85%
        )

        val liveStatus = CoreBatteryStatus(
            percentage = 80, // Live shows 80% (total 10% drop)
            isCharging = false,
            pluggedSource = 0,
            currentMicroAmperes = -300000,
            voltageMillivolts = 4000,
            temperatureCelsius = 25.0f,
            timestampEpochMillis = System.currentTimeMillis()
        )

        // Mock learned rates (good rates)
        coEvery { mockDischargeRatesCache.getAverageScreenOnRateMah() } returns 250.0
        coEvery { mockDischargeRatesCache.getAverageScreenOffRateMah() } returns 50.0

        // Mock time converter
        every { mockTimeConverter.millisToHours(7200000L) } returns 2.0

        // Mock discharge rate calculator
        every { mockDischargeRateCalculator.calculateMahConsumed(10, 3000.0) } returns 300.0
        every { mockDischargeRateCalculator.calculateMixedRate(any(), any(), any(), any()) } returns 120.0

        // Mock screen time calculation result
        val expectedScreenTimes = ScreenTimeCalculator.ScreenTimes(
            onTimeHours = 1.0,
            offTimeHours = 1.0,
            onTimeMillis = 3600000L, // 1 hour
            offTimeMillis = 3600000L, // 1 hour
            onMahConsumed = 250.0,
            offMahConsumed = 50.0
        )
        every { mockScreenTimeCalculator.calculateScreenTimes(any(), any(), any()) } returns expectedScreenTimes

        // When: Re-estimation is performed
        val result = reEstimator.reEstimateFullSessionScreenTimes(
            cachedSession, liveStatus, 3000.0
        )

        // Then: Session should be updated with re-estimated values
        assertNotNull("Result should not be null", result)
        assertEquals("Current percentage should be updated", 80, result!!.currentPercentage)
        assertEquals("Screen on time should be estimated", 3600000L, result.screenOnTimeMillis)
        assertEquals("Screen off time should be estimated", 3600000L, result.screenOffTimeMillis)
        assertEquals("Total consumption should be calculated", 300.0, result.totalMahConsumed, 0.1)
    }

    @Test
    fun `test re-estimation with fallback rates when learned rates are invalid`() = runTest {
        // Given: A session with invalid learned rates
        val sessionStartTime = System.currentTimeMillis() - 3600000L // 1 hour ago
        val cachedSession = createTestSession(
            startTime = sessionStartTime,
            startPercentage = 100,
            currentPercentage = 95
        )

        val liveStatus = CoreBatteryStatus(
            percentage = 90, // 10% drop total
            isCharging = false,
            pluggedSource = 0,
            currentMicroAmperes = -200000,
            voltageMillivolts = 4000,
            temperatureCelsius = 25.0f,
            timestampEpochMillis = System.currentTimeMillis()
        )

        // Mock invalid learned rates (screen off rate is 0)
        coEvery { mockDischargeRatesCache.getAverageScreenOnRateMah() } returns 250.0
        coEvery { mockDischargeRatesCache.getAverageScreenOffRateMah() } returns 0.0

        // Mock time converter
        every { mockTimeConverter.millisToHours(3600000L) } returns 1.0

        // Mock discharge rate calculator
        every { mockDischargeRateCalculator.calculateMahConsumed(10, 3000.0) } returns 300.0
        every { mockDischargeRateCalculator.calculateMixedRate(any(), any(), any(), any()) } returns 120.0

        // Mock screen time calculation with fallback rates
        val fallbackScreenTimes = ScreenTimeCalculator.ScreenTimes(
            onTimeHours = 0.5,
            offTimeHours = 0.5,
            onTimeMillis = 1800000L, // 30 minutes
            offTimeMillis = 1800000L, // 30 minutes
            onMahConsumed = 200.0,
            offMahConsumed = 100.0
        )
        every { mockScreenTimeCalculator.calculateScreenTimes(any(), any(), any()) } returns fallbackScreenTimes

        // When: Re-estimation is performed
        val result = reEstimator.reEstimateFullSessionScreenTimes(
            cachedSession, liveStatus, 3000.0
        )

        // Then: Fallback rates should be used
        assertNotNull("Result should not be null", result)
        assertEquals("Screen on time should use fallback estimation", 1800000L, result!!.screenOnTimeMillis)
        assertEquals("Screen off time should use fallback estimation", 1800000L, result.screenOffTimeMillis)
        
        // Verify that fallback rates were used in calculation
        verify { mockScreenTimeCalculator.calculateScreenTimes(1.0, 300.0, any()) }
    }

    @Test
    fun `test re-estimation with very short session duration`() = runTest {
        // Given: A very short session (less than minimum duration)
        val sessionStartTime = System.currentTimeMillis() - 5000L // 5 seconds ago
        val cachedSession = createTestSession(
            startTime = sessionStartTime,
            startPercentage = 90,
            currentPercentage = 90
        )

        val liveStatus = CoreBatteryStatus(
            percentage = 90, // No change
            isCharging = false,
            pluggedSource = 0,
            currentMicroAmperes = -100000,
            voltageMillivolts = 4000,
            temperatureCelsius = 25.0f,
            timestampEpochMillis = System.currentTimeMillis()
        )

        // When: Re-estimation is performed
        val result = reEstimator.reEstimateFullSessionScreenTimes(
            cachedSession, liveStatus, 3000.0
        )

        // Then: Minimal processing should be applied
        assertNotNull("Result should not be null", result)
        assertEquals("Current percentage should be updated", 90, result!!.currentPercentage)
        // Screen times should remain as they were in cached session
        assertEquals("Screen on time should remain unchanged", cachedSession.screenOnTimeMillis, result.screenOnTimeMillis)
        assertEquals("Screen off time should remain unchanged", cachedSession.screenOffTimeMillis, result.screenOffTimeMillis)
    }

    @Test
    fun `test re-estimation with battery percentage increase`() = runTest {
        // Given: A session where battery percentage increased (charging occurred)
        val sessionStartTime = System.currentTimeMillis() - 3600000L // 1 hour ago
        val cachedSession = createTestSession(
            startTime = sessionStartTime,
            startPercentage = 80,
            currentPercentage = 80
        )

        val liveStatus = CoreBatteryStatus(
            percentage = 85, // Increased by 5%
            isCharging = false,
            pluggedSource = 0,
            currentMicroAmperes = -100000,
            voltageMillivolts = 4000,
            temperatureCelsius = 25.0f,
            timestampEpochMillis = System.currentTimeMillis()
        )

        // Mock time converter
        every { mockTimeConverter.millisToHours(3600000L) } returns 1.0

        // When: Re-estimation is performed
        val result = reEstimator.reEstimateFullSessionScreenTimes(
            cachedSession, liveStatus, 3000.0
        )

        // Then: Should assume screen OFF with 0 consumption
        assertNotNull("Result should not be null", result)
        assertEquals("Current percentage should be updated", 85, result!!.currentPercentage)
        assertEquals("Total consumption should be 0", 0.0, result.totalMahConsumed, 0.1)
        assertEquals("Screen on time should be 0", 0L, result.screenOnTimeMillis)
        assertEquals("Screen off time should be full duration", 3600000L, result.screenOffTimeMillis)
    }

    private fun createTestSession(
        startTime: Long = System.currentTimeMillis(),
        startPercentage: Int = 100,
        currentPercentage: Int = 90,
        isActive: Boolean = true
    ): DischargeSessionData {
        return DischargeSessionData(
            startTimeEpochMillis = startTime,
            lastUpdateTimeEpochMillis = System.currentTimeMillis(),
            startPercentage = startPercentage,
            currentPercentage = currentPercentage,
            currentPercentageAtLastUpdate = currentPercentage,
            isActive = isActive,
            screenOnTimeMillis = 0L,
            screenOffTimeMillis = 0L,
            totalMahConsumed = 0.0,
            screenOnMahConsumed = 0.0,
            screenOffMahConsumed = 0.0,
            avgScreenOnDischargeRateMahPerHour = 250.0,
            avgScreenOffDischargeRateMahPerHour = 50.0,
            avgMixedDischargeRateMahPerHour = 120.0,
            avgPercentPerHour = 10.0,
            currentDischargeRate = 8.0
        )
    }
}
