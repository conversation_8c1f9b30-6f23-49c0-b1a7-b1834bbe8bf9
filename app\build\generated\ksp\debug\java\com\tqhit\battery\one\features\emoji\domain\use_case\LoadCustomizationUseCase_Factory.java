package com.tqhit.battery.one.features.emoji.domain.use_case;

import com.tqhit.battery.one.features.emoji.domain.repository.BatteryStyleRepository;
import com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class LoadCustomizationUseCase_Factory implements Factory<LoadCustomizationUseCase> {
  private final Provider<CustomizationRepository> customizationRepositoryProvider;

  private final Provider<BatteryStyleRepository> batteryStyleRepositoryProvider;

  public LoadCustomizationUseCase_Factory(
      Provider<CustomizationRepository> customizationRepositoryProvider,
      Provider<BatteryStyleRepository> batteryStyleRepositoryProvider) {
    this.customizationRepositoryProvider = customizationRepositoryProvider;
    this.batteryStyleRepositoryProvider = batteryStyleRepositoryProvider;
  }

  @Override
  public LoadCustomizationUseCase get() {
    return newInstance(customizationRepositoryProvider.get(), batteryStyleRepositoryProvider.get());
  }

  public static LoadCustomizationUseCase_Factory create(
      Provider<CustomizationRepository> customizationRepositoryProvider,
      Provider<BatteryStyleRepository> batteryStyleRepositoryProvider) {
    return new LoadCustomizationUseCase_Factory(customizationRepositoryProvider, batteryStyleRepositoryProvider);
  }

  public static LoadCustomizationUseCase newInstance(
      CustomizationRepository customizationRepository,
      BatteryStyleRepository batteryStyleRepository) {
    return new LoadCustomizationUseCase(customizationRepository, batteryStyleRepository);
  }
}
