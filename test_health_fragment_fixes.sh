#!/bin/bash

# Health Fragment Data Collection and Display Issues - Comprehensive Test Script
# This script tests the fixes for:
# 1. Cumulative sessions stuck at 10
# 2. Charts using sample data instead of real data
# 3. Chart timeline accuracy issues
# 4. Temperature Y-axis display bug

set -e

APP_PACKAGE="com.fc.p.tj.charginganimation.batterycharging.chargeeffect"
LOGCAT_TAGS="CoreBatteryStatsService:D BatteryHistoryManager:D HistoryBatteryRepository:D HealthFragment:D HealthRepository:D ChargingSessionManager:D"

# Use specific device (real device, not emulator)
DEVICE_ID="10AC9C1MHS00105"
ADB_CMD="adb -s $DEVICE_ID"

echo "=========================================="
echo "Health Fragment Fixes - Comprehensive Test"
echo "=========================================="

# Function to log with timestamp
log_with_timestamp() {
    echo "[$(date '+%H:%M:%S')] $1"
}

# Function to wait for user input
wait_for_user() {
    echo ""
    read -p "Press Enter to continue..."
    echo ""
}

# Function to check if device is connected
check_device() {
    if ! $ADB_CMD devices | grep -q "$DEVICE_ID.*device$"; then
        echo "ERROR: Target device $DEVICE_ID not connected or not authorized"
        echo "Available devices:"
        adb devices
        echo "Please connect the target device and enable USB debugging"
        exit 1
    fi
    log_with_timestamp "Target device $DEVICE_ID connected and ready"
}

# Function to clear app data and restart
reset_app() {
    log_with_timestamp "Clearing app data and restarting..."
    $ADB_CMD shell am force-stop $APP_PACKAGE
    $ADB_CMD shell pm clear $APP_PACKAGE
    sleep 2
    $ADB_CMD shell am start -n "$APP_PACKAGE/.MainActivity"
    sleep 3
    log_with_timestamp "App restarted with clean data"
}

# Function to start filtered logcat monitoring
start_logcat_monitoring() {
    log_with_timestamp "Starting filtered logcat monitoring..."
    $ADB_CMD logcat -c  # Clear existing logs
    $ADB_CMD logcat $LOGCAT_TAGS > health_test_logs.txt &
    LOGCAT_PID=$!
    log_with_timestamp "Logcat monitoring started (PID: $LOGCAT_PID)"
    sleep 2
}

# Function to stop logcat monitoring
stop_logcat_monitoring() {
    if [ ! -z "$LOGCAT_PID" ]; then
        kill $LOGCAT_PID 2>/dev/null || true
        log_with_timestamp "Logcat monitoring stopped"
    fi
}

# Function to navigate to health fragment
navigate_to_health() {
    log_with_timestamp "Navigating to Health fragment..."
    # Tap on health/stats tab (assuming it's the rightmost tab)
    $ADB_CMD shell input tap 300 2100  # Adjust coordinates based on your device
    sleep 2
    log_with_timestamp "Health fragment should now be visible"
}

# Function to simulate battery level changes
simulate_battery_changes() {
    log_with_timestamp "Simulating battery level changes for data collection..."
    
    # Set initial battery level
    $ADB_CMD shell dumpsys battery set level 80
    $ADB_CMD shell dumpsys battery set status 2  # Not charging
    sleep 30

    # Gradual battery decrease to generate history
    for level in 78 76 74 72 70; do
        log_with_timestamp "Setting battery level to ${level}%"
        $ADB_CMD shell dumpsys battery set level $level
        sleep 60  # Wait 1 minute between changes
    done

    # Reset to normal battery behavior
    $ADB_CMD shell dumpsys battery reset
    log_with_timestamp "Battery simulation completed"
}

# Function to test chart button interactions
test_chart_buttons() {
    log_with_timestamp "Testing chart button interactions..."
    
    # Test percentage chart buttons (4h, 8h, 12h, 24h)
    log_with_timestamp "Testing percentage chart buttons..."
    $ADB_CMD shell input tap 100 1400  # 4h button
    sleep 3
    $ADB_CMD shell input tap 200 1400  # 8h button
    sleep 3
    $ADB_CMD shell input tap 300 1400  # 12h button
    sleep 3
    $ADB_CMD shell input tap 400 1400  # 24h button
    sleep 3

    # Test temperature chart buttons
    log_with_timestamp "Testing temperature chart buttons..."
    $ADB_CMD shell input tap 100 1800  # 4h temp button
    sleep 3
    $ADB_CMD shell input tap 200 1800  # 8h temp button
    sleep 3
    $ADB_CMD shell input tap 300 1800  # 12h temp button
    sleep 3
    $ADB_CMD shell input tap 400 1800  # 24h temp button
    sleep 3
    
    log_with_timestamp "Chart button testing completed"
}

# Function to analyze logs for issues
analyze_logs() {
    log_with_timestamp "Analyzing logs for data collection issues..."
    
    echo ""
    echo "=== SESSION COUNTING ANALYSIS ==="
    grep -i "session" health_test_logs.txt | tail -10
    
    echo ""
    echo "=== CHART DATA SOURCE ANALYSIS ==="
    grep -i "sample\|real.*data\|production_test" health_test_logs.txt | tail -10
    
    echo ""
    echo "=== TEMPERATURE Y-AXIS ANALYSIS ==="
    grep -i "temp_y_axis\|temperature.*label" health_test_logs.txt | tail -10
    
    echo ""
    echo "=== CHART TIMELINE ANALYSIS ==="
    grep -i "timestamp\|chart_debug.*time" health_test_logs.txt | tail -10
    
    echo ""
    echo "=== CORE BATTERY STATS SERVICE ==="
    grep -i "corebatterystats" health_test_logs.txt | tail -5
}

# Main test execution
main() {
    log_with_timestamp "Starting Health Fragment comprehensive test"
    
    # Pre-test setup
    check_device
    
    echo ""
    echo "This test will:"
    echo "1. Clear app data and restart"
    echo "2. Start comprehensive logcat monitoring"
    echo "3. Navigate to Health fragment"
    echo "4. Simulate battery changes to generate real data"
    echo "5. Test chart button interactions"
    echo "6. Analyze logs for the reported issues"
    echo ""
    
    wait_for_user
    
    # Start monitoring
    start_logcat_monitoring
    
    # Reset app to clean state
    reset_app
    
    # Navigate to health fragment
    navigate_to_health
    
    log_with_timestamp "Waiting for initial data collection..."
    sleep 10
    
    # Test session clearing functionality
    log_with_timestamp "Testing session clearing functionality..."
    echo "NOTE: You may need to manually trigger clearCachedSessionDataForTesting() from the app"
    wait_for_user
    
    # Simulate battery changes for real data collection
    echo "Starting battery simulation to generate real historical data..."
    echo "This will take about 5 minutes..."
    wait_for_user
    simulate_battery_changes
    
    # Test chart interactions
    test_chart_buttons
    
    # Wait for data processing
    log_with_timestamp "Waiting for data processing to complete..."
    sleep 30
    
    # Stop monitoring and analyze
    stop_logcat_monitoring
    analyze_logs
    
    echo ""
    echo "=========================================="
    echo "TEST COMPLETED"
    echo "=========================================="
    echo ""
    echo "Log file: health_test_logs.txt"
    echo ""
    echo "Key things to verify:"
    echo "1. Session count should NOT be stuck at exactly 10"
    echo "2. Charts should show 'PRODUCTION_TEST: Using real battery data' messages"
    echo "3. Temperature Y-axis should show proper temperature values (not '0')"
    echo "4. Chart timestamps should reflect actual time progression"
    echo "5. No 'sample data generation' messages should appear"
    echo ""
    echo "If issues persist, check the log analysis above for specific problems."
}

# Cleanup function
cleanup() {
    stop_logcat_monitoring
    # Reset battery to normal behavior
    $ADB_CMD shell dumpsys battery reset 2>/dev/null || true
}

# Set up cleanup on script exit
trap cleanup EXIT

# Run main test
main
