<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_charge" modulePackage="com.tqhit.battery.one" filePath="app\src\main\res\layout\fragment_charge.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.core.widget.NestedScrollView" rootNodeViewId="@+id/scroll_view"><Targets><Target id="@+id/scroll_view" tag="layout/fragment_charge_0" view="androidx.core.widget.NestedScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="2331" endOffset="39"/></Target><Target id="@+id/sale_container" view="LinearLayout"><Expressions/><location startLine="10" startOffset="8" endLine="15" endOffset="48"/></Target><Target id="@+id/promo_container" view="LinearLayout"><Expressions/><location startLine="16" startOffset="8" endLine="21" endOffset="48"/></Target><Target id="@+id/charge_up" view="LinearLayout"><Expressions/><location startLine="22" startOffset="8" endLine="184" endOffset="22"/></Target><Target id="@+id/percent_layout" view="RelativeLayout"><Expressions/><location startLine="29" startOffset="12" endLine="68" endOffset="28"/></Target><Target id="@+id/pus" view="RelativeLayout"><Expressions/><location startLine="40" startOffset="16" endLine="67" endOffset="32"/></Target><Target id="@+id/text_percent" view="TextView"><Expressions/><location startLine="45" startOffset="20" endLine="52" endOffset="52"/></Target><Target id="@+id/charge_prog_bar_percent" view="com.google.android.material.progressindicator.CircularProgressIndicator"><Expressions/><location startLine="53" startOffset="20" endLine="66" endOffset="49"/></Target><Target id="@+id/time_num" view="LinearLayout"><Expressions/><location startLine="69" startOffset="12" endLine="183" endOffset="26"/></Target><Target id="@+id/day_block" view="LinearLayout"><Expressions/><location startLine="100" startOffset="16" endLine="127" endOffset="30"/></Target><Target id="@+id/imageView18" view="ImageView"><Expressions/><location startLine="113" startOffset="20" endLine="118" endOffset="57"/></Target><Target id="@+id/text_time_day" view="TextView"><Expressions/><location startLine="119" startOffset="20" endLine="126" endOffset="52"/></Target><Target id="@+id/all_block" view="LinearLayout"><Expressions/><location startLine="128" startOffset="16" endLine="155" endOffset="30"/></Target><Target id="@+id/text_time_night" view="TextView"><Expressions/><location startLine="147" startOffset="20" endLine="154" endOffset="52"/></Target><Target id="@+id/night_block" view="LinearLayout"><Expressions/><location startLine="156" startOffset="16" endLine="182" endOffset="30"/></Target><Target id="@+id/text_time_daynight" view="TextView"><Expressions/><location startLine="174" startOffset="20" endLine="181" endOffset="52"/></Target><Target id="@+id/not_charging_message" view="TextView"><Expressions/><location startLine="187" startOffset="8" endLine="200" endOffset="39"/></Target><Target id="@+id/battery_alarm_btn" view="TextView"><Expressions/><location startLine="215" startOffset="12" endLine="233" endOffset="67"/></Target><Target id="@+id/battery_wear_info" view="ImageView"><Expressions/><location startLine="234" startOffset="12" endLine="245" endOffset="66"/></Target><Target id="@+id/test_view" view="TextView"><Expressions/><location startLine="246" startOffset="12" endLine="260" endOffset="58"/></Target><Target id="@+id/da_view" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="261" startOffset="12" endLine="350" endOffset="63"/></Target><Target id="@+id/damage_bar_seekwhite" view="ProgressBar"><Expressions/><location startLine="283" startOffset="16" endLine="298" endOffset="69"/></Target><Target id="@+id/damage_bar_percent" view="ProgressBar"><Expressions/><location startLine="299" startOffset="16" endLine="314" endOffset="69"/></Target><Target id="@+id/seekBar" view="SeekBar"><Expressions/><location startLine="315" startOffset="16" endLine="328" endOffset="78"/></Target><Target id="@+id/var_damage_up4" view="TextView"><Expressions/><location startLine="329" startOffset="16" endLine="349" endOffset="62"/></Target><Target id="@+id/cs_text" view="TextView"><Expressions/><location startLine="365" startOffset="12" endLine="378" endOffset="58"/></Target><Target id="@+id/cs_2_1" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="379" startOffset="12" endLine="465" endOffset="63"/></Target><Target id="@+id/c_text_ni_power" view="TextView"><Expressions/><location startLine="386" startOffset="16" endLine="396" endOffset="62"/></Target><Target id="@+id/progressBar_power_minus" view="ProgressBar"><Expressions/><location startLine="397" startOffset="16" endLine="408" endOffset="69"/></Target><Target id="@+id/progressBar_power" view="ProgressBar"><Expressions/><location startLine="409" startOffset="16" endLine="424" endOffset="69"/></Target><Target id="@+id/val_power_charging" view="TextView"><Expressions/><location startLine="425" startOffset="16" endLine="437" endOffset="62"/></Target><Target id="@+id/p_text" view="TextView"><Expressions/><location startLine="438" startOffset="16" endLine="447" endOffset="62"/></Target><Target id="@+id/textedd" view="TextView"><Expressions/><location startLine="448" startOffset="16" endLine="464" endOffset="62"/></Target><Target id="@+id/cs_1" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="466" startOffset="12" endLine="532" endOffset="63"/></Target><Target id="@+id/v_text" view="TextView"><Expressions/><location startLine="473" startOffset="16" endLine="482" endOffset="62"/></Target><Target id="@+id/progressBar_voltage" view="ProgressBar"><Expressions/><location startLine="483" startOffset="16" endLine="498" endOffset="69"/></Target><Target id="@+id/val_voltage" view="TextView"><Expressions/><location startLine="499" startOffset="16" endLine="514" endOffset="62"/></Target><Target id="@+id/text_remain_var22" view="TextView"><Expressions/><location startLine="515" startOffset="16" endLine="531" endOffset="62"/></Target><Target id="@+id/cs_2" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="533" startOffset="12" endLine="619" endOffset="63"/></Target><Target id="@+id/c_text_ni_ampere" view="TextView"><Expressions/><location startLine="540" startOffset="16" endLine="550" endOffset="62"/></Target><Target id="@+id/progressBar_current_minus" view="ProgressBar"><Expressions/><location startLine="551" startOffset="16" endLine="562" endOffset="69"/></Target><Target id="@+id/progressBar_current" view="ProgressBar"><Expressions/><location startLine="563" startOffset="16" endLine="578" endOffset="69"/></Target><Target id="@+id/val_currrent_charging" view="TextView"><Expressions/><location startLine="579" startOffset="16" endLine="591" endOffset="62"/></Target><Target id="@+id/c_text" view="TextView"><Expressions/><location startLine="592" startOffset="16" endLine="601" endOffset="62"/></Target><Target id="@+id/text_remain_var33" view="TextView"><Expressions/><location startLine="602" startOffset="16" endLine="618" endOffset="62"/></Target><Target id="@+id/cs_3" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="620" startOffset="12" endLine="683" endOffset="63"/></Target><Target id="@+id/progressBar_temp" view="ProgressBar"><Expressions/><location startLine="627" startOffset="16" endLine="642" endOffset="69"/></Target><Target id="@+id/val_temp2" view="TextView"><Expressions/><location startLine="643" startOffset="16" endLine="655" endOffset="62"/></Target><Target id="@+id/val_temp_text" view="TextView"><Expressions/><location startLine="656" startOffset="16" endLine="665" endOffset="62"/></Target><Target id="@+id/text_remain_var44" view="TextView"><Expressions/><location startLine="666" startOffset="16" endLine="682" endOffset="62"/></Target><Target id="@+id/cs_4" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="684" startOffset="12" endLine="759" endOffset="63"/></Target><Target id="@+id/progressBar_averagespeed_grey" view="ProgressBar"><Expressions/><location startLine="691" startOffset="16" endLine="706" endOffset="69"/></Target><Target id="@+id/progressBar_averagespeed" view="ProgressBar"><Expressions/><location startLine="707" startOffset="16" endLine="718" endOffset="69"/></Target><Target id="@+id/speedcharge_percent_text" view="TextView"><Expressions/><location startLine="719" startOffset="16" endLine="728" endOffset="62"/></Target><Target id="@+id/val_average_speed" view="TextView"><Expressions/><location startLine="729" startOffset="16" endLine="741" endOffset="62"/></Target><Target id="@+id/text_remain_var55" view="TextView"><Expressions/><location startLine="742" startOffset="16" endLine="758" endOffset="62"/></Target><Target id="@+id/remaining_table" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="761" startOffset="8" endLine="955" endOffset="59"/></Target><Target id="@+id/rem_text" view="TextView"><Expressions/><location startLine="775" startOffset="12" endLine="788" endOffset="58"/></Target><Target id="@+id/rem_1" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="789" startOffset="12" endLine="856" endOffset="63"/></Target><Target id="@+id/progressBar_remain_to_var2" view="ProgressBar"><Expressions/><location startLine="796" startOffset="16" endLine="811" endOffset="69"/></Target><Target id="@+id/progressBar_remain_to_var" view="ProgressBar"><Expressions/><location startLine="812" startOffset="16" endLine="823" endOffset="69"/></Target><Target id="@+id/val_remain_to_var" view="TextView"><Expressions/><location startLine="824" startOffset="16" endLine="836" endOffset="62"/></Target><Target id="@+id/text_remain_var" view="TextView"><Expressions/><location startLine="837" startOffset="16" endLine="855" endOffset="62"/></Target><Target id="@+id/rem_2" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="857" startOffset="12" endLine="954" endOffset="63"/></Target><Target id="@+id/progressBar_remain_to_100_2" view="ProgressBar"><Expressions/><location startLine="864" startOffset="16" endLine="879" endOffset="69"/></Target><Target id="@+id/progressBar_remain_to_100" view="ProgressBar"><Expressions/><location startLine="880" startOffset="16" endLine="891" endOffset="69"/></Target><Target id="@+id/val_remain_to_100" view="TextView"><Expressions/><location startLine="892" startOffset="16" endLine="904" endOffset="62"/></Target><Target id="@+id/rem_val_barrier" view="androidx.constraintlayout.widget.Barrier"><Expressions/><location startLine="905" startOffset="16" endLine="910" endOffset="98"/></Target><Target id="@+id/text_remain_to_100_charge" view="TextView"><Expressions/><location startLine="911" startOffset="16" endLine="929" endOffset="62"/></Target><Target id="@+id/text_remain_to_100" view="TextView"><Expressions/><location startLine="930" startOffset="16" endLine="947" endOffset="62"/></Target><Target id="@+id/rem_barrier" view="androidx.constraintlayout.widget.Barrier"><Expressions/><location startLine="948" startOffset="16" endLine="953" endOffset="117"/></Target><Target id="@+id/current_session_block" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="956" startOffset="8" endLine="1447" endOffset="59"/></Target><Target id="@+id/charge_session_info" view="ImageView"><Expressions/><location startLine="970" startOffset="12" endLine="981" endOffset="64"/></Target><Target id="@+id/st_text" view="TextView"><Expressions/><location startLine="982" startOffset="12" endLine="997" endOffset="58"/></Target><Target id="@+id/s_6" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="998" startOffset="12" endLine="1069" endOffset="63"/></Target><Target id="@+id/text_speed_charge_day_session2" view="TextView"><Expressions/><location startLine="1036" startOffset="16" endLine="1045" endOffset="95"/></Target><Target id="@+id/text_percent_charge_day_session" view="TextView"><Expressions/><location startLine="1046" startOffset="16" endLine="1055" endOffset="68"/></Target><Target id="@+id/te77" view="TextView"><Expressions/><location startLine="1056" startOffset="16" endLine="1068" endOffset="62"/></Target><Target id="@+id/s_5" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1070" startOffset="12" endLine="1141" endOffset="63"/></Target><Target id="@+id/text_speed_charge_night_session" view="TextView"><Expressions/><location startLine="1108" startOffset="16" endLine="1117" endOffset="97"/></Target><Target id="@+id/text_percent_charge_night_session" view="TextView"><Expressions/><location startLine="1118" startOffset="16" endLine="1127" endOffset="68"/></Target><Target id="@+id/te88" view="TextView"><Expressions/><location startLine="1128" startOffset="16" endLine="1140" endOffset="62"/></Target><Target id="@+id/s_3" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1142" startOffset="12" endLine="1218" endOffset="63"/></Target><Target id="@+id/text_speed_charge_all_session" view="TextView"><Expressions/><location startLine="1181" startOffset="16" endLine="1194" endOffset="95"/></Target><Target id="@+id/text_percent_charge_all_session" view="TextView"><Expressions/><location startLine="1195" startOffset="16" endLine="1204" endOffset="68"/></Target><Target id="@+id/te99" view="TextView"><Expressions/><location startLine="1205" startOffset="16" endLine="1217" endOffset="62"/></Target><Target id="@+id/s_4" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1219" startOffset="12" endLine="1312" endOffset="63"/></Target><Target id="@+id/textView_percent" view="TextView"><Expressions/><location startLine="1234" startOffset="16" endLine="1244" endOffset="95"/></Target><Target id="@+id/charge_session_percent" view="TextView"><Expressions/><location startLine="1245" startOffset="16" endLine="1259" endOffset="79"/></Target><Target id="@+id/textView9" view="TextView"><Expressions/><location startLine="1260" startOffset="16" endLine="1274" endOffset="92"/></Target><Target id="@+id/text_speed_charge_day_session" view="TextView"><Expressions/><location startLine="1275" startOffset="16" endLine="1288" endOffset="96"/></Target><Target id="@+id/text_percent_charge_session_last" view="TextView"><Expressions/><location startLine="1289" startOffset="16" endLine="1298" endOffset="67"/></Target><Target id="@+id/te0" view="TextView"><Expressions/><location startLine="1299" startOffset="16" endLine="1311" endOffset="62"/></Target><Target id="@+id/s_2" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1313" startOffset="12" endLine="1371" endOffset="63"/></Target><Target id="@+id/textView7" view="TextView"><Expressions/><location startLine="1328" startOffset="16" endLine="1338" endOffset="90"/></Target><Target id="@+id/var_speedcharge_percent_now" view="TextView"><Expressions/><location startLine="1348" startOffset="16" endLine="1357" endOffset="71"/></Target><Target id="@+id/text226" view="TextView"><Expressions/><location startLine="1358" startOffset="16" endLine="1370" endOffset="62"/></Target><Target id="@+id/s_1" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1372" startOffset="12" endLine="1428" endOffset="63"/></Target><Target id="@+id/time_charge_session_start" view="TextView"><Expressions/><location startLine="1387" startOffset="16" endLine="1400" endOffset="92"/></Target><Target id="@+id/text_fulltime_charge_session" view="TextView"><Expressions/><location startLine="1401" startOffset="16" endLine="1414" endOffset="73"/></Target><Target id="@+id/textView6" view="TextView"><Expressions/><location startLine="1415" startOffset="16" endLine="1427" endOffset="62"/></Target><Target id="@+id/reset_session_charge_button" view="TextView"><Expressions/><location startLine="1429" startOffset="12" endLine="1446" endOffset="63"/></Target><Target id="@+id/amperage_table" view="RelativeLayout"><Expressions/><location startLine="1448" startOffset="8" endLine="2010" endOffset="24"/></Target><Target id="@+id/selector_amperage" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1459" startOffset="12" endLine="1503" endOffset="63"/></Target><Target id="@+id/btn_selector1" view="TextView"><Expressions/><location startLine="1467" startOffset="16" endLine="1483" endOffset="62"/></Target><Target id="@+id/btn_selector2" view="TextView"><Expressions/><location startLine="1484" startOffset="16" endLine="1502" endOffset="62"/></Target><Target id="@+id/percent_graph_change" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1504" startOffset="12" endLine="1566" endOffset="63"/></Target><Target id="@+id/btn1" view="TextView"><Expressions/><location startLine="1512" startOffset="16" endLine="1528" endOffset="62"/></Target><Target id="@+id/btn2" view="TextView"><Expressions/><location startLine="1529" startOffset="16" endLine="1546" endOffset="62"/></Target><Target id="@+id/btn3" view="TextView"><Expressions/><location startLine="1547" startOffset="16" endLine="1565" endOffset="62"/></Target><Target id="@+id/wear_rate_percent" view="TextView"><Expressions/><location startLine="1567" startOffset="12" endLine="1580" endOffset="48"/></Target><Target id="@+id/sdfsd" view="LinearLayout"><Expressions/><location startLine="1581" startOffset="12" endLine="1604" endOffset="26"/></Target><Target id="@+id/under_graph_percent" view="LinearLayout"><Expressions/><location startLine="1605" startOffset="12" endLine="1734" endOffset="26"/></Target><Target id="@+id/day_7_percent" view="TextView"><Expressions/><location startLine="1628" startOffset="20" endLine="1635" endOffset="52"/></Target><Target id="@+id/day_6_percent" view="TextView"><Expressions/><location startLine="1643" startOffset="20" endLine="1650" endOffset="52"/></Target><Target id="@+id/day_5_percent" view="TextView"><Expressions/><location startLine="1658" startOffset="20" endLine="1665" endOffset="52"/></Target><Target id="@+id/day_4_percent" view="TextView"><Expressions/><location startLine="1673" startOffset="20" endLine="1680" endOffset="52"/></Target><Target id="@+id/day_3_percent" view="TextView"><Expressions/><location startLine="1688" startOffset="20" endLine="1695" endOffset="52"/></Target><Target id="@+id/day_2_percent" view="TextView"><Expressions/><location startLine="1703" startOffset="20" endLine="1710" endOffset="52"/></Target><Target id="@+id/day_1_percent" view="TextView"><Expressions/><location startLine="1718" startOffset="20" endLine="1725" endOffset="52"/></Target><Target id="@+id/graph_percent" view="RelativeLayout"><Expressions/><location startLine="1735" startOffset="12" endLine="2009" endOffset="28"/></Target><Target id="@+id/t26" view="TextView"><Expressions/><location startLine="1824" startOffset="24" endLine="1828" endOffset="65"/></Target><Target id="@+id/t1" view="TextView"><Expressions/><location startLine="1838" startOffset="24" endLine="1845" endOffset="47"/></Target><Target id="@+id/t2" view="TextView"><Expressions/><location startLine="1855" startOffset="24" endLine="1862" endOffset="47"/></Target><Target id="@+id/t3" view="TextView"><Expressions/><location startLine="1872" startOffset="24" endLine="1879" endOffset="47"/></Target><Target id="@+id/t4" view="TextView"><Expressions/><location startLine="1889" startOffset="24" endLine="1896" endOffset="47"/></Target><Target id="@+id/t5" view="TextView"><Expressions/><location startLine="1906" startOffset="24" endLine="1913" endOffset="47"/></Target><Target id="@+id/t6" view="TextView"><Expressions/><location startLine="1923" startOffset="24" endLine="1930" endOffset="47"/></Target><Target id="@+id/t7" view="TextView"><Expressions/><location startLine="1940" startOffset="24" endLine="1947" endOffset="47"/></Target><Target id="@+id/t8" view="TextView"><Expressions/><location startLine="1957" startOffset="24" endLine="1964" endOffset="47"/></Target><Target id="@+id/t9" view="TextView"><Expressions/><location startLine="1974" startOffset="24" endLine="1981" endOffset="47"/></Target><Target id="@+id/chart1_percent" view="RelativeLayout"><Expressions/><location startLine="1996" startOffset="16" endLine="2008" endOffset="32"/></Target><Target id="@+id/chart_percent" view="com.github.mikephil.charting.charts.LineChart"><Expressions/><location startLine="2003" startOffset="20" endLine="2007" endOffset="48"/></Target><Target id="@+id/indent_down" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="2011" startOffset="8" endLine="2297" endOffset="59"/></Target><Target id="@+id/i_text" view="TextView"><Expressions/><location startLine="2025" startOffset="12" endLine="2041" endOffset="58"/></Target><Target id="@+id/charge_avg_info" view="ImageView"><Expressions/><location startLine="2042" startOffset="12" endLine="2053" endOffset="63"/></Target><Target id="@+id/i3" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="2054" startOffset="12" endLine="2134" endOffset="63"/></Target><Target id="@+id/mah_3" view="TextView"><Expressions/><location startLine="2062" startOffset="16" endLine="2073" endOffset="62"/></Target><Target id="@+id/text_speed_charge_all" view="TextView"><Expressions/><location startLine="2074" startOffset="16" endLine="2087" endOffset="62"/></Target><Target id="@+id/per_3" view="TextView"><Expressions/><location startLine="2088" startOffset="16" endLine="2098" endOffset="86"/></Target><Target id="@+id/text_percent_charge_all" view="TextView"><Expressions/><location startLine="2099" startOffset="16" endLine="2112" endOffset="62"/></Target><Target id="@+id/i2" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="2135" startOffset="12" endLine="2215" endOffset="63"/></Target><Target id="@+id/mah_2" view="TextView"><Expressions/><location startLine="2143" startOffset="16" endLine="2154" endOffset="62"/></Target><Target id="@+id/text_speed_charge_night" view="TextView"><Expressions/><location startLine="2155" startOffset="16" endLine="2168" endOffset="62"/></Target><Target id="@+id/per_2" view="TextView"><Expressions/><location startLine="2169" startOffset="16" endLine="2179" endOffset="88"/></Target><Target id="@+id/text_percent_charge_night" view="TextView"><Expressions/><location startLine="2180" startOffset="16" endLine="2193" endOffset="62"/></Target><Target id="@+id/i1" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="2216" startOffset="12" endLine="2296" endOffset="63"/></Target><Target id="@+id/mah_1" view="TextView"><Expressions/><location startLine="2224" startOffset="16" endLine="2235" endOffset="62"/></Target><Target id="@+id/text_speed_charge_day" view="TextView"><Expressions/><location startLine="2236" startOffset="16" endLine="2249" endOffset="62"/></Target><Target id="@+id/per_1" view="TextView"><Expressions/><location startLine="2250" startOffset="16" endLine="2260" endOffset="86"/></Target><Target id="@+id/text_percent_charge_day" view="TextView"><Expressions/><location startLine="2261" startOffset="16" endLine="2274" endOffset="62"/></Target><Target id="@+id/update_view" view="LinearLayout"><Expressions/><location startLine="2301" startOffset="8" endLine="2329" endOffset="22"/></Target><Target id="@+id/update_view_btn" view="TextView"><Expressions/><location startLine="2311" startOffset="12" endLine="2328" endOffset="47"/></Target></Targets></Layout>