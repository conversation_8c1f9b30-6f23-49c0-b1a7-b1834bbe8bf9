<?xml version="1.0" encoding="utf-8"?>
<!-- 
 * Current charging session section
 * This section contains:
 * - Section title
 * - Session duration
 * - Current charge rate
 * - Average speed (screen on/off/mixed)
 * - Total charged percentage and mAh
 * - Screen on/off stats
 */
-->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/charge_current_session_root"
    android:orientation="vertical"
    android:background="@drawable/white_block"
    android:padding="8dp"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="9dp"
    android:layout_marginEnd="9dp"
    android:layout_marginBottom="14dp">

    <!-- Section Title with info button -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="8dp">
        
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/current_charge_session"
            android:textColor="?attr/black"
            android:textSize="19sp" />
            
        <ImageView
            android:id="@+id/charge_session_info_button"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_note"
            android:contentDescription="@string/information" />
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="?android:attr/listDivider"
        android:layout_marginBottom="8dp" />

    <!-- Session duration -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="6dp">
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/session_duration"
            android:textColor="?attr/black"
            android:textSize="14sp" />
        <TextView
            android:id="@+id/val_session_duration"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="-"
            android:textColor="?attr/colorr"
            android:textSize="14sp" />
    </LinearLayout>

    <!-- Current charge rate -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="6dp">
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/current_rate"
            android:textColor="?attr/black"
            android:textSize="14sp" />
        <TextView
            android:id="@+id/val_current_rate"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="-"
            android:textColor="?attr/colorr"
            android:textSize="14sp" />
    </LinearLayout>

    <!-- Average speed (screen on) -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="6dp">
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/avg_speed_screen_on"
            android:textColor="?attr/black"
            android:textSize="14sp" />
        <TextView
            android:id="@+id/val_avg_speed_screen_on"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="-"
            android:textColor="?attr/colorr"
            android:textSize="14sp" />
    </LinearLayout>

    <!-- Average speed (screen off) -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="6dp">
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/avg_speed_screen_off"
            android:textColor="?attr/black"
            android:textSize="14sp" />
        <TextView
            android:id="@+id/val_avg_speed_screen_off"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="-"
            android:textColor="?attr/colorr"
            android:textSize="14sp" />
    </LinearLayout>

    <!-- Average speed (mixed) -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="6dp">
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/avg_speed_mixed"
            android:textColor="?attr/black"
            android:textSize="14sp" />
        <TextView
            android:id="@+id/val_avg_speed_mixed"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="-"
            android:textColor="?attr/colorr"
            android:textSize="14sp" />
    </LinearLayout>

    <!-- Total charged percentage -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="6dp">
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/total_charged_percent"
            android:textColor="?attr/black"
            android:textSize="14sp" />
        <TextView
            android:id="@+id/val_total_charged_percent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="-"
            android:textColor="?attr/colorr"
            android:textSize="14sp" />
    </LinearLayout>

    <!-- Total charged mAh -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="6dp">
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/total_charged_mah"
            android:textColor="?attr/black"
            android:textSize="14sp" />
        <TextView
            android:id="@+id/val_total_charged_mah"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="-"
            android:textColor="?attr/colorr"
            android:textSize="14sp" />
    </LinearLayout>

    <!-- Screen on time -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="6dp">
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/screen_on_time"
            android:textColor="?attr/black"
            android:textSize="14sp" />
        <TextView
            android:id="@+id/val_screen_on_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="-"
            android:textColor="?attr/colorr"
            android:textSize="14sp" />
    </LinearLayout>

    <!-- Screen off time -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/screen_off_time"
            android:textColor="?attr/black"
            android:textSize="14sp" />
        <TextView
            android:id="@+id/val_screen_off_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="-"
            android:textColor="?attr/colorr"
            android:textSize="14sp" />
    </LinearLayout>

    <!-- Reset button -->
    <Button
        android:id="@+id/btn_reset_session"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="@string/reset_session"
        android:textAllCaps="false" />
</LinearLayout>