<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_notification" modulePackage="com.tqhit.battery.one" filePath="app\src\main\res\layout\dialog_notification.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.RelativeLayout"><Targets><Target tag="layout/dialog_notification_0" view="RelativeLayout"><Expressions/><location startLine="1" startOffset="0" endLine="167" endOffset="16"/></Target><Target id="@+id/strelka" view="RelativeLayout"><Expressions/><location startLine="24" startOffset="16" endLine="51" endOffset="32"/></Target><Target id="@+id/exit" view="Button"><Expressions/><location startLine="31" startOffset="20" endLine="38" endOffset="74"/></Target><Target id="@+id/up_text" view="TextView"><Expressions/><location startLine="52" startOffset="16" endLine="68" endOffset="59"/></Target><Target id="@+id/main_text" view="TextView"><Expressions/><location startLine="83" startOffset="16" endLine="91" endOffset="54"/></Target><Target id="@+id/cancel_view" view="RelativeLayout"><Expressions/><location startLine="97" startOffset="16" endLine="128" endOffset="32"/></Target><Target id="@+id/cancel_btn" view="Button"><Expressions/><location startLine="103" startOffset="20" endLine="111" endOffset="74"/></Target><Target id="@+id/sdg1" view="LinearLayout"><Expressions/><location startLine="112" startOffset="20" endLine="127" endOffset="34"/></Target><Target id="@+id/text_btn_cancel" view="TextView"><Expressions/><location startLine="118" startOffset="24" endLine="126" endOffset="58"/></Target><Target id="@+id/margin" view="RelativeLayout"><Expressions/><location startLine="129" startOffset="16" endLine="133" endOffset="57"/></Target><Target id="@+id/confirm_change_capacity_error" view="Button"><Expressions/><location startLine="138" startOffset="20" endLine="146" endOffset="74"/></Target><Target id="@+id/sdfgsdfg1" view="LinearLayout"><Expressions/><location startLine="147" startOffset="20" endLine="162" endOffset="34"/></Target><Target id="@+id/text_btn" view="TextView"><Expressions/><location startLine="153" startOffset="24" endLine="161" endOffset="59"/></Target></Targets></Layout>