# Unified Battery Monitoring Migration

## Overview

This document describes the migration from multiple conflicting battery monitoring services to a single unified approach using `CoreBatteryStatsService` as the single source of truth for battery data.

## Problem Statement

The TJ_BatteryOne application was running multiple battery monitoring services simultaneously, causing:

1. **Resource Waste**: Multiple `BroadcastReceiver` instances for `ACTION_BATTERY_CHANGED`
2. **Data Inconsistency**: Different services receiving battery updates at different times
3. **Performance Issues**: Redundant battery polling and processing
4. **Maintenance Complexity**: Bug fixes needed in multiple similar implementations

### Services That Were Running Simultaneously

1. **CoreBatteryStatsService** (New unified approach)
2. **BatteryStatusService** (Legacy discharge module)
3. **BatteryMonitorService** (Legacy general monitoring)
4. **NewChargeMonitorService** (Legacy charge module)

## Solution Implemented

### 1. Disabled Legacy Service Startup

**File: `BatteryApplication.kt`**
- Commented out `startBatteryStatusService()` call
- Added deprecation documentation
- Kept `startCoreBatteryStatsService()` as the single battery monitoring service

**File: `MainActivity.kt`**
- Commented out `BatteryMonitorService` startup
- Replaced `NewChargeMonitorService` with `UnifiedBatteryNotificationService`
- Added deprecation comments explaining the changes

### 2. Created Unified Notification Service

**New Files:**
- `UnifiedBatteryNotificationService.kt`: Replaces legacy charge monitoring
- `UnifiedBatteryNotificationServiceHelper.kt`: Helper for service management

**Key Features:**
- Uses `CoreBatteryStatsProvider` as single data source
- Maps `CoreBatteryStatus` to `NewChargeStatus` for compatibility
- Handles charge/discharge notifications
- Manages anti-theft functionality
- Provides foreground service notification

### 3. Preserved Legacy Code

All legacy services are preserved in `features/legacy/` directories with:
- Clear deprecation comments at the top of each file
- Explanation of issues with the legacy approach
- Migration path documentation
- Reference preservation for future cleanup

### 4. Added Documentation and Tests

**Files:**
- `UnifiedBatteryServiceTest.kt`: Tests for unified approach
- `UNIFIED_BATTERY_MONITORING_MIGRATION.md`: This documentation

## Architecture After Migration

```
CoreBatteryStatsService (Single Source)
    ↓ (CoreBatteryStatus via CoreBatteryStatsProvider)
    ├── StatsChargeRepository → StatsChargeFragment
    ├── BatteryRepository → DischargeFragment  
    └── UnifiedBatteryNotificationService → ChargeNotificationManager
```

## Benefits Achieved

1. **Single Source of Truth**: Only `CoreBatteryStatsService` monitors battery changes
2. **Consistent Data**: All modules receive the same `CoreBatteryStatus` data
3. **Resource Efficiency**: Eliminated duplicate `BroadcastReceiver` registrations
4. **Simplified Architecture**: Clear data flow from single service to all consumers
5. **Maintainability**: Bug fixes only needed in one place

## Testing Requirements

### Manual Testing Steps

1. **Verify Single Service Running**:
   ```bash
   adb shell dumpsys activity services | grep -i battery
   ```
   Should show only `CoreBatteryStatsService` running.

2. **Test Charge Notifications**:
   - Plug in device → Should show charging started notification
   - Unplug device → Should show charging stopped notification
   - Reach target percentage → Should show target reached notification

3. **Test Discharge Functionality**:
   - Unplug device → Discharge session should start
   - Battery statistics should update correctly
   - Screen time tracking should work

4. **Test Data Consistency**:
   - Switch between charge and discharge fragments
   - Verify same battery percentage, current, voltage shown
   - Check logs for consistent `CoreBatteryStatus` data

### ADB Commands for Verification

```bash
# Check running services
adb shell dumpsys activity services | grep -E "(CoreBattery|BatteryStatus|BatteryMonitor|ChargeMonitor)"

# Monitor battery logs
adb logcat -s CoreBatteryStatsService CoreBatteryStatsProvider UnifiedBatteryNotificationService

# Check for duplicate battery receivers
adb shell dumpsys activity broadcasts | grep ACTION_BATTERY_CHANGED
```

## Migration Status

### ✅ Completed
- [x] Disabled legacy service startup in `BatteryApplication.kt`
- [x] Disabled legacy service startup in `MainActivity.kt`
- [x] Created `UnifiedBatteryNotificationService`
- [x] Added deprecation comments to legacy services
- [x] Created test suite for unified approach
- [x] Updated service dependencies in MainActivity

### 🔄 In Progress
- [ ] Verify all notification functionality works correctly
- [ ] Performance testing to confirm resource savings
- [ ] Complete ADB testing verification

### 📋 Future Cleanup (Phase 2)
- [ ] Remove legacy service files after verification period
- [ ] Update dependency injection modules
- [ ] Remove unused data models (`NewBatteryStatus`, `NewChargeStatus`)
- [ ] Consolidate notification managers

## Rollback Plan

If issues are discovered, the migration can be quickly rolled back by:

1. Uncommenting service startup calls in `BatteryApplication.kt`
2. Uncommenting service startup calls in `MainActivity.kt`
3. Reverting to legacy `NewChargeMonitorService` in MainActivity
4. All legacy code is preserved and ready to use

## Performance Impact

**Expected Improvements:**
- Reduced CPU usage from eliminating duplicate battery monitoring
- Lower memory usage from fewer service instances
- Reduced battery drain from consolidated monitoring
- Faster app startup due to fewer services being initialized

**Monitoring Metrics:**
- Service count: Should drop from 4 to 1 battery monitoring service
- Memory usage: Monitor via Android Studio Profiler
- Battery usage: Check in Android Settings → Battery → App usage
