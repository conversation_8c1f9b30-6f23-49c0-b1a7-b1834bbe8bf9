{"logs": [{"outputFile": "com.tqhit.battery.one.app-mergeDebugResources-99:/values-am/values-am.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5444be4bc77930bd89cfbb9f2224d8e4\\transformed\\navigation-ui-2.8.9\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,159", "endColumns": "103,109", "endOffsets": "154,264"}, "to": {"startLines": "267,268", "startColumns": "4,4", "startOffsets": "21996,22100", "endColumns": "103,109", "endOffsets": "22095,22205"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\93d3043f0a8b9466a00a736e170a6ddc\\transformed\\appcompat-1.7.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,203,301,407,493,596,713,791,867,958,1051,1143,1237,1337,1430,1525,1618,1709,1800,1880,1980,2080,2176,2278,2378,2477,2627,2723", "endColumns": "97,97,105,85,102,116,77,75,90,92,91,93,99,92,94,92,90,90,79,99,99,95,101,99,98,149,95,79", "endOffsets": "198,296,402,488,591,708,786,862,953,1046,1138,1232,1332,1425,1520,1613,1704,1795,1875,1975,2075,2171,2273,2373,2472,2622,2718,2798"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,292", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "920,1018,1116,1222,1308,1411,1528,1606,1682,1773,1866,1958,2052,2152,2245,2340,2433,2524,2615,2695,2795,2895,2991,3093,3193,3292,3442,23826", "endColumns": "97,97,105,85,102,116,77,75,90,92,91,93,99,92,94,92,90,90,79,99,99,95,101,99,98,149,95,79", "endOffsets": "1013,1111,1217,1303,1406,1523,1601,1677,1768,1861,1953,2047,2147,2240,2335,2428,2519,2610,2690,2790,2890,2986,3088,3188,3287,3437,3533,23901"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5c5c72c6ff4a7863322da50648a25e99\\transformed\\browser-1.8.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,151,246,352", "endColumns": "95,94,105,96", "endOffsets": "146,241,347,444"}, "to": {"startLines": "85,142,143,144", "startColumns": "4,4,4,4", "startOffsets": "7036,11059,11154,11260", "endColumns": "95,94,105,96", "endOffsets": "7127,11149,11255,11352"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5b80dcbf636dc26335bd1b8e4f16f918\\transformed\\material-1.12.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,261,332,400,475,557,638,727,829,906,965,1029,1114,1176,1234,1319,1382,1444,1502,1568,1630,1685,1781,1838,1897,1953,2020,2125,2205,2286,2378,2463,2544,2673,2746,2817,2931,3013,3089,3140,3191,3257,3323,3396,3467,3542,3610,3683,3754,3821,3919,4004,4071,4158,4246,4320,4388,4473,4524,4602,4666,4746,4828,4890,4954,5017,5083,5178,5273,5358,5449,5504,5559,5635,5714,5789", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,70,67,74,81,80,88,101,76,58,63,84,61,57,84,62,61,57,65,61,54,95,56,58,55,66,104,79,80,91,84,80,128,72,70,113,81,75,50,50,65,65,72,70,74,67,72,70,66,97,84,66,86,87,73,67,84,50,77,63,79,81,61,63,62,65,94,94,84,90,54,54,75,78,74,70", "endOffsets": "256,327,395,470,552,633,722,824,901,960,1024,1109,1171,1229,1314,1377,1439,1497,1563,1625,1680,1776,1833,1892,1948,2015,2120,2200,2281,2373,2458,2539,2668,2741,2812,2926,3008,3084,3135,3186,3252,3318,3391,3462,3537,3605,3678,3749,3816,3914,3999,4066,4153,4241,4315,4383,4468,4519,4597,4661,4741,4823,4885,4949,5012,5078,5173,5268,5353,5444,5499,5554,5630,5709,5784,5855"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,89,90,141,145,148,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,282,293,294,296", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "759,3538,3609,3677,3752,3834,4602,4691,4793,7402,7461,10974,11357,11580,17521,17606,17669,17731,17789,17855,17917,17972,18068,18125,18184,18240,18307,18412,18492,18573,18665,18750,18831,18960,19033,19104,19218,19300,19376,19427,19478,19544,19610,19683,19754,19829,19897,19970,20041,20108,20206,20291,20358,20445,20533,20607,20675,20760,20811,20889,20953,21033,21115,21177,21241,21304,21370,21465,21560,21645,21736,21791,23235,23906,23985,24130", "endLines": "22,50,51,52,53,54,62,63,64,89,90,141,145,148,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,282,293,294,296", "endColumns": "12,70,67,74,81,80,88,101,76,58,63,84,61,57,84,62,61,57,65,61,54,95,56,58,55,66,104,79,80,91,84,80,128,72,70,113,81,75,50,50,65,65,72,70,74,67,72,70,66,97,84,66,86,87,73,67,84,50,77,63,79,81,61,63,62,65,94,94,84,90,54,54,75,78,74,70", "endOffsets": "915,3604,3672,3747,3829,3910,4686,4788,4865,7456,7520,11054,11414,11633,17601,17664,17726,17784,17850,17912,17967,18063,18120,18179,18235,18302,18407,18487,18568,18660,18745,18826,18955,19028,19099,19213,19295,19371,19422,19473,19539,19605,19678,19749,19824,19892,19965,20036,20103,20201,20286,20353,20440,20528,20602,20670,20755,20806,20884,20948,21028,21110,21172,21236,21299,21365,21460,21555,21640,21731,21786,21841,23306,23980,24055,24196"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\237635df39b25799c092d66a208ce67d\\transformed\\jetified-foundation-release\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,85", "endOffsets": "137,223"}, "to": {"startLines": "304,305", "startColumns": "4,4", "startOffsets": "24781,24868", "endColumns": "86,85", "endOffsets": "24863,24949"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\179e6486bd57a16ea175623aa423e7ed\\transformed\\jetified-material3-release\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,166,276,384,491,585,675,782,910,1020,1149,1231,1329,1416,1509,1619,1738,1841,1964,2089,2213,2361,2477,2590,2704,2819,2907,3002,3112,3231,3326,3428,3530,3650,3776,3880,3976,4050,4143,4235,4334,4418,4503,4605,4686,4769,4869,4966,5061,5156,5241,5343,5442,5541,5659,5740,5841", "endColumns": "110,109,107,106,93,89,106,127,109,128,81,97,86,92,109,118,102,122,124,123,147,115,112,113,114,87,94,109,118,94,101,101,119,125,103,95,73,92,91,98,83,84,101,80,82,99,96,94,94,84,101,98,98,117,80,100,96", "endOffsets": "161,271,379,486,580,670,777,905,1015,1144,1226,1324,1411,1504,1614,1733,1836,1959,2084,2208,2356,2472,2585,2699,2814,2902,2997,3107,3226,3321,3423,3525,3645,3771,3875,3971,4045,4138,4230,4329,4413,4498,4600,4681,4764,4864,4961,5056,5151,5236,5338,5437,5536,5654,5735,5836,5933"}, "to": {"startLines": "149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "11638,11749,11859,11967,12074,12168,12258,12365,12493,12603,12732,12814,12912,12999,13092,13202,13321,13424,13547,13672,13796,13944,14060,14173,14287,14402,14490,14585,14695,14814,14909,15011,15113,15233,15359,15463,15559,15633,15726,15818,15917,16001,16086,16188,16269,16352,16452,16549,16644,16739,16824,16926,17025,17124,17242,17323,17424", "endColumns": "110,109,107,106,93,89,106,127,109,128,81,97,86,92,109,118,102,122,124,123,147,115,112,113,114,87,94,109,118,94,101,101,119,125,103,95,73,92,91,98,83,84,101,80,82,99,96,94,94,84,101,98,98,117,80,100,96", "endOffsets": "11744,11854,11962,12069,12163,12253,12360,12488,12598,12727,12809,12907,12994,13087,13197,13316,13419,13542,13667,13791,13939,14055,14168,14282,14397,14485,14580,14690,14809,14904,15006,15108,15228,15354,15458,15554,15628,15721,15813,15912,15996,16081,16183,16264,16347,16447,16544,16639,16734,16819,16921,17020,17119,17237,17318,17419,17516"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\42f95d9fa807b14415e836fc15872a54\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-am\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "75", "startColumns": "4", "startOffsets": "5955", "endColumns": "131", "endOffsets": "6082"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\237e0b5db534c615c4317f1b214e3e7f\\transformed\\jetified-play-services-ads-24.2.0\\res\\values-am\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,239,286,337,398,461,546,606,696,776,876,926,984,1087,1158,1195,1269,1301,1337,1383,1447,1486", "endColumns": "39,46,50,60,62,84,59,89,79,99,49,57,102,70,36,73,31,35,45,63,38,55", "endOffsets": "238,285,336,397,460,545,605,695,775,875,925,983,1086,1157,1194,1268,1300,1336,1382,1446,1485,1541"}, "to": {"startLines": "264,265,266,271,272,273,274,275,276,277,278,279,280,281,285,286,287,288,289,290,291,306", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "21846,21890,21941,22370,22435,22502,22591,22655,22749,22833,22937,22991,23053,23160,23470,23511,23589,23625,23665,23715,23783,24954", "endColumns": "43,50,54,64,66,88,63,93,83,103,53,61,106,74,40,77,35,39,49,67,42,59", "endOffsets": "21885,21936,21991,22430,22497,22586,22650,22744,22828,22932,22986,23048,23155,23230,23506,23584,23620,23660,23710,23778,23821,25009"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8bd7feaae90e869538df51f29dd16595\\transformed\\jetified-media3-ui-1.6.1\\res\\values-am\\values-am.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,283,482,664,744,825,901,986,1067,1133,1195,1279,1362,1429,1492,1553,1619,1719,1821,1922,1991,2067,2135,2201,2280,2360,2422,2487,2540,2597,2643,2704,2762,2837,2896,2958,3017,3074,3138,3188,3244,3314,3384,3436", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,79,80,75,84,80,65,61,83,82,66,62,60,65,99,101,100,68,75,67,65,78,79,61,64,52,56,45,60,57,74,58,61,58,56,63,49,55,69,69,51,63", "endOffsets": "278,477,659,739,820,896,981,1062,1128,1190,1274,1357,1424,1487,1548,1614,1714,1816,1917,1986,2062,2130,2196,2275,2355,2417,2482,2535,2592,2638,2699,2757,2832,2891,2953,3012,3069,3133,3183,3239,3309,3379,3431,3495"}, "to": {"startLines": "2,11,15,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,378,577,7525,7605,7686,7762,7847,7928,7994,8056,8140,8223,8290,8353,8414,8480,8580,8682,8783,8852,8928,8996,9062,9141,9221,9283,9961,10014,10071,10117,10178,10236,10311,10370,10432,10491,10548,10612,10662,10718,10788,10858,10910", "endLines": "10,14,18,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140", "endColumns": "17,12,12,79,80,75,84,80,65,61,83,82,66,62,60,65,99,101,100,68,75,67,65,78,79,61,64,52,56,45,60,57,74,58,61,58,56,63,49,55,69,69,51,63", "endOffsets": "373,572,754,7600,7681,7757,7842,7923,7989,8051,8135,8218,8285,8348,8409,8475,8575,8677,8778,8847,8923,8991,9057,9136,9216,9278,9343,10009,10066,10112,10173,10231,10306,10365,10427,10486,10543,10607,10657,10713,10783,10853,10905,10969"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b572512e02266e069f95737c22215ab9\\transformed\\jetified-media3-exoplayer-1.6.1\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,117,175,237,297,369,432,521,602", "endColumns": "61,57,61,59,71,62,88,80,65", "endOffsets": "112,170,232,292,364,427,516,597,663"}, "to": {"startLines": "115,116,117,118,119,120,121,122,123", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "9348,9410,9468,9530,9590,9662,9725,9814,9895", "endColumns": "61,57,61,59,71,62,88,80,65", "endOffsets": "9405,9463,9525,9585,9657,9720,9809,9890,9956"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9e0a763189144907fb0197c2b097244b\\transformed\\jetified-ui-release\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,188,265,357,453,535,613,696,778,856,934,1015,1085,1158,1231,1303,1383,1448", "endColumns": "82,76,91,95,81,77,82,81,77,77,80,69,72,72,71,79,64,115", "endOffsets": "183,260,352,448,530,608,691,773,851,929,1010,1080,1153,1226,1298,1378,1443,1559"}, "to": {"startLines": "65,66,86,87,88,146,147,269,270,283,284,295,297,298,299,301,302,303", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4870,4953,7132,7224,7320,11419,11497,22210,22292,23311,23389,24060,24201,24274,24347,24520,24600,24665", "endColumns": "82,76,91,95,81,77,82,81,77,77,80,69,72,72,71,79,64,115", "endOffsets": "4948,5025,7219,7315,7397,11492,11575,22287,22365,23384,23465,24125,24269,24342,24414,24595,24660,24776"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d56ddc8f70c1b6c4f2dfff25a6818549\\transformed\\jetified-play-services-base-18.5.0\\res\\values-am\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,291,426,544,642,765,884,988,1086,1210,1309,1450,1569,1700,1823,1879,1932", "endColumns": "97,134,117,97,122,118,103,97,123,98,140,118,130,122,55,52,66", "endOffsets": "290,425,543,641,764,883,987,1085,1209,1308,1449,1568,1699,1822,1878,1931,1998"}, "to": {"startLines": "67,68,69,70,71,72,73,74,76,77,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5030,5132,5271,5393,5495,5622,5745,5853,6087,6215,6318,6463,6586,6721,6848,6908,6965", "endColumns": "101,138,121,101,126,122,107,101,127,102,144,122,134,126,59,56,70", "endOffsets": "5127,5266,5388,5490,5617,5740,5848,5950,6210,6313,6458,6581,6716,6843,6903,6960,7031"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f908cdc45776521b403beeef1508641c\\transformed\\core-1.16.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,248,345,444,540,642,742", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "143,243,340,439,535,637,737,838"}, "to": {"startLines": "55,56,57,58,59,60,61,300", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3915,4008,4108,4205,4304,4400,4502,24419", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "4003,4103,4200,4299,4395,4497,4597,24515"}}]}]}