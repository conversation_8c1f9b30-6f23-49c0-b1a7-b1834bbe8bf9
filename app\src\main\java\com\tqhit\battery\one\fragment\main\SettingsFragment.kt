package com.tqhit.battery.one.fragment.main

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.provider.Settings
import android.view.View
import android.widget.Toast
import androidx.fragment.app.viewModels
import com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment
import com.tqhit.adlib.sdk.data.local.PreferencesHelper
import com.tqhit.battery.one.R
import com.tqhit.battery.one.databinding.FragmentSettingsBinding
import com.tqhit.battery.one.dialog.capacity.SetupPasswordDialog
import com.tqhit.battery.one.dialog.theme.SelectThemeDialog
import com.tqhit.battery.one.dialog.theme.SelectColorDialog
import com.tqhit.battery.one.dialog.language.SelectLanguageDialog
import com.tqhit.battery.one.dialog.utils.NotificationDialog
import com.tqhit.battery.one.viewmodel.AppViewModel
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject
import kotlin.getValue
import androidx.core.net.toUri
//import com.applovin.sdk.AppLovinSdk
//import com.applovin.sdk.AppLovinSdkConfiguration
//import com.applovin.sdk.AppLovinSdkConfiguration.ConsentFlowUserGeography
import com.tqhit.battery.one.ads.core.ApplovinInterstitialAdManager
import com.tqhit.battery.one.databinding.ItemSlideLayout6Binding
import com.tqhit.battery.one.viewmodel.battery.BatteryViewModel

@AndroidEntryPoint
class SettingsFragment : AdLibBaseFragment<FragmentSettingsBinding>() {
    override val binding by lazy {
        FragmentSettingsBinding.inflate(layoutInflater)
    }
    private val appViewModel: AppViewModel by viewModels()
    private val batteryViewModel: BatteryViewModel by viewModels()
    @Inject lateinit var applovinInterstitialAdManager: ApplovinInterstitialAdManager
    @Inject lateinit var preferencesHelper: PreferencesHelper

    override fun setupUI() {
        super.setupUI()
        setupWorkInBackgroundButton()
        setupPrivacySettingButton()
        setupPrivacyButton()
        setupThemeChangeButton()
        setupColorChangeButton()
        setupLanguageChangeButton()
        setupVibrationSwitch()
        setupChargeNotificationSwitch()
        setupDischargeNotificationSwitch()
        setupNotificationSettingsButton()
        setupAnimationOverlaySwitch()
        setupAnimationOverlayTimeSwitch()
        setupAntiThief()
        setupAntiThiefSwitches()
        setupDebuggerButton()
    }

    private fun setupWorkInBackgroundButton() {
        binding.workInBackgoundButton.setOnClickListener {
            requestBatteryOptimizationPermission()
        }
    }

    private fun setupPrivacySettingButton() {
        binding.privacySettingButton.visibility = appViewModel.isConsentFlowUserGeography()
            .let { if (it) View.VISIBLE else View.GONE }
        binding.privacySettingButton.setOnClickListener {
//            val cmpService = AppLovinSdk.getInstance(requireActivity()).cmpService
//            cmpService.showCmpForExistingUser(requireActivity()) { error ->
//                if (null == error)
//                {
//                    // The CMP alert was shown successfully.
//                }
//            }
        }
    }

    private fun setupPrivacyButton() {
        binding.privacyButton.setOnClickListener {
            startActivity(Intent(Intent.ACTION_VIEW, appViewModel.getPrivacyPolicyUrl().toUri()))
        }
    }
    
    private fun setupThemeChangeButton() {
        binding.changeTheme.setOnClickListener {
            applovinInterstitialAdManager.showInterstitialAd(
                "default_iv",
                requireActivity(),
            ) {
                showThemeDialog()
            }
        }
    }

    private fun setupColorChangeButton() {
        binding.changeSecondColorTheme.setOnClickListener {
            applovinInterstitialAdManager.showInterstitialAd(
                "default_iv",
                requireActivity(),
            ) {
                showColorDialog()
            }
        }
    }

    private fun setupLanguageChangeButton() {
        binding.changeLang.setOnClickListener {
            applovinInterstitialAdManager.showInterstitialAd(
                "default_iv",
                requireActivity(),
            ) {
                showLanguageDialog()
            }
        }
    }
    
    private fun setupVibrationSwitch() {
        binding.switchVibration.isChecked = appViewModel.isVibrationEnabled()
        binding.switchVibration.setOnCheckedChangeListener { _, isChecked ->
            appViewModel.setVibrationEnabled(isChecked)
        }
    }

    private fun setupChargeNotificationSwitch() {
        binding.switchIsChargeNotify.isChecked = appViewModel.isChargeNotificationEnabled()
        binding.switchIsChargeNotify.setOnCheckedChangeListener { _, isChecked ->
            appViewModel.setChargeNotificationEnabled(isChecked)
        }
    }

    private fun setupDischargeNotificationSwitch() {
        binding.switchIsDischargeNotify.isChecked = appViewModel.isDischargeNotificationEnabled()
        binding.switchIsDischargeNotify.setOnCheckedChangeListener { _, isChecked ->
            appViewModel.setDischargeNotificationEnabled(isChecked)
        }
    }
    
    private fun setupNotificationSettingsButton() {
        binding.buttonSettingsNotify.setOnClickListener {
            val intent = Intent().apply {
                action = android.provider.Settings.ACTION_APP_NOTIFICATION_SETTINGS
                putExtra(android.provider.Settings.EXTRA_APP_PACKAGE, requireContext().packageName)
            }
            startActivity(intent)
        }
    }

    private fun setupAnimationOverlaySwitch() {
        binding.switchEnableAnimation.isChecked = appViewModel.isAnimationOverlayEnabled()
        binding.switchAnimationTimeBlock.visibility = if (binding.switchEnableAnimation.isChecked) {
            View.VISIBLE
        } else {
            View.GONE
        }
        binding.switchEnableAnimation.setOnCheckedChangeListener { _, isChecked ->
            appViewModel.setAnimationOverlayEnabled(isChecked)
            binding.switchAnimationTimeBlock.visibility = if (isChecked) {
                View.VISIBLE
            } else {
                View.GONE
            }
        }
    }

    private fun setupAntiThief() {
        binding.antiThiefInfo.setOnClickListener {
            applovinInterstitialAdManager.showInterstitialAd(
                "default_iv",
                requireActivity(),
            ) {
                NotificationDialog(
                    context = requireContext(),
                    title = getString(R.string.anti_thief),
                    message = getString(R.string.anti_thief_info),
                ).show()
            }
        }
    }

    private fun setupAnimationOverlayTimeSwitch() {
        binding.switchEnableAnimationTime.isChecked = appViewModel.isAnimationOverlayTimeEnabled()
        binding.switchEnableAnimationTime.setOnCheckedChangeListener { _, isChecked ->
            appViewModel.setAnimationOverlayTimeEnabled(isChecked)
        }
    }
    
    private fun showThemeDialog() {
        if (!isAdded) return
        SelectThemeDialog(
            activity = requireActivity()
        ).show()
    }

    private fun showColorDialog() {
        if (!isAdded) return
        SelectColorDialog(
            activity = requireActivity()
        ).show()
    }

    private fun showLanguageDialog() {
        if (!isAdded) return
        SelectLanguageDialog(
            activity = requireActivity(),
            appViewModel
        ).show()
    }

    private fun setupAntiThiefSwitches() {
        // Anti-Thief Enable Switch
        binding.switchEnableAntiThief.isChecked = appViewModel.isAntiThiefEnabled()
        binding.switchEnableAntiThief.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                if (!appViewModel.isAntiThiefPasswordSet()) {
                    SetupPasswordDialog(
                        context = requireContext(),
                        onConfirm = { password ->
                            if (password.isNotBlank()) {
                                appViewModel.setAntiThiefPassword(password)
                                appViewModel.setAntiThiefEnabled(true)
                                binding.switchEnableAntiThief.isChecked = true
                            } else {
                                binding.switchEnableAntiThief.isChecked = false
                            }
                        },
                        onCancel = {
                            binding.switchEnableAntiThief.isChecked = false
                        }
                    ).show()
                } else {
                    appViewModel.setAntiThiefEnabled(true)
                }
            } else {
                appViewModel.setAntiThiefPassword("")
                appViewModel.setAntiThiefEnabled(false)
            }
        }

        // Anti-Thief Sound Switch
        binding.switchEnableAntiThiefSound.isChecked = appViewModel.isAntiThiefSoundEnabled()
        binding.switchEnableAntiThiefSound.setOnCheckedChangeListener { _, isChecked ->
            appViewModel.setAntiThiefSoundEnabled(isChecked)
        }
    }

    private fun setupDebuggerButton() {
        android.util.Log.d("SettingsFragment", "Setting up debug button")

        // Make sure the debug section is visible
        binding.testNewDischarge.visibility = View.VISIBLE

        binding.testNewDischarge.setOnClickListener {
            android.util.Log.d("SettingsFragment", "Debug button clicked")
            // Legacy TestNewDischargeActivity removed - opening main activity instead
            val intent = Intent(requireContext(), com.tqhit.battery.one.activity.main.MainActivity::class.java)
            startActivity(intent)
        }

        // Log the current visibility state
        android.util.Log.d("SettingsFragment", "Debug button visibility: ${binding.testNewDischarge.visibility == View.VISIBLE}")

        binding.debuggerButton.setOnClickListener {
//            AppLovinSdk.getInstance( context ).showCreativeDebugger()
        }
        binding.mediationDebuggerButton.setOnClickListener {
//            AppLovinSdk.getInstance( context ).showMediationDebugger()
        }
    }

    private fun requestBatteryOptimizationPermission() {
        try {
            if (batteryViewModel.isIgnoringBatteryOptimizations()) {
                Toast.makeText(requireContext(), R.string.permission_granted, Toast.LENGTH_SHORT).show()
                return
            }

            val intent =
                Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS).apply {
                    data = "package:${requireContext().packageName}".toUri()
                }
            startActivity(intent)
        } catch (e: Exception) {
            Toast.makeText(requireContext(), R.string.unexpected_error, Toast.LENGTH_SHORT).show()
        }
    }
}