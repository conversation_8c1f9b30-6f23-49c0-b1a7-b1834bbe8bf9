(com.tqhit.adlib.sdk.AdLibHiltApplication$androidx.lifecycle.LifecycleObserver-com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity(androidx.appcompat.app.AppCompatActivity&androidx.viewpager.widget.PagerAdapterandroid.widget.ProgressBar+com.tqhit.adlib.sdk.base.ui.AdLibBaseDialogMcom.tqhit.battery.one.features.emoji.domain.repository.BatteryStyleRepositoryNcom.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepositorykotlin.Enumjava.lang.ExceptionWcom.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepositoryExceptionJcom.tqhit.battery.one.features.emoji.presentation.customize.CustomizeEventandroidx.fragment.app.Fragmentandroidx.lifecycle.ViewModel(androidx.recyclerview.widget.ListAdapter4androidx.recyclerview.widget.RecyclerView.ViewHolder2androidx.recyclerview.widget.DiffUtil.ItemCallbackandroid.view.ViewMcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEventIcom.tqhit.battery.one.features.emoji.presentation.gallery.NavigationEvent-com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment8androidx.recyclerview.widget.RecyclerView.ItemDecorationandroid.app.DialogBcom.tqhit.battery.one.features.stats.charge.cache.StatsChargeCacheLcom.tqhit.battery.one.features.stats.charge.repository.StatsChargeRepositoryPcom.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProviderandroid.app.ServiceHcom.tqhit.battery.one.features.stats.discharge.cache.CurrentSessionCacheHcom.tqhit.battery.one.features.stats.discharge.cache.DischargeRatesCache+androidx.lifecycle.DefaultLifecycleObserverFcom.tqhit.battery.one.features.stats.discharge.domain.ValidationResult=com.tqhit.battery.one.features.stats.health.cache.HealthCacheGcom.tqhit.battery.one.features.stats.health.repository.HealthRepository1androidx.recyclerview.widget.RecyclerView.Adapter2com.tqhit.battery.one.manager.graph.HistoryManager                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       