  SharedPreferences android.content  BatteryStyleRepositoryImplTest 4com.tqhit.battery.one.features.emoji.data.repository  CustomizationRepositoryImplTest 4com.tqhit.battery.one.features.emoji.data.repository  GetBatteryStylesUseCaseTest 4com.tqhit.battery.one.features.emoji.domain.use_case  LoadCustomizationUseCaseTest 4com.tqhit.battery.one.features.emoji.domain.use_case  SaveCustomizationUseCaseTest 4com.tqhit.battery.one.features.emoji.domain.use_case  CustomizeViewModelTest ;com.tqhit.battery.one.features.emoji.presentation.customize  BatteryGalleryViewModelTest 9com.tqhit.battery.one.features.emoji.presentation.gallery  BatteryStyleAdapterTest Acom.tqhit.battery.one.features.emoji.presentation.gallery.adapter  UnifiedBatteryServiceTest $com.tqhit.battery.one.features.stats  (StatsChargeViewModelPowerCalculationTest +com.tqhit.battery.one.features.stats.charge  ScreenTimeGapValidationTest .com.tqhit.battery.one.features.stats.discharge  AppLifecycleManagerTest 5com.tqhit.battery.one.features.stats.discharge.domain  DischargeCalculatorTest 5com.tqhit.battery.one.features.stats.discharge.domain  EnhancedScreenTimeTrackerTest 5com.tqhit.battery.one.features.stats.discharge.domain  FullSessionReEstimatorTest 5com.tqhit.battery.one.features.stats.discharge.domain  ScreenTimeValidationServiceTest 5com.tqhit.battery.one.features.stats.discharge.domain  TimeConverterTest 5com.tqhit.battery.one.features.stats.discharge.domain  #ScreenTimeEstimationIntegrationTest :com.tqhit.battery.one.features.stats.discharge.integration  !ScreenTimeUiUpdateIntegrationTest :com.tqhit.battery.one.features.stats.discharge.integration  DischargeSessionRepositoryTest 9com.tqhit.battery.one.features.stats.discharge.repository  !CalculateBatteryHealthUseCaseTest 2com.tqhit.battery.one.features.stats.health.domain  BackgroundPermissionManagerTest com.tqhit.battery.one.utils  Assert 	org.junit                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                