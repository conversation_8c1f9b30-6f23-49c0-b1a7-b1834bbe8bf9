package com.tqhit.battery.one.fragment.main

import android.animation.ValueAnimator
import android.text.SpannableString
import android.text.style.ForegroundColorSpan
import android.util.Log
import android.view.animation.AccelerateDecelerateInterpolator
import androidx.activity.result.contract.ActivityResultContracts
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment
import com.tqhit.battery.one.R
import com.tqhit.battery.one.ads.core.ApplovinInterstitialAdManager
import com.tqhit.battery.one.databinding.NewFragmentDischargeBinding
import com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmLowDialog
import com.tqhit.battery.one.dialog.utils.NotificationDialog
import com.tqhit.battery.one.service.VibrationService
import com.tqhit.battery.one.utils.BatteryUtils
import com.tqhit.battery.one.viewmodel.AppViewModel
import com.tqhit.battery.one.viewmodel.battery.BatteryViewModel
import dagger.hilt.android.AndroidEntryPoint
import jakarta.inject.Inject
import kotlin.math.max
import kotlinx.coroutines.launch

@AndroidEntryPoint
class DischargeFragment : AdLibBaseFragment<NewFragmentDischargeBinding>() {
    companion object {
        private const val TAG = "DischargeFragment"
    }

    override val binding by lazy { 
        Log.d(TAG, "Inflating NewFragmentDischargeBinding")
        Log.d(TAG, "LAYOUT CHECK: Using new_fragment_discharge.xml layout (old DischargeFragment)")
        NewFragmentDischargeBinding.inflate(layoutInflater)
    }

    private val batteryViewModel: BatteryViewModel by viewModels()
    private val appViewModel: AppViewModel by viewModels()
    @Inject lateinit var applovinInterstitialAdManager: ApplovinInterstitialAdManager
    @Inject lateinit var vibrationService: VibrationService
    private var currentPercentage = 0

    // region Permission Handling
    private val permissionLauncher =
        registerForActivityResult(ActivityResultContracts.RequestPermission()) { isGranted ->
            if (isGranted) {
                showBatteryAlarmLowDialog()
            } else {
                NotificationDialog(
                    requireActivity(),
                    getString(R.string.notification),
                    getString(R.string.notify_access)
                )
                    .show()
            }
        }
    // endregion

    override fun setupData() {
        super.setupData()
        Log.d(TAG, "setupData: Using new_fragment_discharge.xml with sections:")
        Log.d(TAG, "  - Status and Estimates section: ${binding.includeStatusAndEstimates.root.id}")
        Log.d(TAG, "  - Loss of Charge section: ${binding.includeLossOfCharge.root.id}")
        Log.d(TAG, "  - Current Session Details section: ${binding.includeCurrentSessionDetails.root.id}")
        Log.d(TAG, "  - Actions section: ${binding.includeActionsSection.root.id}")
        
        initializeBatteryPercentage()
        observeBatteryUpdates()
        observeScreenTime()
        observeDischargeSession()
        observeAverageDischargeSpeedAllSession()
    }

    override fun setupListener() {
        super.setupListener()
        Log.d(TAG, "setupListener: Setting up click listeners with new_fragment_discharge.xml")
        setupInfoBlockListeners()
        setupResetSessionListener()
        setupBatteryAlarmListener()
    }

    // region UI Listeners
    private fun setupInfoBlockListeners() {
        Log.d(TAG, "setupInfoBlockListeners: Setting up info block listeners for the new layout")
        binding.includeStatusAndEstimates.saeRlScreenOnEstimate.setOnClickListener {
            applovinInterstitialAdManager.showInterstitialAd(
                "default_iv",
                requireActivity(),
            ) {
                showInfoDialog(R.string.what_time, R.string.day_info)
            }
        }

        binding.includeStatusAndEstimates.saeRlMixedUsageEstimate.setOnClickListener {
            applovinInterstitialAdManager.showInterstitialAd(
                "default_iv",
                requireActivity(),
            ) {
                showInfoDialog(R.string.what_time, R.string.all_info)
            }
        }

        binding.includeStatusAndEstimates.saeRlScreenOffEstimate.setOnClickListener {
            applovinInterstitialAdManager.showInterstitialAd(
                "default_iv",
                requireActivity(),
            ) {
                showInfoDialog(R.string.what_time, R.string.night_info)
            }
        }

        binding.includeLossOfCharge.locIvInfoButton.setOnClickListener {
            applovinInterstitialAdManager.showInterstitialAd(
                "default_iv",
                requireActivity(),
            ) {
                showInfoDialog(R.string.info_in_current_session, R.string.loss_charge_info)
            }
        }

        binding.includeCurrentSessionDetails.csdIvInfoButton.setOnClickListener {
            applovinInterstitialAdManager.showInterstitialAd(
                "default_iv",
                requireActivity(),
            ) {
                showInfoDialog(R.string.current_session, R.string.current_session_info_discharge)
            }
        }

        // Include any additional info buttons if available in the new layout
    }

    private fun setupResetSessionListener() {
        Log.d(TAG, "setupResetSessionListener: Setting up reset session button for the new layout")
        binding.includeActionsSection.actionsBtnResetSession.setOnClickListener {
            batteryViewModel.clearDischargeSessions()
        }
    }

    private fun showInfoDialog(titleResId: Int, messageResId: Int) {
        NotificationDialog(requireContext(), getString(titleResId), getString(messageResId)).show()
    }
    // endregion


    // region Battery Alarm Dialog
    private fun setupBatteryAlarmListener() {
        Log.d(TAG, "setupBatteryAlarmListener: Setting up battery alarm button for the new layout")
        binding.includeActionsSection.actionsBtnBatteryAlarm.setOnClickListener {
            applovinInterstitialAdManager.showInterstitialAd(
                "default_iv",
                requireActivity(),
            ) {
                showBatteryAlarmLowDialog()
            }
        }
    }

    private fun showBatteryAlarmLowDialog() {
        SelectBatteryAlarmLowDialog(requireActivity(), permissionLauncher, appViewModel, vibrationService).show()
    }
    // endregion

    // region Battery Percentage
    private fun initializeBatteryPercentage() {
        Log.d(TAG, "initializeBatteryPercentage: Initializing battery percentage in the new layout")
        lifecycleScope.launch {
            val initialPercentage = batteryViewModel.batteryPercentage.value
            currentPercentage = initialPercentage
            binding.includeStatusAndEstimates.saeTvPercentage.text = buildString {
                append(initialPercentage.toInt())
                append("%")
            }
            setColoredChargingText(initialPercentage.toInt())
            Log.d(TAG, "Battery percentage initialized to $initialPercentage%")
        }
    }

    private fun observeBatteryUpdates() {
        lifecycleScope.launch {
            batteryViewModel.batteryPercentage.collect { percentage ->
                if (percentage != currentPercentage) {
                    Log.d(TAG, "Battery percentage changed from $currentPercentage% to $percentage%")
                    animateBatteryUpdate(percentage.toInt())
                }
            }
        }
    }

    private fun animateBatteryUpdate(newPercentage: Int) {
        val animator = ValueAnimator.ofInt(currentPercentage.toInt(), newPercentage)
        animator.duration = 500
        animator.interpolator = AccelerateDecelerateInterpolator()

        animator.addUpdateListener { animation ->
            val animatedValue = animation.animatedValue as Int
            binding.includeStatusAndEstimates.saeTvPercentage.text = buildString {
                append(animatedValue)
                append("%")
            }
            setColoredChargingText(animatedValue)
        }

        animator.start()
        currentPercentage = newPercentage
    }

    private fun setColoredChargingText(percent: Int) {
        val context = binding.root.context
        val percentStr = "$percent%"
        val fullText =
                context.getString(com.tqhit.battery.one.R.string.charging, percent.toString())
        val start = fullText.indexOf(percentStr)
        val end = start + percentStr.length
        val spannable = SpannableString(fullText)
        val greenColor = getThemeColor(context, com.tqhit.battery.one.R.attr.colorr)
        spannable.setSpan(
                ForegroundColorSpan(greenColor),
                start,
                end,
                android.text.Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        binding.includeStatusAndEstimates.saeTvFormattedStatusText.text = spannable
    }

    private fun getThemeColor(context: android.content.Context, attr: Int): Int {
        val typedValue = android.util.TypedValue()
        val theme = context.theme
        theme.resolveAttribute(attr, typedValue, true)
        return typedValue.data
    }
    // endregion

    // region Screen Time Monitoring
    private fun observeScreenTime() {
        Log.d(TAG, "observeScreenTime: Setting up screen time monitoring with the new layout")
        lifecycleScope.launch {
            batteryViewModel.screenOnTimeRemaining.collect { time ->
                val formatted = BatteryUtils.formatTimeToHoursMinutesFromMillis(time)
                binding.includeStatusAndEstimates.saeTvScreenOnTime.text = formatted
                Log.d(TAG, "Screen ON time remaining updated: $formatted")
            }
        }
        lifecycleScope.launch {
            batteryViewModel.usageStyleTimeRemaining.collect { time ->
                val formatted = BatteryUtils.formatTimeToHoursMinutesFromMillis(time)
                binding.includeStatusAndEstimates.saeTvMixedUsageTime.text = formatted
                Log.d(TAG, "Mixed usage time remaining updated: $formatted")
            }
        }
        lifecycleScope.launch {
            batteryViewModel.screenOffTimeRemaining.collect { time ->
                val formatted = BatteryUtils.formatTimeToHoursMinutesFromMillis(time)
                binding.includeStatusAndEstimates.saeTvScreenOffTime.text = formatted
                Log.d(TAG, "Screen OFF time remaining updated: $formatted")
            }
        }

        // Add observers for 100% battery estimates
        // Note: These fields may need to be adapted or added to the new layout if they don't exist
        lifecycleScope.launch {
            batteryViewModel.screenOnTimeRemainingAt100.collect { time ->
                // Update when the field is available in the new layout
                // binding.includeStatusAndEstimates.saeTvScreenOnTimeAt100.text = BatteryUtils.formatTimeToHoursMinutesFromMillis(time)
                Log.d(TAG, "Screen ON time at 100%: ${BatteryUtils.formatTimeToHoursMinutesFromMillis(time)}")
            }
        }
        lifecycleScope.launch {
            batteryViewModel.usageStyleTimeRemainingAt100.collect { time ->
                // Update when the field is available in the new layout
                // binding.includeStatusAndEstimates.saeTvMixedUsageTimeAt100.text = BatteryUtils.formatTimeToHoursMinutesFromMillis(time)
                Log.d(TAG, "Mixed usage time at 100%: ${BatteryUtils.formatTimeToHoursMinutesFromMillis(time)}")
            }
        }
        lifecycleScope.launch {
            batteryViewModel.screenOffTimeRemainingAt100.collect { time ->
                // Update when the field is available in the new layout
                // binding.includeStatusAndEstimates.saeTvScreenOffTimeAt100.text = BatteryUtils.formatTimeToHoursMinutesFromMillis(time)
                Log.d(TAG, "Screen OFF time at 100%: ${BatteryUtils.formatTimeToHoursMinutesFromMillis(time)}")
            }
        }
    }
    // endregion

    // region Discharge Session Monitoring
    private fun observeDischargeSession() {
        Log.d(TAG, "observeDischargeSession: Setting up discharge session monitoring with the new layout")
        lifecycleScope.launch {
            batteryViewModel.dischargeStartTimeSession.collect { time ->
                val (startTime, endTime) = BatteryUtils.formatSessionTimeRange(time, 0)
                val textString =
                        if (startTime != null &&
                                        (endTime == null || !batteryViewModel.isCharging.value)
                        ) {
                            getString(R.string.since, startTime)
                        } else if (startTime != null && endTime != null) {
                            getString(R.string.since_above, startTime, endTime)
                        } else {
                            ""
                        }
                
                // Update when the field is available in the new layout
                binding.includeCurrentSessionDetails.csdTvSessionStartTime.text = textString
                
                binding.includeCurrentSessionDetails.csdTvTotalTimeValue.text =
                        BatteryUtils.formatElapsedTime(
                                max(0, batteryViewModel.dischargeEndTimeSession.value - time)
                        )
                
                Log.d(TAG, "Discharge session start time updated: $textString")
            }
        }

        lifecycleScope.launch {
            batteryViewModel.dischargeEndTimeSession.collect { time ->
                val elapsed = BatteryUtils.formatElapsedTime(
                        time - batteryViewModel.dischargeStartTimeSession.value
                )
                binding.includeCurrentSessionDetails.csdTvTotalTimeValue.text = elapsed
                
                val (startTime, endTime) =
                        BatteryUtils.formatSessionTimeRange(
                                batteryViewModel.dischargeStartTimeSession.value,
                                time
                        )
                val textString =
                        if (startTime != null &&
                                        (endTime == null || !batteryViewModel.isCharging.value)
                        ) {
                            getString(R.string.since, startTime)
                        } else if (startTime != null && endTime != null) {
                            getString(R.string.since_above, startTime, endTime)
                        } else {
                            ""
                        }
                
                binding.includeCurrentSessionDetails.csdTvSessionStartTime.text = textString
                Log.d(TAG, "Discharge session end time updated, total elapsed: $elapsed")
            }
        }

        lifecycleScope.launch {
            batteryViewModel.dischargeAverageSpeedSession.collect { rate ->
                val formatted = String.format("%.1f", -rate)
                binding.includeCurrentSessionDetails.csdTvAvgSpeedPercentValue.text = formatted
                Log.d(TAG, "Discharge average speed updated: $formatted %/h")
            }
        }

        lifecycleScope.launch {
            batteryViewModel.dischargeAverageSpeedMilliAmperesSession.collect { value ->
                val formatted = (-value).toString()
                binding.includeCurrentSessionDetails.csdTvAvgSpeedMahValue.text = formatted
                Log.d(TAG, "Discharge average speed in mAh updated: $formatted mAh/h")
            }
        }

        lifecycleScope.launch {
            batteryViewModel.dischargeEndPercentSession.collect { percent ->
                val percentRange = " (${batteryViewModel.dischargeStartPercentSession.value} → $percent)"
                
                // Update total consumed in the current session details section
                val percentageDropped = (-(batteryViewModel.dischargeStartPercentSession.value - percent))
                        .toInt()
                        .toString()
                binding.includeCurrentSessionDetails.csdTvTotalConsumedPercentValue.text = percentageDropped

                // Calculate and display screen on percentage decrease
                val screenOnPercentDecrease =
                        batteryViewModel.dischargeScreenOnPercentCurrentSession.toDouble()
                binding.includeLossOfCharge.locTvScreenOnPercentageDropped.text = 
                        String.format("%.1f", screenOnPercentDecrease)

                // Calculate and display screen off percentage decrease
                val screenOffPercentDecrease =
                        batteryViewModel.dischargeScreenOffPercentCurrentSession.toDouble()
                binding.includeLossOfCharge.locTvScreenOffPercentageDropped.text = 
                        String.format("%.1f", screenOffPercentDecrease)

                // Calculate and display screen on mA based on percentage
                val screenOnMilliAmperes =
                        (screenOnPercentDecrease * batteryViewModel.batteryCapacity.value / 100.0)
                                .toInt()
                binding.includeLossOfCharge.locTvScreenOnMahConsumed.text = screenOnMilliAmperes.toString()

                // Calculate and display screen off mA based on percentage
                val screenOffMilliAmperes =
                        (screenOffPercentDecrease * batteryViewModel.batteryCapacity.value / 100.0)
                                .toInt()
                binding.includeLossOfCharge.locTvScreenOffMahConsumed.text = screenOffMilliAmperes.toString()
                
                Log.d(TAG, "Discharge end percent updated: $percentRange, dropped: $percentageDropped%")
                Log.d(TAG, "Screen ON: ${screenOnPercentDecrease}% ($screenOnMilliAmperes mAh)")
                Log.d(TAG, "Screen OFF: ${screenOffPercentDecrease}% ($screenOffMilliAmperes mAh)")
            }
        }

        lifecycleScope.launch {
            batteryViewModel.dischargeTotalMilliAmperesSession.collect { value ->
                val formatted = (-value).toString()
                binding.includeCurrentSessionDetails.csdTvTotalConsumedMahValue.text = formatted
                Log.d(TAG, "Total discharge mAh updated: $formatted mAh")
            }
        }

        lifecycleScope.launch {
            batteryViewModel.dischargeScreenOffAverageSpeedSession.collect { value ->
                val formatted = String.format("%.1f", -value)
                binding.includeCurrentSessionDetails.csdTvScreenOffPercentValue.text = formatted
                Log.d(TAG, "Screen OFF discharge rate updated: $formatted %/h")
            }
        }

        lifecycleScope.launch {
            batteryViewModel.dischargeScreenOffMilliAmperesSession.collect { value ->
                val formatted = (-value).toString()
                binding.includeCurrentSessionDetails.csdTvScreenOffMahValue.text = formatted
                Log.d(TAG, "Screen OFF discharge rate in mAh updated: $formatted mAh/h")
            }
        }

        lifecycleScope.launch {
            batteryViewModel.dischargeScreenOnAverageSpeedSession.collect { value ->
                val formatted = String.format("%.1f", -value)
                binding.includeCurrentSessionDetails.csdTvScreenOnPercentValue.text = formatted
                Log.d(TAG, "Screen ON discharge rate updated: $formatted %/h")
            }
        }

        lifecycleScope.launch {
            batteryViewModel.dischargeScreenOnMilliAmperesSession.collect { value ->
                val formatted = (-value).toString()
                binding.includeCurrentSessionDetails.csdTvScreenOnMahValue.text = formatted
                Log.d(TAG, "Screen ON discharge rate in mAh updated: $formatted mAh/h")
            }
        }

        lifecycleScope.launch {
            batteryViewModel.dischargeRightNowPercentPerHourSession.collect { rate ->
                val formatted = String.format("%.1f", -rate)
                binding.includeCurrentSessionDetails.csdTvCurrentRateValue.text = formatted
                Log.d(TAG, "Current discharge rate updated: $formatted %/h")
            }
        }
    }

    private fun observeAverageDischargeSpeedAllSession() {
        Log.d(TAG, "observeAverageDischargeSpeedAllSession: Setting up average stats monitoring")
        // These fields may need to be added to the new layout
        // For now, just log the values
        lifecycleScope.launch {
            batteryViewModel.averageSpeed.collect { rate ->
                Log.d(TAG, "Average discharge speed (percent/h): ${String.format("%.1f", -rate)}")
            }
        }

        lifecycleScope.launch {
            batteryViewModel.averageMilliAmperes.collect { value ->
                Log.d(TAG, "Average discharge speed (mAh/h): ${-value}")
            }
        }

        lifecycleScope.launch {
            batteryViewModel.averageScreenOffSpeed.collect { value ->
                Log.d(TAG, "Average screen OFF discharge speed (percent/h): ${String.format("%.1f", -value)}")
            }
        }

        lifecycleScope.launch {
            batteryViewModel.averageScreenOffMilliAmperes.collect { value ->
                Log.d(TAG, "Average screen OFF discharge speed (mAh/h): ${-value}")
            }
        }

        lifecycleScope.launch {
            batteryViewModel.averageScreenOnSpeed.collect { value ->
                Log.d(TAG, "Average screen ON discharge speed (percent/h): ${String.format("%.1f", -value)}")
            }
        }

        lifecycleScope.launch {
            batteryViewModel.averageScreenOnMilliAmperes.collect { value ->
                Log.d(TAG, "Average screen ON discharge speed (mAh/h): ${-value}")
            }
        }
    }
    // endregion
}
