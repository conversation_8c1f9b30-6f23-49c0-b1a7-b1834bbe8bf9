## PRD: `CoreBatteryModule`

**Module Location:** `features/stats/corebattery/`

**1. Introduction & Goal**
To establish a single, reliable source for raw battery status information (percentage, charging state, current, voltage, temperature, plugged state, timestamp) for the entire application, eliminating redundancy and ensuring consistency.

**2. Core Features**
*   Continuous background monitoring of `Intent.ACTION_BATTERY_CHANGED`.
*   Extraction and normalization of raw battery data into a `CoreBatteryStatus` object.
*   Provision of `CoreBatteryStatus` as a `StateFlow` for consumption by other modules.
*   Foreground service operation to ensure reliability.

**3. Key Components & Files**

*   **`data/core_battery_status.kt`**
    *   **Class:** `CoreBatteryStatus` (Data Class)
        *   **Attributes:** `percentage: Int`, `isCharging: Boolean`, `pluggedSource: Int`, `currentMicroAmperes: Long`, `voltageMillivolts: Int`, `temperatureCelsius: Float`, `timestampEpochMillis: Long`
        *   **Purpose:** Immutable representation of a single battery status snapshot.
        *   **Logging:** Includes a companion object method `logCreation(status: CoreBatteryStatus)` and an `init` block to log its creation details.

*   **`service/core_battery_stats_service.kt`**
    *   **Class:** `CoreBatteryStatsService` (Android Service, `@AndroidEntryPoint`)
        *   **Injects:** `CoreBatteryStatsProvider`
        *   **Attributes:** `batteryManager: BatteryManager`, `batteryEventReceiver: BroadcastReceiver?`
        *   **Constants:** `TAG`, `NOTIFICATION_ID`, `CHANNEL_ID`, `CHANNEL_NAME`, `ACTION_START_MONITORING`, `ACTION_STOP_MONITORING`.
        *   **Methods:**
            *   `onCreate()`: Initializes `batteryManager`, calls `createNotificationChannel()`.
            *   `onStartCommand(intent: Intent?, flags: Int, startId: Int): Int`: Handles start/stop actions, starts foreground service, calls `startMonitoring()`.
            *   `isReceiverRegistered(): Boolean`: Checks if `batteryEventReceiver` is active.
            *   `startMonitoring()`: Registers `batteryEventReceiver` for `ACTION_BATTERY_CHANGED`. Emits initial status.
            *   `stopMonitoringAndService()`: Unregisters receiver, stops foreground, stops self.
            *   `getInitialBatteryStatus(): CoreBatteryStatus`: Retrieves current battery status for immediate use.
            *   `extractBatteryStatus(intent: Intent): CoreBatteryStatus`: Parses `Intent` data into `CoreBatteryStatus`. Performs µA normalization if needed (though the plan is to trust `BATTERY_PROPERTY_CURRENT_NOW` as µA). Logs extracted values.
            *   `createDefaultStatus(): CoreBatteryStatus`: Provides a fallback status.
            *   `createServiceNotification(status: CoreBatteryStatus): Notification`: Builds the foreground service notification.
            *   `updateForegroundNotification(status: CoreBatteryStatus)`: (Optional) Updates the notification content.
            *   `createNotificationChannel()`: Creates the notification channel for Android O+.
            *   `onDestroy()`: Calls `stopMonitoringAndService()`.
            *   `onBind(intent: Intent?): IBinder?`: Returns null.
        *   **Logic Flow (onReceive in `batteryEventReceiver`):**
            1.  Receive `ACTION_BATTERY_CHANGED`.
            2.  Call `extractBatteryStatus(intent)` to get `CoreBatteryStatus`.
            3.  Call `coreBatteryStatsProvider.updateStatus(newStatus)`.
            4.  Log received and parsed status.

*   **`service/core_battery_service_helper.kt`**
    *   **Class:** `CoreBatteryServiceHelper` (Singleton, `@Inject constructor`)
        *   **Injects:** `@ApplicationContext context: Context`
        *   **Constants:** `TAG`
        *   **Methods:**
            *   `isServiceRunning(): Boolean`: Checks if `CoreBatteryStatsService` is active.
            *   `startService()`: Sends `ACTION_START_MONITORING` intent to `CoreBatteryStatsService`. Handles foreground service start for O+. Logs action.
            *   `stopService()`: Sends `ACTION_STOP_MONITORING` intent. Logs action.
        *   **Purpose:** Centralized control for starting and stopping the `CoreBatteryStatsService`.

*   **`domain/core_battery_stats_provider.kt`**
    *   **Interface:** `CoreBatteryStatsProvider`
        *   **Properties:** `coreBatteryStatusFlow: StateFlow<CoreBatteryStatus?>` (Read-only)
        *   **Methods:** `updateStatus(newStatus: CoreBatteryStatus): Unit`, `getCurrentStatus(): CoreBatteryStatus?`
    *   **Class:** `DefaultCoreBatteryStatsProvider` (Singleton, `@Inject constructor`, implements `CoreBatteryStatsProvider`)
        *   **Attributes:** `_statusFlow: MutableStateFlow<CoreBatteryStatus?>`
        *   **Constants:** `TAG`
        *   **Methods:** Implements interface methods. `updateStatus` sets `_statusFlow.value`. Logs updates.
        *   **Purpose:** Holds and exposes the latest `CoreBatteryStatus` as a reactive stream.

*   **`di/core_battery_di_module.kt`**
    *   **Module:** `CoreBatteryDIModule` (Hilt Module)
        *   **Binds:** `CoreBatteryStatsProvider` to `DefaultCoreBatteryStatsProvider`.
        *   **Purpose:** Provides dependencies for the core battery module.

**4. Implementation Plan & Testing (Phase 1)**

*   **Phase 1.1: Setup & Data Class**
    *   **Task 1.1.1:** Create module structure: `features/stats/corebattery` with `data`, `service`, `domain`, `di` subfolders.
    *   **Task 1.1.2:** Implement `CoreBatteryStatus.kt`.
        *   **Test:** Basic instantiation and logging in a unit test (not Android-dependent).
*   **Phase 1.2: Provider Implementation**
    *   **Task 1.2.1:** Implement `core_battery_stats_provider.kt` (interface and `DefaultCoreBatteryStatsProvider`).
    *   **Task 1.2.2:** Implement `core_battery_di_module.kt`.
        *   **Test:** Unit test `DefaultCoreBatteryStatsProviderTest.kt` (as provided in the previous response, verifying flow emission and `getCurrentStatus`).
*   **Phase 1.3: Service Implementation (Core Logic & Notification)**
    *   **Task 1.3.1:** Implement skeleton of `CoreBatteryStatsService.kt` with `onCreate`, `onStartCommand` (basic intent handling), `onBind`, `onDestroy`.
    *   **Task 1.3.2:** Implement `createNotificationChannel()` and `createServiceNotification()`.
    *   **Task 1.3.3:** Implement `extractBatteryStatus(intent: Intent): CoreBatteryStatus`.
        *   **Test:** Unit test `CoreBatteryStatsServiceRobolectricTest.kt` focusing on `extractBatteryStatus` logic (as provided previously).
    *   **Task 1.3.4:** Implement `getInitialBatteryStatus()` and `createDefaultStatus()`.
*   **Phase 1.4: Service Monitoring Logic & Provider Integration**
    *   **Task 1.4.1:** Implement `startMonitoring()` in `CoreBatteryStatsService` (register receiver, emit initial status via provider).
    *   **Task 1.4.2:** Implement `BroadcastReceiver` logic within `startMonitoring()` to call `extractBatteryStatus` and update `coreBatteryStatsProvider`.
    *   **Task 1.4.3:** Implement `stopMonitoringAndService()` in `CoreBatteryStatsService`.
    *   **Task 1.4.4:** Complete `onStartCommand` logic to correctly call `startMonitoring`, `startForeground`, and `stopMonitoringAndService`.
*   **Phase 1.5: Service Helper & Application Integration**
    *   **Task 1.5.1:** Implement `core_battery_service_helper.kt`.
    *   **Task 1.5.2:** Integrate `CoreBatteryServiceHelper` into `BatteryApplication.kt` to start the service on app launch.
        *   **Test (Manual/Log-based):**
            1.  Compile and run the app.
            2.  Verify logs from `CoreBatteryStatsService` (created, started, receiver registered).
            3.  Verify logs from `CoreBatteryStatsProvider` showing status updates.
            4.  Verify the foreground service notification appears.
            5.  Use ADB or emulator controls to change battery level/charging state and confirm logs show new `CoreBatteryStatus` being emitted.
            6.  (Optional) Create a temporary "DebugFragment" that injects `CoreBatteryStatsProvider` and displays `coreBatteryStatusFlow.value` in TextViews to visually confirm data flow.
            7.  Test stopping the service (if a debug mechanism is added, e.g., via the DebugFragment or ADB intent) and ensure it cleans up.

---

## PRD: New Lean `StatsChargeModule`

**Module Location:** `features/stats/charge/`

**1. Introduction & Goal**
To provide essential charge-specific statistics and estimations, consuming raw data from the `CoreBatteryModule`. This module focuses on a lean implementation for displaying core charging information.

**2. Core Features (Phased)**
*   Display current battery percentage, charging status, and key electrical parameters.
*   Track basic charge session metrics (start/end time, start/end percentage, duration, % charged).
*   Estimate time to full charge based on a simple average rate.
*   (Optional) Notify on reaching a user-defined target percentage.

**3. Key Components & Files**

*   **`data/stats_charge_status.kt`**
    *   **Class:** `StatsChargeStatus` (Data Class)
        *   **Attributes:** Mirrors `CoreBatteryStatus` or selects relevant fields: `percentage: Int`, `isCharging: Boolean`, `currentMicroAmperes: Long`, `voltageMillivolts: Int`, `temperatureCelsius: Float`.
        *   **Purpose:** Tailored status representation for the charge feature.
        *   **Logging:** Similar `logCreation` as `CoreBatteryStatus`.

*   **`data/stats_charge_session.kt`**
    *   **Class:** `StatsChargeSession` (Data Class)
        *   **Attributes:** `startTimeEpochMillis: Long`, `endTimeEpochMillis: Long? = null`, `startPercentage: Int`, `endPercentage: Int? = null`, `isActive: Boolean = true`.
        *   Calculated properties (getters): `durationMillis: Long`, `percentageCharged: Int`.
        *   **Purpose:** Represents a single, simple charge session.
        *   **Logging:** `logCreation` and `logUpdate` methods.

*   **`cache/stats_charge_prefs_cache.kt`**
    *   **Interface:** `StatsChargeCache`
        *   **Methods:** `suspend fun saveActiveSession(session: StatsChargeSession): Unit`, `suspend fun getActiveSession(): StatsChargeSession?`, `suspend fun clearActiveSession(): Unit`.
    *   **Class:** `PrefsStatsChargeCache` (Singleton, `@Inject constructor`, implements `StatsChargeCache`)
        *   **Injects:** `PreferencesHelper`, `Gson`
        *   **Constants:** `KEY_ACTIVE_CHARGE_SESSION`
        *   **Purpose:** Persists the currently active charge session to SharedPreferences.
        *   **Logging:** Log save/load/clear operations.

*   **`repository/stats_charge_repository.kt`**
    *   **Interface:** `StatsChargeRepository`
        *   **Properties:** `statsChargeStatusFlow: Flow<StatsChargeStatus>`, `activeChargeSessionFlow: Flow<StatsChargeSession?>`
        *   **Methods:** (Potentially) `suspend fun resetCurrentSession(): Unit`
    *   **Class:** `DefaultStatsChargeRepository` (Singleton, `@Inject constructor`, implements `StatsChargeRepository`)
        *   **Injects:** `CoreBatteryStatsProvider`, `StatsChargeCache`, `AppRepository` (for battery capacity if needed for advanced estimation later)
        *   **Attributes:** `_statsChargeStatusFlow: MutableStateFlow<StatsChargeStatus>`, `_activeChargeSessionFlow: MutableStateFlow<StatsChargeSession?>`, `lastCoreStatus: CoreBatteryStatus?`
        *   **Constants:** `TAG`
        *   **`init` block:**
            1.  Launch coroutine to load active session from `statsChargeCache`.
            2.  Launch coroutine to collect `coreBatteryStatsProvider.coreBatteryStatusFlow`.
        *   **Logic Flow (on collecting `CoreBatteryStatus`):**
            1.  Map `CoreBatteryStatus` to `StatsChargeStatus` and emit.
            2.  If `CoreBatteryStatus.isCharging` is true and `_activeChargeSessionFlow.value` is null or inactive:
                *   Create new `StatsChargeSession` (startPercentage from `CoreBatteryStatus.percentage`, startTime = current time).
                *   Emit it and save to `statsChargeCache`. Log session start.
            3.  If `CoreBatteryStatus.isCharging` is false and `_activeChargeSessionFlow.value` is active:
                *   Finalize the active session (set endTime, endPercentage).
                *   Emit it (now inactive) and save to `statsChargeCache` (or clear active, and potentially save to a history if implemented later). Log session end.
            4.  If `CoreBatteryStatus.isCharging` is true and `_activeChargeSessionFlow.value` is active:
                *   (Optional for lean version, can be added later) Update current session metrics if significant change.
        *   `resetCurrentSession()`: Clears active session from cache and flow.
        *   **Logging:** Log status mapping, session start/end events.

*   **`domain/calculate_simple_charge_estimate_use_case.kt`**
    *   **Class:** `CalculateSimpleChargeEstimateUseCase` (`@Inject constructor`)
        *   **Constants:** `TAG`, `DEFAULT_CHARGE_RATE_PERCENT_PER_HOUR` (e.g., 20.0)
        *   **Method:** `fun execute(currentStatus: StatsChargeStatus, activeSession: StatsChargeSession?, batteryCapacityMah: Int, targetPercentage: Int = 100): Long` (returns timeToTargetMillis)
            *   **Logic:**
                1.  If not charging or already at/above target, return 0.
                2.  Calculate `percentageToCharge = targetPercentage - currentStatus.percentage`.
                3.  Determine charge rate:
                    *   If `activeSession` is valid (exists, duration > e.g., 5 mins, percentageCharged > 0): Calculate rate from session (`percentageCharged / sessionDurationHours`).
                    *   Else: Use `DEFAULT_CHARGE_RATE_PERCENT_PER_HOUR`.
                4.  Calculate `timeHours = percentageToCharge / chargeRate`.
                5.  Convert `timeHours` to milliseconds.
            *   **Logging:** Log input parameters, calculated rate, and estimated time.

*   **`presentation/stats_charge_view_model.kt`**
    *   **Class:** `StatsChargeViewModel` (ViewModel, `@HiltViewModel`)
        *   **Injects:** `StatsChargeRepository`, `CalculateSimpleChargeEstimateUseCase`, `AppRepository` (for target percentage preference and capacity).
        *   **UI State Data Class:** `StatsChargeUiState(isLoading: Boolean, status: StatsChargeStatus?, session: StatsChargeSession?, timeToFullMillis: Long, timeToTargetMillis: Long, targetPercentage: Int)`
        *   **Attributes:** `_uiState: MutableStateFlow<StatsChargeUiState>`, `uiState: StateFlow<StatsChargeUiState>`
        *   **`init` block:**
            1.  Combine flows: `statsChargeRepository.statsChargeStatusFlow`, `statsChargeRepository.activeChargeSessionFlow`, `appRepository.chargeAlarmPercentFlow`.
            2.  In the `combine` block:
                *   Get `batteryCapacityMah` from `appRepository`.
                *   Call `calculateSimpleChargeEstimateUseCase.execute(...)` for timeToFull and timeToTarget.
                *   Update `_uiState` with all data.
        *   **Method:** `fun setTargetChargePercentage(percentage: Int)`: Saves to `appRepository`.
        *   **Method:** `fun resetChargeSession()`: Calls `statsChargeRepository.resetCurrentSession()`.
        *   **Logging:** Log UI state updates.

*   **`presentation/stats_charge_fragment.kt`**
    *   **Class:** `StatsChargeFragment` (Fragment, `@AndroidEntryPoint`)
        *   **Injects:** `StatsChargeViewModel`
        *   **UI:** Simple layout to display:
            *   Percentage, charging status, current, voltage, temp.
            *   Session: Start time, duration, % charged.
            *   Estimates: Time to full, Time to target (with target % displayed).
            *   SeekBar for target percentage.
            *   Reset button.
        *   **Logic:** Observe `viewModel.uiState` and update TextViews. SeekBar updates `viewModel.setTargetChargePercentage()`. Reset button calls `viewModel.resetChargeSession()`.
        *   **Logging:** Log UI updates.

*   **`di/stats_charge_di_module.kt`**
    *   **Module:** `StatsChargeDIModule`
        *   **Binds:** `StatsChargeRepository` to `DefaultStatsChargeRepository`, `StatsChargeCache` to `PrefsStatsChargeCache`.
        *   **Provides:** `Gson` if not already globally available.

**4. Implementation Plan & Testing (Phase 2)**

*   **Phase 2.1: Basic Structure & Data Flow (No Session/Estimation)**
    *   **Task 2.1.1:** Create module structure: `features/stats/charge` with subfolders.
    *   **Task 2.1.2:** Implement `StatsChargeStatus.kt`.
    *   **Task 2.1.3:** Implement skeleton `DefaultStatsChargeRepository` that injects `CoreBatteryStatsProvider` and maps `CoreBatteryStatus` to `StatsChargeStatus`, emitting it. No session logic yet.
    *   **Task 2.1.4:** Implement `StatsChargeViewModel` to collect from `StatsChargeRepository` and expose basic status in `StatsChargeUiState` (session and estimates will be null/default).
    *   **Task 2.1.5:** Implement `StatsChargeFragment` to display only the basic `StatsChargeStatus` fields.
    *   **Task 2.1.6:** Setup DI module.
        *   **Test (Manual/Log-based):**
            1.  Compile and run. Navigate to `StatsChargeFragment`.
            2.  Verify basic battery info (percentage, charging, current, voltage, temp) is displayed and updates correctly by observing logs from `CoreBatteryModule` through to `StatsChargeViewModel` and Fragment.
            3.  Ensure no crashes and basic data flow is working.

*   **Phase 2.2: Simple Session Tracking**
    *   **Task 2.2.1:** Implement `StatsChargeSession.kt`.
    *   **Task 2.2.2:** Implement `stats_charge_prefs_cache.kt` (interface and Prefs implementation).
    *   **Task 2.2.3:** Enhance `DefaultStatsChargeRepository`:
        *   Add logic to start a new `StatsChargeSession` when `isCharging` becomes true. Save to cache, emit via `activeChargeSessionFlow`. Log: "STATS_CHARGE_REPO: New session started: ID=xxx, StartPercent=yy%".
        *   Add logic to finalize the active session when `isCharging` becomes false. Update endTime, endPercentage. Save to cache (or clear active). Log: "STATS_CHARGE_REPO: Session ended: ID=xxx, EndPercent=zz%, Duration=ttt ms".
        *   Load active session from cache in `init`.
    *   **Task 2.2.4:** Update `StatsChargeViewModel` to include `session: StatsChargeSession?` in `StatsChargeUiState` and populate it from `activeChargeSessionFlow`.
    *   **Task 2.2.5:** Update `StatsChargeFragment` to display session duration and percentage charged. Implement reset button functionality.
        *   **Test (Manual/Log-based):**
            1.  Plug in device: Verify session starts (logs, UI).
            2.  Observe session duration updating.
            3.  Unplug device: Verify session ends (logs, UI updates to show final stats or clears).
            4.  Restart app while charging: Verify active session is restored.
            5.  Test reset button during an active session.

*   **Phase 2.3: Basic Charge Time Estimation**
    *   **Task 2.3.1:** Implement `CalculateSimpleChargeEstimateUseCase.kt`.
        *   **Test:** Unit test the use case with various inputs (charging, not charging, different percentages, session states). Log inputs and outputs.
    *   **Task 2.3.2:** Update `StatsChargeViewModel`:
        *   Inject the use case.
        *   In the `combine` block, call the use case to get `timeToFullMillis` and `timeToTargetMillis`.
        *   Update `StatsChargeUiState` with these estimates and the `targetPercentage`.
        *   Implement `setTargetChargePercentage` to save preference via `AppRepository`.
    *   **Task 2.3.3:** Update `StatsChargeFragment`:
        *   Display "Time to Full" and "Time to Target".
        *   Implement SeekBar for `targetPercentage`. Initialize its value from `uiState.targetPercentage`.
        *   **Test (Manual/Log-based):**
            1.  While charging, observe "Time to Full" and "Time to Target".
            2.  Change target percentage with SeekBar and verify "Time to Target" updates.
            3.  Verify estimates are reasonable (e.g., using default rate initially, then session-based rate after a few minutes of charging).
            4.  Check logs from `CalculateSimpleChargeEstimateUseCase` for calculation details.

---

## PRD: New Lean `StatsDischargeModule`

**Module Location:** `features/stats/discharge/`

**1. Introduction & Goal**
To provide essential discharge-specific statistics and estimations, consuming raw data from the `CoreBatteryModule`. This module focuses on a lean implementation for displaying core discharging information, primarily time remaining.

**2. Core Features (Phased)**
*   Display current battery percentage and key electrical parameters when not charging.
*   Track basic discharge session metrics (start/end time, start/end percentage, duration, % discharged).
*   Estimate "Time Remaining" based on a simple average discharge rate.
*   (Optional) Notify on reaching a user-defined low battery threshold.

**3. Key Components & Files** (Structure mirrors `StatsChargeModule` closely)

*   **`data/stats_discharge_status.kt`**
    *   **Class:** `StatsDischargeStatus` (Data Class)
        *   **Attributes:** `percentage: Int`, `currentMicroAmperes: Long`, `voltageMillivolts: Int`, `temperatureCelsius: Float`. (Note: `isCharging` will always be false here, derived externally).
        *   **Purpose:** Tailored status for discharge.
        *   **Logging:** `logCreation`.

*   **`data/stats_discharge_session.kt`**
    *   **Class:** `StatsDischargeSession` (Data Class)
        *   **Attributes:** `startTimeEpochMillis: Long`, `endTimeEpochMillis: Long? = null`, `startPercentage: Int`, `endPercentage: Int? = null`, `isActive: Boolean = true`.
        *   Calculated properties: `durationMillis: Long`, `percentageDischarged: Int`.
        *   **Purpose:** Represents a single discharge session.
        *   **Logging:** `logCreation`, `logUpdate`.

*   **`cache/stats_discharge_prefs_cache.kt`**
    *   **Interface:** `StatsDischargeCache`
        *   **Methods:** `suspend fun saveActiveSession(session: StatsDischargeSession): Unit`, `suspend fun getActiveSession(): StatsDischargeSession?`, `suspend fun clearActiveSession(): Unit`.
    *   **Class:** `PrefsStatsDischargeCache` (Singleton, `@Inject constructor`, implements `StatsDischargeCache`)
        *   **Injects:** `PreferencesHelper`, `Gson`
        *   **Constants:** `KEY_ACTIVE_DISCHARGE_SESSION`
        *   **Logging:** Log save/load/clear.

*   **`repository/stats_discharge_repository.kt`**
    *   **Interface:** `StatsDischargeRepository`
        *   **Properties:** `statsDischargeStatusFlow: Flow<StatsDischargeStatus>`, `activeDischargeSessionFlow: Flow<StatsDischargeSession?>`
        *   **Methods:** `suspend fun resetCurrentSession(): Unit`
    *   **Class:** `DefaultStatsDischargeRepository` (Singleton, `@Inject constructor`, implements `StatsDischargeRepository`)
        *   **Injects:** `CoreBatteryStatsProvider`, `StatsDischargeCache`, `AppRepository` (for capacity).
        *   **Attributes:** Similar to charge repository.
        *   **`init` block:** Load session, collect `CoreBatteryStatus`.
        *   **Logic Flow (on collecting `CoreBatteryStatus`):**
            1.  If `CoreBatteryStatus.isCharging` is true:
                *   If an active discharge session exists, finalize it. Log: "STATS_DISCHARGE_REPO: Session ended (device charging): ID=xxx, EndPercent=zz%, Duration=ttt ms".
            2.  If `CoreBatteryStatus.isCharging` is false:
                *   Map `CoreBatteryStatus` to `StatsDischargeStatus` and emit.
                *   If no active discharge session: Start a new `StatsDischargeSession`. Log: "STATS_DISCHARGE_REPO: New session started: ID=xxx, StartPercent=yy%".
                *   (Optional for lean) Update current active session if significant change.
        *   **Logging:** Status mapping, session start/end.

*   **`domain/calculate_simple_discharge_estimate_use_case.kt`**
    *   **Class:** `CalculateSimpleDischargeEstimateUseCase` (`@Inject constructor`)
        *   **Constants:** `TAG`, `DEFAULT_DISCHARGE_RATE_MA` (e.g., 150.0 mA, a moderate mixed use)
        *   **Method:** `fun execute(currentStatus: StatsDischargeStatus, activeSession: StatsDischargeSession?, batteryCapacityMah: Int): Long` (returns timeRemainingMillis)
            *   **Logic:**
                1.  Calculate `remainingCapacityMah = batteryCapacityMah * (currentStatus.percentage / 100.0)`.
                2.  Determine discharge rate (mA):
                    *   If `activeSession` is valid (duration > 5 mins, percentageDischarged > 0):
                        *   `mahDischarged = batteryCapacityMah * (session.percentageDischarged / 100.0)`.
                        *   `sessionDurationHours = session.durationMillis / (3600.0 * 1000.0)`.
                        *   `rate = mahDischarged / sessionDurationHours`.
                    *   Else if `currentStatus.currentMicroAmperes < -10000` (significant discharge current):
                        *   `rate = abs(currentStatus.currentMicroAmperes / 1000.0)`.
                    *   Else: Use `DEFAULT_DISCHARGE_RATE_MA`.
                3.  Calculate `timeHours = remainingCapacityMah / rate`.
                4.  Convert to milliseconds.
            *   **Logging:** Log inputs, calculated rate, estimated time.

*   **`presentation/stats_discharge_view_model.kt`**
    *   **Class:** `StatsDischargeViewModel` (ViewModel, `@HiltViewModel`)
        *   **Injects:** `StatsDischargeRepository`, `CalculateSimpleDischargeEstimateUseCase`, `AppRepository`.
        *   **UI State Data Class:** `StatsDischargeUiState(isLoading: Boolean, status: StatsDischargeStatus?, session: StatsDischargeSession?, timeRemainingMillis: Long)`
        *   **Attributes:** `_uiState`, `uiState`.
        *   **`init` block:**
            1.  Combine `statsDischargeRepository.statsDischargeStatusFlow` and `statsDischargeRepository.activeDischargeSessionFlow`.
            2.  In `combine`: If not charging, call `calculateSimpleDischargeEstimateUseCase.execute(...)`.
            3.  Update `_uiState`.
        *   **Method:** `fun resetDischargeSession()`: Calls `statsDischargeRepository.resetCurrentSession()`.
        *   **Logging:** Log UI state updates.

*   **`presentation/stats_discharge_fragment.kt`**
    *   **Class:** `StatsDischargeFragment` (Fragment, `@AndroidEntryPoint`)
        *   **UI:** Simple layout for:
            *   Percentage, current, voltage, temp (when not charging).
            *   Session: Start time, duration, % discharged.
            *   Estimate: Time Remaining.
            *   Reset button.
        *   **Logic:** Observe `viewModel.uiState`.
        *   **Logging:** UI updates.

*   **`di/stats_discharge_di_module.kt`**
    *   **Module:** `StatsDischargeDIModule`
        *   **Binds:** `StatsDischargeRepository` to `DefaultStatsDischargeRepository`, `StatsDischargeCache` to `PrefsStatsDischargeCache`.

**4. Implementation Plan & Testing (Phase 3)**

*   **Phase 3.1: Basic Structure & Data Flow (No Session/Estimation)**
    *   Tasks mirror Phase 2.1 for Charge, but for Discharge components.
        *   **Test:** Verify basic battery info (percentage, current, voltage, temp) is displayed when *not charging*. Fragment should show a "Currently Charging" message or hide stats if `CoreBatteryStatus.isCharging` is true.

*   **Phase 3.2: Simple Session Tracking**
    *   Tasks mirror Phase 2.2 for Charge.
        *   **Test:**
            1.  Unplug device: Verify discharge session starts.
            2.  Observe duration updating.
            3.  Plug in device: Verify discharge session ends.
            4.  Restart app while discharging: Verify active session restored.

*   **Phase 3.3: Basic Discharge Time Estimation**
    *   Tasks mirror Phase 2.3 for Charge (for time remaining).
        *   **Test:**
            1.  While discharging, observe "Time Remaining".
            2.  Verify estimate is reasonable based on current draw or default rates.
            3.  Check logs from `CalculateSimpleDischargeEstimateUseCase`.