package com.tqhit.battery.one.features.emoji.data.datastore;

import android.content.Context;
import com.google.gson.Gson;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class CustomizationDataStore_Factory implements Factory<CustomizationDataStore> {
  private final Provider<Context> contextProvider;

  private final Provider<Gson> gsonProvider;

  public CustomizationDataStore_Factory(Provider<Context> contextProvider,
      Provider<Gson> gsonProvider) {
    this.contextProvider = contextProvider;
    this.gsonProvider = gsonProvider;
  }

  @Override
  public CustomizationDataStore get() {
    return newInstance(contextProvider.get(), gsonProvider.get());
  }

  public static CustomizationDataStore_Factory create(Provider<Context> contextProvider,
      Provider<Gson> gsonProvider) {
    return new CustomizationDataStore_Factory(contextProvider, gsonProvider);
  }

  public static CustomizationDataStore newInstance(Context context, Gson gson) {
    return new CustomizationDataStore(context, gson);
  }
}
