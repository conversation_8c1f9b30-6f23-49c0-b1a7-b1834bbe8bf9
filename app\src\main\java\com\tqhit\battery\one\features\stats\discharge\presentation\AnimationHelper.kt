package com.tqhit.battery.one.features.stats.discharge.presentation

import android.animation.ValueAnimator
import android.util.Log
import android.view.animation.AccelerateDecelerateInterpolator
import com.tqhit.battery.one.databinding.NewFragmentDischargeBinding

/**
 * Handles animations for the discharge screen, driven by DischargeUiState.
 */
class AnimationHelper(
    private val binding: NewFragmentDischargeBinding,
    private val uiUpdater: DischargeUiUpdater
) {
    companion object {
        private const val TAG = "AnimationHelper"
    }

    private var previousPercentage: Int = -1 // Initialize to a value that ensures first update happens
    private var currentAnimator: ValueAnimator? = null

    /**
     * Animates the battery percentage change based on the provided UI state.
     * This is the primary method called by the fragment.
     * It ensures that uiUpdater.updateStatusAndEstimates is called with the correct state.
     */
    fun animateBatteryUpdate(state: DischargeUiState) {
        val newPercentage = state.batteryPercentage
        
        // Always set the text directly. If animating, animator will override frame by frame.
        // If not animating, this is the final value.
        // This also ensures that if state.isLoadingInitial is true, "..." is shown via uiUpdater.
        if (previousPercentage == -1 && state.isLoadingInitial) {
             // If it's the very first call and still loading, let uiUpdater handle placeholder
            uiUpdater.updateStatusAndEstimates(state)
        } else {
            // For subsequent updates or if not loading initially, ensure percentage is set before animation decision
            binding.includeStatusAndEstimates.saeTvPercentage.text = "${newPercentage}%"
        }

        if (previousPercentage != newPercentage && previousPercentage >= 0 && !state.isLoadingInitial) {
            // Animate only if percentage changed, we have a previous value, and not in initial loading state
            Log.d(TAG, "Animating from $previousPercentage% to $newPercentage% using full state")
            animatePercentage(previousPercentage, newPercentage, state)
        } else {
            // No animation needed (first meaningful update, no change, or initial loading)
            // Call uiUpdater to ensure all parts of the status section are updated (like status text, time estimates)
            Log.d(TAG, "No animation. Previous: $previousPercentage, New: $newPercentage, isLoading: ${state.isLoadingInitial}. Updating UI directly.")
            uiUpdater.updateStatusAndEstimates(state)
        }
        
        // Update the previous percentage for the next call, but only if not in initial loading state.
        // If still loading, previousPercentage remains -1 to allow the first non-loading state to set up correctly.
        if (!state.isLoadingInitial) {
            previousPercentage = newPercentage
        }
    }

    /**
     * Animates between two percentage values, using the full state for UI updates during animation.
     */
    private fun animatePercentage(oldPercentage: Int, newPercentage: Int, fullState: DischargeUiState) {
        currentAnimator?.cancel()

        val animator = ValueAnimator.ofInt(oldPercentage, newPercentage)
        animator.duration = 500
        animator.interpolator = AccelerateDecelerateInterpolator()

        animator.addUpdateListener { animation ->
            val animatedValue = animation.animatedValue as Int
            binding.includeStatusAndEstimates.saeTvPercentage.text = "${animatedValue}%"
            
            // Create a temporary state for uiUpdater that reflects the animated percentage,
            // but carries over other properties (isCharging, isLoadingInitial=false, time estimates etc.) from the fullState.
            val tempAnimatedState = fullState.copy(batteryPercentage = animatedValue, isLoadingInitial = false)
            uiUpdater.updateStatusAndEstimates(tempAnimatedState)
        }
        currentAnimator = animator
        animator.start()
    }

    fun cleanup() {
        currentAnimator?.cancel()
        currentAnimator = null
        previousPercentage = -1 // Reset for next view creation
    }
}
