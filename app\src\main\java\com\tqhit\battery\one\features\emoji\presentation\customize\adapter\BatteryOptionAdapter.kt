package com.tqhit.battery.one.features.emoji.presentation.customize.adapter

import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.ProgressBar
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.google.android.material.card.MaterialCardView
import com.tqhit.battery.one.R
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle

/**
 * Adapter for displaying battery container options in horizontal RecyclerView.
 * 
 * Handles the display of battery container images with selection states,
 * premium badges, and loading indicators. Optimized for horizontal scrolling
 * with efficient image loading and view recycling.
 * 
 * Key features:
 * - Efficient image loading with Glide
 * - Selection state management
 * - Premium content indicators
 * - Loading state handling
 * - Click event handling
 */
class BatteryOptionAdapter(
    private val onBatterySelected: (BatteryStyle) -> Unit
) : ListAdapter<BatteryStyle, BatteryOptionAdapter.BatteryOptionViewHolder>(BatteryStyleDiffCallback()) {
    
    companion object {
        private const val TAG = "BatteryOptionAdapter"
    }
    
    private var selectedBatteryId: String = ""
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BatteryOptionViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_battery_option, parent, false)
        return BatteryOptionViewHolder(view, onBatterySelected)
    }
    
    override fun onBindViewHolder(holder: BatteryOptionViewHolder, position: Int) {
        val batteryStyle = getItem(position)
        val isSelected = batteryStyle.id == selectedBatteryId
        holder.bind(batteryStyle, isSelected)
    }
    
    /**
     * Updates the selected battery ID and refreshes selection states.
     */
    fun setSelectedBatteryId(batteryId: String) {
        if (selectedBatteryId != batteryId) {
            val oldSelectedId = selectedBatteryId
            selectedBatteryId = batteryId
            
            // Refresh items that changed selection state
            currentList.forEachIndexed { index, batteryStyle ->
                if (batteryStyle.id == oldSelectedId || batteryStyle.id == batteryId) {
                    notifyItemChanged(index)
                }
            }
            
            Log.d(TAG, "SELECTION: Updated selected battery ID to: $batteryId")
        }
    }
    
    /**
     * ViewHolder for battery option items.
     */
    class BatteryOptionViewHolder(
        itemView: View,
        private val onBatterySelected: (BatteryStyle) -> Unit
    ) : RecyclerView.ViewHolder(itemView) {
        
        private val cardView: MaterialCardView = itemView as MaterialCardView
        private val batteryImageView: ImageView = itemView.findViewById(R.id.batteryImageView)
        private val selectionIndicator: ImageView = itemView.findViewById(R.id.selectionIndicator)
        private val premiumBadge: ImageView = itemView.findViewById(R.id.premiumBadge)
        private val loadingIndicator: ProgressBar = itemView.findViewById(R.id.loadingIndicator)
        
        private var currentBatteryStyle: BatteryStyle? = null
        
        init {
            itemView.setOnClickListener {
                currentBatteryStyle?.let { batteryStyle ->
                    onBatterySelected(batteryStyle)
                    Log.d(TAG, "CLICK: Battery selected: ${batteryStyle.name}")
                }
            }
        }
        
        fun bind(batteryStyle: BatteryStyle, isSelected: Boolean) {
            currentBatteryStyle = batteryStyle
            
            // Update selection state
            updateSelectionState(isSelected)
            
            // Show premium badge if needed
            premiumBadge.visibility = if (batteryStyle.isPremium) View.VISIBLE else View.GONE
            
            // Load battery image
            loadBatteryImage(batteryStyle)
            
            // Update content description for accessibility
            itemView.contentDescription = buildString {
                append("Battery container: ${batteryStyle.name}")
                if (batteryStyle.isPremium) append(", Premium")
                if (isSelected) append(", Selected")
            }
        }
        
        /**
         * Updates the visual selection state of the item.
         */
        private fun updateSelectionState(isSelected: Boolean) {
            selectionIndicator.visibility = if (isSelected) View.VISIBLE else View.GONE
            
            // Update card appearance for selection
            cardView.apply {
                strokeWidth = if (isSelected) {
                    resources.getDimensionPixelSize(R.dimen.selection_stroke_width)
                } else {
                    resources.getDimensionPixelSize(R.dimen.default_stroke_width)
                }
                
                strokeColor = if (isSelected) {
                    context.getColor(R.color.selection_stroke_color)
                } else {
                    context.getColor(R.color.default_stroke_color)
                }
            }
        }
        
        /**
         * Loads the battery container image using Glide.
         */
        private fun loadBatteryImage(batteryStyle: BatteryStyle) {
            if (batteryStyle.batteryImageUrl.isBlank()) {
                // Show placeholder for empty URL
                batteryImageView.setImageResource(R.drawable.ic_battery_placeholder)
                loadingIndicator.visibility = View.GONE
                return
            }
            
            // Show loading indicator
            loadingIndicator.visibility = View.VISIBLE
            batteryImageView.visibility = View.GONE
            
            Glide.with(itemView.context)
                .load(batteryStyle.batteryImageUrl)
                .diskCacheStrategy(DiskCacheStrategy.ALL)
                .placeholder(R.drawable.ic_battery_placeholder)
                .error(R.drawable.ic_battery_placeholder)
                .into(object : com.bumptech.glide.request.target.CustomTarget<android.graphics.drawable.Drawable>() {
                    override fun onResourceReady(
                        resource: android.graphics.drawable.Drawable,
                        transition: com.bumptech.glide.request.transition.Transition<in android.graphics.drawable.Drawable>?
                    ) {
                        batteryImageView.setImageDrawable(resource)
                        batteryImageView.visibility = View.VISIBLE
                        loadingIndicator.visibility = View.GONE
                        Log.d(TAG, "IMAGE_LOAD: Battery image loaded for: ${batteryStyle.name}")
                    }
                    
                    override fun onLoadCleared(placeholder: android.graphics.drawable.Drawable?) {
                        batteryImageView.setImageDrawable(placeholder)
                        batteryImageView.visibility = View.VISIBLE
                        loadingIndicator.visibility = View.GONE
                    }
                    
                    override fun onLoadFailed(errorDrawable: android.graphics.drawable.Drawable?) {
                        batteryImageView.setImageDrawable(errorDrawable)
                        batteryImageView.visibility = View.VISIBLE
                        loadingIndicator.visibility = View.GONE
                        Log.w(TAG, "IMAGE_LOAD: Failed to load battery image for: ${batteryStyle.name}")
                    }
                })
        }
    }
    
    /**
     * DiffUtil callback for efficient list updates.
     */
    private class BatteryStyleDiffCallback : DiffUtil.ItemCallback<BatteryStyle>() {
        override fun areItemsTheSame(oldItem: BatteryStyle, newItem: BatteryStyle): Boolean {
            return oldItem.id == newItem.id
        }
        
        override fun areContentsTheSame(oldItem: BatteryStyle, newItem: BatteryStyle): Boolean {
            return oldItem == newItem
        }
    }
}
