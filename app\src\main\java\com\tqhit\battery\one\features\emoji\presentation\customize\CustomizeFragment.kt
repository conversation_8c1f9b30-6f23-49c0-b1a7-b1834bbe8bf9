package com.tqhit.battery.one.features.emoji.presentation.customize

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ProgressBar
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.button.MaterialButton
import com.google.android.material.materialswitch.MaterialSwitch
import com.google.android.material.slider.Slider
import com.google.android.material.snackbar.Snackbar
import com.tqhit.battery.one.R
import com.tqhit.battery.one.features.emoji.presentation.customize.adapter.BatteryOptionAdapter
import com.tqhit.battery.one.features.emoji.presentation.customize.adapter.EmojiOptionAdapter
import com.tqhit.battery.one.features.emoji.presentation.customize.view.LivePreviewView
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

/**
 * Fragment for emoji battery customization screen.
 * 
 * Provides a comprehensive interface for customizing emoji battery styles including:
 * - Live preview with real-time updates
 * - Battery container and emoji selection
 * - Visual customization controls (size, color, visibility)
 * - Save, apply, and reset functionality
 * 
 * Follows the MVI pattern with reactive state management and integrates
 * with CoreBatteryStatsProvider for live battery data.
 */
@AndroidEntryPoint
class CustomizeFragment : Fragment() {
    
    companion object {
        private const val TAG = "CustomizeFragment"
        
        fun newInstance(): CustomizeFragment {
            return CustomizeFragment()
        }
    }
    
    private val viewModel: CustomizeViewModel by viewModels()
    
    // UI Components
    private lateinit var livePreviewView: LivePreviewView
    private lateinit var previewLoadingIndicator: ProgressBar
    private lateinit var previewBatterySlider: Slider
    private lateinit var previewBatteryText: TextView
    
    private lateinit var batteryOptionsRecyclerView: RecyclerView
    private lateinit var emojiOptionsRecyclerView: RecyclerView
    
    private lateinit var showEmojiSwitch: MaterialSwitch
    private lateinit var showPercentageSwitch: MaterialSwitch
    private lateinit var emojiSizeSlider: Slider
    private lateinit var percentageFontSizeSlider: Slider
    private lateinit var colorPickerButton: MaterialButton
    
    private lateinit var resetButton: MaterialButton
    private lateinit var saveButton: MaterialButton
    private lateinit var applyButton: MaterialButton
    
    private lateinit var loadingOverlay: View
    private lateinit var loadingText: TextView
    
    // Adapters
    private lateinit var batteryOptionAdapter: BatteryOptionAdapter
    private lateinit var emojiOptionAdapter: EmojiOptionAdapter
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        Log.d(TAG, "LIFECYCLE: Creating view")
        return inflater.inflate(R.layout.fragment_customize, container, false)
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        Log.d(TAG, "LIFECYCLE: View created")
        
        initializeViews(view)
        setupAdapters()
        setupRecyclerViews()
        setupEventListeners()
        observeViewModel()
        
        // Notify ViewModel that screen is ready
        viewModel.handleEvent(CustomizeEvent.OnScreenEnter)
    }
    
    /**
     * Initializes all view references.
     */
    private fun initializeViews(view: View) {
        // Preview components
        livePreviewView = view.findViewById(R.id.livePreviewView)
        previewLoadingIndicator = view.findViewById(R.id.previewLoadingIndicator)
        previewBatterySlider = view.findViewById(R.id.previewBatterySlider)
        previewBatteryText = view.findViewById(R.id.previewBatteryText)
        
        // Style selection components
        batteryOptionsRecyclerView = view.findViewById(R.id.batteryOptionsRecyclerView)
        emojiOptionsRecyclerView = view.findViewById(R.id.emojiOptionsRecyclerView)
        
        // Customization controls
        showEmojiSwitch = view.findViewById(R.id.showEmojiSwitch)
        showPercentageSwitch = view.findViewById(R.id.showPercentageSwitch)
        emojiSizeSlider = view.findViewById(R.id.emojiSizeSlider)
        percentageFontSizeSlider = view.findViewById(R.id.percentageFontSizeSlider)
        colorPickerButton = view.findViewById(R.id.colorPickerButton)
        
        // Action buttons
        resetButton = view.findViewById(R.id.resetButton)
        saveButton = view.findViewById(R.id.saveButton)
        applyButton = view.findViewById(R.id.applyButton)
        
        // Loading overlay
        loadingOverlay = view.findViewById(R.id.loadingOverlay)
        loadingText = view.findViewById(R.id.loadingText)
        
        Log.d(TAG, "INIT: All views initialized")
    }
    
    /**
     * Sets up RecyclerView adapters.
     */
    private fun setupAdapters() {
        batteryOptionAdapter = BatteryOptionAdapter { batteryStyle ->
            viewModel.handleEvent(CustomizeEvent.SelectBatteryContainer(batteryStyle))
        }
        
        emojiOptionAdapter = EmojiOptionAdapter { batteryStyle ->
            viewModel.handleEvent(CustomizeEvent.SelectEmojiCharacter(batteryStyle))
        }
        
        Log.d(TAG, "INIT: Adapters created")
    }
    
    /**
     * Sets up RecyclerViews with layout managers and adapters.
     */
    private fun setupRecyclerViews() {
        // Battery options RecyclerView
        batteryOptionsRecyclerView.apply {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
            adapter = batteryOptionAdapter
            setHasFixedSize(true)
        }
        
        // Emoji options RecyclerView
        emojiOptionsRecyclerView.apply {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
            adapter = emojiOptionAdapter
            setHasFixedSize(true)
        }
        
        Log.d(TAG, "INIT: RecyclerViews configured")
    }
    
    /**
     * Sets up event listeners for all interactive components.
     */
    private fun setupEventListeners() {
        // Preview controls
        previewBatterySlider.addOnChangeListener { _, value, fromUser ->
            if (fromUser) {
                val level = value.toInt()
                previewBatteryText.text = "${level}%"
                viewModel.handleEvent(CustomizeEvent.ChangePreviewBatteryLevel(level))
            }
        }
        
        // Customization controls
        showEmojiSwitch.setOnCheckedChangeListener { _, isChecked ->
            viewModel.handleEvent(CustomizeEvent.ToggleEmojiVisibility(isChecked))
        }
        
        showPercentageSwitch.setOnCheckedChangeListener { _, isChecked ->
            viewModel.handleEvent(CustomizeEvent.TogglePercentageVisibility(isChecked))
        }
        
        emojiSizeSlider.addOnChangeListener { _, value, fromUser ->
            if (fromUser) {
                viewModel.handleEvent(CustomizeEvent.ChangeEmojiSize(value))
            }
        }
        
        percentageFontSizeSlider.addOnChangeListener { _, value, fromUser ->
            if (fromUser) {
                viewModel.handleEvent(CustomizeEvent.ChangePercentageFontSize(value.toInt()))
            }
        }
        
        colorPickerButton.setOnClickListener {
            viewModel.handleEvent(CustomizeEvent.OpenColorPicker)
        }
        
        // Action buttons
        resetButton.setOnClickListener {
            viewModel.handleEvent(CustomizeEvent.ResetToDefaults)
        }
        
        saveButton.setOnClickListener {
            viewModel.handleEvent(CustomizeEvent.SaveConfiguration)
        }
        
        applyButton.setOnClickListener {
            viewModel.handleEvent(CustomizeEvent.ApplyAndEnable)
        }
        
        Log.d(TAG, "INIT: Event listeners configured")
    }
    
    /**
     * Observes ViewModel state changes and updates UI accordingly.
     */
    private fun observeViewModel() {
        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.uiState.collect { state ->
                    updateUI(state)
                }
            }
        }
        
        Log.d(TAG, "INIT: ViewModel observation started")
    }
    
    /**
     * Updates the entire UI based on the current state.
     */
    private fun updateUI(state: CustomizeState) {
        Log.d(TAG, "UI_UPDATE: Updating UI with new state")
        
        updateLoadingState(state)
        updatePreview(state)
        updateStyleSelection(state)
        updateCustomizationControls(state)
        updateActionButtons(state)
        updateErrorState(state)
    }
    
    /**
     * Updates loading states and overlays.
     */
    private fun updateLoadingState(state: CustomizeState) {
        // Main loading overlay
        loadingOverlay.visibility = if (state.isLoading) View.VISIBLE else View.GONE
        loadingText.text = when {
            state.isLoading -> getString(R.string.loading_customization)
            state.isSaving -> getString(R.string.saving_configuration)
            state.isLoadingStyles -> getString(R.string.loading_styles)
            else -> getString(R.string.loading)
        }
        
        // Preview loading indicator
        previewLoadingIndicator.visibility = if (state.isPreviewLoading) View.VISIBLE else View.GONE
    }
    
    /**
     * Updates the live preview display.
     */
    private fun updatePreview(state: CustomizeState) {
        val config = state.getPreviewConfig()
        livePreviewView.updatePreview(
            config = config,
            batteryLevel = state.previewBatteryLevel,
            isCharging = false // TODO: Get from battery provider
        )
        
        // Update preview controls
        if (previewBatterySlider.value.toInt() != state.previewBatteryLevel) {
            previewBatterySlider.value = state.previewBatteryLevel.toFloat()
            previewBatteryText.text = "${state.previewBatteryLevel}%"
        }
    }
    
    /**
     * Updates style selection RecyclerViews.
     */
    private fun updateStyleSelection(state: CustomizeState) {
        // Update battery options
        batteryOptionAdapter.submitList(state.availableBatteryStyles)
        batteryOptionAdapter.setSelectedBatteryId(state.selectedBatteryStyleId)
        
        // Update emoji options
        emojiOptionAdapter.submitList(state.availableEmojiStyles)
        emojiOptionAdapter.setSelectedEmojiId(state.selectedEmojiStyleId)
    }
    
    /**
     * Updates customization control values.
     */
    private fun updateCustomizationControls(state: CustomizeState) {
        val config = state.editingConfig
        
        // Update switches
        if (showEmojiSwitch.isChecked != config.showEmoji) {
            showEmojiSwitch.isChecked = config.showEmoji
        }
        
        if (showPercentageSwitch.isChecked != config.showPercentage) {
            showPercentageSwitch.isChecked = config.showPercentage
        }
        
        // Update sliders
        if (emojiSizeSlider.value != config.emojiSizeScale) {
            emojiSizeSlider.value = config.emojiSizeScale
        }
        
        if (percentageFontSizeSlider.value.toInt() != config.percentageFontSizeDp) {
            percentageFontSizeSlider.value = config.percentageFontSizeDp.toFloat()
        }
    }
    
    /**
     * Updates action button states.
     */
    private fun updateActionButtons(state: CustomizeState) {
        saveButton.isEnabled = state.canSave() && !state.isAnyLoading()
        applyButton.isEnabled = state.canEnableFeature() && !state.isAnyLoading()
        resetButton.isEnabled = !state.isAnyLoading()
        
        // Update button text based on state
        applyButton.text = if (state.currentCustomization.customizationConfig.isFeatureEnabled) {
            getString(R.string.update_and_apply)
        } else {
            getString(R.string.apply_and_enable)
        }
    }
    
    /**
     * Updates error display.
     */
    private fun updateErrorState(state: CustomizeState) {
        val errorMessage = state.getDisplayErrorMessage()
        if (errorMessage != null) {
            showError(errorMessage)
        }
    }
    
    /**
     * Shows an error message to the user.
     */
    private fun showError(message: String) {
        view?.let { view ->
            Snackbar.make(view, message, Snackbar.LENGTH_LONG)
                .setAction(getString(R.string.dismiss)) {
                    viewModel.handleEvent(CustomizeEvent.DismissError)
                }
                .show()
        }
        Log.e(TAG, "ERROR: Showing error message: $message")
    }
    
    override fun onResume() {
        super.onResume()
        Log.d(TAG, "LIFECYCLE: Fragment resumed")
        viewModel.handleEvent(CustomizeEvent.OnResume)
    }
    
    override fun onPause() {
        super.onPause()
        Log.d(TAG, "LIFECYCLE: Fragment paused")
        viewModel.handleEvent(CustomizeEvent.OnPause)
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        Log.d(TAG, "LIFECYCLE: View destroyed")
        viewModel.handleEvent(CustomizeEvent.OnScreenExit)
    }
}
