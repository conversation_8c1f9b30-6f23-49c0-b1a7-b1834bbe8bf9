package com.tqhit.battery.one.features.stats.health.cache

import android.util.Log
import com.tqhit.battery.one.features.stats.health.data.HealthCalculationMode
import com.tqhit.battery.one.features.stats.health.data.HealthStatus
import com.tqhit.adlib.sdk.data.local.PreferencesHelper
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Interface for health-related caching operations.
 * Provides persistent storage for health status and user preferences.
 */
interface HealthCache {
    
    /**
     * Saves the current health status to cache.
     * 
     * @param healthStatus The health status to save
     */
    suspend fun saveHealthStatus(healthStatus: HealthStatus)
    
    /**
     * Loads the cached health status.
     * 
     * @return Cached HealthStatus or null if not available
     */
    suspend fun getHealthStatus(): HealthStatus?
    
    /**
     * Saves the current calculation mode to cache.
     * 
     * @param mode The calculation mode to save
     */
    suspend fun saveCalculationMode(mode: HealthCalculationMode)
    
    /**
     * Loads the cached calculation mode.
     * 
     * @return Cached HealthCalculationMode or CUMULATIVE as default
     */
    suspend fun getCalculationMode(): HealthCalculationMode
    
    /**
     * Clears all cached health data.
     */
    suspend fun clearAll()
}

/**
 * Default implementation of HealthCache using PreferencesHelper.
 * Provides persistent storage for health-related data using SharedPreferences.
 */
@Singleton
class DefaultHealthCache @Inject constructor(
    private val preferencesHelper: PreferencesHelper
) : HealthCache {
    
    companion object {
        private const val TAG = "HealthCache"
        
        // Cache keys
        private const val KEY_HEALTH_PERCENTAGE = "health_percentage"
        private const val KEY_TOTAL_SESSIONS = "health_total_sessions"
        private const val KEY_DESIGN_CAPACITY = "health_design_capacity"
        private const val KEY_EFFECTIVE_CAPACITY = "health_effective_capacity"
        private const val KEY_CALCULATION_MODE = "health_calculation_mode"
        private const val KEY_HEALTH_TIMESTAMP = "health_timestamp"
        
        // Default values
        private const val DEFAULT_HEALTH_PERCENTAGE = 100
        private const val DEFAULT_TOTAL_SESSIONS = 0
        private const val DEFAULT_DESIGN_CAPACITY = 3000
        private const val DEFAULT_EFFECTIVE_CAPACITY = 3000
        private const val DEFAULT_CALCULATION_MODE = "CUMULATIVE"
    }
    
    override suspend fun saveHealthStatus(healthStatus: HealthStatus) {
        try {
            preferencesHelper.saveInt(KEY_HEALTH_PERCENTAGE, healthStatus.healthPercentage)
            preferencesHelper.saveInt(KEY_TOTAL_SESSIONS, healthStatus.totalSessions)
            preferencesHelper.saveInt(KEY_DESIGN_CAPACITY, healthStatus.designCapacityMah)
            preferencesHelper.saveInt(KEY_EFFECTIVE_CAPACITY, healthStatus.effectiveCapacityMah)
            preferencesHelper.saveString(KEY_CALCULATION_MODE, healthStatus.calculationMode.name)
            preferencesHelper.saveLong(KEY_HEALTH_TIMESTAMP, healthStatus.timestampEpochMillis)
            
            Log.d(TAG, "HEALTH_CACHE: Health status saved - " +
                "health=${healthStatus.healthPercentage}%, " +
                "sessions=${healthStatus.totalSessions}, " +
                "mode=${healthStatus.calculationMode}")
                
        } catch (e: Exception) {
            Log.e(TAG, "Failed to save health status to cache", e)
        }
    }
    
    override suspend fun getHealthStatus(): HealthStatus? {
        return try {
            val healthPercentage = preferencesHelper.getInt(KEY_HEALTH_PERCENTAGE, DEFAULT_HEALTH_PERCENTAGE)
            val totalSessions = preferencesHelper.getInt(KEY_TOTAL_SESSIONS, DEFAULT_TOTAL_SESSIONS)
            val designCapacity = preferencesHelper.getInt(KEY_DESIGN_CAPACITY, DEFAULT_DESIGN_CAPACITY)
            val effectiveCapacity = preferencesHelper.getInt(KEY_EFFECTIVE_CAPACITY, DEFAULT_EFFECTIVE_CAPACITY)
            val calculationModeString = preferencesHelper.getString(KEY_CALCULATION_MODE) ?: DEFAULT_CALCULATION_MODE
            val timestamp = preferencesHelper.getLong(KEY_HEALTH_TIMESTAMP, System.currentTimeMillis())
            
            val calculationMode = try {
                HealthCalculationMode.valueOf(calculationModeString)
            } catch (e: IllegalArgumentException) {
                Log.w(TAG, "Invalid calculation mode in cache: $calculationModeString, using default")
                HealthCalculationMode.CUMULATIVE
            }
            
            val cachedStatus = HealthStatus(
                healthPercentage = healthPercentage,
                totalSessions = totalSessions,
                designCapacityMah = designCapacity,
                effectiveCapacityMah = effectiveCapacity,
                calculationMode = calculationMode,
                timestampEpochMillis = timestamp
            )
            
            Log.d(TAG, "HEALTH_CACHE: Health status loaded - " +
                "health=${cachedStatus.healthPercentage}%, " +
                "sessions=${cachedStatus.totalSessions}, " +
                "mode=${cachedStatus.calculationMode}")
            
            cachedStatus
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to load health status from cache", e)
            null
        }
    }
    
    override suspend fun saveCalculationMode(mode: HealthCalculationMode) {
        try {
            preferencesHelper.saveString(KEY_CALCULATION_MODE, mode.name)
            Log.d(TAG, "HEALTH_CACHE: Calculation mode saved: $mode")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to save calculation mode to cache", e)
        }
    }
    
    override suspend fun getCalculationMode(): HealthCalculationMode {
        return try {
            val modeString = preferencesHelper.getString(KEY_CALCULATION_MODE) ?: DEFAULT_CALCULATION_MODE
            val mode = HealthCalculationMode.valueOf(modeString)
            Log.d(TAG, "HEALTH_CACHE: Calculation mode loaded: $mode")
            mode
        } catch (e: Exception) {
            Log.e(TAG, "Failed to load calculation mode from cache, using default", e)
            HealthCalculationMode.CUMULATIVE
        }
    }
    
    override suspend fun clearAll() {
        try {
            preferencesHelper.saveInt(KEY_HEALTH_PERCENTAGE, DEFAULT_HEALTH_PERCENTAGE)
            preferencesHelper.saveInt(KEY_TOTAL_SESSIONS, DEFAULT_TOTAL_SESSIONS)
            preferencesHelper.saveInt(KEY_DESIGN_CAPACITY, DEFAULT_DESIGN_CAPACITY)
            preferencesHelper.saveInt(KEY_EFFECTIVE_CAPACITY, DEFAULT_EFFECTIVE_CAPACITY)
            preferencesHelper.saveString(KEY_CALCULATION_MODE, DEFAULT_CALCULATION_MODE)
            preferencesHelper.saveLong(KEY_HEALTH_TIMESTAMP, 0L)

            Log.d(TAG, "HEALTH_CACHE: All health cache data cleared")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to clear health cache", e)
        }
    }
}

/**
 * Extension functions for HealthCache to provide additional convenience methods.
 */

/**
 * Checks if the cached health status is recent (within the last hour).
 * 
 * @return true if cached data is recent, false otherwise
 */
suspend fun HealthCache.isCachedDataRecent(): Boolean {
    val cachedStatus = getHealthStatus()
    return if (cachedStatus != null) {
        val currentTime = System.currentTimeMillis()
        val cacheAge = currentTime - cachedStatus.timestampEpochMillis
        val oneHourInMillis = 60 * 60 * 1000L
        cacheAge < oneHourInMillis
    } else {
        false
    }
}

/**
 * Gets the age of cached health data in minutes.
 * 
 * @return Age in minutes or -1 if no cached data
 */
suspend fun HealthCache.getCachedDataAgeMinutes(): Long {
    val cachedStatus = getHealthStatus()
    return if (cachedStatus != null) {
        val currentTime = System.currentTimeMillis()
        val cacheAge = currentTime - cachedStatus.timestampEpochMillis
        cacheAge / (60 * 1000L) // Convert to minutes
    } else {
        -1L
    }
}
