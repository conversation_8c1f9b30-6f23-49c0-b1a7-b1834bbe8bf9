package com.tqhit.battery.one.features.stats.discharge.di

import android.content.Context
import com.tqhit.battery.one.features.stats.discharge.cache.CurrentSessionCache
import com.tqhit.battery.one.features.stats.discharge.cache.DischargeRatesCache
import com.tqhit.battery.one.features.stats.discharge.cache.PrefsCurrentSessionCache
import com.tqhit.battery.one.features.stats.discharge.cache.PrefsDischargeRatesCache
import com.tqhit.battery.one.features.stats.discharge.datasource.ScreenStateReceiver
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Dependency injection module for the stats discharge feature
 */
@Module
@InstallIn(SingletonComponent::class)
abstract class StatsDischargeModule {

    @Binds
    @Singleton
    abstract fun bindCurrentSessionCache(
        prefsCurrentSessionCache: PrefsCurrentSessionCache
    ): CurrentSessionCache

    @Binds
    @Singleton
    abstract fun bindDischargeRatesCache(
        prefsDischargeRatesCache: PrefsDischargeRatesCache
    ): DischargeRatesCache
}

/**
 * Providers module for the stats discharge feature
 */
@Module
@InstallIn(SingletonComponent::class)
object StatsDischargeProvidersModule {

    @Provides
    @Singleton
    fun provideScreenStateReceiver(@ApplicationContext context: Context): ScreenStateReceiver {
        return ScreenStateReceiver(context)
    }
}
