package com.tqhit.battery.one.features.stats.discharge.data

/**
 * Data class representing a discharge session
 * Contains all the metrics needed for displaying discharge statistics
 */
data class DischargeSessionData(
    val startTimeEpochMillis: Long = System.currentTimeMillis(),
    val lastUpdateTimeEpochMillis: Long = System.currentTimeMillis(),
    val startPercentage: Int = 0,
    val currentPercentage: Int = 0,
    val currentPercentageAtLastUpdate: Int = 0, // Percentage when last saved/updated
    val isActive: Boolean = true,
    
    // Screen time stats
    val screenOnTimeMillis: Long = 0L,
    val screenOffTimeMillis: Long = 0L,
    
    // Discharge metrics
    val totalMahConsumed: Double = 0.0,
    val screenOnMahConsumed: Double = 0.0,
    val screenOffMahConsumed: Double = 0.0,
    
    // Averages calculated from the current session
    val avgScreenOnDischargeRateMahPerHour: Double = 0.0,
    val avgScreenOffDischargeRateMahPerHour: Double = 0.0,
    val avgMixedDischargeRateMahPerHour: Double = 0.0,
    val avgPercentPerHour: Double = 0.0,
    
    // Right now discharge rate in percent per hour
    val currentDischargeRate: Double = 0.0
) {
    /**
     * Total duration of the session in milliseconds
     * Uses current time if session is active for real-time duration
     * Includes a small buffer to account for timing precision
     */
    val durationMillis: Long
        get() = if (isActive) {
            val realTimeDuration = System.currentTimeMillis() - startTimeEpochMillis
            // Add a small buffer (2 seconds) to account for timing precision and prevent constraint violations
            realTimeDuration + 2000L
        } else {
            lastUpdateTimeEpochMillis - startTimeEpochMillis
        }
    
    /**
     * Total percentage dropped during the session
     */
    val percentageDropped: Int
        get() = startPercentage - currentPercentage
        
    /**
     * Percentage dropped during screen on time
     */
    val screenOnPercentageDropped: Double
        get() = if (totalMahConsumed > 0.0) {
            screenOnMahConsumed / totalMahConsumed * percentageDropped
        } else {
            0.0
        }
        
    /**
     * Percentage dropped during screen off time
     */
    val screenOffPercentageDropped: Double
        get() = if (totalMahConsumed > 0.0) {
            screenOffMahConsumed / totalMahConsumed * percentageDropped
        } else {
            0.0
        }
        
    /**
     * Average screen on discharge rate in % per hour
     */
    val avgScreenOnDischargeRatePercentPerHour: Double
        get() = if (screenOnTimeMillis > 0) {
            screenOnPercentageDropped / (screenOnTimeMillis / 3600000.0)
        } else 0.0
        
    /**
     * Average screen off discharge rate in % per hour
     */
    val avgScreenOffDischargeRatePercentPerHour: Double
        get() = if (screenOffTimeMillis > 0) {
            screenOffPercentageDropped / (screenOffTimeMillis / 3600000.0)
        } else 0.0
}
