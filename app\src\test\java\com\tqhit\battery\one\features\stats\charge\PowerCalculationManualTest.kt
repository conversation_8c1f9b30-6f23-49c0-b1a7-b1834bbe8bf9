package com.tqhit.battery.one.features.stats.charge

import org.junit.Test
import kotlin.math.abs

/**
 * Manual test for power calculation to verify the formula works correctly.
 */
class PowerCalculationManualTest {

    @Test
    fun testPowerCalculationFormula() {
        // Test the power calculation formula manually
        // Power = (Voltage × Current) / 1,000,000
        
        // Test case 1: Typical charging values
        val voltage1 = 4200 // 4.2V in millivolts
        val current1 = 1500000L // 1.5A in microamperes
        val expectedPower1 = 6.3 // 4.2V * 1.5A = 6.3W
        
        val voltageVolts1 = voltage1 / 1000.0
        val currentAmperes1 = current1 / 1000000.0
        val actualPower1 = voltageVolts1 * currentAmperes1
        
        println("Test 1: ${voltage1}mV, ${current1}µA -> ${actualPower1}W (expected: ${expectedPower1}W)")
        assert(abs(actualPower1 - expectedPower1) < 0.01) { "Power calculation failed for test 1" }
        
        // Test case 2: Low power values
        val voltage2 = 3700 // 3.7V in millivolts
        val current2 = 500000L // 0.5A in microamperes
        val expectedPower2 = 1.85 // 3.7V * 0.5A = 1.85W
        
        val voltageVolts2 = voltage2 / 1000.0
        val currentAmperes2 = current2 / 1000000.0
        val actualPower2 = voltageVolts2 * currentAmperes2
        
        println("Test 2: ${voltage2}mV, ${current2}µA -> ${actualPower2}W (expected: ${expectedPower2}W)")
        assert(abs(actualPower2 - expectedPower2) < 0.01) { "Power calculation failed for test 2" }
        
        // Test case 3: Zero current
        val voltage3 = 4000 // 4.0V in millivolts
        val current3 = 0L // 0A in microamperes
        val expectedPower3 = 0.0 // 4.0V * 0A = 0W
        
        val voltageVolts3 = voltage3 / 1000.0
        val currentAmperes3 = current3 / 1000000.0
        val actualPower3 = voltageVolts3 * currentAmperes3
        
        println("Test 3: ${voltage3}mV, ${current3}µA -> ${actualPower3}W (expected: ${expectedPower3}W)")
        assert(abs(actualPower3 - expectedPower3) < 0.01) { "Power calculation failed for test 3" }
        
        println("All power calculation tests passed!")
    }
    
    @Test
    fun testSessionStatisticsCalculation() {
        // Test session statistics calculations manually
        
        // Test case 1: Basic percentage calculation
        val startPercentage = 30
        val currentPercentage = 55
        val expectedChargePercentage = 25 // 55 - 30 = 25
        val actualChargePercentage = currentPercentage - startPercentage
        
        println("Session Test 1: Start=${startPercentage}%, Current=${currentPercentage}% -> Charged=${actualChargePercentage}% (expected: ${expectedChargePercentage}%)")
        assert(actualChargePercentage == expectedChargePercentage) { "Session percentage calculation failed" }
        
        // Test case 2: mAh calculation
        val batteryCapacity = 1000 // 1000mAh
        val chargePercentage = 25 // 25%
        val expectedChargeMah = 250.0 // 25% of 1000mAh = 250mAh
        val actualChargeMah = (chargePercentage / 100.0) * batteryCapacity
        
        println("Session Test 2: ${chargePercentage}% of ${batteryCapacity}mAh -> ${actualChargeMah}mAh (expected: ${expectedChargeMah}mAh)")
        assert(abs(actualChargeMah - expectedChargeMah) < 0.01) { "Session mAh calculation failed" }
        
        println("All session statistics tests passed!")
    }
}
