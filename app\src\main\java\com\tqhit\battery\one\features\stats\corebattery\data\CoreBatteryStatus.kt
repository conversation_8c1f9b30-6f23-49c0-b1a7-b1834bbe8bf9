package com.tqhit.battery.one.features.stats.corebattery.data

import android.util.Log

/**
 * Immutable representation of a single battery status snapshot.
 * This is the core data class that provides raw battery status information
 * for the entire application, ensuring consistency across all modules.
 *
 * @param percentage Battery percentage (0-100)
 * @param isCharging Whether the battery is currently charging
 * @param pluggedSource The power source type (AC, USB, Wireless, etc.)
 * @param currentMicroAmperes Current flow in microamperes (positive when charging, negative when discharging)
 * @param voltageMillivolts Battery voltage in millivolts
 * @param temperatureCelsius Battery temperature in Celsius
 * @param timestampEpochMillis Timestamp when this status was captured
 */
data class CoreBatteryStatus(
    val percentage: Int,
    val isCharging: Boolean,
    val pluggedSource: Int,
    val currentMicroAmperes: Long,
    val voltageMillivolts: Int,
    val temperatureCelsius: Float,
    val timestampEpochMillis: Long
) {
    
    init {
        // Log creation details for debugging
        // Log.d(TAG, "CoreBatteryStatus created: " +
        //     "percentage=$percentage%, " +
        //     "isCharging=$isCharging, " +
        //     "pluggedSource=$pluggedSource, " +
        //     "current=${currentMicroAmperes}µA, " +
        //     "voltage=${voltageMillivolts}mV, " +
        //     "temp=${temperatureCelsius}°C, " +
        //     "timestamp=$timestampEpochMillis")
    }
    
    companion object {
        private const val TAG = "CoreBatteryStatus"
        
        /**
         * Logs the creation of a CoreBatteryStatus object with detailed information.
         * This method can be called externally for additional logging if needed.
         *
         * @param status The CoreBatteryStatus object to log
         */
        fun logCreation(status: CoreBatteryStatus) {
            Log.d(TAG, "CORE_BATTERY_STATUS_CREATED: " +
                "ID=${status.hashCode()}, " +
                "Percentage=${status.percentage}%, " +
                "Charging=${status.isCharging}, " +
                "PluggedSource=${getPluggedSourceName(status.pluggedSource)}, " +
                "Current=${status.currentMicroAmperes}µA, " +
                "Voltage=${status.voltageMillivolts}mV, " +
                "Temperature=${status.temperatureCelsius}°C, " +
                "Timestamp=${status.timestampEpochMillis}")
        }
        
        /**
         * Converts plugged source integer to human-readable string for logging.
         *
         * @param pluggedSource The plugged source integer value
         * @return Human-readable string representation
         */
        private fun getPluggedSourceName(pluggedSource: Int): String {
            return when (pluggedSource) {
                0 -> "UNPLUGGED"
                1 -> "AC"
                2 -> "USB"
                4 -> "WIRELESS"
                8 -> "DOCK"
                else -> "UNKNOWN($pluggedSource)"
            }
        }
        
        /**
         * Creates a default/fallback CoreBatteryStatus when real data is unavailable.
         * This ensures the application always has a valid status object to work with.
         *
         * @return A default CoreBatteryStatus with safe fallback values
         */
        fun createDefault(): CoreBatteryStatus {
            val defaultStatus = CoreBatteryStatus(
                percentage = 0,
                isCharging = false,
                pluggedSource = 0, // UNPLUGGED
                currentMicroAmperes = 0L,
                voltageMillivolts = 0,
                temperatureCelsius = 25.0f, // Room temperature as default
                timestampEpochMillis = System.currentTimeMillis()
            )
            
            // Log.w(TAG, "Created default CoreBatteryStatus due to unavailable battery data")
            // logCreation(defaultStatus)
            
            return defaultStatus
        }
    }
}
