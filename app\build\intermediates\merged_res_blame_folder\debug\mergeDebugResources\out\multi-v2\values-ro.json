{"logs": [{"outputFile": "com.tqhit.battery.one.app-mergeDebugResources-99:/values-ro/values-ro.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5c5c72c6ff4a7863322da50648a25e99\\transformed\\browser-1.8.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,264,377", "endColumns": "106,101,112,102", "endOffsets": "157,259,372,475"}, "to": {"startLines": "169,267,268,269", "startColumns": "4,4,4,4", "startOffsets": "14902,25035,25137,25250", "endColumns": "106,101,112,102", "endOffsets": "15004,25132,25245,25348"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5444be4bc77930bd89cfbb9f2224d8e4\\transformed\\navigation-ui-2.8.9\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,165", "endColumns": "109,123", "endOffsets": "160,284"}, "to": {"startLines": "434,435", "startColumns": "4,4", "startOffsets": "40229,40339", "endColumns": "109,123", "endOffsets": "40334,40458"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\237e0b5db534c615c4317f1b214e3e7f\\transformed\\jetified-play-services-ads-24.2.0\\res\\values-ro\\values.xml", "from": {"startLines": "4,5,6,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,244,290,592,660,788,878,999,1049,1099,1213,1294,1339,1432,1467,1501,1561,1643,1685", "endColumns": "44,45,67,67,127,89,120,49,49,113,80,44,92,34,33,59,81,41,55", "endOffsets": "243,289,357,659,787,877,998,1048,1098,1212,1293,1338,1431,1466,1500,1560,1642,1684,1740"}, "to": {"startLines": "431,432,433,451,452,453,454,455,456,457,458,490,491,492,493,494,495,496,547", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "40058,40107,40157,41670,41742,41874,41968,42093,42147,42201,42319,45122,45171,45268,45307,45345,45409,45495,49637", "endColumns": "48,49,71,71,131,93,124,53,53,117,84,48,96,38,37,63,85,45,59", "endOffsets": "40102,40152,40224,41737,41869,41963,42088,42142,42196,42314,42399,45166,45263,45302,45340,45404,45490,45536,49692"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d56ddc8f70c1b6c4f2dfff25a6818549\\transformed\\jetified-play-services-base-18.5.0\\res\\values-ro\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,453,579,685,832,958,1077,1182,1340,1447,1602,1731,1873,2035,2100,2164", "endColumns": "103,155,125,105,146,125,118,104,157,106,154,128,141,161,64,63,78", "endOffsets": "296,452,578,684,831,957,1076,1181,1339,1446,1601,1730,1872,2034,2099,2163,2242"}, "to": {"startLines": "147,148,149,150,151,152,153,154,156,157,158,159,160,161,162,163,164", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12359,12467,12627,12757,12867,13018,13148,13271,13524,13686,13797,13956,14089,14235,14401,14470,14538", "endColumns": "107,159,129,109,150,129,122,108,161,110,158,132,145,165,68,67,82", "endOffsets": "12462,12622,12752,12862,13013,13143,13266,13375,13681,13792,13951,14084,14230,14396,14465,14533,14616"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\237635df39b25799c092d66a208ce67d\\transformed\\jetified-foundation-release\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,150", "endColumns": "94,99", "endOffsets": "145,245"}, "to": {"startLines": "535,536", "startColumns": "4,4", "startOffsets": "48853,48948", "endColumns": "94,99", "endOffsets": "48943,49043"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9e0a763189144907fb0197c2b097244b\\transformed\\jetified-ui-release\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,286,383,485,573,651,738,829,911,999,1089,1236,1315,1390,1467,1534", "endColumns": "96,83,96,101,87,77,86,90,81,87,89,72,78,74,76,66,114", "endOffsets": "197,281,378,480,568,646,733,824,906,994,1084,1157,1310,1385,1462,1529,1644"}, "to": {"startLines": "145,146,182,183,206,293,295,436,442,482,483,503,520,521,526,527,529", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12178,12275,17335,17432,20468,27136,27279,40463,40950,44425,44513,45989,47629,47708,48078,48155,48274", "endColumns": "96,83,96,101,87,77,86,90,81,87,89,72,78,74,76,66,114", "endOffsets": "12270,12354,17427,17529,20551,27209,27361,40549,41027,44508,44598,46057,47703,47778,48150,48217,48384"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\93d3043f0a8b9466a00a736e170a6ddc\\transformed\\appcompat-1.7.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,6,7,10,11,12,13,14,15,16,17,18,19,22,26,27,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,228,531,636,920,1011,1104,1199,1293,1393,1486,1581,1675,1766,2049,2473,2577,2840", "endColumns": "122,105,104,118,90,92,94,93,99,92,94,93,90,91,107,103,161,81", "endOffsets": "223,329,631,750,1006,1099,1194,1288,1388,1481,1576,1670,1761,1853,2152,2572,2734,2917"}, "to": {"startLines": "42,43,46,47,50,51,52,53,54,55,56,57,58,59,62,66,67,499", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2041,2164,2407,2512,2732,2823,2916,3011,3105,3205,3298,3393,3487,3578,3803,4139,4243,45680", "endColumns": "122,105,104,118,90,92,94,93,99,92,94,93,90,91,107,103,161,81", "endOffsets": "2159,2265,2507,2626,2818,2911,3006,3100,3200,3293,3388,3482,3573,3665,3906,4238,4400,45757"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\179e6486bd57a16ea175623aa423e7ed\\transformed\\jetified-material3-release\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,185,312,429,555,665,762,876,1013,1133,1276,1360,1462,1557,1655,1775,1902,2009,2147,2283,2424,2600,2737,2856,2979,3105,3201,3297,3424,3565,3665,3770,3881,4021,4167,4279,4383,4459,4554,4646,4753,4839,4926,5027,5109,5192,5291,5395,5490,5591,5678,5789,5889,5995,6116,6198,6313", "endColumns": "129,126,116,125,109,96,113,136,119,142,83,101,94,97,119,126,106,137,135,140,175,136,118,122,125,95,95,126,140,99,104,110,139,145,111,103,75,94,91,106,85,86,100,81,82,98,103,94,100,86,110,99,105,120,81,114,103", "endOffsets": "180,307,424,550,660,757,871,1008,1128,1271,1355,1457,1552,1650,1770,1897,2004,2142,2278,2419,2595,2732,2851,2974,3100,3196,3292,3419,3560,3660,3765,3876,4016,4162,4274,4378,4454,4549,4641,4748,4834,4921,5022,5104,5187,5286,5390,5485,5586,5673,5784,5884,5990,6111,6193,6308,6412"}, "to": {"startLines": "309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "28643,28773,28900,29017,29143,29253,29350,29464,29601,29721,29864,29948,30050,30145,30243,30363,30490,30597,30735,30871,31012,31188,31325,31444,31567,31693,31789,31885,32012,32153,32253,32358,32469,32609,32755,32867,32971,33047,33142,33234,33341,33427,33514,33615,33697,33780,33879,33983,34078,34179,34266,34377,34477,34583,34704,34786,34901", "endColumns": "129,126,116,125,109,96,113,136,119,142,83,101,94,97,119,126,106,137,135,140,175,136,118,122,125,95,95,126,140,99,104,110,139,145,111,103,75,94,91,106,85,86,100,81,82,98,103,94,100,86,110,99,105,120,81,114,103", "endOffsets": "28768,28895,29012,29138,29248,29345,29459,29596,29716,29859,29943,30045,30140,30238,30358,30485,30592,30730,30866,31007,31183,31320,31439,31562,31688,31784,31880,32007,32148,32248,32353,32464,32604,32750,32862,32966,33042,33137,33229,33336,33422,33509,33610,33692,33775,33874,33978,34073,34174,34261,34372,34472,34578,34699,34781,34896,35000"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b572512e02266e069f95737c22215ab9\\transformed\\jetified-media3-exoplayer-1.6.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,136,198,263,335,413,493,583,676", "endColumns": "80,61,64,71,77,79,89,92,73", "endOffsets": "131,193,258,330,408,488,578,671,745"}, "to": {"startLines": "239,240,241,242,243,244,245,246,247", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "23114,23195,23257,23322,23394,23472,23552,23642,23735", "endColumns": "80,61,64,71,77,79,89,92,73", "endOffsets": "23190,23252,23317,23389,23467,23547,23637,23730,23804"}}, {"source": "D:\\Duc\\AndroidStudioProjects\\TJ_BatteryOne\\app\\src\\main\\res\\values-ro\\strings.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,237,18,19,20,21,22,23,24,25,26,27,28,29,264,30,31,32,33,34,35,36,240,250,253,255,37,2,263,243,261,38,39,40,41,42,43,44,45,115,116,114,113,117,46,47,48,49,50,51,52,53,54,55,56,238,57,58,242,59,60,61,62,63,64,65,66,67,68,248,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,119,120,258,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,259,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,241,168,169,170,171,172,173,174,175,176,177,178,246,245,244,252,256,179,180,181,182,183,184,254,185,186,187,239,188,260,189,190,191,192,193,194,195,196,247,197,198,257,199,200,201,202,203,251,204,205,206,207,208,209,210,211,212,213,214,215,216,262,217,218,219,220,221,222,223,224,225,226,249,227,228,229,230,231,232,233,234,235,236", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "125,192,241,291,341,391,444,493,543,599,673,723,770,821,869,21735,922,1006,1061,1112,1164,1218,1299,1370,1453,1530,1602,1667,24278,1721,1760,1927,1983,2078,2121,2182,21943,22800,23224,23347,2267,57,24233,22153,24115,2310,2357,2396,2481,2537,2613,3005,3402,12165,12230,11473,11386,12295,3443,3514,3584,3784,4106,4180,4235,4280,4343,4413,4464,21827,4534,4608,22074,4653,4739,4783,4857,5018,5088,5182,5264,5332,5427,22672,5509,5566,5619,5683,5744,5790,5860,5968,6018,6377,6431,6492,7055,7633,7702,7754,7899,7962,8253,8306,8801,8871,9012,9168,9320,9391,9439,9489,9563,9661,9749,9802,9951,10055,10110,10176,10245,10526,10805,11006,11169,11262,12382,12453,23920,12508,12550,12650,12716,12774,13025,13075,13152,13217,13273,13411,13470,13602,13836,13874,13930,13968,14006,14042,14078,14152,14197,14252,14307,14373,23979,14447,14522,14578,14622,14665,14741,15193,15250,15324,15490,15567,15639,15676,15711,15752,15795,15852,15917,15987,16045,16124,16270,21991,16305,16363,16416,16511,16581,16654,16761,16893,16965,17009,17090,22462,22281,22195,23124,23681,17392,17432,17487,17538,17601,17693,23274,17741,17828,17878,21898,17920,24045,18056,18153,18252,18310,18381,18578,18653,18714,22583,18777,18829,23726,18894,18974,19071,19156,19201,23055,19273,19352,19396,19456,19503,19627,20010,20089,20134,20193,20273,20331,20384,24178,20424,20497,20717,20798,20864,20938,20993,21067,21134,21173,22742,21234,21293,21339,21381,21434,21499,21565,21618,21654,21690", "endColumns": "65,47,48,48,48,51,47,48,54,72,48,45,49,46,51,90,82,53,49,50,52,79,69,81,75,70,63,52,75,37,165,54,93,41,59,83,46,253,48,332,41,66,43,40,61,45,37,83,54,74,390,395,39,63,63,690,85,83,69,68,198,320,72,53,43,61,68,49,68,69,72,43,77,84,42,72,159,68,92,80,66,93,80,68,55,51,62,59,44,68,106,48,357,52,59,561,576,67,50,143,61,289,51,493,68,139,154,150,69,46,48,72,96,86,51,147,102,53,64,67,279,277,199,161,91,77,69,53,57,40,98,64,56,249,48,75,63,54,136,57,130,232,36,54,36,36,34,34,72,43,53,53,64,72,64,73,54,42,41,74,450,55,72,164,75,70,35,33,39,41,55,63,68,56,77,144,33,81,56,51,93,68,71,105,130,70,42,79,300,119,179,84,98,43,38,53,49,61,90,46,71,85,48,40,43,134,68,95,97,56,69,195,73,59,61,87,50,63,192,78,95,83,43,70,67,77,42,58,45,122,381,77,43,57,78,56,51,38,53,71,218,79,64,72,53,72,65,37,59,56,57,44,40,51,63,64,51,34,34,43", "endOffsets": "186,235,285,335,385,438,487,537,593,667,717,764,815,863,916,21821,1000,1055,1106,1158,1212,1293,1364,1447,1524,1596,1661,1715,24349,1754,1921,1977,2072,2115,2176,2261,21985,23049,23268,23675,2304,119,24272,22189,24172,2351,2390,2475,2531,2607,2999,3396,3437,12224,12289,12159,11467,12374,3508,3578,3778,4100,4174,4229,4274,4337,4407,4458,4528,21892,4602,4647,22147,4733,4777,4851,5012,5082,5176,5258,5326,5421,5503,22736,5560,5613,5677,5738,5784,5854,5962,6012,6371,6425,6486,7049,7627,7696,7748,7893,7956,8247,8300,8795,8865,9006,9162,9314,9385,9433,9483,9557,9655,9743,9796,9945,10049,10104,10170,10239,10520,10799,11000,11163,11256,11335,12447,12502,23973,12544,12644,12710,12768,13019,13069,13146,13211,13267,13405,13464,13596,13830,13868,13924,13962,14000,14036,14072,14146,14191,14246,14301,14367,14441,24039,14516,14572,14616,14659,14735,15187,15244,15318,15484,15561,15633,15670,15705,15746,15789,15846,15911,15981,16039,16118,16264,16299,22068,16357,16410,16505,16575,16648,16755,16887,16959,17003,17084,17386,22577,22456,22275,23218,23720,17426,17481,17532,17595,17687,17735,23341,17822,17872,17914,21937,18050,24109,18147,18246,18304,18375,18572,18647,18708,18771,22666,18823,18888,23914,18968,19065,19150,19195,19267,23118,19346,19390,19450,19497,19621,20004,20083,20128,20187,20267,20325,20378,20418,24227,20491,20711,20792,20858,20932,20987,21061,21128,21167,21228,22794,21287,21333,21375,21428,21493,21559,21612,21648,21684,21729"}, "to": {"startLines": "26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,44,45,48,49,60,61,63,64,65,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,114,115,116,117,118,126,127,128,131,132,133,134,135,136,137,138,139,140,141,142,143,165,166,167,168,170,171,172,173,174,175,176,177,178,179,180,181,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,207,208,209,210,211,213,265,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,291,292,294,296,297,299,300,301,302,303,304,305,306,307,308,366,367,368,369,383,384,430,437,438,439,440,441,443,444,445,446,447,448,449,450,459,460,461,462,463,464,465,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,484,485,486,487,488,489,497,498,502,504,505,506,507,508,509,510,511,513,514,515,516,517,518,519,523,524,525,528,530,531,532,533,534,537,538,539,540,541,542,543,544,545,546,548,549,550,551,552,553,554,555", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1168,1234,1282,1331,1380,1429,1481,1529,1578,1633,1706,1755,1801,1851,1898,1950,2270,2353,2631,2681,3670,3723,3911,3981,4063,4405,4476,4540,4593,4669,4707,4873,4928,5022,5064,5124,5208,5255,5509,5558,5891,5933,6000,6044,6085,6147,6193,6231,6315,6370,6445,6836,7232,7272,7336,7400,8091,8177,8261,8331,8400,8599,8920,8993,9047,9091,9606,9675,9725,9794,9864,10664,10708,10786,11093,11136,11209,11369,11438,11531,11612,11679,11773,11854,11923,11979,12031,14621,14681,14726,14795,15009,15058,15416,15469,15529,16091,16668,16736,16787,16931,16993,17283,17534,18028,18097,18237,18392,18543,18613,18660,18709,18782,18879,18966,19018,19166,19269,19323,19388,19456,19736,20014,20214,20376,20556,20634,20704,20758,20816,20919,24875,25353,25410,25660,25709,25785,25849,25904,26041,26099,26230,26463,26500,26555,26592,26629,26664,26699,26772,26816,26870,26998,27063,27214,27366,27440,27555,27598,27640,27715,28166,28222,28295,28460,28536,28607,35005,35039,35079,35121,36151,36215,40001,40554,40632,40777,40811,40893,41032,41084,41178,41247,41319,41425,41556,41627,42404,42484,42785,42905,43085,43170,43269,43392,43431,43485,43535,43597,43688,43735,43807,43893,43942,43983,44027,44162,44231,44327,44603,44660,44730,44926,45000,45060,45541,45629,45925,46062,46255,46334,46430,46514,46558,46629,46697,46854,46897,46956,47002,47125,47507,47585,47884,47942,48021,48222,48389,48428,48482,48554,48773,49048,49113,49186,49240,49313,49379,49417,49477,49534,49592,49697,49738,49790,49854,49919,49971,50006,50041", "endColumns": "65,47,48,48,48,51,47,48,54,72,48,45,49,46,51,90,82,53,49,50,52,79,69,81,75,70,63,52,75,37,165,54,93,41,59,83,46,253,48,332,41,66,43,40,61,45,37,83,54,74,390,395,39,63,63,690,85,83,69,68,198,320,72,53,43,61,68,49,68,69,72,43,77,84,42,72,159,68,92,80,66,93,80,68,55,51,62,59,44,68,106,48,357,52,59,561,576,67,50,143,61,289,51,493,68,139,154,150,69,46,48,72,96,86,51,147,102,53,64,67,279,277,199,161,91,77,69,53,57,40,98,64,56,249,48,75,63,54,136,57,130,232,36,54,36,36,34,34,72,43,53,53,64,72,64,73,54,42,41,74,450,55,72,164,75,70,35,33,39,41,55,63,68,56,77,144,33,81,56,51,93,68,71,105,130,70,42,79,300,119,179,84,98,43,38,53,49,61,90,46,71,85,48,40,43,134,68,95,97,56,69,195,73,59,61,87,50,63,192,78,95,83,43,70,67,77,42,58,45,122,381,77,43,57,78,56,51,38,53,71,218,79,64,72,53,72,65,37,59,56,57,44,40,51,63,64,51,34,34,43", "endOffsets": "1229,1277,1326,1375,1424,1476,1524,1573,1628,1701,1750,1796,1846,1893,1945,2036,2348,2402,2676,2727,3718,3798,3976,4058,4134,4471,4535,4588,4664,4702,4868,4923,5017,5059,5119,5203,5250,5504,5553,5886,5928,5995,6039,6080,6142,6188,6226,6310,6365,6440,6831,7227,7267,7331,7395,8086,8172,8256,8326,8395,8594,8915,8988,9042,9086,9148,9670,9720,9789,9859,9932,10703,10781,10866,11131,11204,11364,11433,11526,11607,11674,11768,11849,11918,11974,12026,12089,14676,14721,14790,14897,15053,15411,15464,15524,16086,16663,16731,16782,16926,16988,17278,17330,18023,18092,18232,18387,18538,18608,18655,18704,18777,18874,18961,19013,19161,19264,19318,19383,19451,19731,20009,20209,20371,20463,20629,20699,20753,20811,20852,21013,24935,25405,25655,25704,25780,25844,25899,26036,26094,26225,26458,26495,26550,26587,26624,26659,26694,26767,26811,26865,26919,27058,27131,27274,27435,27490,27593,27635,27710,28161,28217,28290,28455,28531,28602,28638,35034,35074,35116,35172,36210,36279,40053,40627,40772,40806,40888,40945,41079,41173,41242,41314,41420,41551,41622,41665,42479,42780,42900,43080,43165,43264,43308,43426,43480,43530,43592,43683,43730,43802,43888,43937,43978,44022,44157,44226,44322,44420,44655,44725,44921,44995,45055,45117,45624,45675,45984,46250,46329,46425,46509,46553,46624,46692,46770,46892,46951,46997,47120,47502,47580,47624,47937,48016,48073,48269,48423,48477,48549,48768,48848,49108,49181,49235,49308,49374,49412,49472,49529,49587,49632,49733,49785,49849,49914,49966,50001,50036,50080"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8bd7feaae90e869538df51f29dd16595\\transformed\\jetified-media3-ui-1.6.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,582,852,940,1030,1119,1216,1310,1385,1451,1548,1646,1715,1778,1841,1910,2024,2137,2251,2328,2408,2477,2553,2652,2753,2819,2882,2935,2993,3041,3102,3166,3236,3301,3370,3431,3489,3555,3607,3669,3745,3821,3878", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "17,12,12,87,89,88,96,93,74,65,96,97,68,62,62,68,113,112,113,76,79,68,75,98,100,65,62,52,57,47,60,63,69,64,68,60,57,65,51,61,75,75,56,69", "endOffsets": "281,577,847,935,1025,1114,1211,1305,1380,1446,1543,1641,1710,1773,1836,1905,2019,2132,2246,2323,2403,2472,2548,2647,2748,2814,2877,2930,2988,3036,3097,3161,3231,3296,3365,3426,3484,3550,3602,3664,3740,3816,3873,3943"}, "to": {"startLines": "2,11,16,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,381,677,21084,21172,21262,21351,21448,21542,21617,21683,21780,21878,21947,22010,22073,22142,22256,22369,22483,22560,22640,22709,22785,22884,22985,23051,23809,23862,23920,23968,24029,24093,24163,24228,24297,24358,24416,24482,24534,24596,24672,24748,24805", "endLines": "10,15,20,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264", "endColumns": "17,12,12,87,89,88,96,93,74,65,96,97,68,62,62,68,113,112,113,76,79,68,75,98,100,65,62,52,57,47,60,63,69,64,68,60,57,65,51,61,75,75,56,69", "endOffsets": "376,672,942,21167,21257,21346,21443,21537,21612,21678,21775,21873,21942,22005,22068,22137,22251,22364,22478,22555,22635,22704,22780,22879,22980,23046,23109,23857,23915,23963,24024,24088,24158,24223,24292,24353,24411,24477,24529,24591,24667,24743,24800,24870"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f908cdc45776521b403beeef1508641c\\transformed\\core-1.16.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,454,556,665,782", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "148,250,350,449,551,660,777,878"}, "to": {"startLines": "119,120,121,122,123,124,125,522", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "9937,10035,10137,10237,10336,10438,10547,47783", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "10030,10132,10232,10331,10433,10542,10659,47879"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5b80dcbf636dc26335bd1b8e4f16f918\\transformed\\material-1.12.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,321,413,501,588,684,774,875,996,1080,1142,1208,1303,1377,1437,1521,1583,1649,1707,1780,1843,1899,2018,2075,2136,2192,2266,2411,2497,2572,2661,2740,2824,2957,3039,3122,3268,3358,3438,3493,3544,3610,3683,3761,3832,3917,3988,4065,4139,4211,4317,4408,4482,4577,4675,4749,4829,4930,4983,5069,5135,5224,5314,5376,5440,5503,5577,5689,5799,5909,6014,6073,6128,6207,6293,6370", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "12,91,87,86,95,89,100,120,83,61,65,94,73,59,83,61,65,57,72,62,55,118,56,60,55,73,144,85,74,88,78,83,132,81,82,145,89,79,54,50,65,72,77,70,84,70,76,73,71,105,90,73,94,97,73,79,100,52,85,65,88,89,61,63,62,73,111,109,109,104,58,54,78,85,76,78", "endOffsets": "316,408,496,583,679,769,870,991,1075,1137,1203,1298,1372,1432,1516,1578,1644,1702,1775,1838,1894,2013,2070,2131,2187,2261,2406,2492,2567,2656,2735,2819,2952,3034,3117,3263,3353,3433,3488,3539,3605,3678,3756,3827,3912,3983,4060,4134,4206,4312,4403,4477,4572,4670,4744,4824,4925,4978,5064,5130,5219,5309,5371,5435,5498,5572,5684,5794,5904,6009,6068,6123,6202,6288,6365,6444"}, "to": {"startLines": "21,109,110,111,112,113,129,130,144,212,214,266,290,298,370,371,372,373,374,375,376,377,378,379,380,381,382,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,466,500,501,512", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "947,9153,9245,9333,9420,9516,10871,10972,12094,20857,21018,24940,26924,27495,35177,35261,35323,35389,35447,35520,35583,35639,35758,35815,35876,35932,36006,36284,36370,36445,36534,36613,36697,36830,36912,36995,37141,37231,37311,37366,37417,37483,37556,37634,37705,37790,37861,37938,38012,38084,38190,38281,38355,38450,38548,38622,38702,38803,38856,38942,39008,39097,39187,39249,39313,39376,39450,39562,39672,39782,39887,39946,43313,45762,45848,46775", "endLines": "25,109,110,111,112,113,129,130,144,212,214,266,290,298,370,371,372,373,374,375,376,377,378,379,380,381,382,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,466,500,501,512", "endColumns": "12,91,87,86,95,89,100,120,83,61,65,94,73,59,83,61,65,57,72,62,55,118,56,60,55,73,144,85,74,88,78,83,132,81,82,145,89,79,54,50,65,72,77,70,84,70,76,73,71,105,90,73,94,97,73,79,100,52,85,65,88,89,61,63,62,73,111,109,109,104,58,54,78,85,76,78", "endOffsets": "1163,9240,9328,9415,9511,9601,10967,11088,12173,20914,21079,25030,26993,27550,35256,35318,35384,35442,35515,35578,35634,35753,35810,35871,35927,36001,36146,36365,36440,36529,36608,36692,36825,36907,36990,37136,37226,37306,37361,37412,37478,37551,37629,37700,37785,37856,37933,38007,38079,38185,38276,38350,38445,38543,38617,38697,38798,38851,38937,39003,39092,39182,39244,39308,39371,39445,39557,39667,39777,39882,39941,39996,43387,45843,45920,46849"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\42f95d9fa807b14415e836fc15872a54\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-ro\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "139", "endOffsets": "334"}, "to": {"startLines": "155", "startColumns": "4", "startOffsets": "13380", "endColumns": "143", "endOffsets": "13519"}}]}]}