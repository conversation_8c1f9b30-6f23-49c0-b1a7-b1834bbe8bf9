<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_slide_layout_7" modulePackage="com.tqhit.battery.one" filePath="app\src\main\res\layout\item_slide_layout_7.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.RelativeLayout" rootNodeViewId="@+id/manw"><Targets><Target id="@+id/manw" tag="layout/item_slide_layout_7_0" view="RelativeLayout"><Expressions/><location startLine="1" startOffset="0" endLine="377" endOffset="16"/></Target><Target id="@+id/indent_top" view="LinearLayout"><Expressions/><location startLine="13" startOffset="12" endLine="17" endOffset="53"/></Target><Target id="@+id/main" view="LinearLayout"><Expressions/><location startLine="18" startOffset="12" endLine="250" endOffset="26"/></Target><Target id="@+id/textView4" view="TextView"><Expressions/><location startLine="28" startOffset="16" endLine="42" endOffset="52"/></Target><Target id="@+id/text5" view="LinearLayout"><Expressions/><location startLine="43" startOffset="16" endLine="66" endOffset="30"/></Target><Target id="@+id/device_name" view="TextView"><Expressions/><location startLine="91" startOffset="24" endLine="107" endOffset="60"/></Target><Target id="@+id/discharging" view="TextView"><Expressions/><location startLine="126" startOffset="24" endLine="142" endOffset="60"/></Target><Target id="@+id/polarity" view="TextView"><Expressions/><location startLine="161" startOffset="24" endLine="177" endOffset="60"/></Target><Target id="@+id/parameter" view="TextView"><Expressions/><location startLine="196" startOffset="24" endLine="212" endOffset="60"/></Target><Target id="@+id/capacity" view="TextView"><Expressions/><location startLine="231" startOffset="24" endLine="247" endOffset="60"/></Target><Target id="@+id/change_capacity" view="Button"><Expressions/><location startLine="291" startOffset="20" endLine="303" endOffset="74"/></Target><Target id="@+id/text_view_reset_charge" view="TextView"><Expressions/><location startLine="304" startOffset="20" endLine="317" endOffset="56"/></Target><Target id="@+id/progressbar2" view="com.google.android.material.progressindicator.CircularProgressIndicator"><Expressions/><location startLine="322" startOffset="4" endLine="337" endOffset="33"/></Target><Target id="@+id/button" view="RelativeLayout"><Expressions/><location startLine="338" startOffset="4" endLine="376" endOffset="20"/></Target><Target id="@+id/start_main_activity" view="Button"><Expressions/><location startLine="359" startOffset="8" endLine="367" endOffset="62"/></Target></Targets></Layout>