package com.tqhit.battery.one.features.emoji.presentation.customize;

import com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase;
import com.tqhit.battery.one.features.emoji.domain.use_case.ResetCustomizationUseCase;
import com.tqhit.battery.one.features.emoji.domain.use_case.SaveCustomizationUseCase;
import com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class CustomizeViewModel_Factory implements Factory<CustomizeViewModel> {
  private final Provider<LoadCustomizationUseCase> loadCustomizationUseCaseProvider;

  private final Provider<SaveCustomizationUseCase> saveCustomizationUseCaseProvider;

  private final Provider<ResetCustomizationUseCase> resetCustomizationUseCaseProvider;

  private final Provider<CoreBatteryStatsProvider> coreBatteryStatsProvider;

  public CustomizeViewModel_Factory(
      Provider<LoadCustomizationUseCase> loadCustomizationUseCaseProvider,
      Provider<SaveCustomizationUseCase> saveCustomizationUseCaseProvider,
      Provider<ResetCustomizationUseCase> resetCustomizationUseCaseProvider,
      Provider<CoreBatteryStatsProvider> coreBatteryStatsProvider) {
    this.loadCustomizationUseCaseProvider = loadCustomizationUseCaseProvider;
    this.saveCustomizationUseCaseProvider = saveCustomizationUseCaseProvider;
    this.resetCustomizationUseCaseProvider = resetCustomizationUseCaseProvider;
    this.coreBatteryStatsProvider = coreBatteryStatsProvider;
  }

  @Override
  public CustomizeViewModel get() {
    return newInstance(loadCustomizationUseCaseProvider.get(), saveCustomizationUseCaseProvider.get(), resetCustomizationUseCaseProvider.get(), coreBatteryStatsProvider.get());
  }

  public static CustomizeViewModel_Factory create(
      Provider<LoadCustomizationUseCase> loadCustomizationUseCaseProvider,
      Provider<SaveCustomizationUseCase> saveCustomizationUseCaseProvider,
      Provider<ResetCustomizationUseCase> resetCustomizationUseCaseProvider,
      Provider<CoreBatteryStatsProvider> coreBatteryStatsProvider) {
    return new CustomizeViewModel_Factory(loadCustomizationUseCaseProvider, saveCustomizationUseCaseProvider, resetCustomizationUseCaseProvider, coreBatteryStatsProvider);
  }

  public static CustomizeViewModel newInstance(LoadCustomizationUseCase loadCustomizationUseCase,
      SaveCustomizationUseCase saveCustomizationUseCase,
      ResetCustomizationUseCase resetCustomizationUseCase,
      CoreBatteryStatsProvider coreBatteryStatsProvider) {
    return new CustomizeViewModel(loadCustomizationUseCase, saveCustomizationUseCase, resetCustomizationUseCase, coreBatteryStatsProvider);
  }
}
