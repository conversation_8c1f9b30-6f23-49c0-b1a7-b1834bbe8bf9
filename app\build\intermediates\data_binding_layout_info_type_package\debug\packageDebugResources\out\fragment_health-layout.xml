<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_health" modulePackage="com.tqhit.battery.one" filePath="app\src\main\res\layout\fragment_health.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.core.widget.NestedScrollView" rootNodeViewId="@+id/scroll_view"><Targets><Target id="@+id/scroll_view" tag="layout/fragment_health_0" view="androidx.core.widget.NestedScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="2174" endOffset="39"/></Target><Target id="@+id/degree_of_wear" view="LinearLayout"><Expressions/><location startLine="9" startOffset="8" endLine="548" endOffset="22"/></Target><Target id="@+id/d_t" view="TextView"><Expressions/><location startLine="27" startOffset="16" endLine="35" endOffset="53"/></Target><Target id="@+id/degree_wear_info" view="ImageView"><Expressions/><location startLine="36" startOffset="16" endLine="43" endOffset="53"/></Target><Target id="@+id/cumulative_1" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="50" startOffset="16" endLine="120" endOffset="67"/></Target><Target id="@+id/health_first_progressbar_cumulative" view="ProgressBar"><Expressions/><location startLine="60" startOffset="20" endLine="75" endOffset="73"/></Target><Target id="@+id/health_percent_damage_cumulative" view="TextView"><Expressions/><location startLine="76" startOffset="20" endLine="87" endOffset="66"/></Target><Target id="@+id/percent_cumulative" view="TextView"><Expressions/><location startLine="88" startOffset="20" endLine="100" endOffset="66"/></Target><Target id="@+id/text_remain_var_cumulative" view="TextView"><Expressions/><location startLine="101" startOffset="20" endLine="119" endOffset="66"/></Target><Target id="@+id/singular_1" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="121" startOffset="16" endLine="190" endOffset="67"/></Target><Target id="@+id/health_first_progressbar_singular" view="ProgressBar"><Expressions/><location startLine="131" startOffset="20" endLine="146" endOffset="73"/></Target><Target id="@+id/health_percent_damage_singular" view="TextView"><Expressions/><location startLine="147" startOffset="20" endLine="158" endOffset="66"/></Target><Target id="@+id/percent_singular" view="TextView"><Expressions/><location startLine="159" startOffset="20" endLine="171" endOffset="66"/></Target><Target id="@+id/text_remain_var_singular" view="TextView"><Expressions/><location startLine="172" startOffset="20" endLine="189" endOffset="66"/></Target><Target id="@+id/d_2" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="192" startOffset="12" endLine="241" endOffset="63"/></Target><Target id="@+id/d_21" view="TextView"><Expressions/><location startLine="199" startOffset="16" endLine="210" endOffset="62"/></Target><Target id="@+id/health_full_batery_capacity" view="TextView"><Expressions/><location startLine="211" startOffset="16" endLine="224" endOffset="62"/></Target><Target id="@+id/d_22" view="TextView"><Expressions/><location startLine="225" startOffset="16" endLine="240" endOffset="62"/></Target><Target id="@+id/singular_calculated" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="247" startOffset="16" endLine="312" endOffset="67"/></Target><Target id="@+id/singular_capacity_ni" view="TextView"><Expressions/><location startLine="257" startOffset="20" endLine="269" endOffset="66"/></Target><Target id="@+id/singular_31" view="TextView"><Expressions/><location startLine="270" startOffset="20" endLine="281" endOffset="66"/></Target><Target id="@+id/health_checked_batery_capacity_singular" view="TextView"><Expressions/><location startLine="282" startOffset="20" endLine="295" endOffset="66"/></Target><Target id="@+id/singular_32" view="TextView"><Expressions/><location startLine="296" startOffset="20" endLine="311" endOffset="66"/></Target><Target id="@+id/cumulative_calculated" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="313" startOffset="16" endLine="378" endOffset="67"/></Target><Target id="@+id/cumulative_capacity_ni" view="TextView"><Expressions/><location startLine="323" startOffset="20" endLine="335" endOffset="66"/></Target><Target id="@+id/cumulative_31" view="TextView"><Expressions/><location startLine="336" startOffset="20" endLine="347" endOffset="66"/></Target><Target id="@+id/health_checked_batery_capacity_cumulative" view="TextView"><Expressions/><location startLine="348" startOffset="20" endLine="361" endOffset="66"/></Target><Target id="@+id/cumulative_32" view="TextView"><Expressions/><location startLine="362" startOffset="20" endLine="377" endOffset="66"/></Target><Target id="@+id/cumulative_session_info" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="385" startOffset="16" endLine="416" endOffset="67"/></Target><Target id="@+id/health_count_of_sessions_cumulative" view="TextView"><Expressions/><location startLine="395" startOffset="20" endLine="415" endOffset="66"/></Target><Target id="@+id/singular_session_info" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="417" startOffset="16" endLine="448" endOffset="67"/></Target><Target id="@+id/health_count_of_sessions_singular" view="TextView"><Expressions/><location startLine="427" startOffset="20" endLine="447" endOffset="66"/></Target><Target id="@+id/cumulative_btn" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="454" startOffset="16" endLine="479" endOffset="67"/></Target><Target id="@+id/singular_btn" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="480" startOffset="16" endLine="506" endOffset="67"/></Target><Target id="@+id/method_text" view="TextView"><Expressions/><location startLine="514" startOffset="16" endLine="529" endOffset="62"/></Target><Target id="@+id/method_text_singular" view="TextView"><Expressions/><location startLine="530" startOffset="16" endLine="546" endOffset="62"/></Target><Target id="@+id/time_dead_viewgroup" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="549" startOffset="8" endLine="947" endOffset="59"/></Target><Target id="@+id/dead_time_up" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="559" startOffset="12" endLine="864" endOffset="63"/></Target><Target id="@+id/dead_time_up_singular" view="LinearLayout"><Expressions/><location startLine="567" startOffset="16" endLine="715" endOffset="30"/></Target><Target id="@+id/proggersBarDamage_cumulative" view="RelativeLayout"><Expressions/><location startLine="585" startOffset="24" endLine="623" endOffset="40"/></Target><Target id="@+id/damage_bar_percent_current_singular" view="ProgressBar"><Expressions/><location startLine="593" startOffset="28" endLine="603" endOffset="81"/></Target><Target id="@+id/damage_bar_seekwhite_singular" view="ProgressBar"><Expressions/><location startLine="604" startOffset="28" endLine="613" endOffset="81"/></Target><Target id="@+id/seekBar_singular" view="SeekBar"><Expressions/><location startLine="624" startOffset="24" endLine="637" endOffset="61"/></Target><Target id="@+id/text_top_dead_time_111_cumulative" view="RelativeLayout"><Expressions/><location startLine="656" startOffset="24" endLine="694" endOffset="40"/></Target><Target id="@+id/percent_damage_cumulative" view="LinearLayout"><Expressions/><location startLine="671" startOffset="28" endLine="693" endOffset="42"/></Target><Target id="@+id/percent_damage_dead_singular" view="TextView"><Expressions/><location startLine="679" startOffset="32" endLine="685" endOffset="64"/></Target><Target id="@+id/text_dead_singular" view="TextView"><Expressions/><location startLine="702" startOffset="24" endLine="713" endOffset="60"/></Target><Target id="@+id/dead_time_up_cummulative" view="LinearLayout"><Expressions/><location startLine="716" startOffset="16" endLine="863" endOffset="30"/></Target><Target id="@+id/proggersBarDamage" view="RelativeLayout"><Expressions/><location startLine="734" startOffset="24" endLine="772" endOffset="40"/></Target><Target id="@+id/damage_bar_percent_current" view="ProgressBar"><Expressions/><location startLine="742" startOffset="28" endLine="752" endOffset="81"/></Target><Target id="@+id/damage_bar_seekwhite" view="ProgressBar"><Expressions/><location startLine="753" startOffset="28" endLine="762" endOffset="81"/></Target><Target id="@+id/seekBar" view="SeekBar"><Expressions/><location startLine="773" startOffset="24" endLine="786" endOffset="61"/></Target><Target id="@+id/text_top_dead_time_111" view="RelativeLayout"><Expressions/><location startLine="805" startOffset="24" endLine="843" endOffset="40"/></Target><Target id="@+id/percent_damage" view="LinearLayout"><Expressions/><location startLine="820" startOffset="28" endLine="842" endOffset="42"/></Target><Target id="@+id/percent_damage_dead" view="TextView"><Expressions/><location startLine="828" startOffset="32" endLine="834" endOffset="64"/></Target><Target id="@+id/text_dead" view="TextView"><Expressions/><location startLine="851" startOffset="24" endLine="861" endOffset="60"/></Target><Target id="@+id/dead_time_text" view="TextView"><Expressions/><location startLine="865" startOffset="12" endLine="882" endOffset="58"/></Target><Target id="@+id/prediction_wear_info" view="ImageView"><Expressions/><location startLine="883" startOffset="12" endLine="895" endOffset="71"/></Target><Target id="@+id/blurView_dead_top" view="eightbitlab.com.blurview.BlurView"><Expressions/><location startLine="913" startOffset="12" endLine="946" endOffset="47"/></Target><Target id="@+id/health_access2" view="LinearLayout"><Expressions/><location startLine="924" startOffset="16" endLine="945" endOffset="30"/></Target><Target id="@+id/why_i_need_to_do_this" view="LinearLayout"><Expressions/><location startLine="957" startOffset="12" endLine="962" endOffset="53"/></Target><Target id="@+id/percent_graph_change" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="963" startOffset="12" endLine="1043" endOffset="63"/></Target><Target id="@+id/btn0" view="TextView"><Expressions/><location startLine="971" startOffset="16" endLine="987" endOffset="62"/></Target><Target id="@+id/btn1" view="TextView"><Expressions/><location startLine="988" startOffset="16" endLine="1005" endOffset="62"/></Target><Target id="@+id/btn2" view="TextView"><Expressions/><location startLine="1006" startOffset="16" endLine="1023" endOffset="62"/></Target><Target id="@+id/btn3" view="TextView"><Expressions/><location startLine="1024" startOffset="16" endLine="1042" endOffset="62"/></Target><Target id="@+id/history_database" view="TextView"><Expressions/><location startLine="1044" startOffset="12" endLine="1067" endOffset="47"/></Target><Target id="@+id/wear_rate_percent" view="TextView"><Expressions/><location startLine="1068" startOffset="12" endLine="1076" endOffset="50"/></Target><Target id="@+id/sdfsd" view="TextView"><Expressions/><location startLine="1077" startOffset="12" endLine="1093" endOffset="47"/></Target><Target id="@+id/under_graph_percent" view="LinearLayout"><Expressions/><location startLine="1094" startOffset="12" endLine="1181" endOffset="26"/></Target><Target id="@+id/day_7_percent" view="TextView"><Expressions/><location startLine="1111" startOffset="16" endLine="1119" endOffset="46"/></Target><Target id="@+id/day_6_percent" view="TextView"><Expressions/><location startLine="1120" startOffset="16" endLine="1128" endOffset="46"/></Target><Target id="@+id/day_5_percent" view="TextView"><Expressions/><location startLine="1129" startOffset="16" endLine="1137" endOffset="46"/></Target><Target id="@+id/day_4_percent" view="TextView"><Expressions/><location startLine="1138" startOffset="16" endLine="1146" endOffset="46"/></Target><Target id="@+id/day_3_percent" view="TextView"><Expressions/><location startLine="1147" startOffset="16" endLine="1155" endOffset="46"/></Target><Target id="@+id/day_2_percent" view="TextView"><Expressions/><location startLine="1156" startOffset="16" endLine="1164" endOffset="46"/></Target><Target id="@+id/day_1_percent" view="TextView"><Expressions/><location startLine="1165" startOffset="16" endLine="1173" endOffset="46"/></Target><Target id="@+id/graph_percent" view="RelativeLayout"><Expressions/><location startLine="1182" startOffset="12" endLine="1379" endOffset="28"/></Target><Target id="@+id/chart1_percent" view="RelativeLayout"><Expressions/><location startLine="1367" startOffset="16" endLine="1378" endOffset="32"/></Target><Target id="@+id/chart_percent" view="com.github.mikephil.charting.charts.LineChart"><Expressions/><location startLine="1374" startOffset="20" endLine="1377" endOffset="61"/></Target><Target id="@+id/grahp_temp_viewgroup" view="RelativeLayout"><Expressions/><location startLine="1381" startOffset="8" endLine="1795" endOffset="24"/></Target><Target id="@+id/time_graph_change" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1392" startOffset="12" endLine="1473" endOffset="63"/></Target><Target id="@+id/btn0_t" view="TextView"><Expressions/><location startLine="1401" startOffset="16" endLine="1417" endOffset="63"/></Target><Target id="@+id/btn1_t" view="TextView"><Expressions/><location startLine="1418" startOffset="16" endLine="1435" endOffset="62"/></Target><Target id="@+id/btn2_t" view="TextView"><Expressions/><location startLine="1436" startOffset="16" endLine="1453" endOffset="62"/></Target><Target id="@+id/btn3_t" view="TextView"><Expressions/><location startLine="1454" startOffset="16" endLine="1472" endOffset="62"/></Target><Target id="@+id/wear_rate3" view="TextView"><Expressions/><location startLine="1474" startOffset="12" endLine="1482" endOffset="50"/></Target><Target id="@+id/under_graph_temp" view="LinearLayout"><Expressions/><location startLine="1499" startOffset="12" endLine="1586" endOffset="26"/></Target><Target id="@+id/day_7_temp" view="TextView"><Expressions/><location startLine="1516" startOffset="16" endLine="1524" endOffset="46"/></Target><Target id="@+id/day_6_temp" view="TextView"><Expressions/><location startLine="1525" startOffset="16" endLine="1533" endOffset="46"/></Target><Target id="@+id/day_5_temp" view="TextView"><Expressions/><location startLine="1534" startOffset="16" endLine="1542" endOffset="46"/></Target><Target id="@+id/day_4_temp" view="TextView"><Expressions/><location startLine="1543" startOffset="16" endLine="1551" endOffset="46"/></Target><Target id="@+id/day_3_temp" view="TextView"><Expressions/><location startLine="1552" startOffset="16" endLine="1560" endOffset="46"/></Target><Target id="@+id/day_2_temp" view="TextView"><Expressions/><location startLine="1561" startOffset="16" endLine="1569" endOffset="46"/></Target><Target id="@+id/day_1_temp" view="TextView"><Expressions/><location startLine="1570" startOffset="16" endLine="1578" endOffset="46"/></Target><Target id="@+id/graph_temp" view="RelativeLayout"><Expressions/><location startLine="1587" startOffset="12" endLine="1794" endOffset="28"/></Target><Target id="@+id/t1_temp" view="TextView"><Expressions/><location startLine="1606" startOffset="20" endLine="1616" endOffset="51"/></Target><Target id="@+id/t2_temp" view="TextView"><Expressions/><location startLine="1617" startOffset="20" endLine="1627" endOffset="51"/></Target><Target id="@+id/t3_temp" view="TextView"><Expressions/><location startLine="1628" startOffset="20" endLine="1638" endOffset="51"/></Target><Target id="@+id/t4_temp" view="TextView"><Expressions/><location startLine="1639" startOffset="20" endLine="1649" endOffset="51"/></Target><Target id="@+id/t5_temp" view="TextView"><Expressions/><location startLine="1650" startOffset="20" endLine="1660" endOffset="51"/></Target><Target id="@+id/t6_temp" view="TextView"><Expressions/><location startLine="1661" startOffset="20" endLine="1671" endOffset="51"/></Target><Target id="@+id/t7_temp" view="TextView"><Expressions/><location startLine="1672" startOffset="20" endLine="1682" endOffset="51"/></Target><Target id="@+id/t8_temp" view="TextView"><Expressions/><location startLine="1683" startOffset="20" endLine="1693" endOffset="51"/></Target><Target id="@+id/t9_temp" view="TextView"><Expressions/><location startLine="1694" startOffset="20" endLine="1704" endOffset="51"/></Target><Target id="@+id/chart1_l" view="RelativeLayout"><Expressions/><location startLine="1781" startOffset="16" endLine="1793" endOffset="32"/></Target><Target id="@+id/chart1" view="com.github.mikephil.charting.charts.LineChart"><Expressions/><location startLine="1789" startOffset="20" endLine="1792" endOffset="62"/></Target><Target id="@+id/wear_rate2" view="TextView"><Expressions/><location startLine="1804" startOffset="12" endLine="1812" endOffset="50"/></Target><Target id="@+id/under_graph" view="LinearLayout"><Expressions/><location startLine="1830" startOffset="12" endLine="1916" endOffset="26"/></Target><Target id="@+id/day_7" view="TextView"><Expressions/><location startLine="1846" startOffset="16" endLine="1854" endOffset="46"/></Target><Target id="@+id/day_6" view="TextView"><Expressions/><location startLine="1855" startOffset="16" endLine="1863" endOffset="46"/></Target><Target id="@+id/day_5" view="TextView"><Expressions/><location startLine="1864" startOffset="16" endLine="1872" endOffset="46"/></Target><Target id="@+id/day_4" view="TextView"><Expressions/><location startLine="1873" startOffset="16" endLine="1881" endOffset="46"/></Target><Target id="@+id/day_3" view="TextView"><Expressions/><location startLine="1882" startOffset="16" endLine="1890" endOffset="46"/></Target><Target id="@+id/day_2" view="TextView"><Expressions/><location startLine="1891" startOffset="16" endLine="1899" endOffset="46"/></Target><Target id="@+id/day_1" view="TextView"><Expressions/><location startLine="1900" startOffset="16" endLine="1908" endOffset="46"/></Target><Target id="@+id/graph" view="RelativeLayout"><Expressions/><location startLine="1917" startOffset="12" endLine="2136" endOffset="28"/></Target><Target id="@+id/t1" view="TextView"><Expressions/><location startLine="1935" startOffset="20" endLine="1945" endOffset="51"/></Target><Target id="@+id/t2" view="TextView"><Expressions/><location startLine="1946" startOffset="20" endLine="1956" endOffset="51"/></Target><Target id="@+id/t3" view="TextView"><Expressions/><location startLine="1957" startOffset="20" endLine="1967" endOffset="51"/></Target><Target id="@+id/t4" view="TextView"><Expressions/><location startLine="1968" startOffset="20" endLine="1978" endOffset="51"/></Target><Target id="@+id/t5" view="TextView"><Expressions/><location startLine="1979" startOffset="20" endLine="1989" endOffset="51"/></Target><Target id="@+id/t6" view="TextView"><Expressions/><location startLine="1990" startOffset="20" endLine="2000" endOffset="51"/></Target><Target id="@+id/t7" view="TextView"><Expressions/><location startLine="2001" startOffset="20" endLine="2011" endOffset="51"/></Target><Target id="@+id/t8" view="TextView"><Expressions/><location startLine="2012" startOffset="20" endLine="2022" endOffset="51"/></Target><Target id="@+id/t9" view="TextView"><Expressions/><location startLine="2023" startOffset="20" endLine="2033" endOffset="51"/></Target><Target id="@+id/progbar_7" view="com.tqhit.battery.one.component.progress.VerticalProgressBar"><Expressions/><location startLine="2044" startOffset="20" endLine="2056" endOffset="73"/></Target><Target id="@+id/progbar_6" view="com.tqhit.battery.one.component.progress.VerticalProgressBar"><Expressions/><location startLine="2057" startOffset="20" endLine="2069" endOffset="73"/></Target><Target id="@+id/progbar_5" view="com.tqhit.battery.one.component.progress.VerticalProgressBar"><Expressions/><location startLine="2070" startOffset="20" endLine="2082" endOffset="73"/></Target><Target id="@+id/progbar_4" view="com.tqhit.battery.one.component.progress.VerticalProgressBar"><Expressions/><location startLine="2083" startOffset="20" endLine="2095" endOffset="73"/></Target><Target id="@+id/progbar_3" view="com.tqhit.battery.one.component.progress.VerticalProgressBar"><Expressions/><location startLine="2096" startOffset="20" endLine="2108" endOffset="73"/></Target><Target id="@+id/progbar_2" view="com.tqhit.battery.one.component.progress.VerticalProgressBar"><Expressions/><location startLine="2109" startOffset="20" endLine="2121" endOffset="73"/></Target><Target id="@+id/progbar_1" view="com.tqhit.battery.one.component.progress.VerticalProgressBar"><Expressions/><location startLine="2122" startOffset="20" endLine="2134" endOffset="73"/></Target><Target id="@+id/indent_down" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="2138" startOffset="8" endLine="2143" endOffset="47"/></Target><Target id="@+id/update_view" view="LinearLayout"><Expressions/><location startLine="2144" startOffset="8" endLine="2172" endOffset="22"/></Target><Target id="@+id/update_view_btn" view="TextView"><Expressions/><location startLine="2154" startOffset="12" endLine="2171" endOffset="47"/></Target></Targets></Layout>