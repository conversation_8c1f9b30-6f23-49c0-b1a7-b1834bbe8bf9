package com.tqhit.battery.one.features.stats

import android.content.Context
import com.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus
import com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider
import com.tqhit.battery.one.features.stats.corebattery.domain.DefaultCoreBatteryStatsProvider
import com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationServiceHelper
import io.mockk.mockk
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.runTest
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test

/**
 * Test class to verify the unified battery monitoring approach works correctly
 * and that CoreBatteryStatsProvider serves as the single source of truth.
 */
@OptIn(ExperimentalCoroutinesApi::class)
class UnifiedBatteryServiceTest {

    private lateinit var coreBatteryStatsProvider: CoreBatteryStatsProvider
    private lateinit var mockContext: Context
    private lateinit var serviceHelper: UnifiedBatteryNotificationServiceHelper

    @Before
    fun setup() {
        coreBatteryStatsProvider = DefaultCoreBatteryStatsProvider()
        mockContext = mockk(relaxed = true)
        serviceHelper = UnifiedBatteryNotificationServiceHelper(mockContext)
    }

    @Test
    fun `test CoreBatteryStatsProvider provides consistent data`() = runTest {
        // Given
        val testStatus = CoreBatteryStatus(
            percentage = 85,
            isCharging = true,
            pluggedSource = 2, // USB
            currentMicroAmperes = 1500000L, // 1.5A charging
            voltageMillivolts = 4200,
            temperatureCelsius = 25.5f,
            timestampEpochMillis = System.currentTimeMillis()
        )

        // When
        coreBatteryStatsProvider.updateStatus(testStatus)

        // Then
        val receivedStatus = coreBatteryStatsProvider.coreBatteryStatusFlow.first()
        assertNotNull("Status should not be null", receivedStatus)
        assertEquals("Percentage should match", 85, receivedStatus!!.percentage)
        assertTrue("Should be charging", receivedStatus.isCharging)
        assertEquals("Current should match", 1500000L, receivedStatus.currentMicroAmperes)
        assertEquals("Voltage should match", 4200, receivedStatus.voltageMillivolts)
        assertEquals("Temperature should match", 25.5f, receivedStatus.temperatureCelsius, 0.01f)
    }

    @Test
    fun `test CoreBatteryStatsProvider handles charging state changes`() = runTest {
        // Given - Initial charging state
        val chargingStatus = CoreBatteryStatus(
            percentage = 50,
            isCharging = true,
            pluggedSource = 2,
            currentMicroAmperes = 1000000L,
            voltageMillivolts = 4000,
            temperatureCelsius = 25.0f,
            timestampEpochMillis = System.currentTimeMillis()
        )

        // When - Update to charging
        coreBatteryStatsProvider.updateStatus(chargingStatus)
        val chargingReceived = coreBatteryStatsProvider.coreBatteryStatusFlow.first()

        // Then - Verify charging state
        assertTrue("Should be charging", chargingReceived!!.isCharging)
        assertTrue("Current should be positive when charging", chargingReceived.currentMicroAmperes > 0)

        // Given - Discharging state
        val dischargingStatus = chargingStatus.copy(
            isCharging = false,
            pluggedSource = 0,
            currentMicroAmperes = -500000L, // Discharging
            timestampEpochMillis = System.currentTimeMillis()
        )

        // When - Update to discharging
        coreBatteryStatsProvider.updateStatus(dischargingStatus)
        val dischargingReceived = coreBatteryStatsProvider.coreBatteryStatusFlow.first()

        // Then - Verify discharging state
        assertFalse("Should not be charging", dischargingReceived!!.isCharging)
        assertTrue("Current should be negative when discharging", dischargingReceived.currentMicroAmperes < 0)
        assertEquals("Plugged source should be 0 (unplugged)", 0, dischargingReceived.pluggedSource)
    }

    @Test
    fun `test CoreBatteryStatsProvider getCurrentStatus returns latest status`() {
        // Given
        val testStatus = CoreBatteryStatus(
            percentage = 75,
            isCharging = false,
            pluggedSource = 0,
            currentMicroAmperes = -800000L,
            voltageMillivolts = 3800,
            temperatureCelsius = 30.0f,
            timestampEpochMillis = System.currentTimeMillis()
        )

        // When
        coreBatteryStatsProvider.updateStatus(testStatus)

        // Then
        val currentStatus = coreBatteryStatsProvider.getCurrentStatus()
        assertNotNull("Current status should not be null", currentStatus)
        assertEquals("Percentage should match", 75, currentStatus!!.percentage)
        assertFalse("Should not be charging", currentStatus.isCharging)
        assertEquals("Current should match", -800000L, currentStatus.currentMicroAmperes)
    }

    @Test
    fun `test CoreBatteryStatsProvider handles null initial state`() {
        // Given - Fresh provider instance
        val freshProvider = DefaultCoreBatteryStatsProvider()

        // When - Get current status before any updates
        val initialStatus = freshProvider.getCurrentStatus()

        // Then
        assertNull("Initial status should be null", initialStatus)
    }

    @Test
    fun `test battery percentage boundaries`() = runTest {
        // Test minimum percentage (0%)
        val minStatus = CoreBatteryStatus(
            percentage = 0,
            isCharging = false,
            pluggedSource = 0,
            currentMicroAmperes = -1000000L,
            voltageMillivolts = 3200,
            temperatureCelsius = 20.0f,
            timestampEpochMillis = System.currentTimeMillis()
        )

        coreBatteryStatsProvider.updateStatus(minStatus)
        val receivedMin = coreBatteryStatsProvider.coreBatteryStatusFlow.first()
        assertEquals("Minimum percentage should be 0", 0, receivedMin!!.percentage)

        // Test maximum percentage (100%)
        val maxStatus = minStatus.copy(
            percentage = 100,
            isCharging = true,
            currentMicroAmperes = 0L, // Trickle charge or maintenance
            timestampEpochMillis = System.currentTimeMillis()
        )

        coreBatteryStatsProvider.updateStatus(maxStatus)
        val receivedMax = coreBatteryStatsProvider.coreBatteryStatusFlow.first()
        assertEquals("Maximum percentage should be 100", 100, receivedMax!!.percentage)
    }

    @Test
    fun `test service helper is properly initialized`() {
        // Given - Service helper with mock context
        assertNotNull("Service helper should be initialized", serviceHelper)
        
        // When/Then - Service helper should not throw exceptions during basic operations
        try {
            // These methods should not throw exceptions even with mock context
            serviceHelper.isServiceRunning() // Should return false with mock
            // Note: startService() and stopService() would require actual context for testing
        } catch (e: Exception) {
            fail("Service helper basic operations should not throw exceptions: ${e.message}")
        }
    }
}
