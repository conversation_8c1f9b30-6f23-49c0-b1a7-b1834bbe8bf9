package com.tqhit.battery.one.utils

import android.Manifest
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.provider.Settings
import androidx.activity.result.ActivityResultLauncher
import androidx.appcompat.app.AlertDialog
import androidx.core.content.PermissionChecker
import androidx.core.content.PermissionChecker.checkSelfPermission
import androidx.fragment.app.Fragment
import com.tqhit.battery.one.R
import android.app.Activity

object PermissionUtils {
    private const val NOTIFICATION_PERMISSION_REQUEST_CODE = 1
    private var notificationPermissionDeniedCount = 0

    fun isNotificationPermissionGranted(context: Context): Bo<PERSON>an {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            checkSelfPermission(context, Manifest.permission.POST_NOTIFICATIONS) ==
                    PermissionChecker.PERMISSION_GRANTED
        } else {
            true
        }
    }

    fun requestNotificationPermission(
            context: Context,
            permissionLauncher: ActivityResultLauncher<String>,
            onPermissionGranted: () -> Unit = {},
            onPermissionDenied: () -> Unit = {}
    ) {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU) {
            onPermissionGranted()
            return
        }

        if (isNotificationPermissionGranted(context)) {
            onPermissionGranted()
            return
        }

        if (notificationPermissionDeniedCount >= 2) {
            showSettingsDialog(context, onPermissionDenied)
            return
        }

        // Safe way to check if we should show rationale without casting context
        val shouldShowRationale = when (context) {
            is Activity -> shouldShowRequestPermissionRationaleForActivity(context)
            is Fragment -> shouldShowRequestPermissionRationaleForFragment(context)
            else -> false
        }

        if (shouldShowRationale) {
            showRationaleDialog(
                    context,
                    onRequestPermission = {
                        permissionLauncher.launch(Manifest.permission.POST_NOTIFICATIONS)
                    },
                    onPermissionDenied = onPermissionDenied
            )
        } else {
            permissionLauncher.launch(Manifest.permission.POST_NOTIFICATIONS)
        }
    }

    // For Activity contexts
    fun shouldShowRequestPermissionRationaleForActivity(activity: Activity): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            androidx.core.app.ActivityCompat.shouldShowRequestPermissionRationale(
                    activity,
                    Manifest.permission.POST_NOTIFICATIONS
            )
        } else {
            false
        }
    }

    // For Fragment contexts
    fun shouldShowRequestPermissionRationaleForFragment(fragment: Fragment): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            fragment.shouldShowRequestPermissionRationale(Manifest.permission.POST_NOTIFICATIONS)
        } else {
            false
        }
    }

    // Keep for backward compatibility but mark as deprecated
    @Deprecated("Use the typed versions instead to avoid ClassCastException", 
               ReplaceWith("shouldShowRequestPermissionRationaleForActivity(context as Activity) or shouldShowRequestPermissionRationaleForFragment(context as Fragment)"))
    fun shouldShowRequestPermissionRationale(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            when (context) {
                is Activity -> shouldShowRequestPermissionRationaleForActivity(context)
                is Fragment -> shouldShowRequestPermissionRationaleForFragment(context)
                else -> false
            }
        } else {
            false
        }
    }

    private fun showRationaleDialog(
            context: Context,
            onRequestPermission: () -> Unit,
            onPermissionDenied: () -> Unit
    ) {
        AlertDialog.Builder(context)
                .setTitle(R.string.notifications_permission_title)
                .setMessage(R.string.notify_access)
                .setPositiveButton(R.string.notifications_permission_confirm) { _, _ ->
                    onRequestPermission()
                }
                .setNegativeButton(R.string.notifications_permission_decline) { dialog, _ ->
                    dialog.dismiss()
                    notificationPermissionDeniedCount++
                    onPermissionDenied()
                }
                .setCancelable(false)
                .show()
    }

    private fun showSettingsDialog(context: Context, onPermissionDenied: () -> Unit) {
        AlertDialog.Builder(context)
                .setTitle(R.string.notifications_permission_title)
                .setMessage(R.string.notify_access)
                .setPositiveButton(R.string.grant_permission) { _, _ -> openAppSettings(context) }
                .setNegativeButton(R.string.cancel) { dialog, _ ->
                    dialog.dismiss()
                    onPermissionDenied()
                }
                .setCancelable(false)
                .show()
    }

    fun openAppSettings(context: Context) {
        val intent =
                Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                    data = Uri.fromParts("package", context.packageName, null)
                }
        context.startActivity(intent)
    }

    fun resetNotificationPermissionDeniedCount() {
        notificationPermissionDeniedCount = 0
    }

    fun incrementNotificationPermissionDeniedCount() {
        notificationPermissionDeniedCount++
    }
}
