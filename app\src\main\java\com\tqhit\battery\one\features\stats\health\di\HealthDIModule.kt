package com.tqhit.battery.one.features.stats.health.di

import com.tqhit.battery.one.features.stats.health.cache.DefaultHealthCache
import com.tqhit.battery.one.features.stats.health.cache.HealthCache
import com.tqhit.battery.one.features.stats.health.repository.DefaultHealthRepository
import com.tqhit.battery.one.features.stats.health.repository.HealthRepository
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Dagger Hilt module for health feature dependency injection.
 * Provides bindings for health-related interfaces and implementations.
 */
@Module
@InstallIn(SingletonComponent::class)
abstract class HealthDIModule {
    
    /**
     * Binds the default HealthRepository implementation.
     * 
     * @param defaultHealthRepository The default implementation
     * @return HealthRepository interface
     */
    @Binds
    @Singleton
    abstract fun bindHealthRepository(
        defaultHealthRepository: DefaultHealthRepository
    ): HealthRepository
    
    /**
     * Binds the default HealthCache implementation.
     * 
     * @param defaultHealthCache The default implementation
     * @return HealthCache interface
     */
    @Binds
    @Singleton
    abstract fun bindHealthCache(
        defaultHealthCache: DefaultHealthCache
    ): HealthCache
}
