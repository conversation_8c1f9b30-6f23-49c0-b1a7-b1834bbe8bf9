{"logs": [{"outputFile": "com.tqhit.battery.one.app-mergeDebugResources-99:/values-fr/values-fr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8bd7feaae90e869538df51f29dd16595\\transformed\\jetified-media3-ui-1.6.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,291,483,692,781,872,951,1049,1146,1225,1291,1397,1504,1569,1635,1699,1771,1891,2014,2136,2211,2299,2372,2452,2543,2636,2702,2766,2819,2879,2927,2988,3059,3130,3197,3275,3340,3399,3465,3517,3577,3651,3725,3779", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,88,90,78,97,96,78,65,105,106,64,65,63,71,119,122,121,74,87,72,79,90,92,65,63,52,59,47,60,70,70,66,77,64,58,65,51,59,73,73,53,65", "endOffsets": "286,478,687,776,867,946,1044,1141,1220,1286,1392,1499,1564,1630,1694,1766,1886,2009,2131,2206,2294,2367,2447,2538,2631,2697,2761,2814,2874,2922,2983,3054,3125,3192,3270,3335,3394,3460,3512,3572,3646,3720,3774,3840"}, "to": {"startLines": "2,11,15,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,386,578,21907,21996,22087,22166,22264,22361,22440,22506,22612,22719,22784,22850,22914,22986,23106,23229,23351,23426,23514,23587,23667,23758,23851,23917,24694,24747,24807,24855,24916,24987,25058,25125,25203,25268,25327,25393,25445,25505,25579,25653,25707", "endLines": "10,14,18,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261", "endColumns": "17,12,12,88,90,78,97,96,78,65,105,106,64,65,63,71,119,122,121,74,87,72,79,90,92,65,63,52,59,47,60,70,70,66,77,64,58,65,51,59,73,73,53,65", "endOffsets": "381,573,782,21991,22082,22161,22259,22356,22435,22501,22607,22714,22779,22845,22909,22981,23101,23224,23346,23421,23509,23582,23662,23753,23846,23912,23976,24742,24802,24850,24911,24982,25053,25120,25198,25263,25322,25388,25440,25500,25574,25648,25702,25768"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9e0a763189144907fb0197c2b097244b\\transformed\\jetified-ui-release\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,292,392,492,579,658,750,842,929,1010,1095,1246,1324,1398,1476,1545", "endColumns": "98,87,99,99,86,78,91,91,86,80,84,75,77,73,77,68,121", "endOffsets": "199,287,387,487,574,653,745,837,924,1005,1090,1166,1319,1393,1471,1540,1662"}, "to": {"startLines": "142,143,179,180,203,290,292,433,439,479,480,500,517,518,523,524,526", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12414,12513,17983,18083,21285,28175,28326,41627,42114,45837,45918,47452,49194,49272,49646,49724,49845", "endColumns": "98,87,99,99,86,78,91,91,86,80,84,75,77,73,77,68,121", "endOffsets": "12508,12596,18078,18178,21367,28249,28413,41714,42196,45913,45998,47523,49267,49341,49719,49788,49962"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b572512e02266e069f95737c22215ab9\\transformed\\jetified-media3-exoplayer-1.6.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,194,263,335,418,495,592,685", "endColumns": "73,64,68,71,82,76,96,92,82", "endOffsets": "124,189,258,330,413,490,587,680,763"}, "to": {"startLines": "236,237,238,239,240,241,242,243,244", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "23981,24055,24120,24189,24261,24344,24421,24518,24611", "endColumns": "73,64,68,71,82,76,96,92,82", "endOffsets": "24050,24115,24184,24256,24339,24416,24513,24606,24689"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f908cdc45776521b403beeef1508641c\\transformed\\core-1.16.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,782", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "148,250,349,451,555,659,777,878"}, "to": {"startLines": "116,117,118,119,120,121,122,519", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "10146,10244,10346,10445,10547,10651,10755,49346", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "10239,10341,10440,10542,10646,10750,10868,49442"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\42f95d9fa807b14415e836fc15872a54\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-fr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "160", "endOffsets": "355"}, "to": {"startLines": "152", "startColumns": "4", "startOffsets": "13664", "endColumns": "164", "endOffsets": "13824"}}, {"source": "D:\\Duc\\AndroidStudioProjects\\TJ_BatteryOne\\app\\src\\main\\res\\values-fr\\strings.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,237,18,19,20,21,22,23,24,25,26,27,28,29,265,30,31,32,33,34,35,36,241,251,254,256,37,2,264,244,262,38,39,40,41,42,43,44,45,115,116,114,113,117,46,47,48,49,50,51,52,53,54,55,56,239,57,58,243,59,60,61,62,63,64,65,66,67,68,249,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,119,120,259,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,260,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,242,168,169,170,171,172,173,174,175,176,177,178,247,246,245,253,257,179,180,181,182,183,184,255,185,186,187,240,188,261,189,190,191,192,193,194,195,196,248,197,198,258,199,200,201,202,203,252,204,205,206,207,208,209,210,211,212,213,214,215,216,263,217,218,219,220,221,222,223,224,225,226,250,227,228,229,230,231,232,233,234,235,236", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "130,201,253,303,353,404,457,505,555,610,683,734,782,833,880,23015,932,1014,1072,1124,1175,1232,1313,1387,1471,1550,1620,1693,25780,1747,1786,1969,2026,2120,2163,2223,23265,24156,24647,24774,2322,57,25734,23468,25609,2367,2416,2455,2551,2610,2686,3117,3555,12954,13021,12178,12089,13085,3598,3679,3757,3993,4346,4437,4489,4531,4590,4663,4711,23151,4778,4850,23390,4894,4975,5019,5091,5270,5342,5441,5524,5594,5687,24029,5757,5818,5877,5948,6010,6057,6129,6242,6292,6678,6733,6795,7440,8105,8180,8235,8391,8465,8781,8837,9379,9442,9585,9750,9916,9981,10028,10076,10153,10253,10346,10398,10546,10651,10711,10779,10854,11179,11471,11699,11857,11961,13172,13245,25395,13301,13343,13432,13505,13563,13819,13872,13956,14027,14096,14247,14312,14460,14710,14749,14805,14843,14881,14917,14953,15039,15081,15144,15202,15275,25462,15351,15420,15480,15525,15566,15639,16107,16164,16243,16426,16510,16593,16630,16665,16706,16749,16806,16873,16943,17000,17072,17230,23314,17266,17324,17379,17478,17550,17629,17749,17891,17972,18016,18102,23818,23605,23513,24537,25140,18449,18489,18544,18595,18666,18750,24696,18801,18884,18933,23220,18978,25535,19105,19200,19297,19355,19429,19665,19740,19803,23937,19872,19925,25191,19993,20081,20190,20282,20331,24460,20401,20487,20534,20590,20638,20760,21178,21266,21312,21370,21455,21514,21567,25676,21608,21684,21926,22019,22085,22167,22221,22306,22390,22429,24097,22496,22563,22608,22650,22704,22769,22844,22897,22934,22970", "endColumns": "69,50,48,48,49,51,46,48,53,71,49,46,49,45,50,75,80,56,50,49,55,79,72,82,77,68,71,52,72,37,181,55,92,41,58,97,47,302,47,364,43,71,44,43,65,47,37,94,57,74,429,436,41,65,62,774,87,83,79,76,234,351,89,50,40,57,71,46,65,67,70,42,76,79,42,70,177,70,97,81,68,91,68,66,59,57,69,60,45,70,111,48,384,53,60,643,663,73,53,154,72,314,54,540,61,141,163,164,63,45,46,75,98,91,50,146,103,58,66,73,323,290,226,156,102,81,71,54,65,40,87,71,56,254,51,82,69,67,149,63,146,248,37,54,36,36,34,34,84,40,61,56,71,74,71,67,58,43,39,71,466,55,77,181,82,81,35,33,39,41,55,65,68,55,70,156,34,74,56,53,97,70,77,118,140,79,42,84,345,117,211,90,108,49,38,53,49,69,82,49,76,81,47,43,43,125,72,93,95,56,72,234,73,61,67,90,51,66,202,86,107,90,47,68,75,84,45,54,46,120,416,86,44,56,83,57,51,39,56,74,240,91,64,80,52,83,82,37,65,57,65,43,40,52,63,73,51,35,34,43", "endOffsets": "195,247,297,347,398,451,499,549,604,677,728,776,827,874,926,23086,1008,1066,1118,1169,1226,1307,1381,1465,1544,1614,1687,1741,25848,1780,1963,2020,2114,2157,2217,2316,23308,24454,24690,25134,2361,124,25774,23507,25670,2410,2449,2545,2604,2680,3111,3549,3592,13015,13079,12948,12172,13164,3673,3751,3987,4340,4431,4483,4525,4584,4657,4705,4772,23214,4844,4888,23462,4969,5013,5085,5264,5336,5435,5518,5588,5681,5751,24091,5812,5871,5942,6004,6051,6123,6236,6286,6672,6727,6789,7434,8099,8174,8229,8385,8459,8775,8831,9373,9436,9579,9744,9910,9975,10022,10070,10147,10247,10340,10392,10540,10645,10705,10773,10848,11173,11465,11693,11851,11955,12038,13239,13295,25456,13337,13426,13499,13557,13813,13866,13950,14021,14090,14241,14306,14454,14704,14743,14799,14837,14875,14911,14947,15033,15075,15138,15196,15269,15345,25529,15414,15474,15519,15560,15633,16101,16158,16237,16420,16504,16587,16624,16659,16700,16743,16800,16867,16937,16994,17066,17224,17260,23384,17318,17373,17472,17544,17623,17743,17885,17966,18010,18096,18443,23931,23812,23599,24641,25185,18483,18538,18589,18660,18744,18795,24768,18878,18927,18972,23259,19099,25603,19194,19291,19349,19423,19659,19734,19797,19866,24023,19919,19987,25389,20075,20184,20276,20325,20395,24531,20481,20528,20584,20632,20754,21172,21260,21306,21364,21449,21508,21561,21602,25728,21678,21920,22013,22079,22161,22215,22300,22384,22423,22490,24150,22557,22602,22644,22698,22763,22838,22891,22928,22964,23009"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,41,42,45,46,57,58,60,61,62,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,111,112,113,114,115,123,124,125,128,129,130,131,132,133,134,135,136,137,138,139,140,162,163,164,165,167,168,169,170,171,172,173,174,175,176,177,178,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,204,205,206,207,208,210,262,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,288,289,291,293,294,296,297,298,299,300,301,302,303,304,305,363,364,365,366,380,381,427,434,435,436,437,438,440,441,442,443,444,445,446,447,456,457,458,459,460,461,462,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,481,482,483,484,485,486,494,495,499,501,502,503,504,505,506,507,508,510,511,512,513,514,515,516,520,521,522,525,527,528,529,530,531,534,535,536,537,538,539,540,541,542,543,545,546,547,548,549,550,551,552", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "970,1040,1091,1140,1189,1239,1291,1338,1387,1441,1513,1563,1610,1660,1706,1757,2059,2140,2433,2484,3474,3530,3721,3794,3877,4221,4290,4362,4415,4488,4526,4708,4764,4857,4899,4958,5056,5104,5407,5455,5820,5864,5936,5981,6025,6091,6139,6177,6272,6330,6405,6835,7272,7314,7380,7443,8218,8306,8390,8470,8547,8782,9134,9224,9275,9316,9822,9894,9941,10007,10075,10873,10916,10993,11301,11344,11415,11593,11664,11762,11844,11913,12005,12074,12141,12201,12259,15003,15064,15110,15181,15400,15449,15834,15888,15949,16593,17257,17331,17385,17540,17613,17928,18183,18724,18786,18928,19092,19257,19321,19367,19414,19490,19589,19681,19732,19879,19983,20042,20109,20183,20507,20798,21025,21182,21372,21454,21526,21581,21647,21753,25773,26268,26325,26580,26632,26715,26785,26853,27003,27067,27214,27463,27501,27556,27593,27630,27665,27700,27785,27826,27888,28028,28100,28254,28418,28486,28611,28655,28695,28767,29234,29290,29368,29550,29633,29715,36173,36207,36247,36289,37350,37416,41185,41719,41790,41947,41982,42057,42201,42255,42353,42424,42502,42621,42762,42842,43707,43792,44138,44256,44468,44559,44668,44807,44846,44900,44950,45020,45103,45153,45230,45312,45360,45404,45448,45574,45647,45741,46003,46060,46133,46368,46442,46504,46991,47082,47385,47528,47731,47818,47926,48017,48065,48134,48210,48376,48422,48477,48524,48645,49062,49149,49447,49504,49588,49793,49967,50007,50064,50139,50380,50656,50721,50802,50855,50939,51022,51060,51126,51184,51250,51354,51395,51448,51512,51586,51638,51674,51709", "endColumns": "69,50,48,48,49,51,46,48,53,71,49,46,49,45,50,75,80,56,50,49,55,79,72,82,77,68,71,52,72,37,181,55,92,41,58,97,47,302,47,364,43,71,44,43,65,47,37,94,57,74,429,436,41,65,62,774,87,83,79,76,234,351,89,50,40,57,71,46,65,67,70,42,76,79,42,70,177,70,97,81,68,91,68,66,59,57,69,60,45,70,111,48,384,53,60,643,663,73,53,154,72,314,54,540,61,141,163,164,63,45,46,75,98,91,50,146,103,58,66,73,323,290,226,156,102,81,71,54,65,40,87,71,56,254,51,82,69,67,149,63,146,248,37,54,36,36,34,34,84,40,61,56,71,74,71,67,58,43,39,71,466,55,77,181,82,81,35,33,39,41,55,65,68,55,70,156,34,74,56,53,97,70,77,118,140,79,42,84,345,117,211,90,108,49,38,53,49,69,82,49,76,81,47,43,43,125,72,93,95,56,72,234,73,61,67,90,51,66,202,86,107,90,47,68,75,84,45,54,46,120,416,86,44,56,83,57,51,39,56,74,240,91,64,80,52,83,82,37,65,57,65,43,40,52,63,73,51,35,34,43", "endOffsets": "1035,1086,1135,1184,1234,1286,1333,1382,1436,1508,1558,1605,1655,1701,1752,1828,2135,2192,2479,2529,3525,3605,3789,3872,3950,4285,4357,4410,4483,4521,4703,4759,4852,4894,4953,5051,5099,5402,5450,5815,5859,5931,5976,6020,6086,6134,6172,6267,6325,6400,6830,7267,7309,7375,7438,8213,8301,8385,8465,8542,8777,9129,9219,9270,9311,9369,9889,9936,10002,10070,10141,10911,10988,11068,11339,11410,11588,11659,11757,11839,11908,12000,12069,12136,12196,12254,12324,15059,15105,15176,15288,15444,15829,15883,15944,16588,17252,17326,17380,17535,17608,17923,17978,18719,18781,18923,19087,19252,19316,19362,19409,19485,19584,19676,19727,19874,19978,20037,20104,20178,20502,20793,21020,21177,21280,21449,21521,21576,21642,21683,21836,25840,26320,26575,26627,26710,26780,26848,26998,27062,27209,27458,27496,27551,27588,27625,27660,27695,27780,27821,27883,27940,28095,28170,28321,28481,28540,28650,28690,28762,29229,29285,29363,29545,29628,29710,29746,36202,36242,36284,36340,37411,37480,41236,41785,41942,41977,42052,42109,42250,42348,42419,42497,42616,42757,42837,42880,43787,44133,44251,44463,44554,44663,44713,44841,44895,44945,45015,45098,45148,45225,45307,45355,45399,45443,45569,45642,45736,45832,46055,46128,46363,46437,46499,46567,47077,47129,47447,47726,47813,47921,48012,48060,48129,48205,48290,48417,48472,48519,48640,49057,49144,49189,49499,49583,49641,49840,50002,50059,50134,50375,50467,50716,50797,50850,50934,51017,51055,51121,51179,51245,51289,51390,51443,51507,51581,51633,51669,51704,51748"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5444be4bc77930bd89cfbb9f2224d8e4\\transformed\\navigation-ui-2.8.9\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,158", "endColumns": "102,125", "endOffsets": "153,279"}, "to": {"startLines": "431,432", "startColumns": "4,4", "startOffsets": "41398,41501", "endColumns": "102,125", "endOffsets": "41496,41622"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d56ddc8f70c1b6c4f2dfff25a6818549\\transformed\\jetified-play-services-base-18.5.0\\res\\values-fr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,471,597,702,869,998,1115,1224,1398,1506,1687,1819,1975,2150,2219,2282", "endColumns": "101,175,125,104,166,128,116,108,173,107,180,131,155,174,68,62,79", "endOffsets": "294,470,596,701,868,997,1114,1223,1397,1505,1686,1818,1974,2149,2218,2281,2361"}, "to": {"startLines": "144,145,146,147,148,149,150,151,153,154,155,156,157,158,159,160,161", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12601,12707,12887,13017,13126,13297,13430,13551,13829,14007,14119,14304,14440,14600,14779,14852,14919", "endColumns": "105,179,129,108,170,132,120,112,177,111,184,135,159,178,72,66,83", "endOffsets": "12702,12882,13012,13121,13292,13425,13546,13659,14002,14114,14299,14435,14595,14774,14847,14914,14998"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5c5c72c6ff4a7863322da50648a25e99\\transformed\\browser-1.8.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,264,383", "endColumns": "106,101,118,104", "endOffsets": "157,259,378,483"}, "to": {"startLines": "166,264,265,266", "startColumns": "4,4,4,4", "startOffsets": "15293,25942,26044,26163", "endColumns": "106,101,118,104", "endOffsets": "15395,26039,26158,26263"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\93d3043f0a8b9466a00a736e170a6ddc\\transformed\\appcompat-1.7.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,6,7,10,11,12,13,14,15,16,17,18,19,22,26,27,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,523,629,913,1004,1097,1195,1290,1390,1483,1576,1671,1762,2049,2482,2589,2847", "endColumns": "110,114,105,129,90,92,97,94,99,92,92,94,90,90,110,106,158,86", "endOffsets": "211,326,624,754,999,1092,1190,1285,1385,1478,1571,1666,1757,1848,2155,2584,2743,2929"}, "to": {"startLines": "39,40,43,44,47,48,49,50,51,52,53,54,55,56,59,63,64,496", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1833,1944,2197,2303,2534,2625,2718,2816,2911,3011,3104,3197,3292,3383,3610,3955,4062,47134", "endColumns": "110,114,105,129,90,92,97,94,99,92,92,94,90,90,110,106,158,86", "endOffsets": "1939,2054,2298,2428,2620,2713,2811,2906,3006,3099,3192,3287,3378,3469,3716,4057,4216,47216"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\237e0b5db534c615c4317f1b214e3e7f\\transformed\\jetified-play-services-ads-24.2.0\\res\\values-fr\\values.xml", "from": {"startLines": "4,5,6,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,241,287,594,657,802,920,1042,1092,1150,1282,1384,1432,1527,1563,1598,1654,1735,1775", "endColumns": "41,45,56,62,144,117,121,49,57,131,101,47,94,35,34,55,80,39,55", "endOffsets": "240,286,343,656,801,919,1041,1091,1149,1281,1383,1431,1526,1562,1597,1653,1734,1774,1830"}, "to": {"startLines": "428,429,430,448,449,450,451,452,453,454,455,487,488,489,490,491,492,493,544", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "41241,41287,41337,42885,42952,43101,43223,43349,43403,43465,43601,46572,46624,46723,46763,46802,46862,46947,51294", "endColumns": "45,49,60,66,148,121,125,53,61,135,105,51,98,39,38,59,84,43,59", "endOffsets": "41282,41332,41393,42947,43096,43218,43344,43398,43460,43596,43702,46619,46718,46758,46797,46857,46942,46986,51349"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\237635df39b25799c092d66a208ce67d\\transformed\\jetified-foundation-release\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,144", "endColumns": "88,94", "endOffsets": "139,234"}, "to": {"startLines": "532,533", "startColumns": "4,4", "startOffsets": "50472,50561", "endColumns": "88,94", "endOffsets": "50556,50651"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5b80dcbf636dc26335bd1b8e4f16f918\\transformed\\material-1.12.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,363,444,527,636,731,829,959,1044,1109,1175,1272,1355,1421,1523,1588,1663,1719,1798,1858,1912,2034,2093,2155,2209,2291,2426,2518,2593,2688,2769,2853,2997,3076,3157,3298,3391,3470,3525,3576,3642,3722,3803,3874,3954,4027,4105,4178,4250,4362,4455,4527,4619,4711,4785,4869,4961,5018,5102,5168,5251,5338,5400,5464,5527,5605,5707,5811,5908,6012,6071,6126,6215,6302,6379", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,79,80,82,108,94,97,129,84,64,65,96,82,65,101,64,74,55,78,59,53,121,58,61,53,81,134,91,74,94,80,83,143,78,80,140,92,78,54,50,65,79,80,70,79,72,77,72,71,111,92,71,91,91,73,83,91,56,83,65,82,86,61,63,62,77,101,103,96,103,58,54,88,86,76,80", "endOffsets": "278,358,439,522,631,726,824,954,1039,1104,1170,1267,1350,1416,1518,1583,1658,1714,1793,1853,1907,2029,2088,2150,2204,2286,2421,2513,2588,2683,2764,2848,2992,3071,3152,3293,3386,3465,3520,3571,3637,3717,3798,3869,3949,4022,4100,4173,4245,4357,4450,4522,4614,4706,4780,4864,4956,5013,5097,5163,5246,5333,5395,5459,5522,5600,5702,5806,5903,6007,6066,6121,6210,6297,6374,6455"}, "to": {"startLines": "19,106,107,108,109,110,126,127,141,209,211,263,287,295,367,368,369,370,371,372,373,374,375,376,377,378,379,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,463,497,498,509", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "787,9374,9454,9535,9618,9727,11073,11171,12329,21688,21841,25845,27945,28545,36345,36447,36512,36587,36643,36722,36782,36836,36958,37017,37079,37133,37215,37485,37577,37652,37747,37828,37912,38056,38135,38216,38357,38450,38529,38584,38635,38701,38781,38862,38933,39013,39086,39164,39237,39309,39421,39514,39586,39678,39770,39844,39928,40020,40077,40161,40227,40310,40397,40459,40523,40586,40664,40766,40870,40967,41071,41130,44718,47221,47308,48295", "endLines": "22,106,107,108,109,110,126,127,141,209,211,263,287,295,367,368,369,370,371,372,373,374,375,376,377,378,379,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,463,497,498,509", "endColumns": "12,79,80,82,108,94,97,129,84,64,65,96,82,65,101,64,74,55,78,59,53,121,58,61,53,81,134,91,74,94,80,83,143,78,80,140,92,78,54,50,65,79,80,70,79,72,77,72,71,111,92,71,91,91,73,83,91,56,83,65,82,86,61,63,62,77,101,103,96,103,58,54,88,86,76,80", "endOffsets": "965,9449,9530,9613,9722,9817,11166,11296,12409,21748,21902,25937,28023,28606,36442,36507,36582,36638,36717,36777,36831,36953,37012,37074,37128,37210,37345,37572,37647,37742,37823,37907,38051,38130,38211,38352,38445,38524,38579,38630,38696,38776,38857,38928,39008,39081,39159,39232,39304,39416,39509,39581,39673,39765,39839,39923,40015,40072,40156,40222,40305,40392,40454,40518,40581,40659,40761,40865,40962,41066,41125,41180,44802,47303,47380,48371"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\179e6486bd57a16ea175623aa423e7ed\\transformed\\jetified-material3-release\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,293,415,536,635,729,841,985,1104,1251,1335,1435,1536,1637,1758,1885,1990,2140,2286,2416,2608,2734,2852,2975,3108,3210,3315,3439,3564,3666,3773,3878,4023,4175,4284,4393,4480,4573,4668,4788,4879,4965,5072,5152,5237,5339,5451,5549,5649,5737,5853,5954,6057,6189,6269,6379", "endColumns": "119,117,121,120,98,93,111,143,118,146,83,99,100,100,120,126,104,149,145,129,191,125,117,122,132,101,104,123,124,101,106,104,144,151,108,108,86,92,94,119,90,85,106,79,84,101,111,97,99,87,115,100,102,131,79,109,97", "endOffsets": "170,288,410,531,630,724,836,980,1099,1246,1330,1430,1531,1632,1753,1880,1985,2135,2281,2411,2603,2729,2847,2970,3103,3205,3310,3434,3559,3661,3768,3873,4018,4170,4279,4388,4475,4568,4663,4783,4874,4960,5067,5147,5232,5334,5446,5544,5644,5732,5848,5949,6052,6184,6264,6374,6472"}, "to": {"startLines": "306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "29751,29871,29989,30111,30232,30331,30425,30537,30681,30800,30947,31031,31131,31232,31333,31454,31581,31686,31836,31982,32112,32304,32430,32548,32671,32804,32906,33011,33135,33260,33362,33469,33574,33719,33871,33980,34089,34176,34269,34364,34484,34575,34661,34768,34848,34933,35035,35147,35245,35345,35433,35549,35650,35753,35885,35965,36075", "endColumns": "119,117,121,120,98,93,111,143,118,146,83,99,100,100,120,126,104,149,145,129,191,125,117,122,132,101,104,123,124,101,106,104,144,151,108,108,86,92,94,119,90,85,106,79,84,101,111,97,99,87,115,100,102,131,79,109,97", "endOffsets": "29866,29984,30106,30227,30326,30420,30532,30676,30795,30942,31026,31126,31227,31328,31449,31576,31681,31831,31977,32107,32299,32425,32543,32666,32799,32901,33006,33130,33255,33357,33464,33569,33714,33866,33975,34084,34171,34264,34359,34479,34570,34656,34763,34843,34928,35030,35142,35240,35340,35428,35544,35645,35748,35880,35960,36070,36168"}}]}]}