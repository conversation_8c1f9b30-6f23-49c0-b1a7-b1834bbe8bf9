// Generated by view binder compiler. Do not edit!
package com.tqhit.battery.one.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.materialswitch.MaterialSwitch;
import com.google.android.material.slider.Slider;
import com.tqhit.battery.one.R;
import com.tqhit.battery.one.features.emoji.presentation.customize.view.LivePreviewView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentCustomizeBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final MaterialButton applyButton;

  @NonNull
  public final RecyclerView batteryOptionsRecyclerView;

  @NonNull
  public final MaterialButton colorPickerButton;

  @NonNull
  public final RecyclerView emojiOptionsRecyclerView;

  @NonNull
  public final Slider emojiSizeSlider;

  @NonNull
  public final LivePreviewView livePreviewView;

  @NonNull
  public final FrameLayout loadingOverlay;

  @NonNull
  public final TextView loadingText;

  @NonNull
  public final Slider percentageFontSizeSlider;

  @NonNull
  public final Slider previewBatterySlider;

  @NonNull
  public final TextView previewBatteryText;

  @NonNull
  public final FrameLayout previewContainer;

  @NonNull
  public final ProgressBar previewLoadingIndicator;

  @NonNull
  public final MaterialButton resetButton;

  @NonNull
  public final MaterialButton saveButton;

  @NonNull
  public final MaterialSwitch showEmojiSwitch;

  @NonNull
  public final MaterialSwitch showPercentageSwitch;

  private FragmentCustomizeBinding(@NonNull CoordinatorLayout rootView,
      @NonNull MaterialButton applyButton, @NonNull RecyclerView batteryOptionsRecyclerView,
      @NonNull MaterialButton colorPickerButton, @NonNull RecyclerView emojiOptionsRecyclerView,
      @NonNull Slider emojiSizeSlider, @NonNull LivePreviewView livePreviewView,
      @NonNull FrameLayout loadingOverlay, @NonNull TextView loadingText,
      @NonNull Slider percentageFontSizeSlider, @NonNull Slider previewBatterySlider,
      @NonNull TextView previewBatteryText, @NonNull FrameLayout previewContainer,
      @NonNull ProgressBar previewLoadingIndicator, @NonNull MaterialButton resetButton,
      @NonNull MaterialButton saveButton, @NonNull MaterialSwitch showEmojiSwitch,
      @NonNull MaterialSwitch showPercentageSwitch) {
    this.rootView = rootView;
    this.applyButton = applyButton;
    this.batteryOptionsRecyclerView = batteryOptionsRecyclerView;
    this.colorPickerButton = colorPickerButton;
    this.emojiOptionsRecyclerView = emojiOptionsRecyclerView;
    this.emojiSizeSlider = emojiSizeSlider;
    this.livePreviewView = livePreviewView;
    this.loadingOverlay = loadingOverlay;
    this.loadingText = loadingText;
    this.percentageFontSizeSlider = percentageFontSizeSlider;
    this.previewBatterySlider = previewBatterySlider;
    this.previewBatteryText = previewBatteryText;
    this.previewContainer = previewContainer;
    this.previewLoadingIndicator = previewLoadingIndicator;
    this.resetButton = resetButton;
    this.saveButton = saveButton;
    this.showEmojiSwitch = showEmojiSwitch;
    this.showPercentageSwitch = showPercentageSwitch;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentCustomizeBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentCustomizeBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_customize, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentCustomizeBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.applyButton;
      MaterialButton applyButton = ViewBindings.findChildViewById(rootView, id);
      if (applyButton == null) {
        break missingId;
      }

      id = R.id.batteryOptionsRecyclerView;
      RecyclerView batteryOptionsRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (batteryOptionsRecyclerView == null) {
        break missingId;
      }

      id = R.id.colorPickerButton;
      MaterialButton colorPickerButton = ViewBindings.findChildViewById(rootView, id);
      if (colorPickerButton == null) {
        break missingId;
      }

      id = R.id.emojiOptionsRecyclerView;
      RecyclerView emojiOptionsRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (emojiOptionsRecyclerView == null) {
        break missingId;
      }

      id = R.id.emojiSizeSlider;
      Slider emojiSizeSlider = ViewBindings.findChildViewById(rootView, id);
      if (emojiSizeSlider == null) {
        break missingId;
      }

      id = R.id.livePreviewView;
      LivePreviewView livePreviewView = ViewBindings.findChildViewById(rootView, id);
      if (livePreviewView == null) {
        break missingId;
      }

      id = R.id.loadingOverlay;
      FrameLayout loadingOverlay = ViewBindings.findChildViewById(rootView, id);
      if (loadingOverlay == null) {
        break missingId;
      }

      id = R.id.loadingText;
      TextView loadingText = ViewBindings.findChildViewById(rootView, id);
      if (loadingText == null) {
        break missingId;
      }

      id = R.id.percentageFontSizeSlider;
      Slider percentageFontSizeSlider = ViewBindings.findChildViewById(rootView, id);
      if (percentageFontSizeSlider == null) {
        break missingId;
      }

      id = R.id.previewBatterySlider;
      Slider previewBatterySlider = ViewBindings.findChildViewById(rootView, id);
      if (previewBatterySlider == null) {
        break missingId;
      }

      id = R.id.previewBatteryText;
      TextView previewBatteryText = ViewBindings.findChildViewById(rootView, id);
      if (previewBatteryText == null) {
        break missingId;
      }

      id = R.id.previewContainer;
      FrameLayout previewContainer = ViewBindings.findChildViewById(rootView, id);
      if (previewContainer == null) {
        break missingId;
      }

      id = R.id.previewLoadingIndicator;
      ProgressBar previewLoadingIndicator = ViewBindings.findChildViewById(rootView, id);
      if (previewLoadingIndicator == null) {
        break missingId;
      }

      id = R.id.resetButton;
      MaterialButton resetButton = ViewBindings.findChildViewById(rootView, id);
      if (resetButton == null) {
        break missingId;
      }

      id = R.id.saveButton;
      MaterialButton saveButton = ViewBindings.findChildViewById(rootView, id);
      if (saveButton == null) {
        break missingId;
      }

      id = R.id.showEmojiSwitch;
      MaterialSwitch showEmojiSwitch = ViewBindings.findChildViewById(rootView, id);
      if (showEmojiSwitch == null) {
        break missingId;
      }

      id = R.id.showPercentageSwitch;
      MaterialSwitch showPercentageSwitch = ViewBindings.findChildViewById(rootView, id);
      if (showPercentageSwitch == null) {
        break missingId;
      }

      return new FragmentCustomizeBinding((CoordinatorLayout) rootView, applyButton,
          batteryOptionsRecyclerView, colorPickerButton, emojiOptionsRecyclerView, emojiSizeSlider,
          livePreviewView, loadingOverlay, loadingText, percentageFontSizeSlider,
          previewBatterySlider, previewBatteryText, previewContainer, previewLoadingIndicator,
          resetButton, saveButton, showEmojiSwitch, showPercentageSwitch);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
