<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/scroll_view"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <LinearLayout
            android:orientation="vertical"
            android:id="@+id/degree_of_wear"
            android:background="@drawable/white_block"
            android:paddingTop="7dp"
            android:paddingBottom="8dp"
            android:visibility="visible"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:animateLayoutChanges="true"
            android:paddingStart="8dp"
            android:paddingEnd="8dp"
            android:layout_marginStart="9dp"
            android:layout_marginEnd="9dp">
            <LinearLayout
                android:orientation="horizontal"
                android:layout_width="match_parent"
                android:layout_height="match_parent">
                <TextView
                    android:textSize="19sp"
                    android:textColor="?attr/black"
                    android:id="@+id/d_t"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="@string/wear_rate"
                    android:layout_weight="1"
                    android:layout_marginStart="2dp"/>
                <ImageView
                    android:id="@+id/degree_wear_info"
                    android:visibility="visible"
                    android:layout_width="22sp"
                    android:layout_height="match_parent"
                    android:src="@drawable/ic_note"
                    android:scaleType="fitEnd"
                    android:layout_marginStart="5dp"/>
            </LinearLayout>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="7dp"
                android:animateLayoutChanges="true">
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/cumulative_1"
                    android:background="@drawable/grey_block_line_up_static"
                    android:visibility="visible"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">
                    <ProgressBar
                        android:id="@+id/health_first_progressbar_cumulative"
                        android:layout_width="0dp"
                        android:layout_height="6dp"
                        android:layout_marginTop="4dp"
                        android:layout_marginBottom="6dp"
                        android:max="100"
                        android:progress="80"
                        android:progressDrawable="@drawable/progress_bar"
                        android:layout_marginStart="7dp"
                        android:layout_marginEnd="7dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/text_remain_var_cumulative"
                        style="?android:attr/progressBarStyleHorizontal"/>
                    <TextView
                        android:textSize="13sp"
                        android:textColor="?attr/black"
                        android:id="@+id/health_percent_damage_cumulative"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="7dp"
                        android:text="@string/zero"
                        android:textAlignment="viewStart"
                        app:layout_constraintEnd_toStartOf="@+id/percent_cumulative"
                        app:layout_constraintStart_toEndOf="@+id/text_remain_var_cumulative"
                        app:layout_constraintTop_toTopOf="parent"/>
                    <TextView
                        android:textSize="13sp"
                        android:textColor="?attr/black"
                        android:id="@+id/percent_cumulative"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="7dp"
                        android:text="@string/percent_without_tab"
                        android:textAlignment="viewStart"
                        android:layout_marginEnd="10dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@+id/health_percent_damage_cumulative"
                        app:layout_constraintTop_toTopOf="parent"/>
                    <TextView
                        android:textSize="14sp"
                        android:textColor="?attr/black"
                        android:ellipsize="marquee"
                        android:id="@+id/text_remain_var_cumulative"
                        android:focusableInTouchMode="true"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="7dp"
                        android:text="@string/maximum_capacity"
                        android:singleLine="true"
                        android:marqueeRepeatLimit="marquee_forever"
                        android:textAlignment="viewStart"
                        android:layout_marginStart="10dp"
                        android:layout_marginEnd="5dp"
                        android:layout_alignParentStart="true"
                        app:layout_constraintEnd_toStartOf="@+id/health_percent_damage_cumulative"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"/>
                </androidx.constraintlayout.widget.ConstraintLayout>
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/singular_1"
                    android:background="@drawable/grey_block_line_up_static"
                    android:visibility="gone"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">
                    <ProgressBar
                        android:id="@+id/health_first_progressbar_singular"
                        android:layout_width="0dp"
                        android:layout_height="6dp"
                        android:layout_marginTop="4dp"
                        android:layout_marginBottom="6dp"
                        android:max="100"
                        android:progress="80"
                        android:progressDrawable="@drawable/progress_bar"
                        android:layout_marginStart="7dp"
                        android:layout_marginEnd="7dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/text_remain_var_singular"
                        style="?android:attr/progressBarStyleHorizontal"/>
                    <TextView
                        android:textSize="13sp"
                        android:textColor="?attr/black"
                        android:id="@+id/health_percent_damage_singular"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="7dp"
                        android:text="@string/zero"
                        android:textAlignment="viewStart"
                        app:layout_constraintEnd_toStartOf="@+id/percent_singular"
                        app:layout_constraintStart_toEndOf="@+id/text_remain_var_singular"
                        app:layout_constraintTop_toTopOf="parent"/>
                    <TextView
                        android:textSize="13sp"
                        android:textColor="?attr/black"
                        android:id="@+id/percent_singular"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="7dp"
                        android:text="@string/percent_without_tab"
                        android:textAlignment="viewStart"
                        android:layout_marginEnd="10dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@+id/health_percent_damage_singular"
                        app:layout_constraintTop_toTopOf="parent"/>
                    <TextView
                        android:textSize="14sp"
                        android:textColor="?attr/black"
                        android:ellipsize="marquee"
                        android:id="@+id/text_remain_var_singular"
                        android:focusableInTouchMode="true"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="7dp"
                        android:text="@string/maximum_capacity"
                        android:singleLine="true"
                        android:marqueeRepeatLimit="marquee_forever"
                        android:textAlignment="viewStart"
                        android:layout_marginStart="10dp"
                        android:layout_marginEnd="5dp"
                        app:layout_constraintEnd_toStartOf="@+id/health_percent_damage_singular"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"/>
                </androidx.constraintlayout.widget.ConstraintLayout>
            </androidx.constraintlayout.widget.ConstraintLayout>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/d_2"
                android:background="@drawable/grey_block_line_static"
                android:visibility="visible"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp">
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:id="@+id/d_21"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/ma"
                    android:textAlignment="viewStart"
                    android:layout_marginEnd="10dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/colorr"
                    android:id="@+id/health_full_batery_capacity"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/zero"
                    android:layout_weight="1"
                    android:textAlignment="viewEnd"
                    android:layout_marginStart="5dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/d_21"
                    app:layout_constraintStart_toEndOf="@+id/d_22"
                    app:layout_constraintTop_toTopOf="parent"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:id="@+id/d_22"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:layout_marginBottom="12dp"
                    android:text="@string/design_capacity"
                    android:layout_weight="1"
                    android:textAlignment="viewStart"
                    android:layout_marginStart="10dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/health_full_batery_capacity"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="5dp"
                android:animateLayoutChanges="true">
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/singular_calculated"
                    android:background="@drawable/grey_block_line_static"
                    android:visibility="gone"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">
                    <TextView
                        android:textSize="14sp"
                        android:textColor="?attr/black"
                        android:id="@+id/singular_capacity_ni"
                        android:visibility="invisible"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/not_identified"
                        android:textAlignment="viewStart"
                        android:layout_marginEnd="10dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"/>
                    <TextView
                        android:textSize="14sp"
                        android:textColor="?attr/black"
                        android:id="@+id/singular_31"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/ma"
                        android:textAlignment="viewStart"
                        android:layout_marginEnd="10dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"/>
                    <TextView
                        android:textSize="14sp"
                        android:textColor="?attr/colorr"
                        android:id="@+id/health_checked_batery_capacity_singular"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/zero"
                        android:layout_weight="1"
                        android:textAlignment="viewEnd"
                        android:layout_marginStart="5dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@+id/singular_31"
                        app:layout_constraintStart_toEndOf="@+id/singular_32"
                        app:layout_constraintTop_toTopOf="parent"/>
                    <TextView
                        android:textSize="14sp"
                        android:textColor="?attr/black"
                        android:id="@+id/singular_32"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:layout_marginBottom="12dp"
                        android:text="@string/calculated_capacity"
                        android:layout_weight="1"
                        android:textAlignment="viewStart"
                        android:layout_marginStart="10dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@+id/health_checked_batery_capacity_singular"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"/>
                </androidx.constraintlayout.widget.ConstraintLayout>
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/cumulative_calculated"
                    android:background="@drawable/grey_block_line_static"
                    android:visibility="visible"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">
                    <TextView
                        android:textSize="14sp"
                        android:textColor="?attr/black"
                        android:id="@+id/cumulative_capacity_ni"
                        android:visibility="invisible"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/not_identified"
                        android:textAlignment="viewStart"
                        android:layout_marginEnd="10dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"/>
                    <TextView
                        android:textSize="14sp"
                        android:textColor="?attr/black"
                        android:id="@+id/cumulative_31"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/ma"
                        android:textAlignment="viewStart"
                        android:layout_marginEnd="10dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"/>
                    <TextView
                        android:textSize="14sp"
                        android:textColor="?attr/colorr"
                        android:id="@+id/health_checked_batery_capacity_cumulative"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/zero"
                        android:layout_weight="1"
                        android:textAlignment="viewEnd"
                        android:layout_marginStart="5dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@+id/cumulative_31"
                        app:layout_constraintStart_toEndOf="@+id/cumulative_32"
                        app:layout_constraintTop_toTopOf="parent"/>
                    <TextView
                        android:textSize="14sp"
                        android:textColor="?attr/black"
                        android:id="@+id/cumulative_32"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:layout_marginBottom="12dp"
                        android:text="@string/calculated_capacity"
                        android:layout_weight="1"
                        android:textAlignment="viewStart"
                        android:layout_marginStart="10dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@+id/health_checked_batery_capacity_cumulative"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"/>
                </androidx.constraintlayout.widget.ConstraintLayout>
            </androidx.constraintlayout.widget.ConstraintLayout>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="5dp"
                android:animateLayoutChanges="true">
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/cumulative_session_info"
                    android:background="@drawable/grey_block_line_down_static"
                    android:visibility="visible"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">
                    <TextView
                        android:textSize="14sp"
                        android:textColor="?attr/black"
                        android:ellipsize="marquee"
                        android:id="@+id/health_count_of_sessions_cumulative"
                        android:paddingTop="12dp"
                        android:paddingBottom="12dp"
                        android:focusable="true"
                        android:focusableInTouchMode="true"
                        android:visibility="visible"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:text="@string/zero"
                        android:singleLine="true"
                        android:marqueeRepeatLimit="marquee_forever"
                        android:paddingStart="10dp"
                        android:paddingEnd="10dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"/>
                </androidx.constraintlayout.widget.ConstraintLayout>
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/singular_session_info"
                    android:background="@drawable/grey_block_line_down_static"
                    android:visibility="gone"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">
                    <TextView
                        android:textSize="14sp"
                        android:textColor="?attr/black"
                        android:ellipsize="marquee"
                        android:id="@+id/health_count_of_sessions_singular"
                        android:paddingTop="12dp"
                        android:paddingBottom="12dp"
                        android:focusable="true"
                        android:focusableInTouchMode="true"
                        android:visibility="visible"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:text="@string/zero"
                        android:singleLine="true"
                        android:marqueeRepeatLimit="marquee_forever"
                        android:paddingStart="10dp"
                        android:paddingEnd="10dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"/>
                </androidx.constraintlayout.widget.ConstraintLayout>
            </androidx.constraintlayout.widget.ConstraintLayout>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="8dp">
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/cumulative_btn"
                    android:background="@drawable/grey_block_line_up_left"
                    android:focusable="true"
                    android:clickable="true"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_marginEnd="2.5dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/singular_btn"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">
                    <TextView
                        android:textSize="14sp"
                        android:textColor="?attr/black"
                        android:layout_marginVertical="10dp"
                        android:gravity="center"
                        android:layout_gravity="center"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:text="@string/cumulative"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"/>
                </androidx.constraintlayout.widget.ConstraintLayout>
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/singular_btn"
                    android:background="@drawable/grey_block_line_up_right"
                    android:focusable="true"
                    android:visibility="visible"
                    android:clickable="true"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_marginStart="2.5dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/cumulative_btn"
                    app:layout_constraintTop_toTopOf="parent">
                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginVertical="10dp"
                        android:gravity="center"
                        android:text="@string/singular"
                        android:textColor="?attr/black"
                        android:textSize="14sp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />
                </androidx.constraintlayout.widget.ConstraintLayout>
            </androidx.constraintlayout.widget.ConstraintLayout>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:background="@drawable/grey_block_line_down_static"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:animateLayoutChanges="true">
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:id="@+id/method_text"
                    android:visibility="visible"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="7dp"
                    android:layout_marginBottom="7dp"
                    android:text="@string/cumulative_text"
                    android:layout_marginStart="9dp"
                    android:layout_marginEnd="7dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:id="@+id/method_text_singular"
                    android:visibility="gone"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="7dp"
                    android:layout_marginBottom="7dp"
                    android:text="@string/singular_text"
                    android:textAlignment="viewStart"
                    android:layout_marginStart="9dp"
                    android:layout_marginEnd="7dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
        </LinearLayout>
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/time_dead_viewgroup"
            android:background="@drawable/white_block"
            android:paddingBottom="8dp"
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="14dp"
            android:layout_marginStart="9dp"
            android:layout_marginEnd="9dp">
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/dead_time_up"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:animateLayoutChanges="true"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/dead_time_text">
                <LinearLayout
                    android:orientation="vertical"
                    android:id="@+id/dead_time_up_singular"
                    android:paddingTop="7dp"
                    android:paddingBottom="9dp"
                    android:visibility="visible"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingStart="8dp"
                    android:paddingEnd="8dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">
                    <RelativeLayout
                        android:background="@drawable/grey_block_static"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <RelativeLayout
                            android:id="@+id/proggersBarDamage_cumulative"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginBottom="2dp"
                            android:layout_below="@+id/text_top_dead_time_111_cumulative"
                            android:layout_centerHorizontal="true"
                            android:layout_marginStart="16dp">
                            <ProgressBar
                                android:id="@+id/damage_bar_percent_current_singular"
                                android:visibility="visible"
                                android:layout_width="match_parent"
                                android:layout_height="6dp"
                                android:max="100"
                                android:progress="90"
                                android:progressDrawable="@drawable/progress_bar_grey"
                                android:layout_centerInParent="true"
                                android:scaleX="-1"
                                style="?android:attr/progressBarStyleHorizontal"/>
                            <ProgressBar
                                android:id="@+id/damage_bar_seekwhite_singular"
                                android:visibility="visible"
                                android:layout_width="match_parent"
                                android:layout_height="6dp"
                                android:max="100"
                                android:progress="90"
                                android:progressDrawable="@drawable/progress_bar_empty_mycolor"
                                android:layout_centerInParent="true"
                                style="?android:attr/progressBarStyleHorizontal"/>
                            <SeekBar
                                android:focusable="auto"
                                android:visibility="invisible"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="2dp"
                                android:progressDrawable="@drawable/seek_bar"
                                android:thumb="@drawable/thumb"
                                android:layout_marginStart="0dp"/>
                        </RelativeLayout>
                        <SeekBar
                            android:id="@+id/seekBar_singular"
                            android:focusable="auto"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:max="100"
                            android:progress="90"
                            android:progressDrawable="@drawable/seek_bar"
                            android:thumb="@drawable/thumb"
                            android:layout_alignTop="@+id/proggersBarDamage_cumulative"
                            android:layout_alignBottom="@+id/proggersBarDamage_cumulative"
                            android:paddingStart="8dp"
                            android:paddingEnd="8dp"
                            android:layout_marginStart="0dp"/>
                        <RelativeLayout
                            android:visibility="gone"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:layout_below="@+id/text_top_dead_time_111_cumulative"
                            android:layout_alignParentBottom="false">
                            <ProgressBar
                                android:layout_width="match_parent"
                                android:layout_height="6dp"
                                android:max="100"
                                android:progress="78"
                                android:progressDrawable="@drawable/progress_bar"
                                android:layout_alignParentBottom="false"
                                android:layout_centerHorizontal="true"
                                android:layout_marginStart="14dp"
                                style="?android:attr/progressBarStyleHorizontal"/>
                        </RelativeLayout>
                        <RelativeLayout
                            android:id="@+id/text_top_dead_time_111_cumulative"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">
                            <TextView
                                android:textSize="14sp"
                                android:textColor="?attr/black"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="7dp"
                                android:text="@string/maximum_capacity"
                                android:textAlignment="viewStart"
                                android:layout_marginStart="10dp"
                                android:layout_toStartOf="@+id/percent_damage_cumulative"
                                android:layout_alignParentStart="true"/>
                            <LinearLayout
                                android:orientation="horizontal"
                                android:id="@+id/percent_damage_cumulative"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="7dp"
                                android:layout_marginEnd="10dp"
                                android:layout_alignParentEnd="true">
                                <TextView
                                    android:textSize="13sp"
                                    android:textColor="?attr/black"
                                    android:id="@+id/percent_damage_dead_singular"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@string/zero"/>
                                <TextView
                                    android:textSize="13sp"
                                    android:textColor="?attr/black"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:text="@string/percent_without_tab"
                                    android:layout_weight="1"/>
                            </LinearLayout>
                        </RelativeLayout>
                    </RelativeLayout>
                    <LinearLayout
                        android:gravity="center_vertical"
                        android:background="@drawable/grey_block_static"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp">
                        <TextView
                            android:textSize="14sp"
                            android:textColor="?attr/black"
                            android:id="@+id/text_dead_singular"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="12dp"
                            android:layout_marginBottom="12dp"
                            android:text="@string/zero"
                            android:textAlignment="viewStart"
                            android:layout_marginStart="10dp"
                            android:layout_marginEnd="10dp"/>
                    </LinearLayout>
                </LinearLayout>
                <LinearLayout
                    android:orientation="vertical"
                    android:id="@+id/dead_time_up_cummulative"
                    android:paddingTop="7dp"
                    android:paddingBottom="9dp"
                    android:visibility="gone"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingStart="8dp"
                    android:paddingEnd="8dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">
                    <RelativeLayout
                        android:background="@drawable/grey_block_static"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <RelativeLayout
                            android:id="@+id/proggersBarDamage"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginBottom="2dp"
                            android:layout_below="@+id/text_top_dead_time_111"
                            android:layout_centerHorizontal="true"
                            android:layout_marginStart="16dp">
                            <ProgressBar
                                android:id="@+id/damage_bar_percent_current"
                                android:visibility="visible"
                                android:layout_width="match_parent"
                                android:layout_height="6dp"
                                android:max="100"
                                android:progress="90"
                                android:progressDrawable="@drawable/progress_bar_grey"
                                android:layout_centerInParent="true"
                                android:scaleX="-1"
                                style="?android:attr/progressBarStyleHorizontal"/>
                            <ProgressBar
                                android:id="@+id/damage_bar_seekwhite"
                                android:visibility="visible"
                                android:layout_width="match_parent"
                                android:layout_height="6dp"
                                android:max="100"
                                android:progress="90"
                                android:progressDrawable="@drawable/progress_bar_empty_mycolor"
                                android:layout_centerInParent="true"
                                style="?android:attr/progressBarStyleHorizontal"/>
                            <SeekBar
                                android:focusable="auto"
                                android:visibility="invisible"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="2dp"
                                android:progressDrawable="@drawable/seek_bar"
                                android:thumb="@drawable/thumb"
                                android:layout_marginStart="0dp"/>
                        </RelativeLayout>
                        <SeekBar
                            android:id="@+id/seekBar"
                            android:focusable="auto"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:max="100"
                            android:progress="90"
                            android:progressDrawable="@drawable/seek_bar"
                            android:thumb="@drawable/thumb"
                            android:layout_alignTop="@+id/proggersBarDamage"
                            android:layout_alignBottom="@+id/proggersBarDamage"
                            android:paddingStart="8dp"
                            android:paddingEnd="8dp"
                            android:layout_marginStart="0dp"/>
                        <RelativeLayout
                            android:visibility="gone"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:layout_below="@+id/text_top_dead_time_111"
                            android:layout_alignParentBottom="false">
                            <ProgressBar
                                android:layout_width="match_parent"
                                android:layout_height="6dp"
                                android:max="100"
                                android:progress="78"
                                android:progressDrawable="@drawable/progress_bar"
                                android:layout_alignParentBottom="false"
                                android:layout_centerHorizontal="true"
                                android:layout_marginStart="14dp"
                                style="?android:attr/progressBarStyleHorizontal"/>
                        </RelativeLayout>
                        <RelativeLayout
                            android:id="@+id/text_top_dead_time_111"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">
                            <TextView
                                android:textSize="14sp"
                                android:textColor="?attr/black"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="7dp"
                                android:text="@string/maximum_capacity"
                                android:textAlignment="viewStart"
                                android:layout_marginStart="10dp"
                                android:layout_toStartOf="@+id/percent_damage"
                                android:layout_alignParentStart="true"/>
                            <LinearLayout
                                android:orientation="horizontal"
                                android:id="@+id/percent_damage"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="7dp"
                                android:layout_marginEnd="10dp"
                                android:layout_alignParentEnd="true">
                                <TextView
                                    android:textSize="13sp"
                                    android:textColor="?attr/black"
                                    android:id="@+id/percent_damage_dead"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@string/zero"/>
                                <TextView
                                    android:textSize="13sp"
                                    android:textColor="?attr/black"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:text="@string/percent_without_tab"
                                    android:layout_weight="1"/>
                            </LinearLayout>
                        </RelativeLayout>
                    </RelativeLayout>
                    <LinearLayout
                        android:gravity="center_vertical"
                        android:background="@drawable/grey_block_static"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp">
                        <TextView
                            android:textSize="14sp"
                            android:textColor="?attr/black"
                            android:id="@+id/text_dead"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="12dp"
                            android:layout_marginBottom="12dp"
                            android:text="@string/zero"
                            android:layout_marginStart="10dp"
                            android:layout_marginEnd="10dp"/>
                    </LinearLayout>
                </LinearLayout>
            </androidx.constraintlayout.widget.ConstraintLayout>
            <TextView
                android:textSize="19sp"
                android:textColor="?attr/black"
                android:ellipsize="marquee"
                android:id="@+id/dead_time_text"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="7dp"
                android:text="@string/dead_time"
                android:singleLine="true"
                android:marqueeRepeatLimit="marquee_forever"
                android:layout_marginStart="11dp"
                app:layout_constraintEnd_toStartOf="@+id/prediction_wear_info"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"/>
            <ImageView
                android:id="@+id/prediction_wear_info"
                android:layout_width="22sp"
                android:layout_height="0dp"
                android:src="@drawable/ic_note"
                android:scaleType="fitEnd"
                android:layout_marginStart="5dp"
                android:layout_marginEnd="8dp"
                android:layout_alignParentEnd="true"
                app:layout_constraintBottom_toBottomOf="@+id/dead_time_text"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/dead_time_text"
                app:layout_constraintTop_toTopOf="@+id/dead_time_text"/>
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:background="@drawable/grey_block_static"
                android:paddingTop="7dp"
                android:paddingBottom="7dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/dead_time_text_down"
                android:textAlignment="viewStart"
                android:paddingStart="9dp"
                android:paddingEnd="7dp"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/dead_time_up"/>
            <eightbitlab.com.blurview.BlurView
                android:id="@+id/blurView_dead_top"
                android:focusable="true"
                android:visibility="visible"
                android:clickable="true"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="@+id/dead_time_up"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@+id/dead_time_up">
                <LinearLayout
                    android:gravity="center"
                    android:layout_gravity="center"
                    android:id="@+id/health_access2"
                    android:background="@drawable/grey_block_selected_color"
                    android:focusable="true"
                    android:clickable="true"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp">
                    <TextView
                        android:textSize="14sp"
                        android:textColor="?attr/black"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:layout_marginBottom="12dp"
                        android:text="@string/needed_advanced_access"
                        android:layout_marginStart="10dp"
                        android:layout_marginEnd="10dp"/>
                </LinearLayout>
            </eightbitlab.com.blurview.BlurView>
        </androidx.constraintlayout.widget.ConstraintLayout>
        <RelativeLayout
            android:background="@drawable/white_block"
            android:visibility="visible"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="14dp"
            android:animateLayoutChanges="true"
            android:layout_marginStart="9dp"
            android:layout_marginEnd="9dp">
            <LinearLayout
                android:orientation="horizontal"
                android:id="@+id/why_i_need_to_do_this"
                android:visibility="invisible"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/percent_graph_change"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_below="@+id/under_graph_percent"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp">
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:gravity="center"
                    android:layout_gravity="center"
                    android:id="@+id/btn0"
                    android:background="@drawable/grey_block"
                    android:focusable="true"
                    android:clickable="true"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:text="@string/h_4"
                    android:layout_marginEnd="4dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/btn1"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:gravity="center"
                    android:layout_gravity="center"
                    android:id="@+id/btn1"
                    android:background="@drawable/grey_block"
                    android:focusable="true"
                    android:clickable="true"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:text="@string/h_8"
                    android:layout_marginStart="4dp"
                    android:layout_marginEnd="4dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/btn2"
                    app:layout_constraintStart_toEndOf="@+id/btn0"
                    app:layout_constraintTop_toTopOf="parent"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:gravity="center"
                    android:layout_gravity="center"
                    android:id="@+id/btn2"
                    android:background="@drawable/grey_block"
                    android:focusable="true"
                    android:clickable="true"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:text="@string/h_16"
                    android:layout_marginStart="4dp"
                    android:layout_marginEnd="4dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/btn3"
                    app:layout_constraintStart_toEndOf="@+id/btn1"
                    app:layout_constraintTop_toTopOf="parent"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:gravity="center"
                    android:layout_gravity="center"
                    android:id="@+id/btn3"
                    android:background="@drawable/grey_block"
                    android:paddingTop="10dp"
                    android:paddingBottom="10dp"
                    android:focusable="true"
                    android:clickable="true"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="@string/h_24"
                    android:layout_marginStart="4dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/btn2"
                    app:layout_constraintTop_toTopOf="parent"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:ellipsize="marquee"
                android:gravity="center"
                android:id="@+id/history_database"
                android:background="@drawable/grey_block"
                android:paddingTop="12.5dp"
                android:paddingBottom="12.5dp"
                android:focusable="true"
                android:clickable="true"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="8dp"
                android:text="@string/history"
                android:singleLine="true"
                android:visibility="gone"
                android:layout_below="@+id/sdfsd"
                android:marqueeRepeatLimit="marquee_forever"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"/>
            <TextView
                android:textSize="19sp"
                android:textColor="?attr/black"
                android:id="@+id/wear_rate_percent"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="7dp"
                android:text="@string/percentage_graph"
                android:layout_marginStart="12dp"/>
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:id="@+id/sdfsd"
                android:background="@drawable/grey_block_static"
                android:paddingTop="7dp"
                android:paddingBottom="7dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="@string/graph_percentage_text_down"
                android:layout_below="@+id/percent_graph_change"
                android:textAlignment="viewStart"
                android:paddingStart="9dp"
                android:paddingEnd="7dp"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"/>
            <LinearLayout
                android:orientation="horizontal"
                android:id="@+id/under_graph_percent"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="3dp"
                android:baselineAligned="false"
                android:layout_below="@+id/graph_percent"
                android:layout_centerHorizontal="true"
                android:layout_marginStart="16dp">
                <LinearLayout
                    android:orientation="horizontal"
                    android:background="@drawable/line_vertical"
                    android:visibility="invisible"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1.07"/>
                <TextView
                    android:textSize="11sp"
                    android:textColor="?attr/graph_text"
                    android:gravity="bottom|center_horizontal"
                    android:id="@+id/day_7_percent"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:text="@string/zero"
                    android:layout_weight="1"/>
                <TextView
                    android:textSize="11sp"
                    android:textColor="?attr/graph_text"
                    android:gravity="bottom|center_horizontal"
                    android:id="@+id/day_6_percent"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:text="@string/zero"
                    android:layout_weight="1"/>
                <TextView
                    android:textSize="11sp"
                    android:textColor="?attr/graph_text"
                    android:gravity="bottom|center_horizontal"
                    android:id="@+id/day_5_percent"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:text="@string/zero"
                    android:layout_weight="1"/>
                <TextView
                    android:textSize="11sp"
                    android:textColor="?attr/graph_text"
                    android:gravity="bottom|center_horizontal"
                    android:id="@+id/day_4_percent"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:text="@string/zero"
                    android:layout_weight="1"/>
                <TextView
                    android:textSize="11sp"
                    android:textColor="?attr/graph_text"
                    android:gravity="bottom|center_horizontal"
                    android:id="@+id/day_3_percent"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:text="@string/zero"
                    android:layout_weight="1"/>
                <TextView
                    android:textSize="11sp"
                    android:textColor="?attr/graph_text"
                    android:gravity="bottom|center_horizontal"
                    android:id="@+id/day_2_percent"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:text="@string/zero"
                    android:layout_weight="1"/>
                <TextView
                    android:textSize="11sp"
                    android:textColor="?attr/graph_text"
                    android:gravity="bottom|center_horizontal"
                    android:id="@+id/day_1_percent"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:text="@string/zero"
                    android:layout_weight="1"/>
                <LinearLayout
                    android:orientation="horizontal"
                    android:background="@drawable/line_vertical"
                    android:visibility="invisible"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1.07"/>
            </LinearLayout>
            <RelativeLayout
                android:id="@+id/graph_percent"
                android:layout_width="wrap_content"
                android:layout_height="200dp"
                android:layout_marginTop="7dp"
                android:foreground="@drawable/graph"
                android:layout_below="@+id/wear_rate_percent"
                android:layout_centerHorizontal="true"
                android:layout_marginStart="16dp">
                <LinearLayout
                    android:orientation="vertical"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">
                    <TextView
                        android:textSize="5.5sp"
                        android:visibility="invisible"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="0.5"/>
                    <TextView
                        android:textSize="11sp"
                        android:textColor="?attr/graph_text"
                        android:gravity="start|center_vertical"
                        android:background="@drawable/line_horizontal"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="90%"
                        android:layout_weight="1"
                        android:paddingStart="6dp"/>
                    <TextView
                        android:textSize="11sp"
                        android:textColor="?attr/graph_text"
                        android:gravity="start|center_vertical"
                        android:background="@drawable/line_horizontal"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="80%"
                        android:layout_weight="1"
                        android:paddingStart="6dp"/>
                    <TextView
                        android:textSize="11sp"
                        android:textColor="?attr/graph_text"
                        android:gravity="start|center_vertical"
                        android:background="@drawable/line_horizontal"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="70%"
                        android:layout_weight="1"
                        android:paddingStart="6dp"/>
                    <TextView
                        android:textSize="11sp"
                        android:textColor="?attr/graph_text"
                        android:gravity="start|center_vertical"
                        android:background="@drawable/line_horizontal"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="60%"
                        android:layout_weight="1"
                        android:paddingStart="6dp"/>
                    <TextView
                        android:textSize="11sp"
                        android:textColor="?attr/graph_text"
                        android:gravity="start|center_vertical"
                        android:background="@drawable/line_horizontal"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="50%"
                        android:layout_weight="1"
                        android:paddingStart="6dp"/>
                    <TextView
                        android:textSize="11sp"
                        android:textColor="?attr/graph_text"
                        android:gravity="start|center_vertical"
                        android:background="@drawable/line_horizontal"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="40%"
                        android:layout_weight="1"
                        android:paddingStart="6dp"/>
                    <TextView
                        android:textSize="11sp"
                        android:textColor="?attr/graph_text"
                        android:gravity="start|center_vertical"
                        android:background="@drawable/line_horizontal"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="30%"
                        android:layout_weight="1"
                        android:paddingStart="6dp"/>
                    <TextView
                        android:textSize="11sp"
                        android:textColor="?attr/graph_text"
                        android:gravity="start|center_vertical"
                        android:background="@drawable/line_horizontal"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="20%"
                        android:layout_weight="1"
                        android:paddingStart="6dp"/>
                    <TextView
                        android:textSize="11sp"
                        android:textColor="?attr/graph_text"
                        android:gravity="start|center_vertical"
                        android:background="@drawable/line_horizontal"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="10%"
                        android:layout_weight="1"
                        android:paddingStart="6dp"/>
                    <TextView
                        android:textSize="5.5sp"
                        android:visibility="invisible"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="0.5"/>
                </LinearLayout>
                <LinearLayout
                    android:orientation="horizontal"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:baselineAligned="false">
                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:orientation="horizontal"
                        android:background="@drawable/line_vertical"
                        android:visibility="invisible"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="0.5"/>
                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:background="@drawable/line_vertical"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"/>
                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:background="@drawable/line_vertical"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"/>
                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:background="@drawable/line_vertical"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"/>
                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:background="@drawable/line_vertical"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"/>
                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:background="@drawable/line_vertical"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"/>
                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:background="@drawable/line_vertical"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"/>
                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:background="@drawable/line_vertical"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"/>
                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:orientation="horizontal"
                        android:background="@drawable/line_vertical"
                        android:visibility="invisible"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="0.5"/>
                </LinearLayout>
                <RelativeLayout
                    android:id="@+id/chart1_percent"
                    android:padding="1dp"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:foreground="@drawable/hide_corners_graph"
                    android:layout_centerHorizontal="true">
                    <com.github.mikephil.charting.charts.LineChart
                        android:id="@+id/chart_percent"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"/>
                </RelativeLayout>
            </RelativeLayout>
        </RelativeLayout>
        <RelativeLayout
            android:id="@+id/grahp_temp_viewgroup"
            android:background="@drawable/white_block"
            android:paddingBottom="8dp"
            android:visibility="visible"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="14dp"
            android:animateLayoutChanges="true"
            android:layout_marginStart="9dp"
            android:layout_marginEnd="9dp">
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/time_graph_change"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_below="@+id/under_graph_temp"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp">

                <TextView
                    android:id="@+id/btn0_t"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_gravity="center"
                    android:layout_marginEnd="4dp"
                    android:background="@drawable/grey_block"
                    android:clickable="true"
                    android:focusable="true"
                    android:gravity="center"
                    android:text="@string/h_4"
                    android:textColor="?attr/black"
                    android:textSize="14sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/btn1_t"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:gravity="center"
                    android:layout_gravity="center"
                    android:id="@+id/btn1_t"
                    android:background="@drawable/grey_block"
                    android:focusable="true"
                    android:clickable="true"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:text="@string/h_8"
                    android:layout_marginStart="4dp"
                    android:layout_marginEnd="4dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/btn2_t"
                    app:layout_constraintStart_toEndOf="@+id/btn0_t"
                    app:layout_constraintTop_toTopOf="parent"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:gravity="center"
                    android:layout_gravity="center"
                    android:id="@+id/btn2_t"
                    android:background="@drawable/grey_block"
                    android:focusable="true"
                    android:clickable="true"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:text="@string/h_16"
                    android:layout_marginStart="4dp"
                    android:layout_marginEnd="4dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/btn3_t"
                    app:layout_constraintStart_toEndOf="@+id/btn1_t"
                    app:layout_constraintTop_toTopOf="parent"/>
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:gravity="center"
                    android:layout_gravity="center"
                    android:id="@+id/btn3_t"
                    android:background="@drawable/grey_block"
                    android:paddingTop="10dp"
                    android:paddingBottom="10dp"
                    android:focusable="true"
                    android:clickable="true"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="@string/h_24"
                    android:layout_marginStart="4dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/btn2_t"
                    app:layout_constraintTop_toTopOf="parent"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
            <TextView
                android:textSize="19sp"
                android:textColor="?attr/black"
                android:id="@+id/wear_rate3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="7dp"
                android:text="@string/graph_temp"
                android:layout_marginStart="12dp"/>
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:background="@drawable/grey_block_static"
                android:paddingTop="7dp"
                android:paddingBottom="7dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="@string/graph_temp_text_down"
                android:layout_below="@+id/time_graph_change"
                android:textAlignment="viewStart"
                android:paddingStart="9dp"
                android:paddingEnd="7dp"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"/>
            <LinearLayout
                android:orientation="horizontal"
                android:id="@+id/under_graph_temp"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="3dp"
                android:baselineAligned="false"
                android:layout_below="@+id/graph_temp"
                android:layout_centerHorizontal="true"
                android:layout_marginStart="16dp">
                <LinearLayout
                    android:orientation="horizontal"
                    android:background="@drawable/line_vertical"
                    android:visibility="invisible"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1.07"/>
                <TextView
                    android:textSize="11sp"
                    android:textColor="?attr/graph_text"
                    android:gravity="bottom|center_horizontal"
                    android:id="@+id/day_7_temp"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:text="@string/zero"
                    android:layout_weight="1"/>
                <TextView
                    android:textSize="11sp"
                    android:textColor="?attr/graph_text"
                    android:gravity="bottom|center_horizontal"
                    android:id="@+id/day_6_temp"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:text="@string/zero"
                    android:layout_weight="1"/>
                <TextView
                    android:textSize="11sp"
                    android:textColor="?attr/graph_text"
                    android:gravity="bottom|center_horizontal"
                    android:id="@+id/day_5_temp"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:text="@string/zero"
                    android:layout_weight="1"/>
                <TextView
                    android:textSize="11sp"
                    android:textColor="?attr/graph_text"
                    android:gravity="bottom|center_horizontal"
                    android:id="@+id/day_4_temp"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:text="@string/zero"
                    android:layout_weight="1"/>
                <TextView
                    android:textSize="11sp"
                    android:textColor="?attr/graph_text"
                    android:gravity="bottom|center_horizontal"
                    android:id="@+id/day_3_temp"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:text="@string/zero"
                    android:layout_weight="1"/>
                <TextView
                    android:textSize="11sp"
                    android:textColor="?attr/graph_text"
                    android:gravity="bottom|center_horizontal"
                    android:id="@+id/day_2_temp"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:text="@string/zero"
                    android:layout_weight="1"/>
                <TextView
                    android:textSize="11sp"
                    android:textColor="?attr/graph_text"
                    android:gravity="bottom|center_horizontal"
                    android:id="@+id/day_1_temp"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:text="@string/zero"
                    android:layout_weight="1"/>
                <LinearLayout
                    android:orientation="horizontal"
                    android:background="@drawable/line_vertical"
                    android:visibility="invisible"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1.07"/>
            </LinearLayout>
            <RelativeLayout
                android:id="@+id/graph_temp"
                android:layout_width="wrap_content"
                android:layout_height="200dp"
                android:layout_marginTop="7dp"
                android:foreground="@drawable/graph"
                android:layout_below="@+id/wear_rate3"
                android:layout_centerHorizontal="true"
                android:layout_marginStart="16dp">
                <LinearLayout
                    android:orientation="vertical"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">
                    <TextView
                        android:textSize="5.5sp"
                        android:visibility="invisible"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="0.5"/>
                    <TextView
                        android:textSize="11sp"
                        android:textColor="?attr/graph_text"
                        android:gravity="start|center_vertical"
                        android:id="@+id/t1_temp"
                        android:background="@drawable/line_horizontal"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="0"
                        android:layout_weight="1"
                        android:paddingStart="6dp"/>
                    <TextView
                        android:textSize="11sp"
                        android:textColor="?attr/graph_text"
                        android:gravity="start|center_vertical"
                        android:id="@+id/t2_temp"
                        android:background="@drawable/line_horizontal"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="0"
                        android:layout_weight="1"
                        android:paddingStart="6dp"/>
                    <TextView
                        android:textSize="11sp"
                        android:textColor="?attr/graph_text"
                        android:gravity="start|center_vertical"
                        android:id="@+id/t3_temp"
                        android:background="@drawable/line_horizontal"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="0"
                        android:layout_weight="1"
                        android:paddingStart="6dp"/>
                    <TextView
                        android:textSize="11sp"
                        android:textColor="?attr/graph_text"
                        android:gravity="start|center_vertical"
                        android:id="@+id/t4_temp"
                        android:background="@drawable/line_horizontal"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="0"
                        android:layout_weight="1"
                        android:paddingStart="6dp"/>
                    <TextView
                        android:textSize="11sp"
                        android:textColor="?attr/graph_text"
                        android:gravity="start|center_vertical"
                        android:id="@+id/t5_temp"
                        android:background="@drawable/line_horizontal"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="0"
                        android:layout_weight="1"
                        android:paddingStart="6dp"/>
                    <TextView
                        android:textSize="11sp"
                        android:textColor="?attr/graph_text"
                        android:gravity="start|center_vertical"
                        android:id="@+id/t6_temp"
                        android:background="@drawable/line_horizontal"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="0"
                        android:layout_weight="1"
                        android:paddingStart="6dp"/>
                    <TextView
                        android:textSize="11sp"
                        android:textColor="?attr/graph_text"
                        android:gravity="start|center_vertical"
                        android:id="@+id/t7_temp"
                        android:background="@drawable/line_horizontal"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="0"
                        android:layout_weight="1"
                        android:paddingStart="6dp"/>
                    <TextView
                        android:textSize="11sp"
                        android:textColor="?attr/graph_text"
                        android:gravity="start|center_vertical"
                        android:id="@+id/t8_temp"
                        android:background="@drawable/line_horizontal"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="0"
                        android:layout_weight="1"
                        android:paddingStart="6dp"/>
                    <TextView
                        android:textSize="11sp"
                        android:textColor="?attr/graph_text"
                        android:gravity="start|center_vertical"
                        android:id="@+id/t9_temp"
                        android:background="@drawable/line_horizontal"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="0"
                        android:layout_weight="1"
                        android:paddingStart="6dp"/>
                    <TextView
                        android:textSize="5.5sp"
                        android:visibility="invisible"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="0.5"/>
                </LinearLayout>
                <LinearLayout
                    android:orientation="horizontal"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:baselineAligned="false">
                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:orientation="horizontal"
                        android:background="@drawable/line_vertical"
                        android:visibility="invisible"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="0.5"/>
                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:background="@drawable/line_vertical"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"/>
                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:background="@drawable/line_vertical"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"/>
                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:background="@drawable/line_vertical"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"/>
                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:background="@drawable/line_vertical"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"/>
                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:background="@drawable/line_vertical"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"/>
                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:background="@drawable/line_vertical"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"/>
                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:background="@drawable/line_vertical"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"/>
                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:orientation="horizontal"
                        android:background="@drawable/line_vertical"
                        android:visibility="invisible"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="0.5"/>
                </LinearLayout>
                <RelativeLayout
                    android:id="@+id/chart1_l"
                    android:padding="1dp"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:foreground="@drawable/hide_corners_graph"
                    android:layout_centerHorizontal="true">

                    <com.github.mikephil.charting.charts.LineChart
                        android:id="@+id/chart1"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent" />
                </RelativeLayout>
            </RelativeLayout>
        </RelativeLayout>
        <RelativeLayout
            android:background="@drawable/white_block"
            android:visibility="visible"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="14dp"
            android:layout_marginStart="9dp"
            android:layout_marginEnd="9dp">
            <TextView
                android:textSize="19sp"
                android:textColor="?attr/black"
                android:id="@+id/wear_rate2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="7dp"
                android:text="@string/graph"
                android:layout_marginStart="12dp"/>
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:background="@drawable/grey_block_static"
                android:paddingTop="7dp"
                android:paddingBottom="7dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="8dp"
                android:text="@string/graph_text_down"
                android:layout_below="@+id/under_graph"
                android:textAlignment="viewStart"
                android:paddingStart="9dp"
                android:paddingEnd="7dp"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"/>
            <LinearLayout
                android:orientation="horizontal"
                android:id="@+id/under_graph"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="3dp"
                android:baselineAligned="false"
                android:layout_below="@+id/graph"
                android:layout_centerHorizontal="true"
                android:layout_marginStart="16dp">
                <LinearLayout
                    android:orientation="horizontal"
                    android:visibility="invisible"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1.08"/>
                <TextView
                    android:textSize="11sp"
                    android:textColor="?attr/graph_text"
                    android:gravity="bottom|center_horizontal"
                    android:id="@+id/day_7"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:text="@string/zero"
                    android:layout_weight="1"/>
                <TextView
                    android:textSize="11sp"
                    android:textColor="?attr/graph_text"
                    android:gravity="bottom|center_horizontal"
                    android:id="@+id/day_6"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:text="@string/zero"
                    android:layout_weight="1"/>
                <TextView
                    android:textSize="11sp"
                    android:textColor="?attr/graph_text"
                    android:gravity="bottom|center_horizontal"
                    android:id="@+id/day_5"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:text="@string/zero"
                    android:layout_weight="1"/>
                <TextView
                    android:textSize="11sp"
                    android:textColor="?attr/graph_text"
                    android:gravity="bottom|center_horizontal"
                    android:id="@+id/day_4"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:text="@string/zero"
                    android:layout_weight="1"/>
                <TextView
                    android:textSize="11sp"
                    android:textColor="?attr/graph_text"
                    android:gravity="bottom|center_horizontal"
                    android:id="@+id/day_3"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:text="@string/zero"
                    android:layout_weight="1"/>
                <TextView
                    android:textSize="11sp"
                    android:textColor="?attr/graph_text"
                    android:gravity="bottom|center_horizontal"
                    android:id="@+id/day_2"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:text="@string/zero"
                    android:layout_weight="1"/>
                <TextView
                    android:textSize="11sp"
                    android:textColor="?attr/graph_text"
                    android:gravity="bottom|center_horizontal"
                    android:id="@+id/day_1"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:text="@string/zero"
                    android:layout_weight="1"/>
                <LinearLayout
                    android:orientation="horizontal"
                    android:background="@drawable/line_vertical"
                    android:visibility="invisible"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1.08"/>
            </LinearLayout>
            <RelativeLayout
                android:id="@+id/graph"
                android:layout_width="wrap_content"
                android:layout_height="200dp"
                android:layout_marginTop="7dp"
                android:foreground="@drawable/graph"
                android:layout_below="@+id/wear_rate2"
                android:layout_centerHorizontal="true"
                android:layout_marginStart="16dp">
                <LinearLayout
                    android:orientation="vertical"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">
                    <TextView
                        android:textSize="5.5sp"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="0.5"/>
                    <TextView
                        android:textSize="11sp"
                        android:textColor="?attr/graph_text"
                        android:gravity="start|center_vertical"
                        android:id="@+id/t1"
                        android:background="@drawable/line_horizontal"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="0"
                        android:layout_weight="1"
                        android:paddingStart="6dp"/>
                    <TextView
                        android:textSize="11sp"
                        android:textColor="?attr/graph_text"
                        android:gravity="start|center_vertical"
                        android:id="@+id/t2"
                        android:background="@drawable/line_horizontal"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="0"
                        android:layout_weight="1"
                        android:paddingStart="6dp"/>
                    <TextView
                        android:textSize="11sp"
                        android:textColor="?attr/graph_text"
                        android:gravity="start|center_vertical"
                        android:id="@+id/t3"
                        android:background="@drawable/line_horizontal"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="0"
                        android:layout_weight="1"
                        android:paddingStart="6dp"/>
                    <TextView
                        android:textSize="11sp"
                        android:textColor="?attr/graph_text"
                        android:gravity="start|center_vertical"
                        android:id="@+id/t4"
                        android:background="@drawable/line_horizontal"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="0"
                        android:layout_weight="1"
                        android:paddingStart="6dp"/>
                    <TextView
                        android:textSize="11sp"
                        android:textColor="?attr/graph_text"
                        android:gravity="start|center_vertical"
                        android:id="@+id/t5"
                        android:background="@drawable/line_horizontal"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="0"
                        android:layout_weight="1"
                        android:paddingStart="6dp"/>
                    <TextView
                        android:textSize="11sp"
                        android:textColor="?attr/graph_text"
                        android:gravity="start|center_vertical"
                        android:id="@+id/t6"
                        android:background="@drawable/line_horizontal"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="0"
                        android:layout_weight="1"
                        android:paddingStart="6dp"/>
                    <TextView
                        android:textSize="11sp"
                        android:textColor="?attr/graph_text"
                        android:gravity="start|center_vertical"
                        android:id="@+id/t7"
                        android:background="@drawable/line_horizontal"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="0"
                        android:layout_weight="1"
                        android:paddingStart="6dp"/>
                    <TextView
                        android:textSize="11sp"
                        android:textColor="?attr/graph_text"
                        android:gravity="start|center_vertical"
                        android:id="@+id/t8"
                        android:background="@drawable/line_horizontal"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="0"
                        android:layout_weight="1"
                        android:paddingStart="6dp"/>
                    <TextView
                        android:textSize="11sp"
                        android:textColor="?attr/graph_text"
                        android:gravity="start|center_vertical"
                        android:id="@+id/t9"
                        android:background="@drawable/line_horizontal"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="0"
                        android:layout_weight="1"
                        android:paddingStart="6dp"/>
                    <TextView
                        android:textSize="5.5sp"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="0.5"/>
                </LinearLayout>
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:orientation="horizontal"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">
                    <com.tqhit.battery.one.component.progress.VerticalProgressBar
                        android:id="@+id/progbar_7"
                        android:background="@drawable/line_vertical"
                        android:layout_width="7dp"
                        android:layout_height="match_parent"
                        android:max="110"
                        android:progress="10"
                        android:progressDrawable="@drawable/progress_bar_v"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@+id/progbar_6"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        style="?android:attr/progressBarStyleHorizontal"/>
                    <com.tqhit.battery.one.component.progress.VerticalProgressBar
                        android:id="@+id/progbar_6"
                        android:background="@drawable/line_vertical"
                        android:layout_width="7dp"
                        android:layout_height="match_parent"
                        android:max="110"
                        android:progress="10"
                        android:progressDrawable="@drawable/progress_bar_v"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@+id/progbar_5"
                        app:layout_constraintStart_toEndOf="@+id/progbar_7"
                        app:layout_constraintTop_toTopOf="parent"
                        style="?android:attr/progressBarStyleHorizontal"/>
                    <com.tqhit.battery.one.component.progress.VerticalProgressBar
                        android:id="@+id/progbar_5"
                        android:background="@drawable/line_vertical"
                        android:layout_width="7dp"
                        android:layout_height="match_parent"
                        android:max="110"
                        android:progress="10"
                        android:progressDrawable="@drawable/progress_bar_v"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@+id/progbar_4"
                        app:layout_constraintStart_toEndOf="@+id/progbar_6"
                        app:layout_constraintTop_toTopOf="parent"
                        style="?android:attr/progressBarStyleHorizontal"/>
                    <com.tqhit.battery.one.component.progress.VerticalProgressBar
                        android:id="@+id/progbar_4"
                        android:background="@drawable/line_vertical"
                        android:layout_width="7dp"
                        android:layout_height="match_parent"
                        android:max="110"
                        android:progress="10"
                        android:progressDrawable="@drawable/progress_bar_v"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@+id/progbar_3"
                        app:layout_constraintStart_toEndOf="@+id/progbar_5"
                        app:layout_constraintTop_toTopOf="parent"
                        style="?android:attr/progressBarStyleHorizontal"/>
                    <com.tqhit.battery.one.component.progress.VerticalProgressBar
                        android:id="@+id/progbar_3"
                        android:background="@drawable/line_vertical"
                        android:layout_width="7dp"
                        android:layout_height="match_parent"
                        android:max="110"
                        android:progress="10"
                        android:progressDrawable="@drawable/progress_bar_v"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@+id/progbar_2"
                        app:layout_constraintStart_toEndOf="@+id/progbar_4"
                        app:layout_constraintTop_toTopOf="parent"
                        style="?android:attr/progressBarStyleHorizontal"/>
                    <com.tqhit.battery.one.component.progress.VerticalProgressBar
                        android:id="@+id/progbar_2"
                        android:background="@drawable/line_vertical"
                        android:layout_width="7dp"
                        android:layout_height="match_parent"
                        android:max="110"
                        android:progress="10"
                        android:progressDrawable="@drawable/progress_bar_v"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@+id/progbar_1"
                        app:layout_constraintStart_toEndOf="@+id/progbar_3"
                        app:layout_constraintTop_toTopOf="parent"
                        style="?android:attr/progressBarStyleHorizontal"/>
                    <com.tqhit.battery.one.component.progress.VerticalProgressBar
                        android:id="@+id/progbar_1"
                        android:background="@drawable/line_vertical"
                        android:layout_width="7dp"
                        android:layout_height="match_parent"
                        android:max="110"
                        android:progress="10"
                        android:progressDrawable="@drawable/progress_bar_v"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@+id/progbar_2"
                        app:layout_constraintTop_toTopOf="parent"
                        style="?android:attr/progressBarStyleHorizontal"/>
                </androidx.constraintlayout.widget.ConstraintLayout>
            </RelativeLayout>
        </RelativeLayout>
        <androidx.constraintlayout.widget.ConstraintLayout
            android:orientation="vertical"
            android:id="@+id/indent_down"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginBottom="90dp"/>
        <LinearLayout
            android:gravity="center"
            android:orientation="horizontal"
            android:id="@+id/update_view"
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:id="@+id/update_view_btn"
                android:background="@drawable/grey_block"
                android:paddingTop="12.5dp"
                android:paddingBottom="12.5dp"
                android:focusable="true"
                android:visibility="invisible"
                android:clickable="true"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:layout_marginBottom="4dp"
                android:text=""
                android:singleLine="true"
                android:textAlignment="center"
                android:layout_marginEnd="4dp"/>
        </LinearLayout>
    </LinearLayout>
</androidx.core.widget.NestedScrollView>
