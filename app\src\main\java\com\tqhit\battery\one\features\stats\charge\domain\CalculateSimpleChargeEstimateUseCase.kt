package com.tqhit.battery.one.features.stats.charge.domain

import android.util.Log
import com.tqhit.battery.one.features.stats.charge.data.StatsChargeSession
import com.tqhit.battery.one.features.stats.charge.data.StatsChargeStatus
import javax.inject.Inject

/**
 * Use case for calculating simple charge time estimates.
 * Provides estimates for time to full charge and time to target percentage
 * based on current session data or default rates.
 */
class CalculateSimpleChargeEstimateUseCase @Inject constructor() {
    
    companion object {
        private const val TAG = "ChargeEstimateUseCase"
        
        // Default charge rate in percentage per hour (conservative estimate)
        private const val DEFAULT_CHARGE_RATE_PERCENT_PER_HOUR = 20.0
        
        // Minimum session duration to consider for rate calculation (5 minutes)
        private const val MIN_SESSION_DURATION_MILLIS = 5 * 60 * 1000L
        
        // Minimum percentage charged to consider for rate calculation
        private const val MIN_PERCENTAGE_CHARGED = 1
    }
    
    /**
     * Calculates charge time estimates.
     *
     * @param currentStatus Current battery status
     * @param activeSession Current active charge session (null if none)
     * @param batteryCapacityMah Battery capacity in mAh
     * @param targetPercentage Target percentage for estimation (default 100%)
     * @return Time to target in milliseconds (0 if not charging or already at/above target)
     */
    fun execute(
        currentStatus: StatsChargeStatus,
        activeSession: StatsChargeSession?,
        batteryCapacityMah: Int,
        targetPercentage: Int = 100
    ): Long {
        // Log.d(TAG, "Calculating charge estimate - " +
        //     "currentPercent=${currentStatus.percentage}%, " +
        //     "targetPercent=${targetPercentage}%, " +
        //     "isCharging=${currentStatus.isCharging}, " +
        //     "sessionActive=${activeSession?.isActive}, " +
        //     "batteryCapacity=${batteryCapacityMah}mAh")
        
        // Return 0 if not charging or already at/above target
        if (!currentStatus.isCharging || currentStatus.percentage >= targetPercentage) {
            // Log.d(TAG, "No estimation needed - not charging or already at target")
            return 0L
        }

        // Calculate percentage remaining to charge
        val percentageToCharge = targetPercentage - currentStatus.percentage

        // Determine charge rate
        val chargeRatePercentPerHour = calculateChargeRate(currentStatus, activeSession)

        // Calculate time in hours
        val timeHours = percentageToCharge / chargeRatePercentPerHour

        // Convert to milliseconds
        val timeMillis = (timeHours * 60 * 60 * 1000).toLong()

        // Log.d(TAG, "Charge estimate calculated - " +
        //     "percentageToCharge=${percentageToCharge}%, " +
        //     "chargeRate=${chargeRatePercentPerHour}%/h, " +
        //     "estimatedTime=${timeHours}h (${timeMillis}ms)")

        return timeMillis
    }
    
    /**
     * Calculates the charge rate based on current session or defaults.
     *
     * @param currentStatus Current battery status
     * @param activeSession Current active charge session
     * @return Charge rate in percentage per hour
     */
    private fun calculateChargeRate(
        currentStatus: StatsChargeStatus,
        activeSession: StatsChargeSession?
    ): Double {
        // Try to use session-based rate if available
        val sessionRate = calculateSessionBasedRate(currentStatus, activeSession)
        if (sessionRate != null) {
            // Log.d(TAG, "Using session-based charge rate: ${sessionRate}%/h")
            return sessionRate
        }

        // Fall back to default rate
        // Log.d(TAG, "Using default charge rate: ${DEFAULT_CHARGE_RATE_PERCENT_PER_HOUR}%/h")
        return DEFAULT_CHARGE_RATE_PERCENT_PER_HOUR
    }
    
    /**
     * Calculates charge rate based on current session data.
     *
     * @param currentStatus Current battery status
     * @param activeSession Current active charge session
     * @return Session-based charge rate in percentage per hour, or null if not reliable
     */
    private fun calculateSessionBasedRate(
        currentStatus: StatsChargeStatus,
        activeSession: StatsChargeSession?
    ): Double? {
        if (activeSession == null || !activeSession.isActive) {
            // Log.v(TAG, "No active session for rate calculation")
            return null
        }

        // Check if session has been running long enough
        if (activeSession.durationMillis < MIN_SESSION_DURATION_MILLIS) {
            // Log.v(TAG, "Session too short for reliable rate calculation: ${activeSession.durationMillis}ms")
            return null
        }

        // Calculate percentage charged so far
        val percentageCharged = activeSession.getPercentageCharged(currentStatus.percentage)

        // Check if enough percentage has been charged
        if (percentageCharged < MIN_PERCENTAGE_CHARGED) {
            // Log.v(TAG, "Not enough percentage charged for rate calculation: ${percentageCharged}%")
            return null
        }

        // Calculate rate: percentage charged per hour
        val sessionDurationHours = activeSession.durationMillis / (60.0 * 60.0 * 1000.0)
        val rate = percentageCharged / sessionDurationHours

        // Log.d(TAG, "Session-based rate calculation - " +
        //     "percentageCharged=${percentageCharged}%, " +
        //     "sessionDuration=${sessionDurationHours}h, " +
        //     "rate=${rate}%/h")

        // Sanity check: rate should be reasonable (between 1% and 200% per hour)
        return if (rate in 1.0..200.0) {
            rate
        } else {
            // Log.w(TAG, "Session-based rate unreasonable: ${rate}%/h, using default")
            null
        }
    }
    
    /**
     * Calculates time to full charge (100%).
     *
     * @param currentStatus Current battery status
     * @param activeSession Current active charge session
     * @param batteryCapacityMah Battery capacity in mAh
     * @return Time to full charge in milliseconds
     */
    fun calculateTimeToFull(
        currentStatus: StatsChargeStatus,
        activeSession: StatsChargeSession?,
        batteryCapacityMah: Int
    ): Long {
        return execute(currentStatus, activeSession, batteryCapacityMah, 100)
    }
    
    /**
     * Calculates time to target percentage.
     *
     * @param currentStatus Current battery status
     * @param activeSession Current active charge session
     * @param batteryCapacityMah Battery capacity in mAh
     * @param targetPercentage Target percentage
     * @return Time to target percentage in milliseconds
     */
    fun calculateTimeToTarget(
        currentStatus: StatsChargeStatus,
        activeSession: StatsChargeSession?,
        batteryCapacityMah: Int,
        targetPercentage: Int
    ): Long {
        return execute(currentStatus, activeSession, batteryCapacityMah, targetPercentage)
    }
}
