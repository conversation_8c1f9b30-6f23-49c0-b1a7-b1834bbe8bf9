package com.tqhit.battery.one

import android.app.Activity
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.webkit.WebView
import androidx.appcompat.app.AppCompatDelegate
//import com.applovin.sdk.AppLovinSdk
//import com.ironsource.mediationsdk.IronSource
import com.tqhit.adlib.sdk.AdLibHiltApplication
import com.tqhit.adlib.sdk.data.local.PreferencesHelper
import com.tqhit.adlib.sdk.utils.Constant
import com.tqhit.battery.one.repository.AppRepository
import com.tqhit.battery.one.manager.theme.ThemeManager
import com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryServiceHelper
import com.tqhit.battery.one.utils.BatteryLogger
import dagger.hilt.android.HiltAndroidApp
import javax.inject.Inject
import androidx.core.net.toUri
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
//import com.applovin.sdk.AppLovinMediationProvider
//import com.applovin.sdk.AppLovinPrivacySettings
//import com.applovin.sdk.AppLovinSdkConfiguration
//import com.applovin.sdk.AppLovinSdkConfiguration.ConsentFlowUserGeography
//import com.applovin.sdk.AppLovinSdkInitializationConfiguration
//import com.facebook.ads.AdSettings
import com.google.firebase.FirebaseApp
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.OnLifecycleEvent
import androidx.lifecycle.ProcessLifecycleOwner
// Legacy BatteryStatusServiceHelper import removed - service deprecated

@HiltAndroidApp
class BatteryApplication : AdLibHiltApplication(), LifecycleObserver {

    // Application-level coroutine scope for async initialization
    private val applicationScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)
    @Inject
    lateinit var preferencesHelper: PreferencesHelper
    @Inject
    lateinit var appRepository: AppRepository
    @Inject
    lateinit var applovinNativeAdManager: com.tqhit.battery.one.ads.core.ApplovinNativeAdManager
    @Inject
    lateinit var applovinRewardedAdManager: com.tqhit.battery.one.ads.core.ApplovinRewardedAdManager
    @Inject
    lateinit var applovinInterstitialAdManager: com.tqhit.battery.one.ads.core.ApplovinInterstitialAdManager
    @Inject
    lateinit var applovinBannerAdManager: com.tqhit.battery.one.ads.core.ApplovinBannerAdManager
    @Inject
    lateinit var applovinAppOpenAdManager: com.tqhit.battery.one.ads.core.ApplovinAppOpenAdManager
    // Legacy batteryStatusServiceHelper injection removed - service deprecated
    @Inject
    lateinit var coreBatteryServiceHelper: CoreBatteryServiceHelper

    companion object {
        private const val TAG = "BatteryApplication"
        var appSession: Int = 0
        var appOpenTime: Long = System.currentTimeMillis()
    }

    override fun onCreate() {
        val startTime = System.currentTimeMillis()
        BatteryLogger.d(TAG, "STARTUP_TIMING: BatteryApplication.onCreate() started at $startTime")

        // Essential synchronous initialization only
        val superStartTime = System.currentTimeMillis()
        super.onCreate()
        BatteryLogger.logTiming(TAG, "super.onCreate()", System.currentTimeMillis() - superStartTime)
        ProcessLifecycleOwner.get().lifecycle.addObserver(this)

        // Critical session tracking (keep synchronous for immediate availability)
        val prefsStartTime = System.currentTimeMillis()
        appSession = preferencesHelper.getInt("session", 0)
        appSession++
        preferencesHelper.saveInt("session", appSession)
        BatteryLogger.logTiming(TAG, "Preferences operations", System.currentTimeMillis() - prefsStartTime)

        // WebView setup (keep synchronous as it's needed for UI)
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                val process = getProcessName()
                if (packageName != process) WebView.setDataDirectorySuffix(process)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error: ${e.message}")
        }

        // Move heavy initialization to background thread
        initializeAsyncComponents()

        Log.d(TAG, "STARTUP_TIMING: BatteryApplication.onCreate() completed in ${System.currentTimeMillis() - startTime}ms")

//        val settings = AppLovinSdk.getInstance(this).settings
//        settings.termsAndPrivacyPolicyFlowSettings.isEnabled = true
//        settings.termsAndPrivacyPolicyFlowSettings.privacyPolicyUri =
//            "https://ahugames.com/privacy-policy.html".toUri()
//
//        // Terms of Service URL is optional
//        settings.termsAndPrivacyPolicyFlowSettings.termsOfServiceUri = null
    }

    /**
     * Initialize heavy components asynchronously to avoid blocking app startup
     */
    private fun initializeAsyncComponents() {
        applicationScope.launch(Dispatchers.IO) {
            val asyncStartTime = System.currentTimeMillis()
            Log.d(TAG, "STARTUP_TIMING: Async initialization started")

            try {
                // Firebase initialization (can be async)
                val firebaseStartTime = System.currentTimeMillis()
                FirebaseApp.initializeApp(this@BatteryApplication)
                Log.d(TAG, "STARTUP_TIMING: Async Firebase initialization took ${System.currentTimeMillis() - firebaseStartTime}ms")

                // Battery services startup (can be deferred)
                val servicesStartTime = System.currentTimeMillis()
                startBatteryStatusService()
                startCoreBatteryStatsService()
                Log.d(TAG, "STARTUP_TIMING: Async battery services startup took ${System.currentTimeMillis() - servicesStartTime}ms")

                Log.d(TAG, "STARTUP_TIMING: Total async initialization took ${System.currentTimeMillis() - asyncStartTime}ms")
            } catch (e: Exception) {
                Log.e(TAG, "Error in async initialization", e)
            }
        }
    }

    override fun onCreateExt() {
        val startTime = System.currentTimeMillis()
        Log.d(TAG, "STARTUP_TIMING: onCreateExt() started at $startTime")

        // Essential synchronous setup
        Constant.DEBUG_MODE = false
        admobHelper.setAppOpenAdUnitId("")

        // Critical theme initialization (needed for immediate UI)
        val themeStartTime = System.currentTimeMillis()
        ThemeManager.initialize(this)
        AppCompatDelegate.setDefaultNightMode(
            when (ThemeManager.getSelectedTheme()) {
                "AutoTheme" -> AppCompatDelegate.MODE_NIGHT_FOLLOW_SYSTEM
                "BlackTheme", "BlackThemeInverted", "AmoledTheme", "AmoledThemeInverted" -> AppCompatDelegate.MODE_NIGHT_YES
                else -> AppCompatDelegate.MODE_NIGHT_NO
            }
        )
        Log.d(TAG, "STARTUP_TIMING: Theme initialization took ${System.currentTimeMillis() - themeStartTime}ms")

        // Initialize language (needed for immediate UI)
        val languageStartTime = System.currentTimeMillis()
        initializeLanguage()
        Log.d(TAG, "STARTUP_TIMING: Language initialization took ${System.currentTimeMillis() - languageStartTime}ms")

        // Move non-critical initialization to background
        initializeNonCriticalComponents()

        Log.d(TAG, "STARTUP_TIMING: onCreateExt() completed in ${System.currentTimeMillis() - startTime}ms")
    }

    /**
     * Initialize non-critical components asynchronously
     */
    private fun initializeNonCriticalComponents() {
        applicationScope.launch(Dispatchers.IO) {
            try {
                val remoteConfigStartTime = System.currentTimeMillis()
                initRemoteConfig(R.xml.remote_config_defaults)
                Log.d(TAG, "STARTUP_TIMING: Async remote config initialization took ${System.currentTimeMillis() - remoteConfigStartTime}ms")
            } catch (e: Exception) {
                Log.e(TAG, "Error in non-critical initialization", e)
            }
        }
    }

    private fun initializeLanguage() {
        val savedLanguage = appRepository.getLanguage()
        val languageToUse = if (savedLanguage.isNotEmpty()) savedLanguage else appRepository.getDefaultLanguage()
        appRepository.setLocale(this, languageToUse)
    }

    override fun onActivityPreCreated(activity: Activity, savedInstanceState: Bundle?) {
        super.onActivityPreCreated(activity, savedInstanceState)
        ThemeManager.applyTheme(activity)
    }

    override fun onActivityResumed(activity: Activity) {
        super.onActivityResumed(activity)
//        IronSource.onResume(activity)
    }

    override fun onActivityPaused(activity: Activity) {
        super.onActivityPaused(activity)
//        IronSource.onPause(activity)
    }

    /**
     * DEPRECATED: Legacy BatteryStatusService - kept for reference only
     * This service has been replaced by CoreBatteryStatsService for unified battery monitoring
     * Multiple battery services were causing resource waste and data inconsistency
     */
    private fun startBatteryStatusService() {
        Log.d(TAG, "DEPRECATED: BatteryStatusService startup disabled - using CoreBatteryStatsService instead")
        // Legacy service startup commented out to eliminate duplicate battery monitoring
        // batteryStatusServiceHelper.startService()
    }

    /**
     * Starts the CoreBatteryStatsService to provide core battery monitoring
     */
    private fun startCoreBatteryStatsService() {
        Log.d(TAG, "Starting CoreBatteryStatsService from Application")
        try {
            coreBatteryServiceHelper.startService()
        } catch (e: Exception) {
            Log.e(TAG, "Failed to start CoreBatteryStatsService", e)
        }
    }

    // Lifecycle observer methods
    @OnLifecycleEvent(Lifecycle.Event.ON_START)
    fun onAppForegrounded() {
//        applovinAppOpenAdManager.showAppOpenAd("default_aoa")
        Log.d(TAG, "App moved to foreground")

        // DEPRECATED: Legacy battery service check disabled
        // Using only CoreBatteryStatsService for unified battery monitoring
        // if (!batteryStatusServiceHelper.isServiceRunning()) {
        //     Log.d(TAG, "Battery service not running, starting it now")
        //     batteryStatusServiceHelper.startService()
        // }

        if (!coreBatteryServiceHelper.isServiceRunning()) {
            Log.d(TAG, "CoreBatteryStatsService not running, starting it now")
            coreBatteryServiceHelper.startService()
        }
    }
}