# TJ_BatteryOne Notification Service Optimization Testing Script
# PowerShell version for enhanced reliability and error handling
# Run this script from PowerShell with: .\Test-NotificationOptimizations.ps1

param(
    [string]$AdbPath = "E:\IDE\Android\SDK\platform-tools\adb.exe",
    [string]$PackageId = "com.fc.p.tj.charginganimation.batterycharging.chargeeffect"
)

# Color coding for output
function Write-Success { param($Message) Write-Host "✓ $Message" -ForegroundColor Green }
function Write-Warning { param($Message) Write-Host "⚠ $Message" -ForegroundColor Yellow }
function Write-Error { param($Message) Write-Host "✗ $Message" -ForegroundColor Red }
function Write-Info { param($Message) Write-Host "ℹ $Message" -ForegroundColor Cyan }
function Write-Header { param($Message) Write-Host "`n========================================" -ForegroundColor Magenta; Write-Host $Message -ForegroundColor Magenta; Write-Host "========================================" -ForegroundColor Magenta }

Write-Header "TJ_BatteryOne Notification Optimization Testing"

# Check if ADB exists
if (-not (Test-Path $AdbPath)) {
    Write-Error "ADB not found at: $AdbPath"
    Write-Info "Please verify the ADB path is correct."
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Success "ADB found at: $AdbPath"

# Check device connection
Write-Info "Checking device connection..."
$devices = & $AdbPath devices 2>&1
if ($devices -match "device$") {
    Write-Success "Android device connected successfully!"
} else {
    Write-Error "No Android device connected or ADB not authorized"
    Write-Info "Please connect your device and enable USB debugging"
    Read-Host "Press Enter to exit"
    exit 1
}

# Test 1: Service Consolidation Verification
Write-Header "Test 1: Service Consolidation Verification"

Write-Info "Checking for CoreBatteryStatsService (should be running)..."
$coreService = & $AdbPath shell dumpsys activity services 2>&1 | Select-String "CoreBatteryStatsService"
if ($coreService) {
    Write-Success "CoreBatteryStatsService is running"
    Write-Host $coreService -ForegroundColor Gray
} else {
    Write-Warning "CoreBatteryStatsService not found - may need to start the app"
}

Write-Info "Checking for legacy services (should NOT be running)..."

$batteryStatusService = & $AdbPath shell dumpsys activity services 2>&1 | Select-String "BatteryStatusService"
if ($batteryStatusService) {
    Write-Warning "BatteryStatusService is still running (should be disabled)"
    Write-Host $batteryStatusService -ForegroundColor Gray
} else {
    Write-Success "BatteryStatusService is NOT running (correct)"
}

$batteryMonitorService = & $AdbPath shell dumpsys activity services 2>&1 | Select-String "BatteryMonitorService"
if ($batteryMonitorService) {
    Write-Warning "BatteryMonitorService is still running (should be disabled)"
    Write-Host $batteryMonitorService -ForegroundColor Gray
} else {
    Write-Success "BatteryMonitorService is NOT running (correct)"
}

$newChargeMonitorService = & $AdbPath shell dumpsys activity services 2>&1 | Select-String "NewChargeMonitorService"
if ($newChargeMonitorService) {
    Write-Warning "NewChargeMonitorService is still running (should be disabled)"
    Write-Host $newChargeMonitorService -ForegroundColor Gray
} else {
    Write-Success "NewChargeMonitorService is NOT running (correct)"
}

Read-Host "`nPress Enter to continue to performance testing"

# Test 2: Performance Verification
Write-Header "Test 2: Performance Verification"

Write-Info "Measuring app startup time..."
$startupResult = & $AdbPath shell am start -W "$PackageId/.activity.main.MainActivity" 2>&1
Write-Host $startupResult -ForegroundColor Gray

Write-Info "Waiting 5 seconds for app to stabilize..."
Start-Sleep -Seconds 5

Write-Info "Checking memory usage..."
$memInfo = & $AdbPath shell dumpsys meminfo $PackageId 2>&1 | Select-String "TOTAL"
if ($memInfo) {
    Write-Success "Memory usage information:"
    Write-Host $memInfo -ForegroundColor Gray
} else {
    Write-Warning "Could not retrieve memory information"
}

Write-Info "Checking CPU usage..."
$cpuInfo = & $AdbPath shell top -n 1 2>&1 | Select-String $PackageId
if ($cpuInfo) {
    Write-Success "CPU usage information:"
    Write-Host $cpuInfo -ForegroundColor Gray
} else {
    Write-Info "App may not be visible in top processes (low CPU usage)"
}

Read-Host "`nPress Enter to continue to battery status check"

# Test 3: Battery Status
Write-Header "Test 3: Battery Status"

Write-Info "Current battery status:"
$batteryStatus = & $AdbPath shell dumpsys battery 2>&1
Write-Host $batteryStatus -ForegroundColor Gray

Read-Host "`nPress Enter to start notification monitoring"

# Test 4: Notification Monitoring Setup
Write-Header "Test 4: Notification Monitoring"

Write-Info "Setting up notification monitoring..."
Write-Warning "MANUAL TESTING REQUIRED:"
Write-Host "1. Connect/disconnect charger to test charging state notifications" -ForegroundColor Yellow
Write-Host "2. Set target percentage in app and charge towards it" -ForegroundColor Yellow
Write-Host "3. Turn screen on/off to test adaptive intervals" -ForegroundColor Yellow
Write-Host "4. Monitor the logs below for optimization features" -ForegroundColor Yellow
Write-Host ""
Write-Info "Starting logcat monitoring for notification updates..."
Write-Info "Press Ctrl+C to stop monitoring and continue"
Write-Host ""

# Start logcat monitoring
try {
    & $AdbPath logcat -s "UnifiedBatteryNotificationService:D" "ChargeNotificationManager:D" 2>&1 | ForEach-Object {
        $line = $_
        if ($line -match "interval") {
            Write-Host $line -ForegroundColor Green
        } elseif ($line -match "Forcing notification update") {
            Write-Host $line -ForegroundColor Yellow
        } elseif ($line -match "Screen turned") {
            Write-Host $line -ForegroundColor Cyan
        } elseif ($line -match "Generated notification content") {
            Write-Host $line -ForegroundColor Magenta
        } else {
            Write-Host $line -ForegroundColor Gray
        }
    }
} catch {
    Write-Info "Monitoring stopped by user"
}

# Test 5: Specific Feature Testing
Write-Header "Test 5: Specific Feature Testing Commands"

Write-Info "Here are specific commands you can run to test individual features:"
Write-Host ""

Write-Host "Adaptive Update Intervals:" -ForegroundColor Cyan
Write-Host "  $AdbPath logcat -s `"UnifiedBatteryNotificationService:D`" | findstr `"interval`"" -ForegroundColor Gray
Write-Host ""

Write-Host "Content Caching:" -ForegroundColor Cyan
Write-Host "  $AdbPath logcat -s `"ChargeNotificationManager:V`" | findstr `"cached`"" -ForegroundColor Gray
Write-Host ""

Write-Host "Forced Updates:" -ForegroundColor Cyan
Write-Host "  $AdbPath logcat -s `"UnifiedBatteryNotificationService:D`" | findstr `"Forcing`"" -ForegroundColor Gray
Write-Host ""

Write-Host "Target Percentage Alerts:" -ForegroundColor Cyan
Write-Host "  $AdbPath logcat -s `"UnifiedBatteryNotificationService:D`" `"ChargeNotificationManager:D`" | findstr `"Target`"" -ForegroundColor Gray
Write-Host ""

Write-Host "Charging State Changes:" -ForegroundColor Cyan
Write-Host "  $AdbPath logcat -s `"UnifiedBatteryNotificationService:D`" | findstr `"Charging state`"" -ForegroundColor Gray
Write-Host ""

# Test 6: Log Collection
Write-Header "Test 6: Log Collection"

$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$logFile = "tj_battery_notification_logs_$timestamp.txt"

Write-Info "Collecting comprehensive logs for analysis..."
& $AdbPath logcat -d 2>&1 | Out-File -FilePath $logFile -Encoding UTF8

Write-Success "Logs saved to: $logFile"

$filteredLogFile = "tj_battery_filtered_logs_$timestamp.txt"
Write-Info "Creating filtered logs for notification components..."
& $AdbPath logcat -d -s "UnifiedBatteryNotificationService" "ChargeNotificationManager" "CoreBatteryStatsService" 2>&1 | Out-File -FilePath $filteredLogFile -Encoding UTF8

Write-Success "Filtered logs saved to: $filteredLogFile"

# Summary
Write-Header "Testing Summary"

Write-Info "Testing completed! Review the following:"
Write-Host "✓ Service consolidation verification" -ForegroundColor Green
Write-Host "✓ Performance benchmarking" -ForegroundColor Green
Write-Host "✓ Battery status monitoring" -ForegroundColor Green
Write-Host "✓ Notification monitoring setup" -ForegroundColor Green
Write-Host "✓ Feature-specific testing commands provided" -ForegroundColor Green
Write-Host "✓ Comprehensive log collection" -ForegroundColor Green
Write-Host ""

Write-Info "Key files created:"
Write-Host "- $logFile (comprehensive logs)" -ForegroundColor Gray
Write-Host "- $filteredLogFile (filtered notification logs)" -ForegroundColor Gray
Write-Host ""

Write-Info "For continued monitoring, use the specific commands provided above."
Write-Info "Check the log files for detailed analysis of notification behavior."

Read-Host "`nPress Enter to exit"
