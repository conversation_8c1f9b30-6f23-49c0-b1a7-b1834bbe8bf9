<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_slide_layout_6" modulePackage="com.tqhit.battery.one" filePath="app\src\main\res\layout\item_slide_layout_6.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.RelativeLayout"><Targets><Target tag="layout/item_slide_layout_6_0" view="RelativeLayout"><Expressions/><location startLine="1" startOffset="0" endLine="233" endOffset="16"/></Target><Target id="@+id/textView4" view="TextView"><Expressions/><location startLine="18" startOffset="8" endLine="28" endOffset="44"/></Target><Target id="@+id/textView16" view="TextView"><Expressions/><location startLine="29" startOffset="8" endLine="41" endOffset="44"/></Target><Target id="@+id/textView15" view="TextView"><Expressions/><location startLine="42" startOffset="8" endLine="52" endOffset="44"/></Target><Target id="@+id/dontkillmyapp_button" view="Button"><Expressions/><location startLine="61" startOffset="12" endLine="79" endOffset="66"/></Target><Target id="@+id/textView18" view="TextView"><Expressions/><location startLine="81" startOffset="8" endLine="93" endOffset="44"/></Target><Target id="@+id/work_in_background_permission" view="Button"><Expressions/><location startLine="101" startOffset="12" endLine="119" endOffset="66"/></Target><Target id="@+id/autorun_view" view="LinearLayout"><Expressions/><location startLine="121" startOffset="8" endLine="191" endOffset="22"/></Target><Target id="@+id/textView19" view="TextView"><Expressions/><location startLine="127" startOffset="12" endLine="140" endOffset="55"/></Target><Target id="@+id/button_layout" view="RelativeLayout"><Expressions/><location startLine="150" startOffset="16" endLine="189" endOffset="32"/></Target><Target id="@+id/buttonEnable" view="Button"><Expressions/><location startLine="162" startOffset="20" endLine="174" endOffset="74"/></Target><Target id="@+id/text_view_reset_charge" view="TextView"><Expressions/><location startLine="175" startOffset="20" endLine="188" endOffset="56"/></Target><Target id="@+id/button" view="RelativeLayout"><Expressions/><location startLine="194" startOffset="4" endLine="232" endOffset="20"/></Target><Target id="@+id/next_page" view="Button"><Expressions/><location startLine="215" startOffset="8" endLine="223" endOffset="62"/></Target></Targets></Layout>