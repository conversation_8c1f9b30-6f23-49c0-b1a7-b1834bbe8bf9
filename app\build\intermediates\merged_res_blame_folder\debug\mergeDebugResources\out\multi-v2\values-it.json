{"logs": [{"outputFile": "com.tqhit.battery.one.app-mergeDebugResources-99:/values-it/values-it.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\179e6486bd57a16ea175623aa423e7ed\\transformed\\jetified-material3-release\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,182,306,428,552,657,753,866,1009,1128,1286,1370,1482,1576,1676,1795,1917,2034,2176,2316,2459,2635,2770,2890,3013,3143,3238,3335,3462,3600,3700,3810,3916,4059,4207,4317,4418,4507,4603,4696,4811,4897,4983,5086,5166,5249,5348,5454,5554,5655,5743,5853,5953,6058,6176,6256,6370", "endColumns": "126,123,121,123,104,95,112,142,118,157,83,111,93,99,118,121,116,141,139,142,175,134,119,122,129,94,96,126,137,99,109,105,142,147,109,100,88,95,92,114,85,85,102,79,82,98,105,99,100,87,109,99,104,117,79,113,106", "endOffsets": "177,301,423,547,652,748,861,1004,1123,1281,1365,1477,1571,1671,1790,1912,2029,2171,2311,2454,2630,2765,2885,3008,3138,3233,3330,3457,3595,3695,3805,3911,4054,4202,4312,4413,4502,4598,4691,4806,4892,4978,5081,5161,5244,5343,5449,5549,5650,5738,5848,5948,6053,6171,6251,6365,6472"}, "to": {"startLines": "306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "28357,28484,28608,28730,28854,28959,29055,29168,29311,29430,29588,29672,29784,29878,29978,30097,30219,30336,30478,30618,30761,30937,31072,31192,31315,31445,31540,31637,31764,31902,32002,32112,32218,32361,32509,32619,32720,32809,32905,32998,33113,33199,33285,33388,33468,33551,33650,33756,33856,33957,34045,34155,34255,34360,34478,34558,34672", "endColumns": "126,123,121,123,104,95,112,142,118,157,83,111,93,99,118,121,116,141,139,142,175,134,119,122,129,94,96,126,137,99,109,105,142,147,109,100,88,95,92,114,85,85,102,79,82,98,105,99,100,87,109,99,104,117,79,113,106", "endOffsets": "28479,28603,28725,28849,28954,29050,29163,29306,29425,29583,29667,29779,29873,29973,30092,30214,30331,30473,30613,30756,30932,31067,31187,31310,31440,31535,31632,31759,31897,31997,32107,32213,32356,32504,32614,32715,32804,32900,32993,33108,33194,33280,33383,33463,33546,33645,33751,33851,33952,34040,34150,34250,34355,34473,34553,34667,34774"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b572512e02266e069f95737c22215ab9\\transformed\\jetified-media3-exoplayer-1.6.1\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,187,259,329,405,471,558,643", "endColumns": "70,60,71,69,75,65,86,84,73", "endOffsets": "121,182,254,324,400,466,553,638,712"}, "to": {"startLines": "236,237,238,239,240,241,242,243,244", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "22822,22893,22954,23026,23096,23172,23238,23325,23410", "endColumns": "70,60,71,69,75,65,86,84,73", "endOffsets": "22888,22949,23021,23091,23167,23233,23320,23405,23479"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9e0a763189144907fb0197c2b097244b\\transformed\\jetified-ui-release\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,292,390,490,577,656,762,855,950,1034,1122,1283,1355,1425,1503,1572", "endColumns": "99,86,97,99,86,78,105,92,94,83,87,84,71,69,77,68,120", "endOffsets": "200,287,385,485,572,651,757,850,945,1029,1117,1202,1350,1420,1498,1567,1688"}, "to": {"startLines": "142,143,179,180,203,290,292,433,439,479,480,500,517,518,523,524,526", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "11954,12054,17033,17131,20131,26837,26980,40253,40748,44206,44290,45724,47339,47411,47776,47854,47975", "endColumns": "99,86,97,99,86,78,105,92,94,83,87,84,71,69,77,68,120", "endOffsets": "12049,12136,17126,17226,20213,26911,27081,40341,40838,44285,44373,45804,47406,47476,47849,47918,48091"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\42f95d9fa807b14415e836fc15872a54\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-it\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "131", "endOffsets": "326"}, "to": {"startLines": "152", "startColumns": "4", "startOffsets": "13140", "endColumns": "135", "endOffsets": "13271"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8bd7feaae90e869538df51f29dd16595\\transformed\\jetified-media3-ui-1.6.1\\res\\values-it\\values-it.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,475,663,749,837,916,1008,1100,1178,1243,1343,1441,1506,1574,1639,1710,1838,1972,2098,2168,2261,2336,2412,2508,2606,2675,2743,2796,2854,2902,2963,3037,3108,3171,3252,3310,3371,3437,3489,3551,3627,3703,3761", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,85,87,78,91,91,77,64,99,97,64,67,64,70,127,133,125,69,92,74,75,95,97,68,67,52,57,47,60,73,70,62,80,57,60,65,51,61,75,75,57,69", "endOffsets": "281,470,658,744,832,911,1003,1095,1173,1238,1338,1436,1501,1569,1634,1705,1833,1967,2093,2163,2256,2331,2407,2503,2601,2670,2738,2791,2849,2897,2958,3032,3103,3166,3247,3305,3366,3432,3484,3546,3622,3698,3756,3826"}, "to": {"startLines": "2,11,15,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,381,570,20742,20828,20916,20995,21087,21179,21257,21322,21422,21520,21585,21653,21718,21789,21917,22051,22177,22247,22340,22415,22491,22587,22685,22754,23484,23537,23595,23643,23704,23778,23849,23912,23993,24051,24112,24178,24230,24292,24368,24444,24502", "endLines": "10,14,18,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261", "endColumns": "17,12,12,85,87,78,91,91,77,64,99,97,64,67,64,70,127,133,125,69,92,74,75,95,97,68,67,52,57,47,60,73,70,62,80,57,60,65,51,61,75,75,57,69", "endOffsets": "376,565,753,20823,20911,20990,21082,21174,21252,21317,21417,21515,21580,21648,21713,21784,21912,22046,22172,22242,22335,22410,22486,22582,22680,22749,22817,23532,23590,23638,23699,23773,23844,23907,23988,24046,24107,24173,24225,24287,24363,24439,24497,24567"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f908cdc45776521b403beeef1508641c\\transformed\\core-1.16.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,565,672,802", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "148,250,349,451,560,667,797,898"}, "to": {"startLines": "116,117,118,119,120,121,122,519", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "9693,9791,9893,9992,10094,10203,10310,47481", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "9786,9888,9987,10089,10198,10305,10435,47577"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5444be4bc77930bd89cfbb9f2224d8e4\\transformed\\navigation-ui-2.8.9\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,122", "endOffsets": "159,282"}, "to": {"startLines": "431,432", "startColumns": "4,4", "startOffsets": "40021,40130", "endColumns": "108,122", "endOffsets": "40125,40248"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d56ddc8f70c1b6c4f2dfff25a6818549\\transformed\\jetified-play-services-base-18.5.0\\res\\values-it\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,441,562,666,820,944,1060,1160,1313,1416,1566,1689,1841,2018,2081,2138", "endColumns": "100,146,120,103,153,123,115,99,152,102,149,122,151,176,62,56,72", "endOffsets": "293,440,561,665,819,943,1059,1159,1312,1415,1565,1688,1840,2017,2080,2137,2210"}, "to": {"startLines": "144,145,146,147,148,149,150,151,153,154,155,156,157,158,159,160,161", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12141,12246,12397,12522,12630,12788,12916,13036,13276,13433,13540,13694,13821,13977,14158,14225,14286", "endColumns": "104,150,124,107,157,127,119,103,156,106,153,126,155,180,66,60,76", "endOffsets": "12241,12392,12517,12625,12783,12911,13031,13135,13428,13535,13689,13816,13972,14153,14220,14281,14358"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5c5c72c6ff4a7863322da50648a25e99\\transformed\\browser-1.8.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,253,363", "endColumns": "99,97,109,102", "endOffsets": "150,248,358,461"}, "to": {"startLines": "166,264,265,266", "startColumns": "4,4,4,4", "startOffsets": "14622,24728,24826,24936", "endColumns": "99,97,109,102", "endOffsets": "14717,24821,24931,25034"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\93d3043f0a8b9466a00a736e170a6ddc\\transformed\\appcompat-1.7.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,6,7,10,11,12,13,14,15,16,17,18,19,22,26,27,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,506,611,883,975,1069,1162,1256,1357,1451,1548,1643,1735,2014,2429,2536,2799", "endColumns": "104,102,104,118,91,93,92,93,100,93,96,94,91,91,106,106,162,81", "endOffsets": "205,308,606,725,970,1064,1157,1251,1352,1446,1543,1638,1730,1822,2116,2531,2694,2876"}, "to": {"startLines": "39,40,43,44,47,48,49,50,51,52,53,54,55,56,59,63,64,496", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1792,1897,2134,2239,2459,2551,2645,2738,2832,2933,3027,3124,3219,3311,3530,3855,3962,45417", "endColumns": "104,102,104,118,91,93,92,93,100,93,96,94,91,91,106,106,162,81", "endOffsets": "1892,1995,2234,2353,2546,2640,2733,2827,2928,3022,3119,3214,3306,3398,3632,3957,4120,45494"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\237e0b5db534c615c4317f1b214e3e7f\\transformed\\jetified-play-services-ads-24.2.0\\res\\values-it\\values.xml", "from": {"startLines": "4,5,6,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,241,288,588,651,782,892,1018,1068,1126,1252,1344,1386,1485,1520,1555,1608,1690,1735", "endColumns": "41,46,63,62,130,109,125,49,57,125,91,41,98,34,34,52,81,44,55", "endOffsets": "240,287,351,650,781,891,1017,1067,1125,1251,1343,1385,1484,1519,1554,1607,1689,1734,1790"}, "to": {"startLines": "428,429,430,448,449,450,451,452,453,454,455,487,488,489,490,491,492,493,544", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "39856,39902,39953,41469,41536,41671,41785,41915,41969,42031,42161,44871,44917,45020,45059,45098,45155,45241,49319", "endColumns": "45,50,67,66,134,113,129,53,61,129,95,45,102,38,38,56,85,48,59", "endOffsets": "39897,39948,40016,41531,41666,41780,41910,41964,42026,42156,42252,44912,45015,45054,45093,45150,45236,45285,49374"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5b80dcbf636dc26335bd1b8e4f16f918\\transformed\\material-1.12.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,268,352,433,510,609,704,803,943,1026,1090,1156,1251,1336,1398,1486,1548,1617,1680,1753,1816,1870,1991,2048,2110,2164,2241,2378,2463,2543,2642,2728,2810,2945,3026,3107,3253,3344,3434,3489,3540,3606,3679,3759,3830,3910,3985,4062,4131,4208,4313,4401,4490,4583,4676,4750,4830,4924,4975,5059,5125,5209,5297,5359,5423,5486,5554,5669,5783,5889,5998,6057,6112,6192,6277,6356", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,83,80,76,98,94,98,139,82,63,65,94,84,61,87,61,68,62,72,62,53,120,56,61,53,76,136,84,79,98,85,81,134,80,80,145,90,89,54,50,65,72,79,70,79,74,76,68,76,104,87,88,92,92,73,79,93,50,83,65,83,87,61,63,62,67,114,113,105,108,58,54,79,84,78,81", "endOffsets": "263,347,428,505,604,699,798,938,1021,1085,1151,1246,1331,1393,1481,1543,1612,1675,1748,1811,1865,1986,2043,2105,2159,2236,2373,2458,2538,2637,2723,2805,2940,3021,3102,3248,3339,3429,3484,3535,3601,3674,3754,3825,3905,3980,4057,4126,4203,4308,4396,4485,4578,4671,4745,4825,4919,4970,5054,5120,5204,5292,5354,5418,5481,5549,5664,5778,5884,5993,6052,6107,6187,6272,6351,6433"}, "to": {"startLines": "19,106,107,108,109,110,126,127,141,209,211,263,287,295,367,368,369,370,371,372,373,374,375,376,377,378,379,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,463,497,498,509", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "758,8926,9010,9091,9168,9267,10638,10737,11871,20524,20676,24633,26616,27214,34951,35039,35101,35170,35233,35306,35369,35423,35544,35601,35663,35717,35794,36062,36147,36227,36326,36412,36494,36629,36710,36791,36937,37028,37118,37173,37224,37290,37363,37443,37514,37594,37669,37746,37815,37892,37997,38085,38174,38267,38360,38434,38514,38608,38659,38743,38809,38893,38981,39043,39107,39170,39238,39353,39467,39573,39682,39741,43153,45499,45584,46536", "endLines": "22,106,107,108,109,110,126,127,141,209,211,263,287,295,367,368,369,370,371,372,373,374,375,376,377,378,379,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,463,497,498,509", "endColumns": "12,83,80,76,98,94,98,139,82,63,65,94,84,61,87,61,68,62,72,62,53,120,56,61,53,76,136,84,79,98,85,81,134,80,80,145,90,89,54,50,65,72,79,70,79,74,76,68,76,104,87,88,92,92,73,79,93,50,83,65,83,87,61,63,62,67,114,113,105,108,58,54,79,84,78,81", "endOffsets": "921,9005,9086,9163,9262,9357,10732,10872,11949,20583,20737,24723,26696,27271,35034,35096,35165,35228,35301,35364,35418,35539,35596,35658,35712,35789,35926,36142,36222,36321,36407,36489,36624,36705,36786,36932,37023,37113,37168,37219,37285,37358,37438,37509,37589,37664,37741,37810,37887,37992,38080,38169,38262,38355,38429,38509,38603,38654,38738,38804,38888,38976,39038,39102,39165,39233,39348,39462,39568,39677,39736,39791,43228,45579,45658,46613"}}, {"source": "D:\\Duc\\AndroidStudioProjects\\TJ_BatteryOne\\app\\src\\main\\res\\values-it\\strings.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,237,18,19,20,21,22,23,24,25,26,27,28,29,265,30,31,32,33,34,35,36,241,251,254,256,37,2,264,244,262,38,39,40,41,42,43,44,45,115,116,114,113,117,46,47,48,49,50,51,52,53,54,55,56,239,57,58,243,59,60,61,62,63,64,65,66,67,68,249,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,119,120,259,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,260,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,242,168,169,170,171,172,173,174,175,176,177,178,247,246,245,253,257,179,180,181,182,183,184,255,185,186,187,240,188,261,189,190,191,192,193,194,195,196,248,197,198,258,199,200,201,202,203,252,204,205,206,207,208,209,210,211,212,213,214,215,216,263,217,218,219,220,221,222,223,224,225,226,250,227,228,229,230,231,232,233,234,235,236", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "130,197,246,296,346,396,450,499,548,604,674,724,772,823,871,21454,921,1001,1057,1109,1160,1212,1289,1358,1433,1510,1581,1659,24069,1715,1755,1934,1992,2083,2126,2188,21719,22549,23002,23126,2278,57,24022,21922,23898,2324,2371,2410,2498,2556,2631,3024,3422,12094,12160,11414,11325,12224,3464,3538,3611,3823,4121,4203,4260,4301,4361,4435,4487,21604,4555,4627,21845,4671,4751,4794,4866,5030,5098,5197,5278,5344,5435,22425,5511,5570,5627,5689,5744,5790,5859,5952,6003,6337,6394,6456,7021,7599,7668,7716,7870,7935,8224,8275,8763,8829,8958,9116,9272,9339,9388,9435,9507,9610,9698,9748,9900,9994,10053,10120,10192,10477,10732,10941,11103,11197,12311,12377,23704,12434,12476,12565,12627,12685,12901,12955,13036,13099,13157,13305,13365,13510,13750,13791,13851,13889,13927,13963,13999,14071,14114,14171,14224,14286,23767,14362,14433,14492,14537,14579,14655,15079,15137,15210,15385,15473,15546,15583,15618,15659,15702,15759,15821,15892,15953,16032,16188,21769,16223,16284,16335,16432,16503,16580,16683,16800,16878,16918,17004,22237,22057,21965,22892,23458,17278,17318,17373,17424,17490,17569,23052,17621,17696,17744,21674,17787,23832,17890,17985,18081,18134,18207,18385,18462,18521,22351,18580,18635,23505,18697,18782,18874,18957,19008,22826,19086,19167,19210,19266,19314,19434,19772,19848,19895,19954,20031,20092,20145,23962,20185,20255,20447,20527,20592,20664,20717,20791,20856,20895,22494,20957,21024,21071,21113,21167,21219,21288,21337,21373,21409", "endColumns": "65,47,48,48,48,52,47,47,54,68,48,46,49,46,48,89,78,54,50,49,50,75,67,73,75,69,76,54,69,38,177,56,89,41,60,88,48,275,48,330,44,71,45,41,62,45,37,86,56,73,391,396,40,64,62,678,87,83,72,71,210,296,80,55,39,58,72,50,66,68,70,42,75,78,41,70,162,66,97,79,64,89,74,67,57,55,60,53,44,67,91,49,332,55,60,563,576,67,46,152,63,287,49,486,64,127,156,154,65,47,45,70,101,86,48,150,92,57,65,70,283,253,207,160,92,81,64,55,61,40,87,60,56,214,52,79,61,56,146,58,143,238,39,58,36,36,34,34,70,41,55,51,60,74,63,69,57,43,40,74,422,56,71,173,86,71,35,33,39,41,55,60,69,59,77,154,33,74,59,49,95,69,75,101,115,76,38,84,272,112,178,90,108,45,38,53,49,64,77,50,72,73,46,41,43,101,64,93,94,51,71,176,75,57,57,72,53,60,197,83,90,81,49,76,64,79,41,54,46,118,336,74,45,57,75,59,51,38,58,68,190,78,63,70,51,72,63,37,60,53,65,45,40,52,50,67,47,34,34,43", "endOffsets": "191,240,290,340,390,444,493,542,598,668,718,766,817,865,915,21539,995,1051,1103,1154,1206,1283,1352,1427,1504,1575,1653,1709,24134,1749,1928,1986,2077,2120,2182,2272,21763,22820,23046,23452,2318,124,24063,21959,23956,2365,2404,2492,2550,2625,3018,3416,3458,12154,12218,12088,11408,12303,3532,3605,3817,4115,4197,4254,4295,4355,4429,4481,4549,21668,4621,4665,21916,4745,4788,4860,5024,5092,5191,5272,5338,5429,5505,22488,5564,5621,5683,5738,5784,5853,5946,5997,6331,6388,6450,7015,7593,7662,7710,7864,7929,8218,8269,8757,8823,8952,9110,9266,9333,9382,9429,9501,9604,9692,9742,9894,9988,10047,10114,10186,10471,10726,10935,11097,11191,11274,12371,12428,23761,12470,12559,12621,12679,12895,12949,13030,13093,13151,13299,13359,13504,13744,13785,13845,13883,13921,13957,13993,14065,14108,14165,14218,14280,14356,23826,14427,14486,14531,14573,14649,15073,15131,15204,15379,15467,15540,15577,15612,15653,15696,15753,15815,15886,15947,16026,16182,16217,21839,16278,16329,16426,16497,16574,16677,16794,16872,16912,16998,17272,22345,22231,22051,22996,23499,17312,17367,17418,17484,17563,17615,23120,17690,17738,17781,21713,17884,23892,17979,18075,18128,18201,18379,18456,18515,18574,22419,18629,18691,23698,18776,18868,18951,19002,19080,22886,19161,19204,19260,19308,19428,19766,19842,19889,19948,20025,20086,20139,20179,24016,20249,20441,20521,20586,20658,20711,20785,20850,20889,20951,22543,21018,21065,21107,21161,21213,21282,21331,21367,21403,21448"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,41,42,45,46,57,58,60,61,62,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,111,112,113,114,115,123,124,125,128,129,130,131,132,133,134,135,136,137,138,139,140,162,163,164,165,167,168,169,170,171,172,173,174,175,176,177,178,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,204,205,206,207,208,210,262,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,288,289,291,293,294,296,297,298,299,300,301,302,303,304,305,363,364,365,366,380,381,427,434,435,436,437,438,440,441,442,443,444,445,446,447,456,457,458,459,460,461,462,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,481,482,483,484,485,486,494,495,499,501,502,503,504,505,506,507,508,510,511,512,513,514,515,516,520,521,522,525,527,528,529,530,531,534,535,536,537,538,539,540,541,542,543,545,546,547,548,549,550,551,552", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "926,992,1040,1089,1138,1187,1240,1288,1336,1391,1460,1509,1556,1606,1653,1702,2000,2079,2358,2409,3403,3454,3637,3705,3779,4125,4195,4272,4327,4397,4436,4614,4671,4761,4803,4864,4953,5002,5278,5327,5658,5703,5775,5821,5863,5926,5972,6010,6097,6154,6228,6620,7017,7058,7123,7186,7865,7953,8037,8110,8182,8393,8690,8771,8827,8867,9362,9435,9486,9553,9622,10440,10483,10559,10877,10919,10990,11153,11220,11318,11398,11463,11553,11628,11696,11754,11810,14363,14417,14462,14530,14722,14772,15105,15161,15222,15786,16363,16431,16478,16631,16695,16983,17231,17718,17783,17911,18068,18223,18289,18337,18383,18454,18556,18643,18692,18843,18936,18994,19060,19131,19415,19669,19877,20038,20218,20300,20365,20421,20483,20588,24572,25039,25096,25311,25364,25444,25506,25563,25710,25769,25913,26152,26192,26251,26288,26325,26360,26395,26466,26508,26564,26701,26762,26916,27086,27156,27276,27320,27361,27436,27859,27916,27988,28162,28249,28321,34779,34813,34853,34895,35931,35992,39796,40346,40424,40579,40613,40688,40843,40893,40989,41059,41135,41237,41353,41430,42257,42342,42615,42728,42907,42998,43107,43233,43272,43326,43376,43441,43519,43570,43643,43717,43764,43806,43850,43952,44017,44111,44378,44430,44502,44679,44755,44813,45290,45363,45663,45809,46007,46091,46182,46264,46314,46391,46456,46618,46660,46715,46762,46881,47218,47293,47582,47640,47716,47923,48096,48135,48194,48263,48454,48730,48794,48865,48917,48990,49054,49092,49153,49207,49273,49379,49420,49473,49524,49592,49640,49675,49710", "endColumns": "65,47,48,48,48,52,47,47,54,68,48,46,49,46,48,89,78,54,50,49,50,75,67,73,75,69,76,54,69,38,177,56,89,41,60,88,48,275,48,330,44,71,45,41,62,45,37,86,56,73,391,396,40,64,62,678,87,83,72,71,210,296,80,55,39,58,72,50,66,68,70,42,75,78,41,70,162,66,97,79,64,89,74,67,57,55,60,53,44,67,91,49,332,55,60,563,576,67,46,152,63,287,49,486,64,127,156,154,65,47,45,70,101,86,48,150,92,57,65,70,283,253,207,160,92,81,64,55,61,40,87,60,56,214,52,79,61,56,146,58,143,238,39,58,36,36,34,34,70,41,55,51,60,74,63,69,57,43,40,74,422,56,71,173,86,71,35,33,39,41,55,60,69,59,77,154,33,74,59,49,95,69,75,101,115,76,38,84,272,112,178,90,108,45,38,53,49,64,77,50,72,73,46,41,43,101,64,93,94,51,71,176,75,57,57,72,53,60,197,83,90,81,49,76,64,79,41,54,46,118,336,74,45,57,75,59,51,38,58,68,190,78,63,70,51,72,63,37,60,53,65,45,40,52,50,67,47,34,34,43", "endOffsets": "987,1035,1084,1133,1182,1235,1283,1331,1386,1455,1504,1551,1601,1648,1697,1787,2074,2129,2404,2454,3449,3525,3700,3774,3850,4190,4267,4322,4392,4431,4609,4666,4756,4798,4859,4948,4997,5273,5322,5653,5698,5770,5816,5858,5921,5967,6005,6092,6149,6223,6615,7012,7053,7118,7181,7860,7948,8032,8105,8177,8388,8685,8766,8822,8862,8921,9430,9481,9548,9617,9688,10478,10554,10633,10914,10985,11148,11215,11313,11393,11458,11548,11623,11691,11749,11805,11866,14412,14457,14525,14617,14767,15100,15156,15217,15781,16358,16426,16473,16626,16690,16978,17028,17713,17778,17906,18063,18218,18284,18332,18378,18449,18551,18638,18687,18838,18931,18989,19055,19126,19410,19664,19872,20033,20126,20295,20360,20416,20478,20519,20671,24628,25091,25306,25359,25439,25501,25558,25705,25764,25908,26147,26187,26246,26283,26320,26355,26390,26461,26503,26559,26611,26757,26832,26975,27151,27209,27315,27356,27431,27854,27911,27983,28157,28244,28316,28352,34808,34848,34890,34946,35987,36057,39851,40419,40574,40608,40683,40743,40888,40984,41054,41130,41232,41348,41425,41464,42337,42610,42723,42902,42993,43102,43148,43267,43321,43371,43436,43514,43565,43638,43712,43759,43801,43845,43947,44012,44106,44201,44425,44497,44674,44750,44808,44866,45358,45412,45719,46002,46086,46177,46259,46309,46386,46451,46531,46655,46710,46757,46876,47213,47288,47334,47635,47711,47771,47970,48130,48189,48258,48449,48528,48789,48860,48912,48985,49049,49087,49148,49202,49268,49314,49415,49468,49519,49587,49635,49670,49705,49749"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\237635df39b25799c092d66a208ce67d\\transformed\\jetified-foundation-release\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,153", "endColumns": "97,98", "endOffsets": "148,247"}, "to": {"startLines": "532,533", "startColumns": "4,4", "startOffsets": "48533,48631", "endColumns": "97,98", "endOffsets": "48626,48725"}}]}]}