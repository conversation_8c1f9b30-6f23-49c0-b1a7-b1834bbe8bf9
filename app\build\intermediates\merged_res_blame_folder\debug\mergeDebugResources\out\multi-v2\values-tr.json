{"logs": [{"outputFile": "com.tqhit.battery.one.app-mergeDebugResources-99:/values-tr/values-tr.xml", "map": [{"source": "D:\\Duc\\AndroidStudioProjects\\TJ_BatteryOne\\app\\src\\main\\res\\values-tr\\strings.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,237,18,19,20,21,22,23,24,25,26,27,28,29,265,30,31,32,33,34,35,36,241,251,254,256,37,2,264,244,262,38,39,40,41,42,43,44,45,115,116,114,113,117,46,47,48,49,50,51,52,53,54,55,56,239,57,58,243,59,60,61,62,63,64,65,66,67,68,249,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,119,120,259,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,260,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,242,168,169,170,171,172,173,174,175,176,177,178,247,246,245,253,257,179,180,181,182,183,184,255,185,186,187,240,188,261,189,190,191,192,193,194,195,196,248,197,198,258,199,200,201,202,203,252,204,205,206,207,208,209,210,211,212,213,214,215,216,263,217,218,219,220,221,222,223,224,225,226,250,227,228,229,230,231,232,233,234,235,236", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "125,187,237,289,340,390,442,492,539,595,659,710,758,811,860,20134,912,997,1053,1105,1154,1204,1282,1349,1427,1501,1570,1637,22619,1689,1729,1879,1936,2023,2066,2123,20396,21195,21609,21729,2204,57,22572,20587,22452,2246,2294,2337,2423,2479,2548,2873,3205,11144,11210,10522,10444,11273,3247,3311,3381,3569,3847,3917,3968,4008,4062,4130,4190,20281,4259,4326,20521,4368,4450,4491,4558,4710,4774,4856,4932,4991,5089,21073,5148,5199,5250,5315,5371,5415,5486,5581,5631,5972,6026,6085,6524,6983,7050,7100,7231,7289,7544,7595,8043,8107,8247,8390,8532,8597,8640,8686,8755,8842,8922,8971,9094,9182,9240,9323,9402,9680,9927,10093,10252,10334,11360,11434,22272,11489,11529,11628,11697,11755,11979,12031,12095,12150,12204,12343,12400,12535,12743,12781,12833,12871,12909,12945,12981,13054,13097,13151,13202,13270,22330,13335,13399,13456,13498,13538,13607,13926,13977,14044,14188,14250,14322,14359,14394,14430,14468,14523,14581,14651,14704,14780,14916,20445,14954,15011,15062,15145,15216,15289,15406,15520,15587,15626,15698,20893,20705,20629,21512,22036,15969,16009,16059,16110,16170,16251,21666,16301,16378,16428,20351,16467,22392,16588,16681,16777,16836,16901,17109,17178,17236,20992,17296,17349,22080,17409,17489,17575,17649,17695,21442,17770,17841,17891,17957,18001,18128,18451,18527,18572,18627,18701,18761,18811,22517,18851,18918,19148,19237,19301,19373,19423,19496,19560,19594,21137,19650,19708,19752,19789,19844,19899,19965,20015,20053,20089", "endColumns": "60,48,50,49,48,50,48,45,54,62,49,46,51,47,50,86,83,54,50,47,48,76,65,76,72,67,65,50,68,38,148,55,85,41,55,79,47,245,55,305,40,66,45,40,63,46,41,84,54,67,323,330,40,64,61,620,76,83,62,68,186,276,68,49,38,52,66,58,67,68,65,40,64,80,39,65,150,62,80,74,57,96,57,62,49,49,63,54,42,69,93,48,339,52,57,437,457,65,48,129,56,253,49,446,62,138,141,140,63,41,44,67,85,78,47,121,86,56,81,77,276,245,164,157,80,63,72,53,56,38,97,67,56,222,50,62,53,52,137,55,133,206,36,50,36,36,34,34,71,41,52,49,66,63,60,62,55,40,38,67,317,49,65,142,60,70,35,33,34,36,53,56,68,51,74,134,36,74,55,49,81,69,71,115,112,65,37,70,269,97,186,74,95,42,38,48,49,58,79,48,61,75,48,37,43,119,58,91,94,57,63,206,67,56,58,79,51,58,190,78,84,72,44,73,68,69,48,64,42,125,321,74,43,53,72,58,48,38,53,65,228,87,62,70,48,71,62,32,54,56,56,42,35,53,53,64,48,36,34,43", "endOffsets": "181,231,283,334,384,436,486,533,589,653,704,752,805,854,906,20216,991,1047,1099,1148,1198,1276,1343,1421,1495,1564,1631,1683,22683,1723,1873,1930,2017,2060,2117,2198,20439,21436,21660,22030,2240,119,22613,20623,22511,2288,2331,2417,2473,2542,2867,3199,3241,11204,11267,11138,10516,11352,3305,3375,3563,3841,3911,3962,4002,4056,4124,4184,4253,20345,4320,4362,20581,4444,4485,4552,4704,4768,4850,4926,4985,5083,5142,21131,5193,5244,5309,5365,5409,5480,5575,5625,5966,6020,6079,6518,6977,7044,7094,7225,7283,7538,7589,8037,8101,8241,8384,8526,8591,8634,8680,8749,8836,8916,8965,9088,9176,9234,9317,9396,9674,9921,10087,10246,10328,10393,11428,11483,22324,11523,11622,11691,11749,11973,12025,12089,12144,12198,12337,12394,12529,12737,12775,12827,12865,12903,12939,12975,13048,13091,13145,13196,13264,13329,22386,13393,13450,13492,13532,13601,13920,13971,14038,14182,14244,14316,14353,14388,14424,14462,14517,14575,14645,14698,14774,14910,14948,20515,15005,15056,15139,15210,15283,15400,15514,15581,15620,15692,15963,20986,20887,20699,21603,22074,16003,16053,16104,16164,16245,16295,21723,16372,16422,16461,20390,16582,22446,16675,16771,16830,16895,17103,17172,17230,17290,21067,17343,17403,22266,17483,17569,17643,17689,17764,21506,17835,17885,17951,17995,18122,18445,18521,18566,18621,18695,18755,18805,18845,22566,18912,19142,19231,19295,19367,19417,19490,19554,19588,19644,21189,19702,19746,19783,19838,19893,19959,20009,20047,20083,20128"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,41,42,45,46,57,58,60,61,62,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,111,112,113,114,115,123,124,125,128,129,130,131,132,133,134,135,136,137,138,139,140,162,163,164,165,167,168,169,170,171,172,173,174,175,176,177,178,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,204,205,206,207,208,210,262,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,288,289,291,293,294,296,297,298,299,300,301,302,303,304,305,363,364,365,366,380,381,427,434,435,436,437,438,440,441,442,443,444,445,446,447,456,457,458,459,460,461,462,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,481,482,483,484,485,486,494,495,499,501,502,503,504,505,506,507,508,510,511,512,513,514,515,516,520,521,522,525,527,528,529,530,531,534,535,536,537,538,539,540,541,542,543,545,546,547,548,549,550,551,552", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "912,973,1022,1073,1123,1172,1223,1272,1318,1373,1436,1486,1533,1585,1633,1684,1984,2068,2349,2400,3390,3439,3620,3686,3763,4101,4169,4235,4286,4355,4394,4543,4599,4685,4727,4783,4863,4911,5157,5213,5519,5560,5627,5673,5714,5778,5825,5867,5952,6007,6075,6399,6730,6771,6836,6898,7519,7596,7680,7743,7812,7999,8276,8345,8395,8434,8904,8971,9030,9098,9167,9946,9987,10052,10341,10381,10447,10598,10661,10742,10817,10875,10972,11030,11093,11143,11193,13767,13822,13865,13935,14129,14178,14518,14571,14629,15067,15525,15591,15640,15770,15827,16081,16326,16773,16836,16975,17117,17258,17322,17364,17409,17477,17563,17642,17690,17812,17899,17956,18038,18116,18393,18639,18804,18962,19127,19191,19264,19318,19375,19474,23327,23805,23862,24085,24136,24199,24253,24306,24444,24500,24634,24841,24878,24929,24966,25003,25038,25073,25145,25187,25240,25367,25434,25581,25742,25805,25922,25963,26002,26070,26388,26438,26504,26647,26708,26779,32975,33009,33044,33081,34073,34130,37831,38351,38426,38561,38598,38673,38813,38863,38945,39015,39087,39203,39316,39382,40222,40293,40563,40661,40848,40923,41019,41141,41180,41229,41279,41338,41418,41467,41529,41605,41654,41692,41736,41856,41915,42007,42270,42328,42392,42599,42667,42724,43190,43270,43561,43691,43882,43961,44046,44119,44164,44238,44307,44451,44500,44565,44608,44734,45056,45131,45423,45477,45550,45755,45922,45961,46015,46081,46310,46569,46632,46703,46752,46824,46887,46920,46975,47032,47089,47192,47228,47282,47336,47401,47450,47487,47522", "endColumns": "60,48,50,49,48,50,48,45,54,62,49,46,51,47,50,86,83,54,50,47,48,76,65,76,72,67,65,50,68,38,148,55,85,41,55,79,47,245,55,305,40,66,45,40,63,46,41,84,54,67,323,330,40,64,61,620,76,83,62,68,186,276,68,49,38,52,66,58,67,68,65,40,64,80,39,65,150,62,80,74,57,96,57,62,49,49,63,54,42,69,93,48,339,52,57,437,457,65,48,129,56,253,49,446,62,138,141,140,63,41,44,67,85,78,47,121,86,56,81,77,276,245,164,157,80,63,72,53,56,38,97,67,56,222,50,62,53,52,137,55,133,206,36,50,36,36,34,34,71,41,52,49,66,63,60,62,55,40,38,67,317,49,65,142,60,70,35,33,34,36,53,56,68,51,74,134,36,74,55,49,81,69,71,115,112,65,37,70,269,97,186,74,95,42,38,48,49,58,79,48,61,75,48,37,43,119,58,91,94,57,63,206,67,56,58,79,51,58,190,78,84,72,44,73,68,69,48,64,42,125,321,74,43,53,72,58,48,38,53,65,228,87,62,70,48,71,62,32,54,56,56,42,35,53,53,64,48,36,34,43", "endOffsets": "968,1017,1068,1118,1167,1218,1267,1313,1368,1431,1481,1528,1580,1628,1679,1766,2063,2118,2395,2443,3434,3511,3681,3758,3831,4164,4230,4281,4350,4389,4538,4594,4680,4722,4778,4858,4906,5152,5208,5514,5555,5622,5668,5709,5773,5820,5862,5947,6002,6070,6394,6725,6766,6831,6893,7514,7591,7675,7738,7807,7994,8271,8340,8390,8429,8482,8966,9025,9093,9162,9228,9982,10047,10128,10376,10442,10593,10656,10737,10812,10870,10967,11025,11088,11138,11188,11252,13817,13860,13930,14024,14173,14513,14566,14624,15062,15520,15586,15635,15765,15822,16076,16126,16768,16831,16970,17112,17253,17317,17359,17404,17472,17558,17637,17685,17807,17894,17951,18033,18111,18388,18634,18799,18957,19038,19186,19259,19313,19370,19409,19567,23390,23857,24080,24131,24194,24248,24301,24439,24495,24629,24836,24873,24924,24961,24998,25033,25068,25140,25182,25235,25285,25429,25493,25637,25800,25856,25958,25997,26065,26383,26433,26499,26642,26703,26774,26810,33004,33039,33076,33130,34125,34194,37878,38421,38556,38593,38668,38724,38858,38940,39010,39082,39198,39311,39377,39415,40288,40558,40656,40843,40918,41014,41057,41175,41224,41274,41333,41413,41462,41524,41600,41649,41687,41731,41851,41910,42002,42097,42323,42387,42594,42662,42719,42778,43265,43317,43615,43877,43956,44041,44114,44159,44233,44302,44372,44495,44560,44603,44729,45051,45126,45170,45472,45545,45604,45799,45956,46010,46076,46305,46393,46627,46698,46747,46819,46882,46915,46970,47027,47084,47127,47223,47277,47331,47396,47445,47482,47517,47561"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\237635df39b25799c092d66a208ce67d\\transformed\\jetified-foundation-release\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,139", "endColumns": "83,86", "endOffsets": "134,221"}, "to": {"startLines": "532,533", "startColumns": "4,4", "startOffsets": "46398,46482", "endColumns": "83,86", "endOffsets": "46477,46564"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f908cdc45776521b403beeef1508641c\\transformed\\core-1.16.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,449,551,657,768", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "147,249,347,444,546,652,763,864"}, "to": {"startLines": "116,117,118,119,120,121,122,519", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "9233,9330,9432,9530,9627,9729,9835,45322", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "9325,9427,9525,9622,9724,9830,9941,45418"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8bd7feaae90e869538df51f29dd16595\\transformed\\jetified-media3-ui-1.6.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,472,652,741,834,909,994,1080,1155,1221,1306,1392,1460,1522,1582,1651,1768,1880,1997,2066,2150,2220,2296,2391,2490,2555,2619,2672,2730,2778,2839,2903,2970,3032,3098,3160,3217,3281,3333,3393,3467,3541,3598", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,88,92,74,84,85,74,65,84,85,67,61,59,68,116,111,116,68,83,69,75,94,98,64,63,52,57,47,60,63,66,61,65,61,56,63,51,59,73,73,56,68", "endOffsets": "280,467,647,736,829,904,989,1075,1150,1216,1301,1387,1455,1517,1577,1646,1763,1875,1992,2061,2145,2215,2291,2386,2485,2550,2614,2667,2725,2773,2834,2898,2965,3027,3093,3155,3212,3276,3328,3388,3462,3536,3593,3662"}, "to": {"startLines": "2,11,15,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,567,19636,19725,19818,19893,19978,20064,20139,20205,20290,20376,20444,20506,20566,20635,20752,20864,20981,21050,21134,21204,21280,21375,21474,21539,22279,22332,22390,22438,22499,22563,22630,22692,22758,22820,22877,22941,22993,23053,23127,23201,23258", "endLines": "10,14,18,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261", "endColumns": "17,12,12,88,92,74,84,85,74,65,84,85,67,61,59,68,116,111,116,68,83,69,75,94,98,64,63,52,57,47,60,63,66,61,65,61,56,63,51,59,73,73,56,68", "endOffsets": "375,562,742,19720,19813,19888,19973,20059,20134,20200,20285,20371,20439,20501,20561,20630,20747,20859,20976,21045,21129,21199,21275,21370,21469,21534,21598,22327,22385,22433,22494,22558,22625,22687,22753,22815,22872,22936,22988,23048,23122,23196,23253,23322"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d56ddc8f70c1b6c4f2dfff25a6818549\\transformed\\jetified-play-services-base-18.5.0\\res\\values-tr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,299,450,582,683,826,952,1075,1177,1345,1448,1601,1731,1872,2035,2093,2153", "endColumns": "105,150,131,100,142,125,122,101,167,102,152,129,140,162,57,59,75", "endOffsets": "298,449,581,682,825,951,1074,1176,1344,1447,1600,1730,1871,2034,2092,2152,2228"}, "to": {"startLines": "144,145,146,147,148,149,150,151,153,154,155,156,157,158,159,160,161", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "11516,11626,11781,11917,12022,12169,12299,12426,12679,12851,12958,13115,13249,13394,13561,13623,13687", "endColumns": "109,154,135,104,146,129,126,105,171,106,156,133,144,166,61,63,79", "endOffsets": "11621,11776,11912,12017,12164,12294,12421,12527,12846,12953,13110,13244,13389,13556,13618,13682,13762"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b572512e02266e069f95737c22215ab9\\transformed\\jetified-media3-exoplayer-1.6.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,191,256,317,397,469,559,655", "endColumns": "76,58,64,60,79,71,89,95,75", "endOffsets": "127,186,251,312,392,464,554,650,726"}, "to": {"startLines": "236,237,238,239,240,241,242,243,244", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "21603,21680,21739,21804,21865,21945,22017,22107,22203", "endColumns": "76,58,64,60,79,71,89,95,75", "endOffsets": "21675,21734,21799,21860,21940,22012,22102,22198,22274"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9e0a763189144907fb0197c2b097244b\\transformed\\jetified-ui-release\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,377,477,561,644,744,832,916,996,1084,1229,1304,1376,1454,1522", "endColumns": "92,83,94,99,83,82,99,87,83,79,87,70,74,71,77,67,117", "endOffsets": "193,277,372,472,556,639,739,827,911,991,1079,1150,1299,1371,1449,1517,1635"}, "to": {"startLines": "142,143,179,180,203,290,292,433,439,479,480,500,517,518,523,524,526", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "11339,11432,16131,16226,19043,25498,25642,38263,38729,42102,42182,43620,45175,45250,45609,45687,45804", "endColumns": "92,83,94,99,83,82,99,87,83,79,87,70,74,71,77,67,117", "endOffsets": "11427,11511,16221,16321,19122,25576,25737,38346,38808,42177,42265,43686,45245,45317,45682,45750,45917"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\42f95d9fa807b14415e836fc15872a54\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-tr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "142", "endOffsets": "337"}, "to": {"startLines": "152", "startColumns": "4", "startOffsets": "12532", "endColumns": "146", "endOffsets": "12674"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5c5c72c6ff4a7863322da50648a25e99\\transformed\\browser-1.8.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,261,368", "endColumns": "99,105,106,105", "endOffsets": "150,256,363,469"}, "to": {"startLines": "166,264,265,266", "startColumns": "4,4,4,4", "startOffsets": "14029,23486,23592,23699", "endColumns": "99,105,106,105", "endOffsets": "14124,23587,23694,23800"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5444be4bc77930bd89cfbb9f2224d8e4\\transformed\\navigation-ui-2.8.9\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,160", "endColumns": "104,116", "endOffsets": "155,272"}, "to": {"startLines": "431,432", "startColumns": "4,4", "startOffsets": "38041,38146", "endColumns": "104,116", "endOffsets": "38141,38258"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5b80dcbf636dc26335bd1b8e4f16f918\\transformed\\material-1.12.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,340,415,492,591,682,778,890,972,1032,1096,1187,1264,1325,1416,1479,1542,1601,1670,1733,1787,1895,1953,2015,2069,2142,2263,2347,2427,2526,2610,2701,2841,2918,2994,3125,3212,3288,3341,3395,3461,3531,3608,3679,3759,3830,3905,3983,4054,4155,4240,4329,4424,4517,4589,4661,4757,4809,4895,4962,5046,5136,5198,5262,5325,5395,5489,5591,5680,5780,5837,5895,5974,6058,6133", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,74,74,76,98,90,95,111,81,59,63,90,76,60,90,62,62,58,68,62,53,107,57,61,53,72,120,83,79,98,83,90,139,76,75,130,86,75,52,53,65,69,76,70,79,70,74,77,70,100,84,88,94,92,71,71,95,51,85,66,83,89,61,63,62,69,93,101,88,99,56,57,78,83,74,73", "endOffsets": "260,335,410,487,586,677,773,885,967,1027,1091,1182,1259,1320,1411,1474,1537,1596,1665,1728,1782,1890,1948,2010,2064,2137,2258,2342,2422,2521,2605,2696,2836,2913,2989,3120,3207,3283,3336,3390,3456,3526,3603,3674,3754,3825,3900,3978,4049,4150,4235,4324,4419,4512,4584,4656,4752,4804,4890,4957,5041,5131,5193,5257,5320,5390,5484,5586,5675,5775,5832,5890,5969,6053,6128,6202"}, "to": {"startLines": "19,106,107,108,109,110,126,127,141,209,211,263,287,295,367,368,369,370,371,372,373,374,375,376,377,378,379,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,463,497,498,509", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "747,8487,8562,8637,8714,8813,10133,10229,11257,19414,19572,23395,25290,25861,33135,33226,33289,33352,33411,33480,33543,33597,33705,33763,33825,33879,33952,34199,34283,34363,34462,34546,34637,34777,34854,34930,35061,35148,35224,35277,35331,35397,35467,35544,35615,35695,35766,35841,35919,35990,36091,36176,36265,36360,36453,36525,36597,36693,36745,36831,36898,36982,37072,37134,37198,37261,37331,37425,37527,37616,37716,37773,41062,43402,43486,44377", "endLines": "22,106,107,108,109,110,126,127,141,209,211,263,287,295,367,368,369,370,371,372,373,374,375,376,377,378,379,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,463,497,498,509", "endColumns": "12,74,74,76,98,90,95,111,81,59,63,90,76,60,90,62,62,58,68,62,53,107,57,61,53,72,120,83,79,98,83,90,139,76,75,130,86,75,52,53,65,69,76,70,79,70,74,77,70,100,84,88,94,92,71,71,95,51,85,66,83,89,61,63,62,69,93,101,88,99,56,57,78,83,74,73", "endOffsets": "907,8557,8632,8709,8808,8899,10224,10336,11334,19469,19631,23481,25362,25917,33221,33284,33347,33406,33475,33538,33592,33700,33758,33820,33874,33947,34068,34278,34358,34457,34541,34632,34772,34849,34925,35056,35143,35219,35272,35326,35392,35462,35539,35610,35690,35761,35836,35914,35985,36086,36171,36260,36355,36448,36520,36592,36688,36740,36826,36893,36977,37067,37129,37193,37256,37326,37420,37522,37611,37711,37768,37826,41136,43481,43556,44446"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\93d3043f0a8b9466a00a736e170a6ddc\\transformed\\appcompat-1.7.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,6,7,10,11,12,13,14,15,16,17,18,19,22,26,27,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,515,621,896,987,1080,1172,1266,1366,1459,1561,1656,1747,2024,2434,2543,2797", "endColumns": "113,98,105,119,90,92,91,93,99,92,101,94,90,90,103,108,155,79", "endOffsets": "214,313,616,736,982,1075,1167,1261,1361,1454,1556,1651,1742,1833,2123,2538,2694,2872"}, "to": {"startLines": "39,40,43,44,47,48,49,50,51,52,53,54,55,56,59,63,64,496", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1771,1885,2123,2229,2448,2539,2632,2724,2818,2918,3011,3113,3208,3299,3516,3836,3945,43322", "endColumns": "113,98,105,119,90,92,91,93,99,92,101,94,90,90,103,108,155,79", "endOffsets": "1880,1979,2224,2344,2534,2627,2719,2813,2913,3006,3108,3203,3294,3385,3615,3940,4096,43397"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\237e0b5db534c615c4317f1b214e3e7f\\transformed\\jetified-play-services-ads-24.2.0\\res\\values-tr\\values.xml", "from": {"startLines": "4,5,6,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,241,288,591,654,793,907,1042,1095,1148,1269,1361,1401,1492,1528,1562,1614,1700,1740", "endColumns": "41,46,56,62,138,113,134,52,52,120,91,39,90,35,33,51,85,39,55", "endOffsets": "240,287,344,653,792,906,1041,1094,1147,1268,1360,1400,1491,1527,1561,1613,1699,1739,1795"}, "to": {"startLines": "428,429,430,448,449,450,451,452,453,454,455,487,488,489,490,491,492,493,544", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "37883,37929,37980,39420,39487,39630,39748,39887,39944,40001,40126,42783,42827,42922,42962,43000,43056,43146,47132", "endColumns": "45,50,60,66,142,117,138,56,56,124,95,43,94,39,37,55,89,43,59", "endOffsets": "37924,37975,38036,39482,39625,39743,39882,39939,39996,40121,40217,42822,42917,42957,42995,43051,43141,43185,47187"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\179e6486bd57a16ea175623aa423e7ed\\transformed\\jetified-material3-release\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,283,401,516,612,708,821,954,1076,1216,1301,1399,1488,1585,1700,1821,1924,2061,2197,2319,2490,2608,2724,2842,2957,3047,3145,3269,3398,3499,3601,3707,3843,3983,4095,4197,4273,4370,4468,4578,4664,4749,4866,4946,5030,5130,5230,5326,5421,5509,5615,5715,5814,5935,6015,6122", "endColumns": "114,112,117,114,95,95,112,132,121,139,84,97,88,96,114,120,102,136,135,121,170,117,115,117,114,89,97,123,128,100,101,105,135,139,111,101,75,96,97,109,85,84,116,79,83,99,99,95,94,87,105,99,98,120,79,106,92", "endOffsets": "165,278,396,511,607,703,816,949,1071,1211,1296,1394,1483,1580,1695,1816,1919,2056,2192,2314,2485,2603,2719,2837,2952,3042,3140,3264,3393,3494,3596,3702,3838,3978,4090,4192,4268,4365,4463,4573,4659,4744,4861,4941,5025,5125,5225,5321,5416,5504,5610,5710,5809,5930,6010,6117,6210"}, "to": {"startLines": "306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "26815,26930,27043,27161,27276,27372,27468,27581,27714,27836,27976,28061,28159,28248,28345,28460,28581,28684,28821,28957,29079,29250,29368,29484,29602,29717,29807,29905,30029,30158,30259,30361,30467,30603,30743,30855,30957,31033,31130,31228,31338,31424,31509,31626,31706,31790,31890,31990,32086,32181,32269,32375,32475,32574,32695,32775,32882", "endColumns": "114,112,117,114,95,95,112,132,121,139,84,97,88,96,114,120,102,136,135,121,170,117,115,117,114,89,97,123,128,100,101,105,135,139,111,101,75,96,97,109,85,84,116,79,83,99,99,95,94,87,105,99,98,120,79,106,92", "endOffsets": "26925,27038,27156,27271,27367,27463,27576,27709,27831,27971,28056,28154,28243,28340,28455,28576,28679,28816,28952,29074,29245,29363,29479,29597,29712,29802,29900,30024,30153,30254,30356,30462,30598,30738,30850,30952,31028,31125,31223,31333,31419,31504,31621,31701,31785,31885,31985,32081,32176,32264,32370,32470,32569,32690,32770,32877,32970"}}]}]}