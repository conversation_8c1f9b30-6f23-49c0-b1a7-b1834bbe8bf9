package com.tqhit.battery.one.features.emoji.domain.model

import org.junit.Assert.*
import org.junit.Test

/**
 * Unit tests for BatteryStyleConfig domain model.
 * Tests configuration creation, validation, and utility methods.
 */
class BatteryStyleConfigTest {
    
    @Test
    fun `test default battery style config creation`() {
        // When
        val config = BatteryStyleConfig()
        
        // Then
        assertTrue(config.showEmoji)
        assertTrue(config.showPercentage)
        assertEquals(14, config.percentageFontSizeDp)
        assertEquals(1.0f, config.emojiSizeScale, 0.01f)
        assertEquals(0xFFFFFFFF.toInt(), config.percentageColor) // White
    }
    
    @Test
    fun `test custom battery style config creation`() {
        // Given
        val showEmoji = false
        val showPercentage = true
        val percentageFontSizeDp = 20
        val emojiSizeScale = 1.5f
        val percentageColor = 0xFF00FF00.toInt() // Green
        
        // When
        val config = BatteryStyleConfig(
            showEmoji = showEmoji,
            showPercentage = showPercentage,
            percentageFontSizeDp = percentageFontSizeDp,
            emojiSizeScale = emojiSizeScale,
            percentageColor = percentageColor
        )
        
        // Then
        assertEquals(showEmoji, config.showEmoji)
        assertEquals(showPercentage, config.showPercentage)
        assertEquals(percentageFontSizeDp, config.percentageFontSizeDp)
        assertEquals(emojiSizeScale, config.emojiSizeScale, 0.01f)
        assertEquals(percentageColor, config.percentageColor)
    }
    
    @Test
    fun `test isValid returns true for valid config`() {
        // Given
        val validConfig = BatteryStyleConfig(
            showEmoji = true,
            showPercentage = true,
            percentageFontSizeDp = 16,
            emojiSizeScale = 1.2f,
            percentageColor = 0xFFFF0000.toInt()
        )
        
        // When & Then
        assertTrue(validConfig.isValid())
    }
    
    @Test
    fun `test isValid returns true for minimum valid values`() {
        // Given
        val minValidConfig = BatteryStyleConfig(
            showEmoji = false,
            showPercentage = false,
            percentageFontSizeDp = 5, // Minimum
            emojiSizeScale = 0.5f, // Minimum
            percentageColor = 0x00000000
        )
        
        // When & Then
        assertTrue(minValidConfig.isValid())
    }
    
    @Test
    fun `test isValid returns true for maximum valid values`() {
        // Given
        val maxValidConfig = BatteryStyleConfig(
            showEmoji = true,
            showPercentage = true,
            percentageFontSizeDp = 40, // Maximum
            emojiSizeScale = 2.0f, // Maximum
            percentageColor = 0xFFFFFFFF.toInt()
        )
        
        // When & Then
        assertTrue(maxValidConfig.isValid())
    }
    
    @Test
    fun `test isValid returns false for font size below minimum`() {
        // Given
        val invalidConfig = BatteryStyleConfig(
            percentageFontSizeDp = 4, // Below minimum of 5
            emojiSizeScale = 1.0f
        )
        
        // When & Then
        assertFalse(invalidConfig.isValid())
    }
    
    @Test
    fun `test isValid returns false for font size above maximum`() {
        // Given
        val invalidConfig = BatteryStyleConfig(
            percentageFontSizeDp = 41, // Above maximum of 40
            emojiSizeScale = 1.0f
        )
        
        // When & Then
        assertFalse(invalidConfig.isValid())
    }
    
    @Test
    fun `test isValid returns false for emoji scale below minimum`() {
        // Given
        val invalidConfig = BatteryStyleConfig(
            percentageFontSizeDp = 14,
            emojiSizeScale = 0.4f // Below minimum of 0.5f
        )
        
        // When & Then
        assertFalse(invalidConfig.isValid())
    }
    
    @Test
    fun `test isValid returns false for emoji scale above maximum`() {
        // Given
        val invalidConfig = BatteryStyleConfig(
            percentageFontSizeDp = 14,
            emojiSizeScale = 2.1f // Above maximum of 2.0f
        )
        
        // When & Then
        assertFalse(invalidConfig.isValid())
    }
    
    @Test
    fun `test validated clamps font size to minimum`() {
        // Given
        val invalidConfig = BatteryStyleConfig(
            percentageFontSizeDp = 3, // Below minimum
            emojiSizeScale = 1.0f
        )
        
        // When
        val validatedConfig = invalidConfig.validated()
        
        // Then
        assertEquals(5, validatedConfig.percentageFontSizeDp) // Clamped to minimum
        assertEquals(1.0f, validatedConfig.emojiSizeScale, 0.01f) // Unchanged
        assertTrue(validatedConfig.isValid())
    }
    
    @Test
    fun `test validated clamps font size to maximum`() {
        // Given
        val invalidConfig = BatteryStyleConfig(
            percentageFontSizeDp = 50, // Above maximum
            emojiSizeScale = 1.0f
        )
        
        // When
        val validatedConfig = invalidConfig.validated()
        
        // Then
        assertEquals(40, validatedConfig.percentageFontSizeDp) // Clamped to maximum
        assertEquals(1.0f, validatedConfig.emojiSizeScale, 0.01f) // Unchanged
        assertTrue(validatedConfig.isValid())
    }
    
    @Test
    fun `test validated clamps emoji scale to minimum`() {
        // Given
        val invalidConfig = BatteryStyleConfig(
            percentageFontSizeDp = 14,
            emojiSizeScale = 0.3f // Below minimum
        )
        
        // When
        val validatedConfig = invalidConfig.validated()
        
        // Then
        assertEquals(14, validatedConfig.percentageFontSizeDp) // Unchanged
        assertEquals(0.5f, validatedConfig.emojiSizeScale, 0.01f) // Clamped to minimum
        assertTrue(validatedConfig.isValid())
    }
    
    @Test
    fun `test validated clamps emoji scale to maximum`() {
        // Given
        val invalidConfig = BatteryStyleConfig(
            percentageFontSizeDp = 14,
            emojiSizeScale = 3.0f // Above maximum
        )
        
        // When
        val validatedConfig = invalidConfig.validated()
        
        // Then
        assertEquals(14, validatedConfig.percentageFontSizeDp) // Unchanged
        assertEquals(2.0f, validatedConfig.emojiSizeScale, 0.01f) // Clamped to maximum
        assertTrue(validatedConfig.isValid())
    }
    
    @Test
    fun `test validated clamps both values when both are invalid`() {
        // Given
        val invalidConfig = BatteryStyleConfig(
            percentageFontSizeDp = 100, // Above maximum
            emojiSizeScale = 0.1f // Below minimum
        )
        
        // When
        val validatedConfig = invalidConfig.validated()
        
        // Then
        assertEquals(40, validatedConfig.percentageFontSizeDp) // Clamped to maximum
        assertEquals(0.5f, validatedConfig.emojiSizeScale, 0.01f) // Clamped to minimum
        assertTrue(validatedConfig.isValid())
    }
    
    @Test
    fun `test validated preserves valid values`() {
        // Given
        val validConfig = BatteryStyleConfig(
            showEmoji = false,
            showPercentage = true,
            percentageFontSizeDp = 18,
            emojiSizeScale = 1.3f,
            percentageColor = 0xFF0000FF.toInt()
        )
        
        // When
        val validatedConfig = validConfig.validated()
        
        // Then
        assertEquals(validConfig.showEmoji, validatedConfig.showEmoji)
        assertEquals(validConfig.showPercentage, validatedConfig.showPercentage)
        assertEquals(validConfig.percentageFontSizeDp, validatedConfig.percentageFontSizeDp)
        assertEquals(validConfig.emojiSizeScale, validatedConfig.emojiSizeScale, 0.01f)
        assertEquals(validConfig.percentageColor, validatedConfig.percentageColor)
        assertTrue(validatedConfig.isValid())
    }
    
    @Test
    fun `test createDefault returns valid default config`() {
        // When
        val defaultConfig = BatteryStyleConfig.createDefault()
        
        // Then
        assertTrue(defaultConfig.showEmoji)
        assertTrue(defaultConfig.showPercentage)
        assertEquals(14, defaultConfig.percentageFontSizeDp)
        assertEquals(1.0f, defaultConfig.emojiSizeScale, 0.01f)
        assertEquals(0xFFFFFFFF.toInt(), defaultConfig.percentageColor)
        assertTrue(defaultConfig.isValid())
    }
}
