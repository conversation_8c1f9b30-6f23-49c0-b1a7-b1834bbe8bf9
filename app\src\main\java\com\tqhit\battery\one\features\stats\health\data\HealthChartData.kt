package com.tqhit.battery.one.features.stats.health.data

import android.util.Log
import com.github.mikephil.charting.data.Entry
import kotlin.math.sin
import kotlin.random.Random

/**
 * Data model for health-related chart information.
 * Contains processed data ready for chart visualization.
 * 
 * @param batteryPercentageEntries List of entries for battery percentage chart
 * @param temperatureEntries List of entries for temperature chart
 * @param dailyWearData List of daily wear percentages for the last 7 days
 * @param selectedTimeRangeHours Currently selected time range for charts (4, 8, 12, 24)
 * @param timestampEpochMillis Timestamp when this chart data was generated
 */
data class HealthChartData(
    val batteryPercentageEntries: List<Entry>,
    val temperatureEntries: List<Entry>,
    val dailyWearData: List<Double>,
    val selectedTimeRangeHours: Int,
    val timestampEpochMillis: Long = System.currentTimeMillis()
) {
    
    /**
     * Validates that the chart data contains reasonable values.
     *
     * @return true if all data is valid for chart display
     */
    fun isValid(): Boolean {
        val batteryValid = batteryPercentageEntries.isNotEmpty()
        val tempValid = temperatureEntries.isNotEmpty()
        val dailyWearValid = dailyWearData.size == 7
        val timeRangeValid = selectedTimeRangeHours in listOf(4, 8, 12, 24)
        val timestampValid = timestampEpochMillis > 0

        val isValid = batteryValid && tempValid && dailyWearValid && timeRangeValid && timestampValid

        if (!isValid) {
            android.util.Log.w(TAG, "VALIDATION: HealthChartData validation failed - " +
                "battery: $batteryValid (${batteryPercentageEntries.size} entries), " +
                "temp: $tempValid (${temperatureEntries.size} entries), " +
                "dailyWear: $dailyWearValid (${dailyWearData.size} entries), " +
                "timeRange: $timeRangeValid ($selectedTimeRangeHours), " +
                "timestamp: $timestampValid ($timestampEpochMillis)")
        } else {
            android.util.Log.d(TAG, "VALIDATION: HealthChartData is valid - " +
                "battery: ${batteryPercentageEntries.size}, temp: ${temperatureEntries.size}, " +
                "dailyWear: ${dailyWearData.size}, timeRange: ${selectedTimeRangeHours}h")
        }

        return isValid
    }
    
    /**
     * Gets the maximum battery percentage from the chart data.
     * 
     * @return Maximum battery percentage or 0 if no data
     */
    fun getMaxBatteryPercentage(): Float {
        return batteryPercentageEntries.maxOfOrNull { it.y } ?: 0f
    }
    
    /**
     * Gets the minimum battery percentage from the chart data.
     * 
     * @return Minimum battery percentage or 0 if no data
     */
    fun getMinBatteryPercentage(): Float {
        return batteryPercentageEntries.minOfOrNull { it.y } ?: 0f
    }
    
    /**
     * Gets the maximum temperature from the chart data.
     * 
     * @return Maximum temperature or 0 if no data
     */
    fun getMaxTemperature(): Float {
        return temperatureEntries.maxOfOrNull { it.y } ?: 0f
    }
    
    /**
     * Gets the minimum temperature from the chart data.
     * 
     * @return Minimum temperature or 0 if no data
     */
    fun getMinTemperature(): Float {
        return temperatureEntries.minOfOrNull { it.y } ?: 0f
    }
    
    /**
     * Gets the maximum daily wear value.
     * 
     * @return Maximum daily wear percentage or 0 if no data
     */
    fun getMaxDailyWear(): Double {
        return dailyWearData.maxOrNull() ?: 0.0
    }
    
    /**
     * Gets the average daily wear value.
     * 
     * @return Average daily wear percentage
     */
    fun getAverageDailyWear(): Double {
        return if (dailyWearData.isNotEmpty()) {
            dailyWearData.average()
        } else {
            0.0
        }
    }
    
    companion object {
        private const val TAG = "HealthChartData"
        
        /**
         * Logs the creation of a HealthChartData object with summary information.
         *
         * @param chartData The HealthChartData object to log
         */
        fun logCreation(chartData: HealthChartData) {
            Log.d(TAG, "HEALTH_CHART_DATA_CREATED: " +
                "ID=${chartData.hashCode()}, " +
                "BatteryEntries=${chartData.batteryPercentageEntries.size}, " +
                "TempEntries=${chartData.temperatureEntries.size}, " +
                "DailyWearDays=${chartData.dailyWearData.size}, " +
                "TimeRange=${chartData.selectedTimeRangeHours}h, " +
                "Valid=${chartData.isValid()}")
        }
        
        /**
         * Creates a default/empty HealthChartData when real data is unavailable.
         * This ensures the application always has a valid chart data object to work with.
         *
         * @param timeRangeHours The time range to use for default data
         * @return A default HealthChartData with empty data
         */
        fun createEmpty(timeRangeHours: Int = 4): HealthChartData {
            val emptyChartData = HealthChartData(
                batteryPercentageEntries = emptyList(),
                temperatureEntries = emptyList(),
                dailyWearData = List(7) { 0.0 }, // 7 days of zero wear
                selectedTimeRangeHours = timeRangeHours
            )
            
            Log.d(TAG, "Empty HealthChartData created: timeRange=${timeRangeHours}h")
            return emptyChartData
        }
        
        /**
         * Creates sample HealthChartData for demonstration purposes.
         * This is useful for testing and when no real historical data is available.
         *
         * @param timeRangeHours The time range to generate sample data for
         * @return Sample HealthChartData with realistic values
         */
        fun createSample(timeRangeHours: Int = 4): HealthChartData {
            // Generate sample battery percentage data
            val batteryEntries = mutableListOf<Entry>()
            val dataPoints = timeRangeHours * 4 // 4 points per hour
            
            for (i in 0 until dataPoints) {
                val timeX = i.toFloat()
                // Simulate battery percentage changes over time
                val batteryY = (50 + 30 * sin(i * 0.1) + Random.nextDouble() * 10).toFloat()
                batteryEntries.add(Entry(timeX, batteryY.coerceIn(0f, 100f)))
            }
            
            // Generate sample temperature data
            val temperatureEntries = mutableListOf<Entry>()
            for (i in 0 until dataPoints) {
                val timeX = i.toFloat()
                // Simulate temperature variations
                val tempY = (25 + 10 * sin(i * 0.05) + Random.nextDouble() * 5).toFloat()
                temperatureEntries.add(Entry(timeX, tempY.coerceIn(15f, 45f)))
            }
            
            // Generate sample daily wear data (7 days)
            val dailyWear = List(7) {
                (Random.nextDouble() * 20 + 5) // Random wear between 5-25%
            }
            
            val sampleChartData = HealthChartData(
                batteryPercentageEntries = batteryEntries,
                temperatureEntries = temperatureEntries,
                dailyWearData = dailyWear,
                selectedTimeRangeHours = timeRangeHours
            )
            
            logCreation(sampleChartData)
            Log.d(TAG, "Sample HealthChartData created with ${batteryEntries.size} battery points, " +
                "${temperatureEntries.size} temperature points, and 7 daily wear values")
            
            return sampleChartData
        }
    }
}

/**
 * Enumeration of supported chart time ranges.
 * Defines the available time periods for historical data visualization.
 */
enum class HealthChartTimeRange(val hours: Int, val displayName: String) {
    FOUR_HOURS(4, "4h"),
    EIGHT_HOURS(8, "8h"),
    TWELVE_HOURS(12, "12h"),
    TWENTY_FOUR_HOURS(24, "24h");
    
    companion object {
        /**
         * Gets all available time ranges as a list.
         * 
         * @return List of all HealthChartTimeRange values
         */
        fun getAllRanges(): List<HealthChartTimeRange> {
            return values().toList()
        }
        
        /**
         * Finds a time range by its hour value.
         * 
         * @param hours The number of hours to find
         * @return Matching HealthChartTimeRange or FOUR_HOURS as default
         */
        fun fromHours(hours: Int): HealthChartTimeRange {
            return values().find { it.hours == hours } ?: FOUR_HOURS
        }
    }
}
